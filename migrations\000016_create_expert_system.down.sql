-- Drop triggers
DROP TRIGGER IF EXISTS update_expert_applications_updated_at ON expert_applications;
DROP TRIGGER IF EXISTS update_expert_profiles_updated_at ON expert_profiles;
DROP FUNCTION IF EXISTS update_expert_updated_at_column();

-- Drop indexes
DROP INDEX IF EXISTS idx_expert_applications_user_id;
DROP INDEX IF EXISTS idx_expert_applications_tenant_id;
DROP INDEX IF EXISTS idx_expert_applications_status;
DROP INDEX IF EXISTS idx_expert_applications_submitted_at;

DROP INDEX IF EXISTS idx_expert_profiles_user_id;
DROP INDEX IF EXISTS idx_expert_profiles_tenant_id;
DROP INDEX IF EXISTS idx_expert_profiles_status;
DROP INDEX IF EXISTS idx_expert_profiles_expertise;
DROP INDEX IF EXISTS idx_expert_profiles_rating;
DROP INDEX IF EXISTS idx_expert_profiles_availability;

DROP INDEX IF EXISTS idx_expert_specializations_profile_id;
DROP INDEX IF EXISTS idx_expert_specializations_name;

-- Drop tables
DROP TABLE IF EXISTS expert_specializations;
DROP TABLE IF EXISTS expert_profiles;
DROP TABLE IF EXISTS expert_applications;