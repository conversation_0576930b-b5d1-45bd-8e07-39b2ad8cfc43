package addon

import (
	"encoding/json"
	"fmt"
	"strings"
)

// GraphQLGenerator generates GraphQL types from addon configurations
type GraphQLGenerator struct{}

// NewGraphQLGenerator creates a new GraphQL generator
func NewGraphQLGenerator() *GraphQLGenerator {
	return &GraphQLGenerator{}
}

// AddonGraphQLSchema represents the generated GraphQL schema for an addon
type AddonGraphQLSchema struct {
	Types      []GraphQLType      `json:"types"`
	Queries    []GraphQLQuery     `json:"queries"`
	Mutations  []GraphQLMutation  `json:"mutations"`
	Inputs     []GraphQLInput     `json:"inputs"`
	Enums      []GraphQLEnum      `json:"enums"`
	Scalars    []GraphQLScalar    `json:"scalars"`
	Directives []GraphQLDirective `json:"directives"`
}

// GraphQLType represents a GraphQL object type
type GraphQLType struct {
	Name        string             `json:"name"`
	Description string             `json:"description"`
	Fields      []GraphQLField     `json:"fields"`
	Interfaces  []string           `json:"interfaces"`
	Directives  []GraphQLDirective `json:"directives"`
}

// GraphQLField represents a field in a GraphQL type
type GraphQLField struct {
	Name        string             `json:"name"`
	Type        string             `json:"type"`
	Description string             `json:"description"`
	Arguments   []GraphQLArgument  `json:"arguments"`
	Directives  []GraphQLDirective `json:"directives"`
	Nullable    bool               `json:"nullable"`
	List        bool               `json:"list"`
}

// GraphQLQuery represents a GraphQL query
type GraphQLQuery struct {
	Name        string             `json:"name"`
	Type        string             `json:"type"`
	Description string             `json:"description"`
	Arguments   []GraphQLArgument  `json:"arguments"`
	Directives  []GraphQLDirective `json:"directives"`
}

// GraphQLMutation represents a GraphQL mutation
type GraphQLMutation struct {
	Name        string             `json:"name"`
	Type        string             `json:"type"`
	Description string             `json:"description"`
	Arguments   []GraphQLArgument  `json:"arguments"`
	Directives  []GraphQLDirective `json:"directives"`
}

// GraphQLInput represents a GraphQL input type
type GraphQLInput struct {
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Fields      []GraphQLInputField `json:"fields"`
}

// GraphQLInputField represents a field in a GraphQL input type
type GraphQLInputField struct {
	Name         string `json:"name"`
	Type         string `json:"type"`
	Description  string `json:"description"`
	DefaultValue string `json:"default_value"`
	Nullable     bool   `json:"nullable"`
}

// GraphQLEnum represents a GraphQL enum type
type GraphQLEnum struct {
	Name        string             `json:"name"`
	Description string             `json:"description"`
	Values      []GraphQLEnumValue `json:"values"`
}

// GraphQLEnumValue represents a value in a GraphQL enum
type GraphQLEnumValue struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Deprecated  bool   `json:"deprecated"`
}

// GraphQLScalar represents a GraphQL scalar type
type GraphQLScalar struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

// GraphQLDirective represents a GraphQL directive
type GraphQLDirective struct {
	Name      string            `json:"name"`
	Arguments []GraphQLArgument `json:"arguments"`
}

// GraphQLArgument represents a GraphQL argument
type GraphQLArgument struct {
	Name         string `json:"name"`
	Type         string `json:"type"`
	Description  string `json:"description"`
	DefaultValue string `json:"default_value"`
	Nullable     bool   `json:"nullable"`
}

// GenerateFromAddonConfig generates GraphQL schema from addon configuration
func (g *GraphQLGenerator) GenerateFromAddonConfig(config string) (*AddonGraphQLSchema, error) {
	var addonConfig map[string]interface{}
	if err := json.Unmarshal([]byte(config), &addonConfig); err != nil {
		return nil, fmt.Errorf("failed to parse addon config: %w", err)
	}

	schema := &AddonGraphQLSchema{
		Types:      []GraphQLType{},
		Queries:    []GraphQLQuery{},
		Mutations:  []GraphQLMutation{},
		Inputs:     []GraphQLInput{},
		Enums:      []GraphQLEnum{},
		Scalars:    []GraphQLScalar{},
		Directives: []GraphQLDirective{},
	}

	// Extract addon metadata
	addonName := g.getStringValue(addonConfig, "name", "UnknownAddon")
	addonPrefix := g.sanitizeName(addonName)

	// Generate types from data models
	if models, ok := addonConfig["models"].(map[string]interface{}); ok {
		for modelName, modelDef := range models {
			if modelDefMap, ok := modelDef.(map[string]interface{}); ok {
				graphqlType := g.generateTypeFromModel(addonPrefix, modelName, modelDefMap)
				schema.Types = append(schema.Types, graphqlType)

				// Generate corresponding input type
				inputType := g.generateInputFromModel(addonPrefix, modelName, modelDefMap)
				schema.Inputs = append(schema.Inputs, inputType)
			}
		}
	}

	// Generate queries from endpoints
	if endpoints, ok := addonConfig["endpoints"].(map[string]interface{}); ok {
		for endpointName, endpointDef := range endpoints {
			if endpointDefMap, ok := endpointDef.(map[string]interface{}); ok {
				if method := g.getStringValue(endpointDefMap, "method", "GET"); method == "GET" {
					query := g.generateQueryFromEndpoint(addonPrefix, endpointName, endpointDefMap)
					schema.Queries = append(schema.Queries, query)
				} else {
					mutation := g.generateMutationFromEndpoint(addonPrefix, endpointName, endpointDefMap)
					schema.Mutations = append(schema.Mutations, mutation)
				}
			}
		}
	}

	// Generate enums from configuration
	if enums, ok := addonConfig["enums"].(map[string]interface{}); ok {
		for enumName, enumDef := range enums {
			if enumDefMap, ok := enumDef.(map[string]interface{}); ok {
				graphqlEnum := g.generateEnumFromConfig(addonPrefix, enumName, enumDefMap)
				schema.Enums = append(schema.Enums, graphqlEnum)
			}
		}
	}

	// Add default scalars if needed
	schema.Scalars = append(schema.Scalars, GraphQLScalar{
		Name:        "JSON",
		Description: "JSON scalar type for flexible data",
	})

	return schema, nil
}

// generateTypeFromModel generates a GraphQL type from a data model
func (g *GraphQLGenerator) generateTypeFromModel(prefix, modelName string, modelDef map[string]interface{}) GraphQLType {
	typeName := g.sanitizeName(prefix + modelName)

	graphqlType := GraphQLType{
		Name:        typeName,
		Description: g.getStringValue(modelDef, "description", fmt.Sprintf("%s type generated from addon", typeName)),
		Fields:      []GraphQLField{},
		Interfaces:  []string{},
		Directives:  []GraphQLDirective{},
	}

	// Add ID field by default
	graphqlType.Fields = append(graphqlType.Fields, GraphQLField{
		Name:        "id",
		Type:        "ID!",
		Description: "Unique identifier",
		Nullable:    false,
	})

	// Generate fields from model properties
	if properties, ok := modelDef["properties"].(map[string]interface{}); ok {
		for fieldName, fieldDef := range properties {
			if fieldDefMap, ok := fieldDef.(map[string]interface{}); ok {
				field := g.generateFieldFromProperty(fieldName, fieldDefMap)
				graphqlType.Fields = append(graphqlType.Fields, field)
			}
		}
	}

	// Add timestamp fields
	graphqlType.Fields = append(graphqlType.Fields,
		GraphQLField{
			Name:        "createdAt",
			Type:        "Time!",
			Description: "Creation timestamp",
			Nullable:    false,
		},
		GraphQLField{
			Name:        "updatedAt",
			Type:        "Time!",
			Description: "Last update timestamp",
			Nullable:    false,
		},
	)

	return graphqlType
}

// generateInputFromModel generates a GraphQL input type from a data model
func (g *GraphQLGenerator) generateInputFromModel(prefix, modelName string, modelDef map[string]interface{}) GraphQLInput {
	inputName := g.sanitizeName(prefix + modelName + "Input")

	graphqlInput := GraphQLInput{
		Name:        inputName,
		Description: fmt.Sprintf("Input type for %s", modelName),
		Fields:      []GraphQLInputField{},
	}

	// Generate input fields from model properties
	if properties, ok := modelDef["properties"].(map[string]interface{}); ok {
		for fieldName, fieldDef := range properties {
			if fieldDefMap, ok := fieldDef.(map[string]interface{}); ok {
				field := g.generateInputFieldFromProperty(fieldName, fieldDefMap)
				graphqlInput.Fields = append(graphqlInput.Fields, field)
			}
		}
	}

	return graphqlInput
}

// generateQueryFromEndpoint generates a GraphQL query from an endpoint
func (g *GraphQLGenerator) generateQueryFromEndpoint(prefix, endpointName string, endpointDef map[string]interface{}) GraphQLQuery {
	queryName := g.sanitizeName(prefix + endpointName)

	query := GraphQLQuery{
		Name:        queryName,
		Type:        "JSON",
		Description: g.getStringValue(endpointDef, "description", fmt.Sprintf("Query %s", endpointName)),
		Arguments:   []GraphQLArgument{},
		Directives:  []GraphQLDirective{},
	}

	// Generate arguments from parameters
	if params, ok := endpointDef["parameters"].(map[string]interface{}); ok {
		for paramName, paramDef := range params {
			if paramDefMap, ok := paramDef.(map[string]interface{}); ok {
				arg := g.generateArgumentFromParameter(paramName, paramDefMap)
				query.Arguments = append(query.Arguments, arg)
			}
		}
	}

	return query
}

// generateMutationFromEndpoint generates a GraphQL mutation from an endpoint
func (g *GraphQLGenerator) generateMutationFromEndpoint(prefix, endpointName string, endpointDef map[string]interface{}) GraphQLMutation {
	mutationName := g.sanitizeName(prefix + endpointName)

	mutation := GraphQLMutation{
		Name:        mutationName,
		Type:        "JSON",
		Description: g.getStringValue(endpointDef, "description", fmt.Sprintf("Mutation %s", endpointName)),
		Arguments:   []GraphQLArgument{},
		Directives:  []GraphQLDirective{},
	}

	// Generate arguments from parameters
	if params, ok := endpointDef["parameters"].(map[string]interface{}); ok {
		for paramName, paramDef := range params {
			if paramDefMap, ok := paramDef.(map[string]interface{}); ok {
				arg := g.generateArgumentFromParameter(paramName, paramDefMap)
				mutation.Arguments = append(mutation.Arguments, arg)
			}
		}
	}

	return mutation
}

// generateEnumFromConfig generates a GraphQL enum from configuration
func (g *GraphQLGenerator) generateEnumFromConfig(prefix, enumName string, enumDef map[string]interface{}) GraphQLEnum {
	enumTypeName := g.sanitizeName(prefix + enumName)

	graphqlEnum := GraphQLEnum{
		Name:        enumTypeName,
		Description: g.getStringValue(enumDef, "description", fmt.Sprintf("Enum %s", enumName)),
		Values:      []GraphQLEnumValue{},
	}

	// Generate enum values
	if values, ok := enumDef["values"].([]interface{}); ok {
		for _, value := range values {
			if valueStr, ok := value.(string); ok {
				graphqlEnum.Values = append(graphqlEnum.Values, GraphQLEnumValue{
					Name:        strings.ToUpper(valueStr),
					Description: valueStr,
					Deprecated:  false,
				})
			}
		}
	}

	return graphqlEnum
}

// generateFieldFromProperty generates a GraphQL field from a model property
func (g *GraphQLGenerator) generateFieldFromProperty(fieldName string, fieldDef map[string]interface{}) GraphQLField {
	fieldType := g.mapTypeToGraphQL(g.getStringValue(fieldDef, "type", "String"))
	required := g.getBoolValue(fieldDef, "required", false)

	if required {
		fieldType += "!"
	}

	return GraphQLField{
		Name:        fieldName,
		Type:        fieldType,
		Description: g.getStringValue(fieldDef, "description", ""),
		Arguments:   []GraphQLArgument{},
		Directives:  []GraphQLDirective{},
		Nullable:    !required,
	}
}

// generateInputFieldFromProperty generates a GraphQL input field from a model property
func (g *GraphQLGenerator) generateInputFieldFromProperty(fieldName string, fieldDef map[string]interface{}) GraphQLInputField {
	fieldType := g.mapTypeToGraphQL(g.getStringValue(fieldDef, "type", "String"))
	required := g.getBoolValue(fieldDef, "required", false)

	if required {
		fieldType += "!"
	}

	return GraphQLInputField{
		Name:         fieldName,
		Type:         fieldType,
		Description:  g.getStringValue(fieldDef, "description", ""),
		DefaultValue: g.getStringValue(fieldDef, "default", ""),
		Nullable:     !required,
	}
}

// generateArgumentFromParameter generates a GraphQL argument from an endpoint parameter
func (g *GraphQLGenerator) generateArgumentFromParameter(paramName string, paramDef map[string]interface{}) GraphQLArgument {
	paramType := g.mapTypeToGraphQL(g.getStringValue(paramDef, "type", "String"))
	required := g.getBoolValue(paramDef, "required", false)

	if required {
		paramType += "!"
	}

	return GraphQLArgument{
		Name:         paramName,
		Type:         paramType,
		Description:  g.getStringValue(paramDef, "description", ""),
		DefaultValue: g.getStringValue(paramDef, "default", ""),
		Nullable:     !required,
	}
}

// mapTypeToGraphQL maps addon types to GraphQL types
func (g *GraphQLGenerator) mapTypeToGraphQL(addonType string) string {
	typeMap := map[string]string{
		"string":    "String",
		"int":       "Int",
		"integer":   "Int",
		"float":     "Float",
		"number":    "Float",
		"boolean":   "Boolean",
		"bool":      "Boolean",
		"date":      "Time",
		"datetime":  "Time",
		"timestamp": "Time",
		"json":      "JSON",
		"object":    "JSON",
		"array":     "[String]",
		"list":      "[String]",
	}

	if graphqlType, exists := typeMap[strings.ToLower(addonType)]; exists {
		return graphqlType
	}

	return "String" // Default fallback
}

// Helper functions
func (g *GraphQLGenerator) getStringValue(data map[string]interface{}, key, defaultValue string) string {
	if value, ok := data[key].(string); ok {
		return value
	}
	return defaultValue
}

func (g *GraphQLGenerator) getBoolValue(data map[string]interface{}, key string, defaultValue bool) bool {
	if value, ok := data[key].(bool); ok {
		return value
	}
	return defaultValue
}

func (g *GraphQLGenerator) sanitizeName(name string) string {
	// Remove special characters and make it a valid GraphQL name
	name = strings.ReplaceAll(name, " ", "")
	name = strings.ReplaceAll(name, "-", "")
	name = strings.ReplaceAll(name, "_", "")

	// Ensure it starts with a letter
	if len(name) > 0 && !((name[0] >= 'A' && name[0] <= 'Z') || (name[0] >= 'a' && name[0] <= 'z')) {
		name = "Addon" + name
	}

	// Capitalize first letter
	if len(name) > 0 {
		name = strings.ToUpper(string(name[0])) + name[1:]
	}

	return name
}

// GenerateSchemaString generates a GraphQL schema string from the schema object
func (g *GraphQLGenerator) GenerateSchemaString(schema *AddonGraphQLSchema) string {
	var builder strings.Builder

	// Generate scalars
	for _, scalar := range schema.Scalars {
		builder.WriteString(fmt.Sprintf("scalar %s\n\n", scalar.Name))
	}

	// Generate enums
	for _, enum := range schema.Enums {
		builder.WriteString(fmt.Sprintf("enum %s {\n", enum.Name))
		for _, value := range enum.Values {
			builder.WriteString(fmt.Sprintf("  %s\n", value.Name))
		}
		builder.WriteString("}\n\n")
	}

	// Generate types
	for _, gqlType := range schema.Types {
		builder.WriteString(fmt.Sprintf("type %s {\n", gqlType.Name))
		for _, field := range gqlType.Fields {
			builder.WriteString(fmt.Sprintf("  %s: %s\n", field.Name, field.Type))
		}
		builder.WriteString("}\n\n")
	}

	// Generate input types
	for _, input := range schema.Inputs {
		builder.WriteString(fmt.Sprintf("input %s {\n", input.Name))
		for _, field := range input.Fields {
			builder.WriteString(fmt.Sprintf("  %s: %s\n", field.Name, field.Type))
		}
		builder.WriteString("}\n\n")
	}

	// Generate queries
	if len(schema.Queries) > 0 {
		builder.WriteString("extend type Query {\n")
		for _, query := range schema.Queries {
			args := ""
			if len(query.Arguments) > 0 {
				var argStrs []string
				for _, arg := range query.Arguments {
					argStrs = append(argStrs, fmt.Sprintf("%s: %s", arg.Name, arg.Type))
				}
				args = "(" + strings.Join(argStrs, ", ") + ")"
			}
			builder.WriteString(fmt.Sprintf("  %s%s: %s\n", query.Name, args, query.Type))
		}
		builder.WriteString("}\n\n")
	}

	// Generate mutations
	if len(schema.Mutations) > 0 {
		builder.WriteString("extend type Mutation {\n")
		for _, mutation := range schema.Mutations {
			args := ""
			if len(mutation.Arguments) > 0 {
				var argStrs []string
				for _, arg := range mutation.Arguments {
					argStrs = append(argStrs, fmt.Sprintf("%s: %s", arg.Name, arg.Type))
				}
				args = "(" + strings.Join(argStrs, ", ") + ")"
			}
			builder.WriteString(fmt.Sprintf("  %s%s: %s\n", mutation.Name, args, mutation.Type))
		}
		builder.WriteString("}\n\n")
	}

	return builder.String()
}
