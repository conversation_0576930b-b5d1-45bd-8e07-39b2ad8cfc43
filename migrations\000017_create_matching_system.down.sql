-- Drop triggers
DROP TRIGGER IF EXISTS update_client_requirements_updated_at ON client_requirements;
DROP TRIGGER IF EXISTS update_expert_client_matches_updated_at ON expert_client_matches;
DROP TRIGGER IF EXISTS update_project_engagements_updated_at ON project_engagements;
DROP TRIGGER IF EXISTS update_project_milestones_updated_at ON project_milestones;

-- Drop indexes
DROP INDEX IF EXISTS idx_client_requirements_user_id;
DROP INDEX IF EXISTS idx_client_requirements_tenant_id;
DROP INDEX IF EXISTS idx_client_requirements_status;
DROP INDEX IF EXISTS idx_client_requirements_expertise;

DROP INDEX IF EXISTS idx_expert_client_matches_client_req;
DROP INDEX IF EXISTS idx_expert_client_matches_expert;
DROP INDEX IF EXISTS idx_expert_client_matches_status;
DROP INDEX IF EXISTS idx_expert_client_matches_score;

DROP INDEX IF EXISTS idx_project_engagements_client_req;
DROP INDEX IF EXISTS idx_project_engagements_expert;
DROP INDEX IF EXISTS idx_project_engagements_status;
DROP INDEX IF EXISTS idx_project_engagements_dates;

DROP INDEX IF EXISTS idx_project_milestones_engagement;
DROP INDEX IF EXISTS idx_project_milestones_due_date;
DROP INDEX IF EXISTS idx_project_milestones_status;

DROP INDEX IF EXISTS idx_project_communications_engagement;
DROP INDEX IF EXISTS idx_project_communications_sender;
DROP INDEX IF EXISTS idx_project_communications_created;

-- Drop tables
DROP TABLE IF EXISTS project_communications;
DROP TABLE IF EXISTS project_milestones;
DROP TABLE IF EXISTS project_engagements;
DROP TABLE IF EXISTS expert_client_matches;
DROP TABLE IF EXISTS client_requirements;