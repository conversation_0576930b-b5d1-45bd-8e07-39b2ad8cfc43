package addon

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"goVwPlatformAPI/internal/crypto"
)

// APIConfigurationManager handles external API configurations
type APIConfigurationManager struct {
	db     DatabaseInterface
	crypto *crypto.CryptoService
}

// NewAPIConfigurationManager creates a new API configuration manager
func NewAPIConfigurationManager(db DatabaseInterface, cryptoService *crypto.CryptoService) *APIConfigurationManager {
	return &APIConfigurationManager{
		db:     db,
		crypto: cryptoService,
	}
}

// DatabaseInterface defines the database operations needed
type DatabaseInterface interface {
	CreateAPIConfiguration(ctx context.Context, config *APIConfiguration, userID string) error
	UpdateAPIConfiguration(ctx context.Context, id string, config *APIConfiguration, userID string) error
	DeleteAPIConfiguration(ctx context.Context, id string, userID string) error
	GetAPIConfiguration(ctx context.Context, id string, userID string) (*APIConfiguration, error)
	ListAPIConfigurations(ctx context.Context, userID string, status *APIStatus) ([]*APIConfiguration, error)

	CreateAPICredentials(ctx context.Context, creds *APICredentials, userID string) error
	UpdateAPICredentials(ctx context.Context, id string, creds *APICredentials, userID string) error
	DeleteAPICredentials(ctx context.Context, id string, userID string) error
	GetAPICredentials(ctx context.Context, id string, userID string) (*APICredentials, error)
	ListAPICredentials(ctx context.Context, userID string, apiConfigID *string) ([]*APICredentials, error)

	CreateAPIConnection(ctx context.Context, conn *APIConnection, userID string) error
	UpdateAPIConnection(ctx context.Context, id string, conn *APIConnection, userID string) error
	GetAPIConnection(ctx context.Context, id string, userID string) (*APIConnection, error)
	ListAPIConnections(ctx context.Context, userID string, apiConfigID *string) ([]*APIConnection, error)
}

// Enhanced APIConfiguration with JSON marshaling support
type APIConfigurationData struct {
	Endpoints map[string]*EndpointData `json:"endpoints"`
	Headers   map[string]string        `json:"headers"`
	Security  *SecurityConfigData      `json:"security"`
	RateLimit *RateLimitData           `json:"rateLimit"`
	Metadata  map[string]interface{}   `json:"metadata"`
}

type EndpointData struct {
	Path        string            `json:"path"`
	Method      string            `json:"method"`
	Description string            `json:"description"`
	Parameters  []ParameterData   `json:"parameters"`
	Headers     map[string]string `json:"headers"`
	RateLimit   *RateLimitData    `json:"rateLimit"`
	Timeout     int               `json:"timeout"`
	Retries     int               `json:"retries"`
}

type ParameterData struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"`
	Required    bool        `json:"required"`
	Default     interface{} `json:"default"`
	Description string      `json:"description"`
	Validation  string      `json:"validation"`
}

type SecurityConfigData struct {
	RequireHTTPS   bool     `json:"requireHttps"`
	AllowedDomains []string `json:"allowedDomains"`
	BlockedDomains []string `json:"blockedDomains"`
	ValidateSSL    bool     `json:"validateSsl"`
	MaxRequestSize int64    `json:"maxRequestSize"`
	AllowedMethods []string `json:"allowedMethods"`
}

type RateLimitData struct {
	RequestsPerSecond int           `json:"requestsPerSecond"`
	RequestsPerMinute int           `json:"requestsPerMinute"`
	RequestsPerHour   int           `json:"requestsPerHour"`
	BurstSize         int           `json:"burstSize"`
	WindowSize        time.Duration `json:"windowSize"`
}

// Value implements driver.Valuer for database storage
func (a APIConfigurationData) Value() (driver.Value, error) {
	return json.Marshal(a)
}

// Scan implements sql.Scanner for database retrieval
func (a *APIConfigurationData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into APIConfigurationData", value)
	}

	return json.Unmarshal(bytes, a)
}

// CreateAPIConfiguration creates a new API configuration
func (m *APIConfigurationManager) CreateAPIConfiguration(ctx context.Context, config *APIConfiguration, userID string) (*APIConfiguration, error) {
	// Validate configuration
	if err := m.validateAPIConfiguration(config); err != nil {
		return nil, fmt.Errorf("invalid API configuration: %w", err)
	}

	// Set creation metadata
	config.ID = generateUUID()
	config.CreatedAt = time.Now()
	config.UpdatedAt = time.Now()
	config.Status = APIStatusActive

	// Store in database
	if err := m.db.CreateAPIConfiguration(ctx, config, userID); err != nil {
		return nil, fmt.Errorf("failed to create API configuration: %w", err)
	}

	return config, nil
}

// UpdateAPIConfiguration updates an existing API configuration
func (m *APIConfigurationManager) UpdateAPIConfiguration(ctx context.Context, id string, updates *APIConfiguration, userID string) (*APIConfiguration, error) {
	// Get existing configuration
	existing, err := m.db.GetAPIConfiguration(ctx, id, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing configuration: %w", err)
	}

	// Apply updates
	if updates.Name != "" {
		existing.Name = updates.Name
	}
	if updates.Description != "" {
		existing.Description = updates.Description
	}
	if updates.BaseURL != "" {
		existing.BaseURL = updates.BaseURL
	}
	if updates.Version != "" {
		existing.Version = updates.Version
	}
	if updates.AuthType != "" {
		existing.AuthType = updates.AuthType
	}
	if updates.Endpoints != nil {
		existing.Endpoints = updates.Endpoints
	}
	if updates.Headers != nil {
		existing.Headers = updates.Headers
	}
	if updates.RateLimit != nil {
		existing.RateLimit = updates.RateLimit
	}
	if updates.Security != nil {
		existing.Security = updates.Security
	}
	if updates.Documentation != "" {
		existing.Documentation = updates.Documentation
	}
	if updates.Status != "" {
		existing.Status = updates.Status
	}
	if updates.Metadata != nil {
		existing.Metadata = updates.Metadata
	}

	existing.UpdatedAt = time.Now()

	// Validate updated configuration
	if err := m.validateAPIConfiguration(existing); err != nil {
		return nil, fmt.Errorf("invalid updated configuration: %w", err)
	}

	// Update in database
	if err := m.db.UpdateAPIConfiguration(ctx, id, existing, userID); err != nil {
		return nil, fmt.Errorf("failed to update API configuration: %w", err)
	}

	return existing, nil
}

// DeleteAPIConfiguration deletes an API configuration
func (m *APIConfigurationManager) DeleteAPIConfiguration(ctx context.Context, id string, userID string) error {
	// Check if configuration exists
	_, err := m.db.GetAPIConfiguration(ctx, id, userID)
	if err != nil {
		return fmt.Errorf("configuration not found: %w", err)
	}

	// Delete associated credentials and connections
	credentials, err := m.db.ListAPICredentials(ctx, userID, &id)
	if err != nil {
		return fmt.Errorf("failed to get associated credentials: %w", err)
	}

	for _, cred := range credentials {
		if err := m.db.DeleteAPICredentials(ctx, cred.ID, userID); err != nil {
			return fmt.Errorf("failed to delete associated credentials: %w", err)
		}
	}

	// Delete configuration
	if err := m.db.DeleteAPIConfiguration(ctx, id, userID); err != nil {
		return fmt.Errorf("failed to delete API configuration: %w", err)
	}

	return nil
}

// GetAPIConfiguration retrieves an API configuration
func (m *APIConfigurationManager) GetAPIConfiguration(ctx context.Context, id string, userID string) (*APIConfiguration, error) {
	return m.db.GetAPIConfiguration(ctx, id, userID)
}

// ListAPIConfigurations lists API configurations
func (m *APIConfigurationManager) ListAPIConfigurations(ctx context.Context, userID string, status *APIStatus) ([]*APIConfiguration, error) {
	return m.db.ListAPIConfigurations(ctx, userID, status)
}

// validateAPIConfiguration validates an API configuration
func (m *APIConfigurationManager) validateAPIConfiguration(config *APIConfiguration) error {
	if config.Name == "" {
		return fmt.Errorf("name is required")
	}
	if config.BaseURL == "" {
		return fmt.Errorf("base URL is required")
	}
	if config.AuthType == "" {
		return fmt.Errorf("authentication type is required")
	}

	// Validate endpoints
	if len(config.Endpoints) == 0 {
		return fmt.Errorf("at least one endpoint is required")
	}

	for path, endpoint := range config.Endpoints {
		if endpoint.Path == "" {
			return fmt.Errorf("endpoint path is required for %s", path)
		}
		if endpoint.Method == "" {
			return fmt.Errorf("endpoint method is required for %s", path)
		}
	}

	// Validate security configuration
	if config.Security != nil {
		if config.Security.MaxRequestSize <= 0 {
			config.Security.MaxRequestSize = 10 * 1024 * 1024 // 10MB default
		}
		if len(config.Security.AllowedMethods) == 0 {
			config.Security.AllowedMethods = []string{"GET", "POST", "PUT", "DELETE"}
		}
	}

	return nil
}

// generateUUID generates a simple UUID (in production, use a proper UUID library)
func generateUUID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}
