package sitemanager

import (
	"context"
	"fmt"
	"time"

	"goVwPlatformAPI/internal/database"
)

type SiteManager struct {
	db *database.DB
}

type Site struct {
	ID           int                    `json:"id"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	UserID       int                    `json:"user_id"`
	TenantID     string                 `json:"tenant_id"`
	Status       string                 `json:"status"`
	CustomDomain string                 `json:"custom_domain"`
	SSLEnabled   bool                   `json:"ssl_enabled"`
	ThemeConfig  map[string]interface{} `json:"theme_config"`
	SEOConfig    map[string]interface{} `json:"seo_config"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
	PageCount    int                    `json:"page_count"`
	IsPublished  bool                   `json:"is_published"`
}

type SitePage struct {
	ID         string    `json:"id"`
	SiteID     int       `json:"site_id"`
	PageID     string    `json:"page_id"`
	IsHomepage bool      `json:"is_homepage"`
	Position   int       `json:"position"`
	TenantID   string    `json:"tenant_id"`
	CreatedAt  time.Time `json:"created_at"`
	Page       *Page     `json:"page,omitempty"`
}

type Page struct {
	ID        string    `json:"id"`
	Title     string    `json:"title"`
	Slug      string    `json:"slug"`
	Content   string    `json:"content"`
	ParentID  *string   `json:"parent_id"`
	Position  int       `json:"position"`
	IsHidden  bool      `json:"is_hidden"`
	UserID    int       `json:"user_id"`
	TenantID  string    `json:"tenant_id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type SiteNavigation struct {
	ID         string    `json:"id"`
	SiteID     int       `json:"site_id"`
	PageID     *string   `json:"page_id"`
	ParentID   *string   `json:"parent_id"`
	Title      string    `json:"title"`
	URL        string    `json:"url"`
	Position   int       `json:"position"`
	IsExternal bool      `json:"is_external"`
	IsVisible  bool      `json:"is_visible"`
	TenantID   string    `json:"tenant_id"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

type CreateSiteInput struct {
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	CustomDomain string                 `json:"custom_domain"`
	ThemeConfig  map[string]interface{} `json:"theme_config"`
	SEOConfig    map[string]interface{} `json:"seo_config"`
}

type UpdateSiteInput struct {
	Name         *string                 `json:"name"`
	Description  *string                 `json:"description"`
	Status       *string                 `json:"status"`
	CustomDomain *string                 `json:"custom_domain"`
	SSLEnabled   *bool                   `json:"ssl_enabled"`
	ThemeConfig  *map[string]interface{} `json:"theme_config"`
	SEOConfig    *map[string]interface{} `json:"seo_config"`
}

func NewSiteManager(db *database.DB) *SiteManager {
	return &SiteManager{db: db}
}

// GetUserSites retrieves all sites for a user
func (sm *SiteManager) GetUserSites(ctx context.Context, userID int, tenantID string) ([]*Site, error) {
	query := `
		SELECT s.id, s.name, s.description, s.user_id, s.tenant_id, s.status,
		       COALESCE(s.custom_domain, ''), s.ssl_enabled, s.theme_config, s.seo_config,
		       s.created_at, s.updated_at,
		       COUNT(sp.page_id) as page_count
		FROM sites s
		LEFT JOIN site_pages sp ON s.id = sp.site_id
		WHERE s.user_id = $1 AND s.tenant_id = $2
		GROUP BY s.id, s.name, s.description, s.user_id, s.tenant_id, s.status,
		         s.custom_domain, s.ssl_enabled, s.theme_config, s.seo_config,
		         s.created_at, s.updated_at
		ORDER BY s.updated_at DESC
	`

	rows, err := sm.db.Pool.Query(ctx, query, userID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user sites: %w", err)
	}
	defer rows.Close()

	var sites []*Site
	for rows.Next() {
		var site Site
		err := rows.Scan(
			&site.ID, &site.Name, &site.Description, &site.UserID, &site.TenantID,
			&site.Status, &site.CustomDomain, &site.SSLEnabled, &site.ThemeConfig,
			&site.SEOConfig, &site.CreatedAt, &site.UpdatedAt, &site.PageCount,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan site: %w", err)
		}

		site.IsPublished = site.Status == "published"
		sites = append(sites, &site)
	}

	return sites, nil
}

// GetSiteByID retrieves a specific site by ID
func (sm *SiteManager) GetSiteByID(ctx context.Context, siteID int, userID int, tenantID string) (*Site, error) {
	var site Site
	query := `
		SELECT s.id, s.name, s.description, s.user_id, s.tenant_id, s.status,
		       COALESCE(s.custom_domain, ''), s.ssl_enabled, s.theme_config, s.seo_config,
		       s.created_at, s.updated_at,
		       COUNT(sp.page_id) as page_count
		FROM sites s
		LEFT JOIN site_pages sp ON s.id = sp.site_id
		WHERE s.id = $1 AND s.user_id = $2 AND s.tenant_id = $3
		GROUP BY s.id, s.name, s.description, s.user_id, s.tenant_id, s.status,
		         s.custom_domain, s.ssl_enabled, s.theme_config, s.seo_config,
		         s.created_at, s.updated_at
	`

	err := sm.db.Pool.QueryRow(ctx, query, siteID, userID, tenantID).Scan(
		&site.ID, &site.Name, &site.Description, &site.UserID, &site.TenantID,
		&site.Status, &site.CustomDomain, &site.SSLEnabled, &site.ThemeConfig,
		&site.SEOConfig, &site.CreatedAt, &site.UpdatedAt, &site.PageCount,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get site: %w", err)
	}

	site.IsPublished = site.Status == "published"
	return &site, nil
}

// CreateSite creates a new site for a user
func (sm *SiteManager) CreateSite(ctx context.Context, userID int, tenantID string, input *CreateSiteInput) (*Site, error) {
	// Start transaction
	tx, err := sm.db.Pool.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Set default configs if not provided
	themeConfig := input.ThemeConfig
	if themeConfig == nil {
		themeConfig = map[string]interface{}{
			"primaryColor":   "#007bff",
			"secondaryColor": "#6c757d",
			"fontFamily":     "Arial, sans-serif",
			"layout":         "default",
		}
	}

	seoConfig := input.SEOConfig
	if seoConfig == nil {
		seoConfig = map[string]interface{}{
			"title":       input.Name,
			"description": input.Description,
			"keywords":    "",
			"robots":      "index,follow",
		}
	}

	// Create the site
	var siteID int
	query := `
		INSERT INTO sites (name, description, user_id, tenant_id, status, custom_domain, 
		                   ssl_enabled, theme_config, seo_config, created_at, updated_at)
		VALUES ($1, $2, $3, $4, 'draft', $5, false, $6, $7, NOW(), NOW())
		RETURNING id
	`

	err = tx.QueryRow(ctx, query, input.Name, input.Description, userID, tenantID,
		input.CustomDomain, themeConfig, seoConfig).Scan(&siteID)
	if err != nil {
		return nil, fmt.Errorf("failed to create site: %w", err)
	}

	// Create a default homepage
	var pageID string
	pageQuery := `
		INSERT INTO pages (title, slug, content, user_id, tenant_id, created_at, updated_at)
		VALUES ($1, 'home', $2, $3, $4, NOW(), NOW())
		RETURNING id
	`

	homeContent := fmt.Sprintf(`
		<div class="hero-section">
			<h1>Welcome to %s</h1>
			<p>%s</p>
			<a href="#about" class="btn btn-primary">Learn More</a>
		</div>
		<div class="content-section">
			<h2>About</h2>
			<p>This is your new website. Start customizing it to make it your own!</p>
		</div>
	`, input.Name, input.Description)

	err = tx.QueryRow(ctx, pageQuery, "Home", homeContent, userID, tenantID).Scan(&pageID)
	if err != nil {
		return nil, fmt.Errorf("failed to create homepage: %w", err)
	}

	// Link the homepage to the site
	_, err = tx.Exec(ctx, `
		INSERT INTO site_pages (site_id, page_id, is_homepage, position, tenant_id, created_at)
		VALUES ($1, $2, true, 0, $3, NOW())
	`, siteID, pageID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to link homepage to site: %w", err)
	}

	// Create default navigation
	_, err = tx.Exec(ctx, `
		INSERT INTO site_navigation (site_id, page_id, title, url, position, is_external, 
		                              is_visible, tenant_id, created_at, updated_at)
		VALUES ($1, $2, 'Home', '/', 0, false, true, $3, NOW(), NOW())
	`, siteID, pageID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to create navigation: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(ctx); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return the created site
	return sm.GetSiteByID(ctx, siteID, userID, tenantID)
}

// UpdateSite updates an existing site
func (sm *SiteManager) UpdateSite(ctx context.Context, siteID int, userID int, tenantID string, input *UpdateSiteInput) (*Site, error) {
	// Build dynamic update query
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if input.Name != nil {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *input.Name)
		argIndex++
	}

	if input.Description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argIndex))
		args = append(args, *input.Description)
		argIndex++
	}

	if input.Status != nil {
		setParts = append(setParts, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, *input.Status)
		argIndex++
	}

	if input.CustomDomain != nil {
		setParts = append(setParts, fmt.Sprintf("custom_domain = $%d", argIndex))
		args = append(args, *input.CustomDomain)
		argIndex++
	}

	if input.SSLEnabled != nil {
		setParts = append(setParts, fmt.Sprintf("ssl_enabled = $%d", argIndex))
		args = append(args, *input.SSLEnabled)
		argIndex++
	}

	if input.ThemeConfig != nil {
		setParts = append(setParts, fmt.Sprintf("theme_config = $%d", argIndex))
		args = append(args, *input.ThemeConfig)
		argIndex++
	}

	if input.SEOConfig != nil {
		setParts = append(setParts, fmt.Sprintf("seo_config = $%d", argIndex))
		args = append(args, *input.SEOConfig)
		argIndex++
	}

	if len(setParts) == 0 {
		return sm.GetSiteByID(ctx, siteID, userID, tenantID)
	}

	// Always update the updated_at field
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add WHERE clause parameters
	args = append(args, siteID, userID, tenantID)

	query := fmt.Sprintf(`
		UPDATE sites 
		SET %s 
		WHERE id = $%d AND user_id = $%d AND tenant_id = $%d
	`, fmt.Sprintf("%s", setParts), argIndex, argIndex+1, argIndex+2)

	_, err := sm.db.Pool.Exec(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to update site: %w", err)
	}

	return sm.GetSiteByID(ctx, siteID, userID, tenantID)
}

// DeleteSite deletes a site and all its associated data
func (sm *SiteManager) DeleteSite(ctx context.Context, siteID int, userID int, tenantID string) error {
	// Start transaction
	tx, err := sm.db.Pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Delete site navigation
	_, err = tx.Exec(ctx, `
		DELETE FROM site_navigation 
		WHERE site_id = $1 AND tenant_id = $2
	`, siteID, tenantID)
	if err != nil {
		return fmt.Errorf("failed to delete site navigation: %w", err)
	}

	// Delete site pages relationships
	_, err = tx.Exec(ctx, `
		DELETE FROM site_pages 
		WHERE site_id = $1 AND tenant_id = $2
	`, siteID, tenantID)
	if err != nil {
		return fmt.Errorf("failed to delete site pages: %w", err)
	}

	// Delete the site
	result, err := tx.Exec(ctx, `
		DELETE FROM sites 
		WHERE id = $1 AND user_id = $2 AND tenant_id = $3
	`, siteID, userID, tenantID)
	if err != nil {
		return fmt.Errorf("failed to delete site: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("site not found or access denied")
	}

	return tx.Commit(ctx)
}

// GetSitePages retrieves all pages for a site
func (sm *SiteManager) GetSitePages(ctx context.Context, siteID int, userID int, tenantID string) ([]*SitePage, error) {
	query := `
		SELECT sp.id, sp.site_id, sp.page_id, sp.is_homepage, sp.position, sp.tenant_id, sp.created_at,
		       p.title, p.slug, p.content, p.parent_id, p.position as page_position, p.is_hidden,
		       p.user_id, p.tenant_id as page_tenant_id, p.created_at as page_created_at, p.updated_at
		FROM site_pages sp
		JOIN pages p ON sp.page_id = p.id
		WHERE sp.site_id = $1 AND sp.tenant_id = $2
		ORDER BY sp.position ASC
	`

	rows, err := sm.db.Pool.Query(ctx, query, siteID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get site pages: %w", err)
	}
	defer rows.Close()

	var sitePages []*SitePage
	for rows.Next() {
		var sp SitePage
		var page Page

		err := rows.Scan(
			&sp.ID, &sp.SiteID, &sp.PageID, &sp.IsHomepage, &sp.Position, &sp.TenantID, &sp.CreatedAt,
			&page.Title, &page.Slug, &page.Content, &page.ParentID, &page.Position, &page.IsHidden,
			&page.UserID, &page.TenantID, &page.CreatedAt, &page.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan site page: %w", err)
		}

		page.ID = sp.PageID
		sp.Page = &page
		sitePages = append(sitePages, &sp)
	}

	return sitePages, nil
}
