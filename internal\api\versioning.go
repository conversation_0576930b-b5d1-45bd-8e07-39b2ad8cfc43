package api

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// VersionManager handles API versioning
type VersionManager struct {
	versions        map[string]*APIVersion
	defaultVersion  string
	deprecationTime time.Duration
	sunsetTime      time.Duration
}

// APIVersion represents an API version
type APIVersion struct {
	Version        string                        `json:"version"`
	Status         VersionStatus                 `json:"status"`
	ReleaseDate    time.Time                     `json:"release_date"`
	DeprecatedDate *time.Time                    `json:"deprecated_date,omitempty"`
	SunsetDate     *time.Time                    `json:"sunset_date,omitempty"`
	Description    string                        `json:"description"`
	Changes        []VersionChange               `json:"changes"`
	Endpoints      map[string]*VersionedEndpoint `json:"endpoints"`
	Middleware     []VersionMiddleware           `json:"middleware"`
	Metadata       map[string]interface{}        `json:"metadata"`
}

// VersionStatus represents the status of an API version
type VersionStatus string

const (
	VersionStatusDevelopment VersionStatus = "development"
	VersionStatusBeta        VersionStatus = "beta"
	VersionStatusStable      VersionStatus = "stable"
	VersionStatusDeprecated  VersionStatus = "deprecated"
	VersionStatusSunset      VersionStatus = "sunset"
)

// VersionChange represents a change in an API version
type VersionChange struct {
	Type        ChangeType `json:"type"`
	Description string     `json:"description"`
	Endpoint    string     `json:"endpoint,omitempty"`
	Field       string     `json:"field,omitempty"`
	Breaking    bool       `json:"breaking"`
	Migration   string     `json:"migration,omitempty"`
}

// ChangeType represents the type of change
type ChangeType string

const (
	ChangeTypeAdded      ChangeType = "added"
	ChangeTypeModified   ChangeType = "modified"
	ChangeTypeDeprecated ChangeType = "deprecated"
	ChangeTypeRemoved    ChangeType = "removed"
	ChangeTypeFixed      ChangeType = "fixed"
	ChangeTypeSecurity   ChangeType = "security"
)

// VersionedEndpoint represents an endpoint in a specific version
type VersionedEndpoint struct {
	Path       string                 `json:"path"`
	Method     string                 `json:"method"`
	Handler    string                 `json:"handler"`
	Deprecated bool                   `json:"deprecated"`
	Removed    bool                   `json:"removed"`
	Changes    []VersionChange        `json:"changes"`
	Parameters map[string]*Parameter  `json:"parameters"`
	Responses  map[string]*Response   `json:"responses"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// VersionMiddleware represents middleware for a specific version
type VersionMiddleware struct {
	Name    string                 `json:"name"`
	Config  map[string]interface{} `json:"config"`
	Enabled bool                   `json:"enabled"`
	Order   int                    `json:"order"`
}

// VersionContext contains version information for a request
type VersionContext struct {
	RequestedVersion string      `json:"requested_version"`
	ResolvedVersion  string      `json:"resolved_version"`
	APIVersion       *APIVersion `json:"api_version"`
	Warnings         []string    `json:"warnings"`
	Deprecations     []string    `json:"deprecations"`
}

// NewVersionManager creates a new version manager
func NewVersionManager() *VersionManager {
	vm := &VersionManager{
		versions:        make(map[string]*APIVersion),
		defaultVersion:  "v1",
		deprecationTime: 365 * 24 * time.Hour, // 1 year
		sunsetTime:      730 * 24 * time.Hour, // 2 years
	}

	// Initialize default versions
	vm.initializeDefaultVersions()

	return vm
}

// initializeDefaultVersions sets up default API versions
func (vm *VersionManager) initializeDefaultVersions() {
	// Version 1.0
	vm.versions["v1"] = &APIVersion{
		Version:     "v1",
		Status:      VersionStatusStable,
		ReleaseDate: time.Now().Add(-365 * 24 * time.Hour), // Released 1 year ago
		Description: "Initial stable release of the VelocityWave API",
		Changes: []VersionChange{
			{
				Type:        ChangeTypeAdded,
				Description: "Initial API release with core functionality",
				Breaking:    false,
			},
		},
		Endpoints: make(map[string]*VersionedEndpoint),
		Middleware: []VersionMiddleware{
			{
				Name:    "rate_limiter",
				Config:  map[string]interface{}{"requests_per_minute": 1000},
				Enabled: true,
				Order:   1,
			},
			{
				Name:    "auth",
				Config:  map[string]interface{}{"required": true},
				Enabled: true,
				Order:   2,
			},
		},
		Metadata: make(map[string]interface{}),
	}

	// Version 1.1
	vm.versions["v1.1"] = &APIVersion{
		Version:     "v1.1",
		Status:      VersionStatusStable,
		ReleaseDate: time.Now().Add(-180 * 24 * time.Hour), // Released 6 months ago
		Description: "Enhanced API with additional features and improvements",
		Changes: []VersionChange{
			{
				Type:        ChangeTypeAdded,
				Description: "Added expert network functionality",
				Endpoint:    "/experts",
				Breaking:    false,
			},
			{
				Type:        ChangeTypeAdded,
				Description: "Added business tools endpoints",
				Endpoint:    "/business-tools",
				Breaking:    false,
			},
			{
				Type:        ChangeTypeModified,
				Description: "Enhanced user profile with additional fields",
				Endpoint:    "/users",
				Field:       "profile",
				Breaking:    false,
			},
		},
		Endpoints: make(map[string]*VersionedEndpoint),
		Middleware: []VersionMiddleware{
			{
				Name:    "rate_limiter",
				Config:  map[string]interface{}{"requests_per_minute": 1500},
				Enabled: true,
				Order:   1,
			},
			{
				Name:    "auth",
				Config:  map[string]interface{}{"required": true},
				Enabled: true,
				Order:   2,
			},
		},
		Metadata: make(map[string]interface{}),
	}

	// Version 2.0 (Current)
	vm.versions["v2"] = &APIVersion{
		Version:     "v2",
		Status:      VersionStatusStable,
		ReleaseDate: time.Now().Add(-30 * 24 * time.Hour), // Released 1 month ago
		Description: "Major API revision with GraphQL support and improved architecture",
		Changes: []VersionChange{
			{
				Type:        ChangeTypeAdded,
				Description: "Added GraphQL endpoint for flexible queries",
				Endpoint:    "/graphql",
				Breaking:    false,
			},
			{
				Type:        ChangeTypeModified,
				Description: "Restructured authentication system",
				Endpoint:    "/auth",
				Breaking:    true,
				Migration:   "Update authentication headers to use Bearer tokens",
			},
			{
				Type:        ChangeTypeDeprecated,
				Description: "REST endpoints are deprecated in favor of GraphQL",
				Breaking:    false,
				Migration:   "Migrate to GraphQL for better performance and flexibility",
			},
			{
				Type:        ChangeTypeAdded,
				Description: "Added comprehensive admin panel APIs",
				Endpoint:    "/admin",
				Breaking:    false,
			},
		},
		Endpoints: make(map[string]*VersionedEndpoint),
		Middleware: []VersionMiddleware{
			{
				Name:    "rate_limiter",
				Config:  map[string]interface{}{"requests_per_minute": 2000},
				Enabled: true,
				Order:   1,
			},
			{
				Name:    "auth",
				Config:  map[string]interface{}{"required": true, "type": "bearer"},
				Enabled: true,
				Order:   2,
			},
			{
				Name:    "graphql_complexity",
				Config:  map[string]interface{}{"max_depth": 10, "max_complexity": 1000},
				Enabled: true,
				Order:   3,
			},
		},
		Metadata: make(map[string]interface{}),
	}

	// Version 2.1 (Beta)
	vm.versions["v2.1"] = &APIVersion{
		Version:     "v2.1",
		Status:      VersionStatusBeta,
		ReleaseDate: time.Now(),
		Description: "Beta release with experimental features",
		Changes: []VersionChange{
			{
				Type:        ChangeTypeAdded,
				Description: "Added real-time subscriptions via WebSocket",
				Endpoint:    "/subscriptions",
				Breaking:    false,
			},
			{
				Type:        ChangeTypeAdded,
				Description: "Added AI-powered content generation",
				Endpoint:    "/ai",
				Breaking:    false,
			},
			{
				Type:        ChangeTypeModified,
				Description: "Enhanced rate limiting with dynamic adjustments",
				Breaking:    false,
			},
		},
		Endpoints: make(map[string]*VersionedEndpoint),
		Middleware: []VersionMiddleware{
			{
				Name:    "rate_limiter",
				Config:  map[string]interface{}{"requests_per_minute": 2500, "dynamic": true},
				Enabled: true,
				Order:   1,
			},
			{
				Name:    "auth",
				Config:  map[string]interface{}{"required": true, "type": "bearer"},
				Enabled: true,
				Order:   2,
			},
			{
				Name:    "graphql_complexity",
				Config:  map[string]interface{}{"max_depth": 12, "max_complexity": 1500},
				Enabled: true,
				Order:   3,
			},
			{
				Name:    "ai_rate_limiter",
				Config:  map[string]interface{}{"requests_per_hour": 100},
				Enabled: true,
				Order:   4,
			},
		},
		Metadata: map[string]interface{}{
			"experimental": true,
			"feedback_url": "https://feedback.velocitywave.com/v2.1",
		},
	}

	vm.defaultVersion = "v2"
}

// ResolveVersion resolves the API version from the request
func (vm *VersionManager) ResolveVersion(r *http.Request) *VersionContext {
	ctx := &VersionContext{
		Warnings:     []string{},
		Deprecations: []string{},
	}

	// Try to get version from different sources
	version := vm.getVersionFromRequest(r)
	ctx.RequestedVersion = version

	// Resolve to actual version
	resolvedVersion := vm.resolveToAvailableVersion(version)
	ctx.ResolvedVersion = resolvedVersion

	// Get API version details
	if apiVersion, exists := vm.versions[resolvedVersion]; exists {
		ctx.APIVersion = apiVersion

		// Add warnings for deprecated versions
		if apiVersion.Status == VersionStatusDeprecated {
			ctx.Warnings = append(ctx.Warnings, fmt.Sprintf("API version %s is deprecated", resolvedVersion))
			if apiVersion.SunsetDate != nil {
				ctx.Warnings = append(ctx.Warnings, fmt.Sprintf("This version will be sunset on %s", apiVersion.SunsetDate.Format("2006-01-02")))
			}
		}

		// Add deprecation notices for specific features
		for _, change := range apiVersion.Changes {
			if change.Type == ChangeTypeDeprecated {
				ctx.Deprecations = append(ctx.Deprecations, change.Description)
			}
		}
	}

	return ctx
}

// getVersionFromRequest extracts version from request
func (vm *VersionManager) getVersionFromRequest(r *http.Request) string {
	// 1. Check Accept header (e.g., "application/vnd.velocitywave.v2+json")
	accept := r.Header.Get("Accept")
	if version := vm.extractVersionFromAcceptHeader(accept); version != "" {
		return version
	}

	// 2. Check custom version header
	if version := r.Header.Get("X-API-Version"); version != "" {
		return version
	}

	// 3. Check query parameter
	if version := r.URL.Query().Get("version"); version != "" {
		return version
	}

	// 4. Check URL path (e.g., "/v2/users")
	if version := vm.extractVersionFromPath(r.URL.Path); version != "" {
		return version
	}

	// 5. Default version
	return vm.defaultVersion
}

// extractVersionFromAcceptHeader extracts version from Accept header
func (vm *VersionManager) extractVersionFromAcceptHeader(accept string) string {
	// Match patterns like "application/vnd.velocitywave.v2+json"
	re := regexp.MustCompile(`application/vnd\.velocitywave\.v?([0-9]+(?:\.[0-9]+)?)\+json`)
	matches := re.FindStringSubmatch(accept)
	if len(matches) > 1 {
		return "v" + matches[1]
	}
	return ""
}

// extractVersionFromPath extracts version from URL path
func (vm *VersionManager) extractVersionFromPath(path string) string {
	// Match patterns like "/v2/users" or "/api/v2/users"
	re := regexp.MustCompile(`^(?:/api)?/v?([0-9]+(?:\.[0-9]+)?)(?:/|$)`)
	matches := re.FindStringSubmatch(path)
	if len(matches) > 1 {
		return "v" + matches[1]
	}
	return ""
}

// resolveToAvailableVersion resolves requested version to available version
func (vm *VersionManager) resolveToAvailableVersion(requestedVersion string) string {
	// If exact version exists, use it
	if _, exists := vm.versions[requestedVersion]; exists {
		return requestedVersion
	}

	// Try to find compatible version
	if compatibleVersion := vm.findCompatibleVersion(requestedVersion); compatibleVersion != "" {
		return compatibleVersion
	}

	// Fall back to default version
	return vm.defaultVersion
}

// findCompatibleVersion finds a compatible version for the requested version
func (vm *VersionManager) findCompatibleVersion(requestedVersion string) string {
	// Parse requested version
	major, minor := vm.parseVersion(requestedVersion)
	if major == 0 {
		return ""
	}

	// Find the highest compatible version
	var bestVersion string
	var bestMajor, bestMinor int

	for version := range vm.versions {
		vMajor, vMinor := vm.parseVersion(version)
		if vMajor == major && vMinor >= minor {
			if vMajor > bestMajor || (vMajor == bestMajor && vMinor > bestMinor) {
				bestVersion = version
				bestMajor = vMajor
				bestMinor = vMinor
			}
		}
	}

	return bestVersion
}

// parseVersion parses version string into major and minor numbers
func (vm *VersionManager) parseVersion(version string) (int, int) {
	// Remove 'v' prefix if present
	version = strings.TrimPrefix(version, "v")

	parts := strings.Split(version, ".")
	if len(parts) == 0 {
		return 0, 0
	}

	major, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, 0
	}

	minor := 0
	if len(parts) > 1 {
		minor, _ = strconv.Atoi(parts[1])
	}

	return major, minor
}

// VersionMiddleware creates HTTP middleware for version handling
func (vm *VersionManager) VersionMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Resolve version
			versionCtx := vm.ResolveVersion(r)

			// Add version context to request
			ctx := context.WithValue(r.Context(), "version", versionCtx)
			r = r.WithContext(ctx)

			// Set response headers
			w.Header().Set("X-API-Version", versionCtx.ResolvedVersion)

			// Add warnings if any
			if len(versionCtx.Warnings) > 0 {
				w.Header().Set("Warning", strings.Join(versionCtx.Warnings, "; "))
			}

			// Add deprecation notices
			if len(versionCtx.Deprecations) > 0 {
				w.Header().Set("Deprecation", "true")
				w.Header().Set("X-Deprecation-Notice", strings.Join(versionCtx.Deprecations, "; "))
			}

			// Check if version is sunset
			if versionCtx.APIVersion != nil && versionCtx.APIVersion.Status == VersionStatusSunset {
				http.Error(w, fmt.Sprintf("API version %s has been sunset", versionCtx.ResolvedVersion), http.StatusGone)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// GetVersionFromContext extracts version context from request context
func GetVersionFromContext(ctx context.Context) *VersionContext {
	if versionCtx, ok := ctx.Value("version").(*VersionContext); ok {
		return versionCtx
	}
	return nil
}

// AddVersion adds a new API version
func (vm *VersionManager) AddVersion(version *APIVersion) {
	vm.versions[version.Version] = version
}

// DeprecateVersion marks a version as deprecated
func (vm *VersionManager) DeprecateVersion(version string, sunsetDate *time.Time) error {
	apiVersion, exists := vm.versions[version]
	if !exists {
		return fmt.Errorf("version %s not found", version)
	}

	now := time.Now()
	apiVersion.Status = VersionStatusDeprecated
	apiVersion.DeprecatedDate = &now
	apiVersion.SunsetDate = sunsetDate

	return nil
}

// SunsetVersion marks a version as sunset
func (vm *VersionManager) SunsetVersion(version string) error {
	apiVersion, exists := vm.versions[version]
	if !exists {
		return fmt.Errorf("version %s not found", version)
	}

	apiVersion.Status = VersionStatusSunset

	return nil
}

// GetAvailableVersions returns all available versions
func (vm *VersionManager) GetAvailableVersions() map[string]*APIVersion {
	versions := make(map[string]*APIVersion)
	for k, v := range vm.versions {
		versions[k] = v
	}
	return versions
}

// GetVersionChangelog returns changelog for a version
func (vm *VersionManager) GetVersionChangelog(version string) ([]VersionChange, error) {
	apiVersion, exists := vm.versions[version]
	if !exists {
		return nil, fmt.Errorf("version %s not found", version)
	}

	return apiVersion.Changes, nil
}

// GetMigrationGuide returns migration guide between versions
func (vm *VersionManager) GetMigrationGuide(fromVersion, toVersion string) ([]VersionChange, error) {
	_, fromExists := vm.versions[fromVersion]
	_, toExists := vm.versions[toVersion]

	if !fromExists {
		return nil, fmt.Errorf("source version %s not found", fromVersion)
	}
	if !toExists {
		return nil, fmt.Errorf("target version %s not found", toVersion)
	}

	var migrationChanges []VersionChange

	// Get all breaking changes between versions
	fromMajor, _ := vm.parseVersion(fromVersion)
	toMajor, _ := vm.parseVersion(toVersion)

	// Collect breaking changes from all intermediate versions
	for version, apiVersion := range vm.versions {
		vMajor, _ := vm.parseVersion(version)
		if vMajor > fromMajor && vMajor <= toMajor {
			for _, change := range apiVersion.Changes {
				if change.Breaking {
					migrationChanges = append(migrationChanges, change)
				}
			}
		}
	}

	return migrationChanges, nil
}

// SetDefaultVersion sets the default API version
func (vm *VersionManager) SetDefaultVersion(version string) error {
	if _, exists := vm.versions[version]; !exists {
		return fmt.Errorf("version %s not found", version)
	}

	vm.defaultVersion = version
	return nil
}

// GetDefaultVersion returns the default API version
func (vm *VersionManager) GetDefaultVersion() string {
	return vm.defaultVersion
}
