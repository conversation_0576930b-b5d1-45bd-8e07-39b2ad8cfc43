-- Update tenants table to match the service implementation
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS slug VARCHAR(255) UNIQUE;
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS domain VARCHAR(255);
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'active';
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS plan_type VARCHAR(50) DEFAULT 'free';
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS max_users INTEGER DEFAULT 1;
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS max_sites INTEGER DEFAULT 1;
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}';

-- Update the default tenant with proper values
UPDATE tenants 
SET slug = 'default', 
    domain = 'localhost', 
    status = 'active', 
    plan_type = 'enterprise',
    max_users = -1,
    max_sites = -1,
    settings = '{}'
WHERE id = '11111111-1111-1111-1111-111111111111';

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_tenants_slug ON tenants(slug);
CREATE INDEX IF NOT EXISTS idx_tenants_domain ON tenants(domain);
CREATE INDEX IF NOT EXISTS idx_tenants_status ON tenants(status);

-- Add tenant_id indexes to existing tables for better query performance
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sites_tenant_id ON sites(tenant_id);

-- Add tenant_id to other tables that might exist
DO $$
BEGIN
    -- Pages table
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'pages') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'pages' AND column_name = 'tenant_id') THEN
        ALTER TABLE pages ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
        CREATE INDEX IF NOT EXISTS idx_pages_tenant_id ON pages(tenant_id);
    END IF;

    -- Addons table
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'addons') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'addons' AND column_name = 'tenant_id') THEN
        ALTER TABLE addons ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
        CREATE INDEX IF NOT EXISTS idx_addons_tenant_id ON addons(tenant_id);
    END IF;

    -- API Keys table
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'api_keys') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'api_keys' AND column_name = 'tenant_id') THEN
        ALTER TABLE api_keys ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
        CREATE INDEX IF NOT EXISTS idx_api_keys_tenant_id ON api_keys(tenant_id);
    END IF;

    -- Subscriptions table
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'subscriptions') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscriptions' AND column_name = 'tenant_id') THEN
        ALTER TABLE subscriptions ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
        CREATE INDEX IF NOT EXISTS idx_subscriptions_tenant_id ON subscriptions(tenant_id);
    END IF;

    -- Page Content table
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'page_content') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_content' AND column_name = 'tenant_id') THEN
        ALTER TABLE page_content ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
        CREATE INDEX IF NOT EXISTS idx_page_content_tenant_id ON page_content(tenant_id);
    END IF;
END $$;