{{define "content"}}
<div class="container-fluid h-100">
    <div class="row h-100">
        <!-- Settings Navigation -->
        <div class="col-md-3 bg-light border-end">
            <div class="p-3 border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-gear me-2"></i>Site Settings
                </h5>
            </div>
            
            <div class="list-group list-group-flush">
                <a href="#general" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                    <i class="bi bi-info-circle me-2"></i>General
                </a>
                <a href="#domain" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-globe me-2"></i>Domain & SSL
                </a>
                <a href="#seo" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-search me-2"></i>SEO Settings
                </a>
                <a href="#theme" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-palette me-2"></i>Theme & Design
                </a>
                <a href="#publishing" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-cloud-upload me-2"></i>Publishing
                </a>
            </div>
        </div>
        
        <!-- Settings Content -->
        <div class="col-md-9 p-4">
            <div class="tab-content">
                <!-- General Settings -->
                <div class="tab-pane fade show active" id="general">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4>General Settings</h4>
                        <button class="btn btn-primary" onclick="saveSiteSettings()">
                            <i class="bi bi-save me-1"></i>Save Changes
                        </button>
                    </div>
                    
                    <form id="generalSettingsForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="siteName" class="form-label">Site Name</label>
                                    <input type="text" class="form-control" id="siteName" name="siteName" required>
                                    <div class="form-text">This will appear in your site's title and navigation.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="siteDescription" class="form-label">Site Description</label>
                                    <textarea class="form-control" id="siteDescription" name="siteDescription" rows="3"></textarea>
                                    <div class="form-text">A brief description of your site for search engines and social media.</div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Domain & SSL Settings -->
                <div class="tab-pane fade" id="domain">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4>Domain & SSL Settings</h4>
                        <button class="btn btn-primary" onclick="saveSiteSettings()">
                            <i class="bi bi-save me-1"></i>Save Changes
                        </button>
                    </div>
                    
                    <form id="domainSettingsForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="customDomain" class="form-label">Custom Domain</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="customDomain" name="customDomain" placeholder="www.yoursite.com">
                                        <button class="btn btn-outline-secondary" type="button" onclick="verifyDomain()">
                                            <i class="bi bi-check-circle me-1"></i>Verify
                                        </button>
                                    </div>
                                    <div class="form-text">Enter your custom domain name (optional).</div>
                                    <div id="domainStatus" class="mt-2"></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Domain Configuration</label>
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>DNS Records Required:</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>Type</th>
                                                            <th>Name</th>
                                                            <th>Value</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td><code>CNAME</code></td>
                                                            <td><code>www</code></td>
                                                            <td><code>sites.velocitywave.com</code></td>
                                                        </tr>
                                                        <tr>
                                                            <td><code>A</code></td>
                                                            <td><code>@</code></td>
                                                            <td><code>185.199.108.153</code></td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <small class="text-muted">Add these DNS records to your domain provider to connect your custom domain.</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="sslEnabled" name="sslEnabled">
                                    <label class="form-check-label" for="sslEnabled">
                                        Enable SSL (HTTPS)
                                    </label>
                                    <div class="form-text">Recommended for security and SEO. SSL certificates are automatically provisioned.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Subdomain Options</label>
                                    <div class="card">
                                        <div class="card-body">
                                            <p class="card-text">Your site is also available at:</p>
                                            <div class="d-flex align-items-center">
                                                <code id="subdomainUrl">yoursite.velocitywave.com</code>
                                                <button class="btn btn-sm btn-outline-primary ms-2" onclick="copySubdomain()">
                                                    <i class="bi bi-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- SEO Settings -->
                <div class="tab-pane fade" id="seo">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4>SEO Settings</h4>
                        <button class="btn btn-primary" onclick="saveSiteSettings()">
                            <i class="bi bi-save me-1"></i>Save Changes
                        </button>
                    </div>
                    
                    <form id="seoSettingsForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="metaTitle" class="form-label">Meta Title</label>
                                    <input type="text" class="form-control" id="metaTitle" name="metaTitle" maxlength="60">
                                    <div class="form-text">
                                        <span id="titleLength">0</span>/60 characters. This appears in search results and browser tabs.
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="metaDescription" class="form-label">Meta Description</label>
                                    <textarea class="form-control" id="metaDescription" name="metaDescription" rows="3" maxlength="160"></textarea>
                                    <div class="form-text">
                                        <span id="descriptionLength">0</span>/160 characters. This appears in search results.
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="metaKeywords" class="form-label">Meta Keywords</label>
                                    <input type="text" class="form-control" id="metaKeywords" name="metaKeywords" placeholder="keyword1, keyword2, keyword3">
                                    <div class="form-text">Comma-separated keywords (optional, less important for modern SEO).</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="ogImage" class="form-label">Social Media Image (Open Graph)</label>
                                    <input type="url" class="form-control" id="ogImage" name="ogImage" placeholder="https://yoursite.com/image.jpg">
                                    <div class="form-text">Image that appears when your site is shared on social media.</div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Theme & Design Settings -->
                <div class="tab-pane fade" id="theme">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4>Theme & Design</h4>
                        <button class="btn btn-primary" onclick="saveSiteSettings()">
                            <i class="bi bi-save me-1"></i>Save Changes
                        </button>
                    </div>
                    
                    <form id="themeSettingsForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-4">
                                    <h6>Bootstrap Theme Presets</h6>
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="btn-group w-100" role="group">
                                                <input type="radio" class="btn-check" name="themePreset" id="defaultTheme" value="default" checked>
                                                <label class="btn btn-outline-primary" for="defaultTheme">Default</label>
                                                
                                                <input type="radio" class="btn-check" name="themePreset" id="darkTheme" value="dark">
                                                <label class="btn btn-outline-primary" for="darkTheme">Dark</label>
                                                
                                                <input type="radio" class="btn-check" name="themePreset" id="minimalTheme" value="minimal">
                                                <label class="btn btn-outline-primary" for="minimalTheme">Minimal</label>
                                                
                                                <input type="radio" class="btn-check" name="themePreset" id="vibrantTheme" value="vibrant">
                                                <label class="btn btn-outline-primary" for="vibrantTheme">Vibrant</label>
                                                
                                                <input type="radio" class="btn-check" name="themePreset" id="customTheme" value="custom">
                                                <label class="btn btn-outline-primary" for="customTheme">Custom</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-4" id="customColorSection">
                                    <h6>Brand Colors</h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="primaryColor" class="form-label">Primary Color</label>
                                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#0d6efd" onchange="applyThemePreview()">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="secondaryColor" class="form-label">Secondary Color</label>
                                            <input type="color" class="form-control form-control-color" id="secondaryColor" value="#6c757d" onchange="applyThemePreview()">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="accentColor" class="form-label">Success Color</label>
                                            <input type="color" class="form-control form-control-color" id="accentColor" value="#198754" onchange="applyThemePreview()">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="warningColor" class="form-label">Warning Color</label>
                                            <input type="color" class="form-control form-control-color" id="warningColor" value="#ffc107" onchange="applyThemePreview()">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="dangerColor" class="form-label">Danger Color</label>
                                            <input type="color" class="form-control form-control-color" id="dangerColor" value="#dc3545" onchange="applyThemePreview()">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="infoColor" class="form-label">Info Color</label>
                                            <input type="color" class="form-control form-control-color" id="infoColor" value="#0dcaf0" onchange="applyThemePreview()">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <h6>Typography</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="fontFamily" class="form-label">Font Family</label>
                                            <select class="form-select" id="fontFamily" onchange="applyThemePreview()">
                                                <option value="system">System Default</option>
                                                <option value="'Inter', sans-serif">Inter (Modern)</option>
                                                <option value="'Roboto', sans-serif">Roboto (Google)</option>
                                                <option value="'Open Sans', sans-serif">Open Sans</option>
                                                <option value="'Lato', sans-serif">Lato</option>
                                                <option value="'Poppins', sans-serif">Poppins</option>
                                                <option value="'Montserrat', sans-serif">Montserrat</option>
                                                <option value="'Playfair Display', serif">Playfair Display (Serif)</option>
                                                <option value="'Merriweather', serif">Merriweather (Serif)</option>
                                                <option value="'Source Code Pro', monospace">Source Code Pro (Mono)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="fontSize" class="form-label">Base Font Size</label>
                                            <select class="form-select" id="fontSize" onchange="applyThemePreview()">
                                                <option value="14px">Small (14px)</option>
                                                <option value="16px" selected>Medium (16px)</option>
                                                <option value="18px">Large (18px)</option>
                                                <option value="20px">Extra Large (20px)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <h6>Theme Preview</h6>
                                    <div class="card" id="themePreview">
                                        <div class="card-header">
                                            <h6 class="mb-0">Preview Components</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <button class="btn btn-primary me-2 mb-2">Primary</button>
                                                    <button class="btn btn-secondary me-2 mb-2">Secondary</button>
                                                    <button class="btn btn-success me-2 mb-2">Success</button>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-2">This is an info alert</div>
                                                    <div class="badge bg-primary me-1">Badge</div>
                                                    <div class="badge bg-secondary">Secondary</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Publishing Settings -->
                <div class="tab-pane fade" id="publishing">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4>Publishing Settings</h4>
                        <button class="btn btn-success" onclick="publishSite()">
                            <i class="bi bi-cloud-upload me-1"></i>Publish Site
                        </button>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Publication Status</h6>
                                    <p class="card-text" id="publishStatus">
                                        <span class="badge bg-secondary">Draft</span>
                                        Your site is currently in draft mode.
                                    </p>
                                    <div class="d-grid gap-2 d-md-flex">
                                        <button class="btn btn-success" onclick="publishSite()">
                                            <i class="bi bi-cloud-upload me-1"></i>Publish Now
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="previewSite()">
                                            <i class="bi bi-eye me-1"></i>Preview
                                        </button>
                                        <button class="btn btn-outline-info" onclick="showPublishingHistory()">
                                            <i class="bi bi-clock-history me-1"></i>History
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentSiteId = new URLSearchParams(window.location.search).get('siteId');
let siteSettings = {};

// Load site settings on page load
document.addEventListener('DOMContentLoaded', function() {
    if (currentSiteId) {
        loadSiteSettings();
    }
});

async function loadSiteSettings() {
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    query GetSiteSettings($siteId: ID!) {
                        siteSettings(siteId: $siteId) {
                            id
                            siteName
                            siteDescription
                            customDomain
                            sslEnabled
                            isPublished
                            themeConfig
                            seoConfig
                        }
                    }
                `,
                variables: { siteId: currentSiteId }
            })
        });
        
        const data = await response.json();
        if (data.data?.siteSettings) {
            siteSettings = data.data.siteSettings;
            populateSettingsForm();
        }
    } catch (error) {
        console.error('Failed to load site settings:', error);
        showAlert('Failed to load site settings', 'error');
    }
}

function populateSettingsForm() {
    // General settings
    document.getElementById('siteName').value = siteSettings.siteName || '';
    document.getElementById('siteDescription').value = siteSettings.siteDescription || '';
    
    // Domain settings
    document.getElementById('customDomain').value = siteSettings.customDomain || '';
    document.getElementById('sslEnabled').checked = siteSettings.sslEnabled || false;
    
    // SEO settings
    const seoConfig = siteSettings.seoConfig || {};
    document.getElementById('metaTitle').value = seoConfig.metaTitle || '';
    document.getElementById('metaDescription').value = seoConfig.metaDescription || '';
    document.getElementById('metaKeywords').value = seoConfig.metaKeywords || '';
    document.getElementById('ogImage').value = seoConfig.ogImage || '';
    
    // Theme settings
    const themeConfig = siteSettings.themeConfig || {};
    document.getElementById('primaryColor').value = themeConfig.primaryColor || '#0d6efd';
    document.getElementById('secondaryColor').value = themeConfig.secondaryColor || '#6c757d';
    document.getElementById('accentColor').value = themeConfig.accentColor || '#198754';
    document.getElementById('warningColor').value = themeConfig.warningColor || '#ffc107';
    document.getElementById('dangerColor').value = themeConfig.dangerColor || '#dc3545';
    document.getElementById('infoColor').value = themeConfig.infoColor || '#0dcaf0';
    document.getElementById('fontFamily').value = themeConfig.fontFamily || 'system';
    document.getElementById('fontSize').value = themeConfig.fontSize || '16px';
    
    // Setup theme preset handlers
    setupThemePresets();
    applyThemePreview();
    
    // Publishing status
    updatePublishingStatus();
    
    // Update subdomain URL
    updateSubdomainUrl();
    
    // Update character counters
    updateCharacterCounters();
    setupCharacterCounters();
}

function getAuthToken() {
    return localStorage.getItem('authToken') || sessionStorage.getItem('authToken') || '';
}

async function saveSiteSettings() {
    if (!currentSiteId) {
        showAlert('No site selected', 'error');
        return;
    }
    
    const settingsInput = {
        siteName: document.getElementById('siteName').value,
        siteDescription: document.getElementById('siteDescription').value,
        customDomain: document.getElementById('customDomain').value || null,
        themeConfig: {
            primaryColor: document.getElementById('primaryColor').value,
            secondaryColor: document.getElementById('secondaryColor').value,
            accentColor: document.getElementById('accentColor').value,
            warningColor: document.getElementById('warningColor').value,
            dangerColor: document.getElementById('dangerColor').value,
            infoColor: document.getElementById('infoColor').value,
            fontFamily: document.getElementById('fontFamily').value,
            fontSize: document.getElementById('fontSize').value
        },
        seoConfig: {
            metaTitle: document.getElementById('metaTitle').value,
            metaDescription: document.getElementById('metaDescription').value,
            metaKeywords: document.getElementById('metaKeywords').value,
            ogImage: document.getElementById('ogImage').value
        }
    };
    
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    mutation UpdateSiteSettings($siteId: ID!, $input: SiteSettingsInput!) {
                        updateSiteSettings(siteId: $siteId, input: $input) {
                            id
                            siteName
                            siteDescription
                            customDomain
                            sslEnabled
                            isPublished
                        }
                    }
                `,
                variables: { 
                    siteId: currentSiteId,
                    input: settingsInput
                }
            })
        });
        
        const data = await response.json();
        if (data.data?.updateSiteSettings) {
            showAlert('Site settings saved successfully!', 'success');
            siteSettings = { ...siteSettings, ...data.data.updateSiteSettings };
        } else {
            throw new Error(data.errors?.[0]?.message || 'Failed to save settings');
        }
    } catch (error) {
        console.error('Failed to save site settings:', error);
        showAlert('Failed to save site settings: ' + error.message, 'error');
    }
}

function setupCharacterCounters() {
    const metaTitle = document.getElementById('metaTitle');
    const metaDescription = document.getElementById('metaDescription');
    
    metaTitle.addEventListener('input', updateCharacterCounters);
    metaDescription.addEventListener('input', updateCharacterCounters);
}

function updateCharacterCounters() {
    const titleLength = document.getElementById('metaTitle').value.length;
    const descriptionLength = document.getElementById('metaDescription').value.length;
    
    document.getElementById('titleLength').textContent = titleLength;
    document.getElementById('descriptionLength').textContent = descriptionLength;
    
    // Add warning colors for optimal lengths
    const titleCounter = document.getElementById('titleLength');
    const descriptionCounter = document.getElementById('descriptionLength');
    
    titleCounter.className = titleLength > 60 ? 'text-danger' : titleLength > 50 ? 'text-warning' : 'text-success';
    descriptionCounter.className = descriptionLength > 160 ? 'text-danger' : descriptionLength > 140 ? 'text-warning' : 'text-success';
}

function updatePublishingStatus() {
    const statusElement = document.getElementById('publishStatus');
    if (siteSettings.isPublished) {
        statusElement.innerHTML = '<span class="badge bg-success">Published</span> Your site is live and accessible to visitors.';
    } else {
        statusElement.innerHTML = '<span class="badge bg-secondary">Draft</span> Your site is currently in draft mode.';
    }
}

async function publishSite() {
    if (!confirm('Are you sure you want to publish this site? It will be accessible to visitors.')) {
        return;
    }
    
    const publishBtn = document.querySelector('button[onclick="publishSite()"]');
    const originalText = publishBtn.innerHTML;
    publishBtn.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Publishing...';
    publishBtn.disabled = true;
    
    try {
        // First compile the site
        const compileResponse = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    mutation CompileSite($siteId: ID!) {
                        compileSite(siteId: $siteId) {
                            success
                            message
                            html
                            css
                            js
                            metadata {
                                compiledAt
                                version
                                pageCount
                                componentCount
                            }
                        }
                    }
                `,
                variables: { siteId: currentSiteId }
            })
        });
        
        const compileData = await compileResponse.json();
        if (!compileData.data?.compileSite?.success) {
            throw new Error(compileData.data?.compileSite?.message || 'Compilation failed');
        }
        
        // Then publish the site
        const publishResponse = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    mutation PublishSite($siteId: ID!, $input: PublishSiteInput!) {
                        publishSite(siteId: $siteId, input: $input) {
                            success
                            message
                            publishedUrl
                            deploymentId
                            publishedAt
                        }
                    }
                `,
                variables: { 
                    siteId: currentSiteId,
                    input: {
                        domain: document.getElementById('customDomain').value || null,
                        generateApiKey: true,
                        versionMessage: 'Published from site settings'
                    }
                }
            })
        });
        
        const publishData = await publishResponse.json();
        if (publishData.data?.publishSite?.success) {
            showAlert('Site published successfully!', 'success');
            siteSettings.isPublished = true;
            updatePublishingStatus();
            
            // Show published URL
            if (publishData.data.publishSite.publishedUrl) {
                showPublishedUrl(publishData.data.publishSite.publishedUrl);
            }
        } else {
            throw new Error(publishData.data?.publishSite?.message || 'Publishing failed');
        }
        
    } catch (error) {
        console.error('Failed to publish site:', error);
        showAlert('Failed to publish site: ' + error.message, 'error');
    } finally {
        publishBtn.innerHTML = originalText;
        publishBtn.disabled = false;
    }
}

function showPublishedUrl(url) {
    const alertHTML = `
        <div class="alert alert-success alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999; min-width: 400px;">
            <h6 class="alert-heading">Site Published!</h6>
            <p class="mb-2">Your site is now live at:</p>
            <div class="d-flex align-items-center">
                <code class="me-2">${url}</code>
                <button class="btn btn-sm btn-outline-success" onclick="window.open('${url}', '_blank')">
                    <i class="bi bi-box-arrow-up-right"></i>
                </button>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    document.body.insertAdjacentHTML('afterbegin', alertHTML);
}

function previewSite() {
    const previewUrl = `/preview/${currentSiteId}`;
    window.open(previewUrl, '_blank');
}

async function verifyDomain() {
    const domain = document.getElementById('customDomain').value;
    if (!domain) {
        showAlert('Please enter a domain name', 'warning');
        return;
    }
    
    const statusDiv = document.getElementById('domainStatus');
    statusDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Verifying domain...';
    
    try {
        // Simulate domain verification API call
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Mock verification result
        const isValid = Math.random() > 0.5; // 50% chance for demo
        
        if (isValid) {
            statusDiv.innerHTML = '<div class="alert alert-success py-2 mb-0"><i class="bi bi-check-circle me-2"></i>Domain verified successfully!</div>';
        } else {
            statusDiv.innerHTML = '<div class="alert alert-warning py-2 mb-0"><i class="bi bi-exclamation-triangle me-2"></i>Domain verification failed. Please check your DNS settings.</div>';
        }
    } catch (error) {
        statusDiv.innerHTML = '<div class="alert alert-danger py-2 mb-0"><i class="bi bi-x-circle me-2"></i>Verification failed. Please try again.</div>';
    }
}

function updateSubdomainUrl() {
    if (siteSettings.siteName) {
        const slug = siteSettings.siteName.toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
        document.getElementById('subdomainUrl').textContent = `${slug}.velocitywave.com`;
    }
}

function setupThemePresets() {
    const presetRadios = document.querySelectorAll('input[name="themePreset"]');
    presetRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                applyThemePreset(this.value);
            }
        });
    });
}

function applyThemePreset(preset) {
    const presets = {
        default: {
            primaryColor: '#0d6efd', secondaryColor: '#6c757d', accentColor: '#198754',
            warningColor: '#ffc107', dangerColor: '#dc3545', infoColor: '#0dcaf0',
            fontFamily: 'system', fontSize: '16px'
        },
        dark: {
            primaryColor: '#375a7f', secondaryColor: '#444', accentColor: '#00bc8c',
            warningColor: '#f39c12', dangerColor: '#e74c3c', infoColor: '#3498db',
            fontFamily: 'system', fontSize: '16px'
        },
        minimal: {
            primaryColor: '#2c3e50', secondaryColor: '#95a5a6', accentColor: '#27ae60',
            warningColor: '#f39c12', dangerColor: '#e74c3c', infoColor: '#3498db',
            fontFamily: "'Inter', sans-serif", fontSize: '16px'
        },
        vibrant: {
            primaryColor: '#e91e63', secondaryColor: '#9c27b0', accentColor: '#4caf50',
            warningColor: '#ff9800', dangerColor: '#f44336', infoColor: '#2196f3',
            fontFamily: "'Poppins', sans-serif", fontSize: '16px'
        }
    };
    
    if (presets[preset]) {
        const config = presets[preset];
        Object.keys(config).forEach(key => {
            const element = document.getElementById(key);
            if (element) element.value = config[key];
        });
        applyThemePreview();
    }
}

function applyThemePreview() {
    const preview = document.getElementById('themePreview');
    const colors = {
        primary: document.getElementById('primaryColor').value,
        secondary: document.getElementById('secondaryColor').value,
        success: document.getElementById('accentColor').value,
        warning: document.getElementById('warningColor').value,
        danger: document.getElementById('dangerColor').value,
        info: document.getElementById('infoColor').value
    };
    
    const fontFamily = document.getElementById('fontFamily').value;
    const fontSize = document.getElementById('fontSize').value;
    
    // Apply styles to preview
    preview.style.fontFamily = fontFamily !== 'system' ? fontFamily : '';
    preview.style.fontSize = fontSize;
    
    // Update CSS custom properties for preview
    Object.keys(colors).forEach(key => {
        preview.style.setProperty(`--bs-${key}`, colors[key]);
    });
}

function copySubdomain() {
    const subdomain = document.getElementById('subdomainUrl').textContent;
    navigator.clipboard.writeText(`https://${subdomain}`).then(() => {
        showAlert('Subdomain URL copied to clipboard!', 'success');
    }).catch(() => {
        showAlert('Failed to copy URL', 'error');
    });
}

function showAlert(message, type) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    document.body.insertAdjacentHTML('afterbegin', alertHTML);
    
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 3000);
}
</script>
{{end}}

{{define "title"}}Site Settings{{end}}