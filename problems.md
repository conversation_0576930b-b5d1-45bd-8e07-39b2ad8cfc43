[{"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidIfaceAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidIfaceAssign"}}, "severity": 8, "message": "cannot use &executableSchema{…} (value of type *executableSchema) as graphql.ExecutableSchema value in return statement: *executableSchema does not implement graphql.ExecutableSchema (wrong type for method Complexity)\n\t\thave Complexity(context.Context, string, string, int, map[string]any) (int, bool)\n\t\twant Complexity(string, string, int, map[string]interface{}) (int, bool)", "source": "compiler", "startLineNumber": 26, "startColumn": 9, "endLineNumber": 31, "endColumn": 3}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "duplicate method GenerateSiteAPIKey", "source": "compiler", "startLineNumber": 1936, "startColumn": 2, "endLineNumber": 1936, "endColumn": 20, "relatedInformation": [{"startLineNumber": 2031, "startColumn": 2, "endLineNumber": 2031, "endColumn": 20, "message": "other declaration of method GenerateSiteAPIKey", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "duplicate method GenerateSiteAPIKey (see details)", "source": "compiler", "startLineNumber": 2031, "startColumn": 2, "endLineNumber": 2031, "endColumn": 20, "relatedInformation": [{"startLineNumber": 1936, "startColumn": 2, "endLineNumber": 1936, "endColumn": 20, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 123333, "startColumn": 17, "endLineNumber": 123333, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 123344, "startColumn": 12, "endLineNumber": 123344, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 125254, "startColumn": 17, "endLineNumber": 125254, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 125259, "startColumn": 12, "endLineNumber": 125259, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 126496, "startColumn": 17, "endLineNumber": 126496, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 126504, "startColumn": 12, "endLineNumber": 126504, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 126864, "startColumn": 17, "endLineNumber": 126864, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 126872, "startColumn": 12, "endLineNumber": 126872, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 31, "startColumn": 17, "endLineNumber": 31, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 39, "startColumn": 10, "endLineNumber": 39, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 75, "startColumn": 19, "endLineNumber": 75, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 104, "startColumn": 11, "endLineNumber": 104, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 118, "startColumn": 13, "endLineNumber": 118, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 128, "startColumn": 11, "endLineNumber": 128, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 142, "startColumn": 13, "endLineNumber": 142, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 152, "startColumn": 11, "endLineNumber": 152, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 166, "startColumn": 13, "endLineNumber": 166, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 176, "startColumn": 11, "endLineNumber": 176, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 190, "startColumn": 13, "endLineNumber": 190, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 200, "startColumn": 11, "endLineNumber": 200, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 214, "startColumn": 13, "endLineNumber": 214, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 224, "startColumn": 11, "endLineNumber": 224, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 254, "startColumn": 17, "endLineNumber": 254, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 281, "startColumn": 11, "endLineNumber": 281, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 291, "startColumn": 20, "endLineNumber": 291, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 310, "startColumn": 13, "endLineNumber": 310, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 325, "startColumn": 11, "endLineNumber": 325, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 335, "startColumn": 10, "endLineNumber": 335, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 346, "startColumn": 11, "endLineNumber": 346, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 374, "startColumn": 11, "endLineNumber": 374, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 392, "startColumn": 11, "endLineNumber": 392, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 406, "startColumn": 11, "endLineNumber": 406, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 420, "startColumn": 11, "endLineNumber": 420, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 434, "startColumn": 11, "endLineNumber": 434, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 448, "startColumn": 10, "endLineNumber": 448, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 468, "startColumn": 13, "endLineNumber": 468, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 631, "startColumn": 46, "endLineNumber": 631, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 695, "startColumn": 46, "endLineNumber": 695, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 763, "startColumn": 46, "endLineNumber": 763, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 793, "startColumn": 54, "endLineNumber": 793, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 848, "startColumn": 62, "endLineNumber": 848, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidRangeExpr", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidRangeExpr"}}, "severity": 8, "message": "cannot range over input.Settings (variable of interface type any)", "source": "compiler", "startLineNumber": 870, "startColumn": 22, "endLineNumber": 870, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 919, "startColumn": 62, "endLineNumber": 919, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 939, "startColumn": 54, "endLineNumber": 939, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 977, "startColumn": 46, "endLineNumber": 977, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.Name undefined (type model.UpdateSiteSettingsInput has no field or method Name)", "source": "compiler", "startLineNumber": 985, "startColumn": 23, "endLineNumber": 985, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.Description undefined (type model.UpdateSiteSettingsInput has no field or method Description)", "source": "compiler", "startLineNumber": 986, "startColumn": 23, "endLineNumber": 986, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.CustomDomain undefined (type model.UpdateSiteSettingsInput has no field or method CustomDomain)", "source": "compiler", "startLineNumber": 987, "startColumn": 23, "endLineNumber": 987, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.ThemeConfig undefined (type model.UpdateSiteSettingsInput has no field or method ThemeConfig)", "source": "compiler", "startLineNumber": 989, "startColumn": 13, "endLineNumber": 989, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.ThemeConfig undefined (type model.UpdateSiteSettingsInput has no field or method ThemeConfig)", "source": "compiler", "startLineNumber": 993, "startColumn": 28, "endLineNumber": 993, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.SeoConfig undefined (type model.UpdateSiteSettingsInput has no field or method SeoConfig)", "source": "compiler", "startLineNumber": 999, "startColumn": 13, "endLineNumber": 999, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.SeoConfig undefined (type model.UpdateSiteSettingsInput has no field or method SeoConfig)", "source": "compiler", "startLineNumber": 1003, "startColumn": 28, "endLineNumber": 1003, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1169, "startColumn": 47, "endLineNumber": 1169, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1203, "startColumn": 47, "endLineNumber": 1203, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.JSON", "source": "compiler", "startLineNumber": 1229, "startColumn": 21, "endLineNumber": 1229, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1254, "startColumn": 20, "endLineNumber": 1254, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1255, "startColumn": 20, "endLineNumber": 1255, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1272, "startColumn": 47, "endLineNumber": 1272, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1289, "startColumn": 63, "endLineNumber": 1289, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidRangeExpr", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidRangeExpr"}}, "severity": 8, "message": "cannot range over input.Features (variable of interface type any)", "source": "compiler", "startLineNumber": 1291, "startColumn": 26, "endLineNumber": 1291, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use input.Limits (variable of interface type any) as map[string]interface{} value in argument to subscriptionService.CreateTier: need type assertion", "source": "compiler", "startLineNumber": 1294, "startColumn": 96, "endLineNumber": 1294, "endColumn": 108}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1306, "startColumn": 23, "endLineNumber": 1306, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1307, "startColumn": 23, "endLineNumber": 1307, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1329, "startColumn": 11, "endLineNumber": 1329, "endColumn": 30}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1335, "startColumn": 25, "endLineNumber": 1335, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1344, "startColumn": 31, "endLineNumber": 1344, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1345, "startColumn": 31, "endLineNumber": 1345, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1347, "startColumn": 31, "endLineNumber": 1347, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1348, "startColumn": 31, "endLineNumber": 1348, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1369, "startColumn": 63, "endLineNumber": 1369, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1390, "startColumn": 57, "endLineNumber": 1390, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use input.Data (variable of interface type any) as map[string]interface{} value in assignment: need type assertion", "source": "compiler", "startLineNumber": 1393, "startColumn": 10, "endLineNumber": 1393, "endColumn": 20}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1439, "startColumn": 57, "endLineNumber": 1439, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1495, "startColumn": 57, "endLineNumber": 1495, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GenerateSiteAPIKey already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:319:28", "source": "compiler", "startLineNumber": 1560, "startColumn": 28, "endLineNumber": 1560, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1566, "startColumn": 11, "endLineNumber": 1566, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1660, "startColumn": 20, "endLineNumber": 1660, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1710, "startColumn": 11, "endLineNumber": 1710, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1719, "startColumn": 11, "endLineNumber": 1719, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1724, "startColumn": 11, "endLineNumber": 1724, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1729, "startColumn": 11, "endLineNumber": 1729, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1736, "startColumn": 20, "endLineNumber": 1736, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1750, "startColumn": 22, "endLineNumber": 1750, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1772, "startColumn": 18, "endLineNumber": 1772, "endColumn": 20}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1788, "startColumn": 47, "endLineNumber": 1788, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1822, "startColumn": 21, "endLineNumber": 1822, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1823, "startColumn": 21, "endLineNumber": 1823, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1842, "startColumn": 47, "endLineNumber": 1842, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1858, "startColumn": 20, "endLineNumber": 1858, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1859, "startColumn": 20, "endLineNumber": 1859, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1875, "startColumn": 47, "endLineNumber": 1875, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1901, "startColumn": 47, "endLineNumber": 1901, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1915, "startColumn": 63, "endLineNumber": 1915, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1930, "startColumn": 24, "endLineNumber": 1930, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1931, "startColumn": 24, "endLineNumber": 1931, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1950, "startColumn": 63, "endLineNumber": 1950, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1960, "startColumn": 31, "endLineNumber": 1960, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1961, "startColumn": 31, "endLineNumber": 1961, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1963, "startColumn": 31, "endLineNumber": 1963, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1964, "startColumn": 31, "endLineNumber": 1964, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1987, "startColumn": 63, "endLineNumber": 1987, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2018, "startColumn": 57, "endLineNumber": 2018, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2068, "startColumn": 11, "endLineNumber": 2068, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2088, "startColumn": 57, "endLineNumber": 2088, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2106, "startColumn": 31, "endLineNumber": 2106, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2107, "startColumn": 31, "endLineNumber": 2107, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2118, "startColumn": 54, "endLineNumber": 2118, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2130, "startColumn": 23, "endLineNumber": 2130, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2155, "startColumn": 46, "endLineNumber": 2155, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2178, "startColumn": 24, "endLineNumber": 2178, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2179, "startColumn": 24, "endLineNumber": 2179, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2201, "startColumn": 46, "endLineNumber": 2201, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2230, "startColumn": 22, "endLineNumber": 2230, "endColumn": 26}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2260, "startColumn": 23, "endLineNumber": 2260, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2261, "startColumn": 23, "endLineNumber": 2261, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2280, "startColumn": 46, "endLineNumber": 2280, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2299, "startColumn": 22, "endLineNumber": 2299, "endColumn": 26}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2328, "startColumn": 62, "endLineNumber": 2328, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2376, "startColumn": 62, "endLineNumber": 2376, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2428, "startColumn": 46, "endLineNumber": 2428, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UnexportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UnexportedName"}}, "severity": 8, "message": "name __InputValueResolver not exported by package generated", "source": "compiler", "startLineNumber": 2713, "startColumn": 45, "endLineNumber": 2713, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UnexportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UnexportedName"}}, "severity": 8, "message": "name __TypeResolver not exported by package generated", "source": "compiler", "startLineNumber": 2716, "startColumn": 39, "endLineNumber": 2716, "endColumn": 53}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Config in struct literal of type model.AddonInstallation", "source": "compiler", "startLineNumber": 344, "startColumn": 3, "endLineNumber": 344, "endColumn": 9}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Config in struct literal of type model.AddonInstallation", "source": "compiler", "startLineNumber": 375, "startColumn": 4, "endLineNumber": 375, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Config in struct literal of type model.AddonInstallation", "source": "compiler", "startLineNumber": 382, "startColumn": 4, "endLineNumber": 382, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Title in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 33, "startColumn": 3, "endLineNumber": 33, "endColumn": 8}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Content in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 35, "startColumn": 3, "endLineNumber": 35, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Title in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 55, "startColumn": 3, "endLineNumber": 55, "endColumn": 8}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.Title undefined (type model.CreateBusinessPlanInput has no field or method Title)", "source": "compiler", "startLineNumber": 55, "startColumn": 22, "endLineNumber": 55, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Content in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 57, "startColumn": 3, "endLineNumber": 57, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.Content undefined (type model.CreateBusinessPlanInput has no field or method Content)", "source": "compiler", "startLineNumber": 57, "startColumn": 22, "endLineNumber": 57, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 104, "startColumn": 25, "endLineNumber": 104, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 226, "startColumn": 21, "endLineNumber": 226, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 227, "startColumn": 21, "endLineNumber": 227, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 231, "startColumn": 20, "endLineNumber": 231, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 232, "startColumn": 20, "endLineNumber": 232, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/expert_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "default", "target": {"$mid": 1, "path": "/golang.org/x/tools/go/analysis/passes/unreachable", "scheme": "https", "authority": "pkg.go.dev"}}, "severity": 4, "message": "unreachable code", "source": "unreachable", "startLineNumber": 400, "startColumn": 2, "endLineNumber": 400, "endColumn": 33, "tags": [1]}]