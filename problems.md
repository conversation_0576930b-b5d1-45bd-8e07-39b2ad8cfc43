[{"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidLit", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidLit"}}, "severity": 8, "message": "invalid composite literal type model.ComponentResult", "source": "compiler", "startLineNumber": 1797, "startColumn": 10, "endLineNumber": 1802, "endColumn": 6}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: generated.__InputValueResolver", "source": "compiler", "startLineNumber": 2759, "startColumn": 45, "endLineNumber": 2759, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: generated.__TypeResolver", "source": "compiler", "startLineNumber": 2762, "startColumn": 39, "endLineNumber": 2762, "endColumn": 53}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.<PERSON><PERSON><PERSON>iew", "source": "compiler", "startLineNumber": 390, "startColumn": 97, "endLineNumber": 390, "endColumn": 108}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.<PERSON><PERSON><PERSON>iew", "source": "compiler", "startLineNumber": 397, "startColumn": 22, "endLineNumber": 397, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Year in struct literal of type model.FinancialPlan", "source": "compiler", "startLineNumber": 120, "startColumn": 3, "endLineNumber": 120, "endColumn": 7}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Revenue in struct literal of type model.FinancialPlan", "source": "compiler", "startLineNumber": 121, "startColumn": 3, "endLineNumber": 121, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Expenses in struct literal of type model.FinancialPlan", "source": "compiler", "startLineNumber": 122, "startColumn": 3, "endLineNumber": 122, "endColumn": 11}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Profit in struct literal of type model.FinancialPlan", "source": "compiler", "startLineNumber": 123, "startColumn": 3, "endLineNumber": 123, "endColumn": 9}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/expert_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "app.Status undefined (type model.ExpertApplication has no field or method Status)", "source": "compiler", "startLineNumber": 159, "startColumn": 17, "endLineNumber": 159, "endColumn": 23}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/expert_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Rating in struct literal of type model.ExpertReview", "source": "compiler", "startLineNumber": 512, "startColumn": 4, "endLineNumber": 512, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/expert_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Comment in struct literal of type model.ExpertReview", "source": "compiler", "startLineNumber": 514, "startColumn": 4, "endLineNumber": 514, "endColumn": 11}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/go.mod", "owner": "_generated_diagnostic_collection_name_#2", "severity": 4, "message": "github.com/agnivade/levenshtein is not used in this module", "source": "go mod tidy", "startLineNumber": 21, "startColumn": 2, "endLineNumber": 21, "endColumn": 40}]