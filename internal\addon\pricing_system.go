package addon

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// PricingSystem handles addon pricing and monetization
type PricingSystem struct {
	marketplace    *Marketplace
	subscriptions  map[string]*Subscription
	transactions   map[string]*Transaction
	paymentMethods map[string]*PaymentMethod
	mutex          sync.RWMutex
	config         PricingConfig
}

// PricingConfig contains pricing system configuration
type PricingConfig struct {
	DefaultCurrency     string  `json:"defaultCurrency"`
	PlatformFeePercent  float64 `json:"platformFeePercent"`
	MinimumPrice        float64 `json:"minimumPrice"`
	MaximumPrice        float64 `json:"maximumPrice"`
	TrialPeriodDays     int     `json:"trialPeriodDays"`
	RefundPeriodDays    int     `json:"refundPeriodDays"`
	PaymentProcessorFee float64 `json:"paymentProcessorFee"`
}

// Subscription represents an addon subscription
type Subscription struct {
	ID              string                 `json:"id"`
	UserID          string                 `json:"userId"`
	AddonID         string                 `json:"addonId"`
	PlanID          string                 `json:"planId"`
	Status          SubscriptionStatus     `json:"status"`
	StartDate       time.Time              `json:"startDate"`
	EndDate         *time.Time             `json:"endDate,omitempty"`
	TrialEndDate    *time.Time             `json:"trialEndDate,omitempty"`
	NextBillingDate time.Time              `json:"nextBillingDate"`
	Amount          float64                `json:"amount"`
	Currency        string                 `json:"currency"`
	BillingCycle    BillingCycle           `json:"billingCycle"`
	PaymentMethodID string                 `json:"paymentMethodId"`
	Metadata        map[string]interface{} `json:"metadata"`
	CreatedAt       time.Time              `json:"createdAt"`
	UpdatedAt       time.Time              `json:"updatedAt"`
}

// SubscriptionStatus represents subscription status
type SubscriptionStatus string

const (
	SubscriptionStatusActive    SubscriptionStatus = "active"
	SubscriptionStatusTrialing  SubscriptionStatus = "trialing"
	SubscriptionStatusPastDue   SubscriptionStatus = "past_due"
	SubscriptionStatusCanceled  SubscriptionStatus = "canceled"
	SubscriptionStatusExpired   SubscriptionStatus = "expired"
	SubscriptionStatusSuspended SubscriptionStatus = "suspended"
)

// BillingCycle represents billing frequency
type BillingCycle string

const (
	BillingCycleMonthly BillingCycle = "monthly"
	BillingCycleYearly  BillingCycle = "yearly"
	BillingCycleWeekly  BillingCycle = "weekly"
	BillingCycleOneTime BillingCycle = "one_time"
)

// Transaction represents a payment transaction
type Transaction struct {
	ID              string                 `json:"id"`
	UserID          string                 `json:"userId"`
	AddonID         string                 `json:"addonId"`
	SubscriptionID  string                 `json:"subscriptionId,omitempty"`
	Type            TransactionType        `json:"type"`
	Status          TransactionStatus      `json:"status"`
	Amount          float64                `json:"amount"`
	Currency        string                 `json:"currency"`
	PlatformFee     float64                `json:"platformFee"`
	ProcessorFee    float64                `json:"processorFee"`
	NetAmount       float64                `json:"netAmount"`
	PaymentMethodID string                 `json:"paymentMethodId"`
	ProcessorTxnID  string                 `json:"processorTxnId"`
	Description     string                 `json:"description"`
	Metadata        map[string]interface{} `json:"metadata"`
	CreatedAt       time.Time              `json:"createdAt"`
	ProcessedAt     *time.Time             `json:"processedAt,omitempty"`
}

// TransactionType represents transaction type
type TransactionType string

const (
	TransactionTypePurchase TransactionType = "purchase"
	TransactionTypeRefund   TransactionType = "refund"
	TransactionTypeRenewal  TransactionType = "renewal"
	TransactionTypeUpgrade  TransactionType = "upgrade"
	TransactionTypeCredit   TransactionType = "credit"
)

// TransactionStatus represents transaction status
type TransactionStatus string

const (
	TransactionStatusPending   TransactionStatus = "pending"
	TransactionStatusCompleted TransactionStatus = "completed"
	TransactionStatusFailed    TransactionStatus = "failed"
	TransactionStatusRefunded  TransactionStatus = "refunded"
	TransactionStatusCanceled  TransactionStatus = "canceled"
)

// PaymentMethod represents a user's payment method
type PaymentMethod struct {
	ID          string                 `json:"id"`
	UserID      string                 `json:"userId"`
	Type        PaymentMethodType      `json:"type"`
	Provider    string                 `json:"provider"`
	Last4       string                 `json:"last4"`
	ExpiryMonth int                    `json:"expiryMonth"`
	ExpiryYear  int                    `json:"expiryYear"`
	Brand       string                 `json:"brand"`
	IsDefault   bool                   `json:"isDefault"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"createdAt"`
	UpdatedAt   time.Time              `json:"updatedAt"`
}

// PaymentMethodType represents payment method type
type PaymentMethodType string

const (
	PaymentMethodTypeCard   PaymentMethodType = "card"
	PaymentMethodTypePaypal PaymentMethodType = "paypal"
	PaymentMethodTypeBank   PaymentMethodType = "bank"
	PaymentMethodTypeCrypto PaymentMethodType = "crypto"
)

// NewPricingSystem creates a new pricing system
func NewPricingSystem(config PricingConfig) *PricingSystem {
	if config.DefaultCurrency == "" {
		config.DefaultCurrency = "USD"
	}
	if config.PlatformFeePercent == 0 {
		config.PlatformFeePercent = 0.15 // 15% platform fee
	}
	if config.MinimumPrice == 0 {
		config.MinimumPrice = 0.99
	}
	if config.MaximumPrice == 0 {
		config.MaximumPrice = 999.99
	}
	if config.TrialPeriodDays == 0 {
		config.TrialPeriodDays = 14
	}
	if config.RefundPeriodDays == 0 {
		config.RefundPeriodDays = 30
	}
	if config.PaymentProcessorFee == 0 {
		config.PaymentProcessorFee = 0.029 // 2.9% + $0.30
	}

	return &PricingSystem{
		marketplace:    NewMarketplace(MarketplaceConfig{}),
		subscriptions:  make(map[string]*Subscription),
		transactions:   make(map[string]*Transaction),
		paymentMethods: make(map[string]*PaymentMethod),
		config:         config,
	}
}

// PurchaseAddon handles addon purchase
func (ps *PricingSystem) PurchaseAddon(ctx context.Context, userID, addonID, paymentMethodID string) (*Transaction, error) {
	ps.mutex.Lock()
	defer ps.mutex.Unlock()

	// Get addon pricing info
	addon, exists := ps.marketplace.addons[addonID]
	if !exists {
		return nil, fmt.Errorf("addon not found")
	}

	// Validate payment method
	paymentMethod, exists := ps.paymentMethods[paymentMethodID]
	if !exists || paymentMethod.UserID != userID {
		return nil, fmt.Errorf("invalid payment method")
	}

	// Calculate fees
	amount := addon.Pricing.Price
	platformFee := amount * ps.config.PlatformFeePercent
	processorFee := amount * ps.config.PaymentProcessorFee
	netAmount := amount - platformFee - processorFee

	// Create transaction
	transaction := &Transaction{
		ID:              generateTransactionID(),
		UserID:          userID,
		AddonID:         addonID,
		Type:            TransactionTypePurchase,
		Status:          TransactionStatusPending,
		Amount:          amount,
		Currency:        addon.Pricing.Currency,
		PlatformFee:     platformFee,
		ProcessorFee:    processorFee,
		NetAmount:       netAmount,
		PaymentMethodID: paymentMethodID,
		Description:     fmt.Sprintf("Purchase of %s", addon.Name),
		Metadata:        make(map[string]interface{}),
		CreatedAt:       time.Now(),
	}

	// Process payment (simulate)
	err := ps.processPayment(transaction)
	if err != nil {
		transaction.Status = TransactionStatusFailed
		return transaction, err
	}

	transaction.Status = TransactionStatusCompleted
	now := time.Now()
	transaction.ProcessedAt = &now

	// Store transaction
	ps.transactions[transaction.ID] = transaction

	return transaction, nil
}

// CreateSubscription creates a subscription for an addon
func (ps *PricingSystem) CreateSubscription(ctx context.Context, userID, addonID, planID, paymentMethodID string) (*Subscription, error) {
	ps.mutex.Lock()
	defer ps.mutex.Unlock()

	addon, exists := ps.marketplace.addons[addonID]
	if !exists {
		return nil, fmt.Errorf("addon not found")
	}

	subscription := &Subscription{
		ID:              generateSubscriptionID(),
		UserID:          userID,
		AddonID:         addonID,
		PlanID:          planID,
		Status:          SubscriptionStatusTrialing,
		StartDate:       time.Now(),
		NextBillingDate: time.Now().AddDate(0, 0, ps.config.TrialPeriodDays),
		Amount:          addon.Pricing.Price,
		Currency:        addon.Pricing.Currency,
		BillingCycle:    BillingCycleMonthly,
		PaymentMethodID: paymentMethodID,
		Metadata:        make(map[string]interface{}),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if addon.Pricing.FreeTrial {
		trialEnd := time.Now().AddDate(0, 0, addon.Pricing.TrialDays)
		subscription.TrialEndDate = &trialEnd
	}

	ps.subscriptions[subscription.ID] = subscription
	return subscription, nil
}

// processPayment simulates payment processing
func (ps *PricingSystem) processPayment(transaction *Transaction) error {
	// Simulate payment processing delay
	time.Sleep(time.Millisecond * 100)

	// Simulate random payment failures (5% failure rate)
	if time.Now().UnixNano()%20 == 0 {
		return fmt.Errorf("payment processing failed")
	}

	return nil
}

// Helper functions
func generateTransactionID() string {
	return fmt.Sprintf("txn_%d", time.Now().UnixNano())
}

func generateSubscriptionID() string {
	return fmt.Sprintf("sub_%d", time.Now().UnixNano())
}
