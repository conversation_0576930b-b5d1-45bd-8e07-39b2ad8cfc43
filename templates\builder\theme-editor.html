{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <!-- Color Controls Column -->
    <div class="col-md-4">
      <div class="card mb-4">
        <div class="card-header">Theme Colors</div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label">Primary Color</label>
            <input type="color" class="form-control spectrum" data-var="--bs-primary" value="#0d6efd">
          </div>
          <!-- Additional color controls -->
        </div>
      </div>
      
      <div class="card">
        <div class="card-header">Theme Actions</div>
        <div class="card-body">
          <button class="btn btn-primary me-2" onclick="exportTheme()">Export</button>
          <button class="btn btn-secondary" onclick="importTheme()">Import</button>
        </div>
      </div>
    </div>

    <!-- Preview Column -->
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">Component Preview</div>
        <div class="card-body">
          <!-- Bootstrap component examples -->
          <button class="btn btn-primary me-2">Primary Button</button>
          <div class="alert alert-primary mt-3">Sample alert</div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}