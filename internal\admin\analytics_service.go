package admin

import (
	"context"
	"goVwPlatformAPI/internal/database"
	"time"
)

// AnalyticsService handles platform analytics and reporting
type AnalyticsService struct {
	db *database.DB
}

// NewAnalyticsService creates a new analytics service
func NewAnalyticsService(db *database.DB) *AnalyticsService {
	return &AnalyticsService{db: db}
}

// PlatformAnalytics represents comprehensive platform analytics
type PlatformAnalytics struct {
	TimeRange            TimeRange             `json:"time_range"`
	UserAnalytics        *UserAnalytics        `json:"user_analytics"`
	ContentAnalytics     *ContentAnalytics     `json:"content_analytics"`
	RevenueAnalytics     *RevenueAnalytics     `json:"revenue_analytics"`
	PerformanceAnalytics *PerformanceAnalytics `json:"performance_analytics"`
	ExpertAnalytics      *ExpertAnalytics      `json:"expert_analytics"`
	TrendData            []TrendDataPoint      `json:"trend_data"`
	TopMetrics           *TopMetrics           `json:"top_metrics"`
	GeneratedAt          time.Time             `json:"generated_at"`
}

// TimeRange represents a time period for analytics
type TimeRange struct {
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
	Period    string    `json:"period"` // day, week, month, quarter, year
}

// UserAnalytics represents user-related analytics
type UserAnalytics struct {
	TotalUsers        int64                 `json:"total_users"`
	NewUsers          int64                 `json:"new_users"`
	ActiveUsers       int64                 `json:"active_users"`
	RetentionRate     float64               `json:"retention_rate"`
	ChurnRate         float64               `json:"churn_rate"`
	UsersByRole       map[string]int64      `json:"users_by_role"`
	UsersByTier       map[string]int64      `json:"users_by_tier"`
	GeographicData    []GeographicDataPoint `json:"geographic_data"`
	EngagementMetrics *EngagementMetrics    `json:"engagement_metrics"`
}

// ContentAnalytics represents content-related analytics
type ContentAnalytics struct {
	TotalSites        int64             `json:"total_sites"`
	PublishedSites    int64             `json:"published_sites"`
	TotalPages        int64             `json:"total_pages"`
	TotalAddons       int64             `json:"total_addons"`
	PopularAddons     []PopularItem     `json:"popular_addons"`
	ContentGrowth     []GrowthDataPoint `json:"content_growth"`
	CategoryBreakdown map[string]int64  `json:"category_breakdown"`
}

// RevenueAnalytics represents revenue-related analytics
type RevenueAnalytics struct {
	TotalRevenue    float64            `json:"total_revenue"`
	MonthlyRevenue  float64            `json:"monthly_revenue"`
	RevenueGrowth   float64            `json:"revenue_growth"`
	ARPU            float64            `json:"arpu"` // Average Revenue Per User
	RevenueByTier   map[string]float64 `json:"revenue_by_tier"`
	RevenueBySource map[string]float64 `json:"revenue_by_source"`
	RevenueTrend    []RevenueDataPoint `json:"revenue_trend"`
}

// PerformanceAnalytics represents performance metrics
type PerformanceAnalytics struct {
	AverageResponseTime time.Duration            `json:"average_response_time"`
	ThroughputRPS       float64                  `json:"throughput_rps"`
	ErrorRate           float64                  `json:"error_rate"`
	UptimePercentage    float64                  `json:"uptime_percentage"`
	PageLoadTimes       map[string]time.Duration `json:"page_load_times"`
	APIPerformance      []APIPerformanceMetric   `json:"api_performance"`
}

// ExpertAnalytics represents expert-related analytics
type ExpertAnalytics struct {
	TotalExperts      int64            `json:"total_experts"`
	ActiveExperts     int64            `json:"active_experts"`
	ExpertsByTier     map[string]int64 `json:"experts_by_tier"`
	AverageRating     float64          `json:"average_rating"`
	CompletedProjects int64            `json:"completed_projects"`
	ExpertEarnings    float64          `json:"expert_earnings"`
	TopExperts        []TopExpert      `json:"top_experts"`
}

// Supporting types
type TrendDataPoint struct {
	Date  time.Time `json:"date"`
	Value float64   `json:"value"`
	Label string    `json:"label"`
}

type TopMetrics struct {
	TopPages   []PopularItem `json:"top_pages"`
	TopAddons  []PopularItem `json:"top_addons"`
	TopExperts []TopExpert   `json:"top_experts"`
	TopUsers   []TopUser     `json:"top_users"`
}

type GeographicDataPoint struct {
	Country   string  `json:"country"`
	UserCount int64   `json:"user_count"`
	Revenue   float64 `json:"revenue"`
}

type EngagementMetrics struct {
	AverageSessionDuration time.Duration `json:"average_session_duration"`
	PagesPerSession        float64       `json:"pages_per_session"`
	BounceRate             float64       `json:"bounce_rate"`
	DailyActiveUsers       int64         `json:"daily_active_users"`
	WeeklyActiveUsers      int64         `json:"weekly_active_users"`
	MonthlyActiveUsers     int64         `json:"monthly_active_users"`
}

type PopularItem struct {
	ID        string  `json:"id"`
	Name      string  `json:"name"`
	Views     int64   `json:"views"`
	Downloads int64   `json:"downloads"`
	Rating    float64 `json:"rating"`
	Revenue   float64 `json:"revenue"`
}

type GrowthDataPoint struct {
	Date  time.Time `json:"date"`
	Count int64     `json:"count"`
	Type  string    `json:"type"`
}

type RevenueDataPoint struct {
	Date   time.Time `json:"date"`
	Amount float64   `json:"amount"`
	Source string    `json:"source"`
}

type APIPerformanceMetric struct {
	Endpoint     string        `json:"endpoint"`
	AverageTime  time.Duration `json:"average_time"`
	RequestCount int64         `json:"request_count"`
	ErrorCount   int64         `json:"error_count"`
	ErrorRate    float64       `json:"error_rate"`
}

type TopExpert struct {
	ID           string   `json:"id"`
	Name         string   `json:"name"`
	Rating       float64  `json:"rating"`
	ProjectCount int64    `json:"project_count"`
	Earnings     float64  `json:"earnings"`
	Specialties  []string `json:"specialties"`
}

type TopUser struct {
	ID           string    `json:"id"`
	Email        string    `json:"email"`
	SitesCreated int64     `json:"sites_created"`
	Revenue      float64   `json:"revenue"`
	LastActive   time.Time `json:"last_active"`
}

// GetPlatformAnalytics retrieves comprehensive platform analytics
func (s *AnalyticsService) GetPlatformAnalytics(ctx context.Context, timeRange TimeRange) (*PlatformAnalytics, error) {
	analytics := &PlatformAnalytics{
		TimeRange:   timeRange,
		GeneratedAt: time.Now(),
	}

	// Get user analytics
	userAnalytics, err := s.getUserAnalytics(ctx, timeRange)
	if err != nil {
		return nil, err
	}
	analytics.UserAnalytics = userAnalytics

	// Get content analytics
	contentAnalytics := s.getContentAnalytics(ctx, timeRange)
	analytics.ContentAnalytics = contentAnalytics

	// Get revenue analytics
	revenueAnalytics := s.getRevenueAnalytics(ctx, timeRange)
	analytics.RevenueAnalytics = revenueAnalytics

	// Get performance analytics
	performanceAnalytics := s.getPerformanceAnalytics(ctx, timeRange)
	analytics.PerformanceAnalytics = performanceAnalytics

	// Get expert analytics
	expertAnalytics := s.getExpertAnalytics(ctx, timeRange)
	analytics.ExpertAnalytics = expertAnalytics

	// Get trend data
	trendData := s.getTrendData(ctx, timeRange)
	analytics.TrendData = trendData

	// Get top metrics
	topMetrics := s.getTopMetrics(ctx, timeRange)
	analytics.TopMetrics = topMetrics

	return analytics, nil
}

// getUserAnalytics retrieves user-related analytics
func (s *AnalyticsService) getUserAnalytics(ctx context.Context, timeRange TimeRange) (*UserAnalytics, error) {
	analytics := &UserAnalytics{
		UsersByRole:    make(map[string]int64),
		UsersByTier:    make(map[string]int64),
		GeographicData: []GeographicDataPoint{},
	}

	// Get total users
	err := s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM users").Scan(&analytics.TotalUsers)
	if err != nil {
		analytics.TotalUsers = 0
	}

	// Get new users in time range
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM users 
		WHERE created_at >= $1 AND created_at <= $2
	`, timeRange.StartDate, timeRange.EndDate).Scan(&analytics.NewUsers)
	if err != nil {
		analytics.NewUsers = 0
	}

	// Get active users (logged in during time range)
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM users 
		WHERE last_login_at >= $1 AND last_login_at <= $2
	`, timeRange.StartDate, timeRange.EndDate).Scan(&analytics.ActiveUsers)
	if err != nil {
		analytics.ActiveUsers = 0
	}

	// Calculate retention and churn rates (simplified)
	if analytics.TotalUsers > 0 {
		analytics.RetentionRate = float64(analytics.ActiveUsers) / float64(analytics.TotalUsers) * 100
		analytics.ChurnRate = 100 - analytics.RetentionRate
	}

	// Get users by role
	rows, err := s.db.Pool.Query(ctx, `
		SELECT COALESCE(role, 'client') as role, COUNT(*) 
		FROM users 
		GROUP BY role
	`)
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var role string
			var count int64
			if err := rows.Scan(&role, &count); err == nil {
				analytics.UsersByRole[role] = count
			}
		}
	}

	// Set engagement metrics (mock data)
	analytics.EngagementMetrics = &EngagementMetrics{
		AverageSessionDuration: 25 * time.Minute,
		PagesPerSession:        4.2,
		BounceRate:             35.5,
		DailyActiveUsers:       analytics.ActiveUsers / 30,
		WeeklyActiveUsers:      analytics.ActiveUsers / 4,
		MonthlyActiveUsers:     analytics.ActiveUsers,
	}

	return analytics, nil
}

// getContentAnalytics retrieves content-related analytics
func (s *AnalyticsService) getContentAnalytics(ctx context.Context, timeRange TimeRange) *ContentAnalytics {
	analytics := &ContentAnalytics{
		CategoryBreakdown: make(map[string]int64),
		PopularAddons:     []PopularItem{},
		ContentGrowth:     []GrowthDataPoint{},
	}

	// Get total sites
	s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM sites").Scan(&analytics.TotalSites)

	// Get published sites
	s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM sites WHERE is_published = true").Scan(&analytics.PublishedSites)

	// Get total pages
	s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM pages").Scan(&analytics.TotalPages)

	// Get total addons
	s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM addon_configs").Scan(&analytics.TotalAddons)

	// Mock popular addons
	analytics.PopularAddons = []PopularItem{
		{ID: "addon1", Name: "Contact Form", Views: 1250, Downloads: 890, Rating: 4.8, Revenue: 2500.00},
		{ID: "addon2", Name: "Analytics Dashboard", Views: 980, Downloads: 650, Rating: 4.6, Revenue: 1800.00},
	}

	return analytics
}

// getRevenueAnalytics retrieves revenue-related analytics
func (s *AnalyticsService) getRevenueAnalytics(ctx context.Context, timeRange TimeRange) *RevenueAnalytics {
	analytics := &RevenueAnalytics{
		RevenueByTier:   make(map[string]float64),
		RevenueBySource: make(map[string]float64),
		RevenueTrend:    []RevenueDataPoint{},
	}

	// Get total revenue
	s.db.Pool.QueryRow(ctx, `
		SELECT COALESCE(SUM(amount), 0) FROM invoices WHERE status = 'PAID'
	`).Scan(&analytics.TotalRevenue)

	// Get monthly revenue
	s.db.Pool.QueryRow(ctx, `
		SELECT COALESCE(SUM(amount), 0) FROM invoices 
		WHERE status = 'PAID' AND created_at >= DATE_TRUNC('month', NOW())
	`).Scan(&analytics.MonthlyRevenue)

	// Calculate ARPU and growth (simplified)
	var userCount int64
	s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM users").Scan(&userCount)
	if userCount > 0 {
		analytics.ARPU = analytics.TotalRevenue / float64(userCount)
	}
	analytics.RevenueGrowth = 15.5 // Mock growth percentage

	// Mock revenue breakdown
	analytics.RevenueByTier = map[string]float64{
		"Free":       0,
		"Pro":        analytics.TotalRevenue * 0.6,
		"Enterprise": analytics.TotalRevenue * 0.4,
	}

	return analytics
}

// getPerformanceAnalytics retrieves performance analytics (mock data)
func (s *AnalyticsService) getPerformanceAnalytics(ctx context.Context, timeRange TimeRange) *PerformanceAnalytics {
	return &PerformanceAnalytics{
		AverageResponseTime: 150 * time.Millisecond,
		ThroughputRPS:       125.5,
		ErrorRate:           0.02,
		UptimePercentage:    99.9,
		PageLoadTimes: map[string]time.Duration{
			"homepage":  800 * time.Millisecond,
			"dashboard": 1200 * time.Millisecond,
			"builder":   1500 * time.Millisecond,
		},
		APIPerformance: []APIPerformanceMetric{
			{Endpoint: "/api/graphql", AverageTime: 200 * time.Millisecond, RequestCount: 10000, ErrorCount: 20, ErrorRate: 0.2},
			{Endpoint: "/api/auth", AverageTime: 100 * time.Millisecond, RequestCount: 5000, ErrorCount: 5, ErrorRate: 0.1},
		},
	}
}

// getExpertAnalytics retrieves expert analytics (mock data)
func (s *AnalyticsService) getExpertAnalytics(ctx context.Context, timeRange TimeRange) *ExpertAnalytics {
	return &ExpertAnalytics{
		TotalExperts:  150,
		ActiveExperts: 120,
		ExpertsByTier: map[string]int64{
			"Standard": 80,
			"Premium":  50,
			"Elite":    20,
		},
		AverageRating:     4.6,
		CompletedProjects: 450,
		ExpertEarnings:    125000.00,
		TopExperts: []TopExpert{
			{ID: "expert1", Name: "John Doe", Rating: 4.9, ProjectCount: 25, Earnings: 15000.00, Specialties: []string{"Web Development", "UI/UX"}},
		},
	}
}

// getTrendData retrieves trend data (mock data)
func (s *AnalyticsService) getTrendData(ctx context.Context, timeRange TimeRange) []TrendDataPoint {
	return []TrendDataPoint{
		{Date: time.Now().AddDate(0, 0, -7), Value: 100, Label: "Users"},
		{Date: time.Now().AddDate(0, 0, -6), Value: 105, Label: "Users"},
		{Date: time.Now().AddDate(0, 0, -5), Value: 110, Label: "Users"},
		{Date: time.Now().AddDate(0, 0, -4), Value: 108, Label: "Users"},
		{Date: time.Now().AddDate(0, 0, -3), Value: 115, Label: "Users"},
		{Date: time.Now().AddDate(0, 0, -2), Value: 120, Label: "Users"},
		{Date: time.Now().AddDate(0, 0, -1), Value: 125, Label: "Users"},
	}
}

// getTopMetrics retrieves top metrics (mock data)
func (s *AnalyticsService) getTopMetrics(ctx context.Context, timeRange TimeRange) *TopMetrics {
	return &TopMetrics{
		TopPages: []PopularItem{
			{ID: "page1", Name: "Homepage", Views: 5000, Downloads: 0, Rating: 0, Revenue: 0},
		},
		TopAddons: []PopularItem{
			{ID: "addon1", Name: "Contact Form", Views: 1250, Downloads: 890, Rating: 4.8, Revenue: 2500.00},
		},
		TopExperts: []TopExpert{
			{ID: "expert1", Name: "John Doe", Rating: 4.9, ProjectCount: 25, Earnings: 15000.00, Specialties: []string{"Web Development"}},
		},
		TopUsers: []TopUser{
			{ID: "user1", Email: "<EMAIL>", SitesCreated: 5, Revenue: 500.00, LastActive: time.Now().Add(-1 * time.Hour)},
		},
	}
}
