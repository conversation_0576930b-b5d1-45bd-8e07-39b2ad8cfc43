{{define "content"}}
<div class="container-fluid h-100">
    <div class="row h-100">
        <!-- Chat <PERSON>bar -->
        <div class="col-md-4 col-lg-3 border-end bg-light h-100">
            <div class="d-flex flex-column h-100">
                <!-- Header -->
                <div class="p-3 border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Messages</h5>
                        <button class="btn btn-primary btn-sm" onclick="startNewConversation()">
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                    
                    <!-- Search -->
                    <div class="mt-3">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" id="searchConversations" placeholder="Search conversations...">
                        </div>
                    </div>
                </div>
                
                <!-- Filter Tabs -->
                <div class="px-3 pt-2">
                    <ul class="nav nav-pills nav-fill" id="conversationTabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-filter="all" href="#" onclick="filterConversations('all')">All</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-filter="unread" href="#" onclick="filterConversations('unread')">Unread</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-filter="projects" href="#" onclick="filterConversations('projects')">Projects</a>
                        </li>
                    </ul>
                </div>
                
                <!-- Conversations List -->
                <div class="flex-grow-1 overflow-auto" id="conversationsList">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading conversations...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Chat Area -->
        <div class="col-md-8 col-lg-9 d-flex flex-column h-100">
            <!-- Chat Header -->
            <div class="border-bottom p-3 bg-white" id="chatHeader" style="display: none;">
                <div class="d-flex align-items-center">
                    <img src="https://via.placeholder.com/40" class="rounded-circle me-3" id="chatUserAvatar" alt="User">
                    <div class="flex-grow-1">
                        <h6 class="mb-0" id="chatUserName">Select a conversation</h6>
                        <small class="text-muted" id="chatUserStatus">Online</small>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="viewProfile()"><i class="bi bi-person me-2"></i>View Profile</a></li>
                            <li><a class="dropdown-item" href="#" onclick="viewProject()"><i class="bi bi-briefcase me-2"></i>View Project</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="blockUser()"><i class="bi bi-slash-circle me-2"></i>Block User</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Messages Area -->
            <div class="flex-grow-1 overflow-auto p-3" id="messagesArea">
                <div class="text-center py-5" id="noConversationSelected">
                    <i class="bi bi-chat-dots display-1 text-muted"></i>
                    <h4 class="mt-3 text-muted">Select a conversation</h4>
                    <p class="text-muted">Choose a conversation from the sidebar to start messaging</p>
                </div>
                
                <div id="messagesList" style="display: none;">
                    <!-- Messages will be loaded here -->
                </div>
            </div>
            
            <!-- Message Input -->
            <div class="border-top p-3 bg-white" id="messageInput" style="display: none;">
                <form id="sendMessageForm" class="d-flex align-items-end">
                    <div class="flex-grow-1 me-3">
                        <textarea class="form-control" id="messageText" rows="1" placeholder="Type your message..." 
                                  style="resize: none; max-height: 120px;"></textarea>
                    </div>
                    <div class="d-flex flex-column">
                        <button type="button" class="btn btn-outline-secondary btn-sm mb-2" onclick="attachFile()">
                            <i class="bi bi-paperclip"></i>
                        </button>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="bi bi-send"></i>
                        </button>
                    </div>
                </form>
                
                <!-- File Upload Area (Hidden) -->
                <input type="file" id="fileInput" multiple style="display: none;" onchange="handleFileSelect(event)">
                
                <!-- Typing Indicator -->
                <div id="typingIndicator" class="mt-2" style="display: none;">
                    <small class="text-muted">
                        <span class="typing-dots">
                            <span></span><span></span><span></span>
                        </span>
                        <span id="typingUserName">Someone</span> is typing...
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Conversation Modal -->
<div class="modal fade" id="newConversationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Start New Conversation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="recipientSearch" class="form-label">Search for user</label>
                    <input type="text" class="form-control" id="recipientSearch" placeholder="Type name or email...">
                </div>
                <div id="userSearchResults">
                    <!-- Search results will appear here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentConversationId = null;
let conversations = [];
let messages = [];
let currentUser = null;
let typingTimer = null;

document.addEventListener('DOMContentLoaded', function() {
    loadCurrentUser();
    loadConversations();
    setupMessageInput();
    setupRealTimeUpdates();
});

async function loadCurrentUser() {
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    query GetCurrentUser {
                        me {
                            id
                            firstName
                            lastName
                            email
                            profilePicture
                        }
                    }
                `
            })
        });
        
        const data = await response.json();
        if (data.data?.me) {
            currentUser = data.data.me;
        }
    } catch (error) {
        console.error('Failed to load current user:', error);
    }
}

async function loadConversations() {
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    query GetConversations {
                        myConversations {
                            id
                            participants {
                                id
                                firstName
                                lastName
                                profilePicture
                            }
                            lastMessage {
                                id
                                content
                                sentAt
                                sender {
                                    id
                                    firstName
                                }
                            }
                            unreadCount
                            updatedAt
                        }
                    }
                `
            })
        });
        
        const data = await response.json();
        if (data.data?.myConversations) {
            conversations = data.data.myConversations;
            renderConversations();
        } else {
            showEmptyConversations();
        }
    } catch (error) {
        console.error('Failed to load conversations:', error);
        showEmptyConversations();
    }
}

function renderConversations() {
    const container = document.getElementById('conversationsList');
    
    if (conversations.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-chat-square-dots display-4 text-muted"></i>
                <p class="mt-3 text-muted">No conversations yet</p>
                <button class="btn btn-primary btn-sm" onclick="startNewConversation()">
                    Start your first conversation
                </button>
            </div>
        `;
        return;
    }
    
    container.innerHTML = conversations.map(conversation => {
        const otherParticipant = conversation.participants.find(p => p.id !== currentUser?.id);
        const lastMessage = conversation.lastMessage;
        const isUnread = conversation.unreadCount > 0;
        
        return `
            <div class="conversation-item ${isUnread ? 'unread' : ''}" onclick="selectConversation('${conversation.id}')" data-conversation-id="${conversation.id}">
                <div class="d-flex align-items-center p-3 border-bottom">
                    <div class="position-relative">
                        <img src="${otherParticipant?.profilePicture || 'https://via.placeholder.com/40'}" 
                             class="rounded-circle me-3" width="40" height="40" alt="${otherParticipant?.firstName}">
                        ${isUnread ? '<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-primary">●</span>' : ''}
                    </div>
                    <div class="flex-grow-1 min-width-0">
                        <div class="d-flex justify-content-between align-items-start">
                            <h6 class="mb-1 text-truncate">${otherParticipant?.firstName} ${otherParticipant?.lastName}</h6>
                            <small class="text-muted">${formatTime(lastMessage?.sentAt)}</small>
                        </div>
                        <p class="mb-0 text-muted small text-truncate">
                            ${lastMessage ? (lastMessage.sender.id === currentUser?.id ? 'You: ' : '') + lastMessage.content : 'No messages yet'}
                        </p>
                        ${isUnread ? `<span class="badge bg-primary rounded-pill mt-1">${conversation.unreadCount}</span>` : ''}
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

async function selectConversation(conversationId) {
    currentConversationId = conversationId;
    
    // Update UI
    document.querySelectorAll('.conversation-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-conversation-id="${conversationId}"]`).classList.add('active');
    
    // Show chat area
    document.getElementById('noConversationSelected').style.display = 'none';
    document.getElementById('chatHeader').style.display = 'block';
    document.getElementById('messagesList').style.display = 'block';
    document.getElementById('messageInput').style.display = 'block';
    
    // Load conversation details
    await loadConversationDetails(conversationId);
    await loadMessages(conversationId);
}

async function loadMessages(conversationId) {
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    query GetMessages($conversationId: ID!) {
                        conversationMessages(conversationId: $conversationId) {
                            id
                            content
                            sentAt
                            messageType
                            attachments
                            sender {
                                id
                                firstName
                                lastName
                                profilePicture
                            }
                            isRead
                        }
                    }
                `,
                variables: { conversationId }
            })
        });
        
        const data = await response.json();
        if (data.data?.conversationMessages) {
            messages = data.data.conversationMessages;
            renderMessages();
        }
    } catch (error) {
        console.error('Failed to load messages:', error);
    }
}

function renderMessages() {
    const container = document.getElementById('messagesList');
    
    container.innerHTML = messages.map(message => {
        const isOwnMessage = message.sender.id === currentUser?.id;
        const messageTime = formatTime(message.sentAt);
        
        return `
            <div class="message-group mb-3 ${isOwnMessage ? 'own-message' : 'other-message'}">
                <div class="d-flex ${isOwnMessage ? 'justify-content-end' : 'justify-content-start'}">
                    ${!isOwnMessage ? `<img src="${message.sender.profilePicture || 'https://via.placeholder.com/32'}" class="rounded-circle me-2" width="32" height="32" alt="${message.sender.firstName}">` : ''}
                    <div class="message-bubble ${isOwnMessage ? 'bg-primary text-white' : 'bg-light'}" style="max-width: 70%;">
                        <div class="p-3">
                            ${!isOwnMessage ? `<small class="fw-bold">${message.sender.firstName}</small><br>` : ''}
                            <div class="message-content">${message.content}</div>
                            ${message.attachments && message.attachments.length > 0 ? renderAttachments(message.attachments) : ''}
                        </div>
                        <div class="px-3 pb-2">
                            <small class="opacity-75">${messageTime}</small>
                        </div>
                    </div>
                    ${isOwnMessage ? `<img src="${currentUser?.profilePicture || 'https://via.placeholder.com/32'}" class="rounded-circle ms-2" width="32" height="32" alt="You">` : ''}
                </div>
            </div>
        `;
    }).join('');
    
    // Scroll to bottom
    container.scrollTop = container.scrollHeight;
}

function setupMessageInput() {
    const form = document.getElementById('sendMessageForm');
    const textarea = document.getElementById('messageText');
    
    // Auto-resize textarea
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        
        // Show typing indicator
        showTypingIndicator();
    });
    
    // Send on Enter (but not Shift+Enter)
    textarea.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            form.dispatchEvent(new Event('submit'));
        }
    });
    
    // Handle form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        await sendMessage();
    });
}

async function sendMessage() {
    const messageText = document.getElementById('messageText');
    const content = messageText.value.trim();
    
    if (!content || !currentConversationId) return;
    
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    mutation SendMessage($conversationId: ID!, $content: String!) {
                        sendMessage(conversationId: $conversationId, content: $content) {
                            id
                            content
                            sentAt
                            sender {
                                id
                                firstName
                                lastName
                            }
                        }
                    }
                `,
                variables: {
                    conversationId: currentConversationId,
                    content: content
                }
            })
        });
        
        const data = await response.json();
        if (data.data?.sendMessage) {
            // Clear input
            messageText.value = '';
            messageText.style.height = 'auto';
            
            // Add message to list
            messages.push(data.data.sendMessage);
            renderMessages();
            
            // Update conversation list
            loadConversations();
        }
    } catch (error) {
        console.error('Failed to send message:', error);
        showAlert('Failed to send message', 'error');
    }
}

function showTypingIndicator() {
    // Clear existing timer
    if (typingTimer) {
        clearTimeout(typingTimer);
    }
    
    // Set new timer
    typingTimer = setTimeout(() => {
        // Hide typing indicator after 3 seconds of inactivity
    }, 3000);
}

function formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
        return date.toLocaleDateString([], { weekday: 'short' });
    } else {
        return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
}

function startNewConversation() {
    const modal = new bootstrap.Modal(document.getElementById('newConversationModal'));
    modal.show();
}

function filterConversations(filter) {
    // Update active tab
    document.querySelectorAll('#conversationTabs .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
    
    // TODO: Implement filtering logic
    console.log('Filtering conversations by:', filter);
}

function getAuthToken() {
    return localStorage.getItem('authToken') || sessionStorage.getItem('authToken') || '';
}

function showAlert(message, type) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    document.body.insertAdjacentHTML('afterbegin', alertHTML);
    
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 3000);
}

// Real-time updates (WebSocket simulation)
function setupRealTimeUpdates() {
    // Simulate real-time updates every 30 seconds
    setInterval(() => {
        if (currentConversationId) {
            loadMessages(currentConversationId);
        }
        loadConversations();
    }, 30000);
}
</script>

<style>
.conversation-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.conversation-item:hover {
    background-color: rgba(0,0,0,0.05);
}

.conversation-item.active {
    background-color: rgba(13, 110, 253, 0.1);
    border-left: 3px solid #0d6efd;
}

.conversation-item.unread {
    background-color: rgba(13, 110, 253, 0.05);
}

.message-bubble {
    border-radius: 18px;
    word-wrap: break-word;
}

.own-message .message-bubble {
    border-bottom-right-radius: 4px;
}

.other-message .message-bubble {
    border-bottom-left-radius: 4px;
}

.typing-dots span {
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #999;
    margin: 0 1px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

#messagesArea {
    background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 100%);
}

.min-width-0 {
    min-width: 0;
}
</style>
{{end}}

{{define "title"}}Messages - VelocityWave{{end}}