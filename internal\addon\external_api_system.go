package addon

import (
	"sync"
	"time"
)

// ExternalAPISystem handles external API integrations for addons
type ExternalAPISystem struct {
	apiConfigs   map[string]*APIConfiguration
	credentials  map[string]*APICredentials
	connections  map[string]*APIConnection
	rateLimiters map[string]*RateLimiter
	mutex        sync.RWMutex
	config       ExternalAPIConfig
}

// ExternalAPIConfig contains system configuration
type ExternalAPIConfig struct {
	MaxConnections     int           `json:"maxConnections"`
	DefaultTimeout     time.Duration `json:"defaultTimeout"`
	MaxRetries         int           `json:"maxRetries"`
	RateLimitEnabled   bool          `json:"rateLimitEnabled"`
	SecurityValidation bool          `json:"securityValidation"`
	LogRequests        bool          `json:"logRequests"`
}

// APIConfiguration represents an external API configuration
type APIConfiguration struct {
	ID            string                 `json:"id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	BaseURL       string                 `json:"baseUrl"`
	Version       string                 `json:"version"`
	AuthType      AuthenticationType     `json:"authType"`
	Endpoints     map[string]*Endpoint   `json:"endpoints"`
	Headers       map[string]string      `json:"headers"`
	RateLimit     *RateLimit             `json:"rateLimit"`
	Security      *SecurityConfig        `json:"security"`
	Documentation string                 `json:"documentation"`
	Status        APIStatus              `json:"status"`
	CreatedAt     time.Time              `json:"createdAt"`
	UpdatedAt     time.Time              `json:"updatedAt"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// AuthenticationType represents authentication method
type AuthenticationType string

const (
	AuthTypeNone   AuthenticationType = "none"
	AuthTypeAPIKey AuthenticationType = "api_key"
	AuthTypeBearer AuthenticationType = "bearer"
	AuthTypeBasic  AuthenticationType = "basic"
	AuthTypeOAuth2 AuthenticationType = "oauth2"
	AuthTypeCustom AuthenticationType = "custom"
)

// APIStatus represents API status
type APIStatus string

const (
	APIStatusActive      APIStatus = "active"
	APIStatusInactive    APIStatus = "inactive"
	APIStatusDeprecated  APIStatus = "deprecated"
	APIStatusMaintenance APIStatus = "maintenance"
)

// Endpoint represents an API endpoint
type Endpoint struct {
	Path        string            `json:"path"`
	Method      string            `json:"method"`
	Description string            `json:"description"`
	Parameters  []Parameter       `json:"parameters"`
	Headers     map[string]string `json:"headers"`
	RateLimit   *RateLimit        `json:"rateLimit"`
	Timeout     time.Duration     `json:"timeout"`
	Retries     int               `json:"retries"`
}

// Parameter represents an endpoint parameter
type Parameter struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"`
	Required    bool        `json:"required"`
	Default     interface{} `json:"default"`
	Description string      `json:"description"`
	Validation  string      `json:"validation"`
}

// RateLimit represents rate limiting configuration
type RateLimit struct {
	RequestsPerSecond int           `json:"requestsPerSecond"`
	RequestsPerMinute int           `json:"requestsPerMinute"`
	RequestsPerHour   int           `json:"requestsPerHour"`
	BurstSize         int           `json:"burstSize"`
	WindowSize        time.Duration `json:"windowSize"`
}

// SecurityConfig represents security configuration
type SecurityConfig struct {
	RequireHTTPS   bool     `json:"requireHttps"`
	AllowedDomains []string `json:"allowedDomains"`
	BlockedDomains []string `json:"blockedDomains"`
	ValidateSSL    bool     `json:"validateSsl"`
	MaxRequestSize int64    `json:"maxRequestSize"`
	AllowedMethods []string `json:"allowedMethods"`
}

// APICredentials represents stored API credentials
type APICredentials struct {
	ID          string                 `json:"id"`
	UserID      string                 `json:"userId"`
	APIConfigID string                 `json:"apiConfigId"`
	Name        string                 `json:"name"`
	AuthType    AuthenticationType     `json:"authType"`
	Credentials map[string]interface{} `json:"credentials"`
	ExpiresAt   *time.Time             `json:"expiresAt,omitempty"`
	IsActive    bool                   `json:"isActive"`
	LastUsed    *time.Time             `json:"lastUsed,omitempty"`
	CreatedAt   time.Time              `json:"createdAt"`
	UpdatedAt   time.Time              `json:"updatedAt"`
}

// APIConnection represents an active API connection
type APIConnection struct {
	ID            string                 `json:"id"`
	UserID        string                 `json:"userId"`
	APIConfigID   string                 `json:"apiConfigId"`
	CredentialsID string                 `json:"credentialsId"`
	Status        ConnectionStatus       `json:"status"`
	LastPing      time.Time              `json:"lastPing"`
	ResponseTime  time.Duration          `json:"responseTime"`
	ErrorCount    int                    `json:"errorCount"`
	SuccessCount  int                    `json:"successCount"`
	TotalRequests int64                  `json:"totalRequests"`
	CreatedAt     time.Time              `json:"createdAt"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// ConnectionStatus represents connection status
type ConnectionStatus string

const (
	ConnectionStatusConnected    ConnectionStatus = "connected"
	ConnectionStatusDisconnected ConnectionStatus = "disconnected"
	ConnectionStatusError        ConnectionStatus = "error"
	ConnectionStatusTesting      ConnectionStatus = "testing"
)

// RateLimiter handles rate limiting for API calls
type RateLimiter struct {
	RequestsPerSecond int
	RequestsPerMinute int
	RequestsPerHour   int
	SecondWindow      []time.Time
	MinuteWindow      []time.Time
	HourWindow        []time.Time
	mutex             sync.Mutex
}
