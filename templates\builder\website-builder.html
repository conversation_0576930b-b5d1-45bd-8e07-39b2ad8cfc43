{{define "content"}}
<div class="container-fluid h-100">
    <div class="row h-100">
        <!-- Component Palette Sidebar -->
        <div class="col-md-3 component-palette p-0">
            <div class="p-3 border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-palette me-2"></i>Components
                </h5>
            </div>
            
            <!-- Bootstrap Components -->
            <div class="accordion" id="componentAccordion">
                <!-- Layout Components -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#layoutComponents">
                            <i class="bi bi-grid-3x3-gap me-2"></i>Layout
                        </button>
                    </h2>
                    <div id="layoutComponents" class="accordion-collapse collapse show">
                        <div class="accordion-body">
                            <div class="component-item" draggable="true" data-component="container">
                                <i class="bi bi-square me-2"></i>Container
                            </div>
                            <div class="component-item" draggable="true" data-component="row">
                                <i class="bi bi-distribute-horizontal me-2"></i>Row
                            </div>
                            <div class="component-item" draggable="true" data-component="column">
                                <i class="bi bi-distribute-vertical me-2"></i>Column
                            </div>
                            <div class="component-item" draggable="true" data-component="card">
                                <i class="bi bi-card-text me-2"></i>Card
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Content Components -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#contentComponents">
                            <i class="bi bi-file-text me-2"></i>Content
                        </button>
                    </h2>
                    <div id="contentComponents" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            <div class="component-item" draggable="true" data-component="heading">
                                <i class="bi bi-type-h1 me-2"></i>Heading
                            </div>
                            <div class="component-item" draggable="true" data-component="paragraph">
                                <i class="bi bi-paragraph me-2"></i>Paragraph
                            </div>
                            <div class="component-item" draggable="true" data-component="image">
                                <i class="bi bi-image me-2"></i>Image
                            </div>
                            <div class="component-item" draggable="true" data-component="button">
                                <i class="bi bi-square-fill me-2"></i>Button
                            </div>
                            <div class="component-item" draggable="true" data-component="list">
                                <i class="bi bi-list-ul me-2"></i>List
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Form Components -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#formComponents">
                            <i class="bi bi-ui-checks me-2"></i>Forms
                        </button>
                    </h2>
                    <div id="formComponents" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            <div class="component-item" draggable="true" data-component="form">
                                <i class="bi bi-ui-checks me-2"></i>Form
                            </div>
                            <div class="component-item" draggable="true" data-component="input">
                                <i class="bi bi-input-cursor me-2"></i>Input
                            </div>
                            <div class="component-item" draggable="true" data-component="textarea">
                                <i class="bi bi-textarea me-2"></i>Textarea
                            </div>
                            <div class="component-item" draggable="true" data-component="select">
                                <i class="bi bi-menu-button me-2"></i>Select
                            </div>
                            <div class="component-item" draggable="true" data-component="checkbox">
                                <i class="bi bi-check-square me-2"></i>Checkbox
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Web Addons -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#webAddons">
                            <i class="bi bi-puzzle me-2"></i>Web Addons
                        </button>
                    </h2>
                    <div id="webAddons" class="accordion-collapse collapse">
                        <div class="accordion-body" id="webAddonsList">
                            <div class="text-center py-3">
                                <div class="loading-spinner"></div>
                                <small class="text-muted d-block mt-2">Loading addons...</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Code Snippets -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#codeSnippets">
                            <i class="bi bi-code-square me-2"></i>Code Snippets
                        </button>
                    </h2>
                    <div id="codeSnippets" class="accordion-collapse collapse">
                        <div class="accordion-body" id="codeSnippetsList">
                            <div class="text-center py-3">
                                <div class="loading-spinner"></div>
                                <small class="text-muted d-block mt-2">Loading snippets...</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Canvas Area -->
        <div class="col-md-6 p-0">
            <!-- Toolbar -->
            <div class="bg-light border-bottom p-2">
                <div class="btn-toolbar" role="toolbar">
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="undoBtn">
                            <i class="bi bi-arrow-counterclockwise"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="redoBtn">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                    
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="saveBtn">
                            <i class="bi bi-floppy me-1"></i>Save
                        </button>
                        <button type="button" class="btn btn-sm btn-velocity" id="previewBtn">
                            <i class="bi bi-eye me-1"></i>Preview
                        </button>
                    </div>
                    
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="mobileView">
                            <i class="bi bi-phone"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="tabletView">
                            <i class="bi bi-tablet"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary active" id="desktopView">
                            <i class="bi bi-display"></i>
                        </button>
                    </div>
                    
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-success" id="publishBtn">
                            <i class="bi bi-cloud-upload me-1"></i>Publish
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Canvas -->
            <div class="website-builder-canvas" id="builderCanvas">
                <div class="drop-zone text-center py-5" id="mainDropZone">
                    <i class="bi bi-plus-circle text-muted" style="font-size: 3rem;"></i>
                    <h4 class="text-muted mt-3">Start Building Your Website</h4>
                    <p class="text-muted">Drag components from the sidebar to begin</p>
                </div>
            </div>
        </div>
        
        <!-- Properties Panel -->
        <div class="col-md-3 theme-editor p-0">
            <div class="p-3 border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-sliders me-2"></i>Properties
                </h5>
            </div>
            
            <div class="p-3" id="propertiesPanel">
                <div class="text-center py-5 text-muted">
                    <i class="bi bi-mouse2 mb-3" style="font-size: 2rem;"></i>
                    <p>Select a component to edit its properties</p>
                </div>
            </div>
            
            <!-- Theme Settings -->
            <div class="border-top">
                <div class="p-3">
                    <h6 class="mb-3">
                        <i class="bi bi-palette me-2"></i>Theme Settings
                    </h6>
                    
                    <div class="color-picker-group">
                        <label class="color-picker-label">Primary Color</label>
                        <input type="color" class="color-picker-input" id="primaryColor" value="#0d6efd">
                    </div>
                    
                    <div class="color-picker-group">
                        <label class="color-picker-label">Secondary Color</label>
                        <input type="color" class="color-picker-input" id="secondaryColor" value="#6c757d">
                    </div>
                    
                    <div class="color-picker-group">
                        <label class="color-picker-label">Success Color</label>
                        <input type="color" class="color-picker-input" id="successColor" value="#198754">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Font Family</label>
                        <select class="form-select" id="fontFamily">
                            <option value="system-ui">System Default</option>
                            <option value="Arial, sans-serif">Arial</option>
                            <option value="Helvetica, sans-serif">Helvetica</option>
                            <option value="Georgia, serif">Georgia</option>
                            <option value="'Times New Roman', serif">Times New Roman</option>
                        </select>
                    </div>
                    
                    <button type="button" class="btn btn-sm btn-velocity w-100" id="applyThemeBtn">
                        Apply Theme
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Component Templates -->
<script id="containerTemplate" type="text/x-handlebars-template">
<div class="container-fluid component-wrapper" data-component="container">
    <div class="component-controls">
        <button class="btn btn-sm btn-outline-primary edit-btn"><i class="bi bi-pencil"></i></button>
        <button class="btn btn-sm btn-outline-danger delete-btn"><i class="bi bi-trash"></i></button>
    </div>
    <div class="container {{containerClass}}">
        <div class="drop-zone-inner">Drop components here</div>
    </div>
</div>
</script>

<script id="headingTemplate" type="text/x-handlebars-template">
<div class="component-wrapper" data-component="heading">
    <div class="component-controls">
        <button class="btn btn-sm btn-outline-primary edit-btn"><i class="bi bi-pencil"></i></button>
        <button class="btn btn-sm btn-outline-danger delete-btn"><i class="bi bi-trash"></i></button>
    </div>
    <{{headingLevel}} class="{{headingClass}}">{{headingText}}</{{headingLevel}}>
</div>
</script>

<script id="buttonTemplate" type="text/x-handlebars-template">
<div class="component-wrapper" data-component="button">
    <div class="component-controls">
        <button class="btn btn-sm btn-outline-primary edit-btn"><i class="bi bi-pencil"></i></button>
        <button class="btn btn-sm btn-outline-danger delete-btn"><i class="bi bi-trash"></i></button>
    </div>
    <button type="button" class="btn {{buttonClass}}">{{buttonText}}</button>
</div>
</script>

<script id="cardTemplate" type="text/x-handlebars-template">
<div class="component-wrapper" data-component="card">
    <div class="component-controls">
        <button class="btn btn-sm btn-outline-primary edit-btn"><i class="bi bi-pencil"></i></button>
        <button class="btn btn-sm btn-outline-danger delete-btn"><i class="bi bi-trash"></i></button>
    </div>
    <div class="card {{cardClass}}">
        {{#if cardHeader}}
        <div class="card-header">{{cardHeader}}</div>
        {{/if}}
        <div class="card-body">
            <h5 class="card-title">{{cardTitle}}</h5>
            <p class="card-text">{{cardText}}</p>
            <div class="drop-zone-inner">Drop components here</div>
        </div>
    </div>
</div>
</script>
{{end}}

{{define "additionalJS"}}
<script src="/static/js/website-builder.js"></script>
{{end}}

{{define "title"}}Website Builder{{end}}