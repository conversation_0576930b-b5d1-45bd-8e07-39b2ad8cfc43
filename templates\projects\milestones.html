{{define "content"}}
<div class="container py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2>Project Milestones</h2>
            <p class="text-muted mb-0" id="projectTitle">Loading project details...</p>
        </div>
        <div>
            <button class="btn btn-outline-primary me-2" onclick="exportMilestones()">
                <i class="bi bi-download me-1"></i>Export
            </button>
            <button class="btn btn-primary" onclick="addMilestone()">
                <i class="bi bi-plus me-1"></i>Add Milestone
            </button>
        </div>
    </div>
    
    <!-- Project Progress Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary" id="totalMilestones">0</h4>
                    <small class="text-muted">Total Milestones</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success" id="completedMilestones">0</h4>
                    <small class="text-muted">Completed</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-warning" id="inProgressMilestones">0</h4>
                    <small class="text-muted">In Progress</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-danger" id="overdueMilestones">0</h4>
                    <small class="text-muted">Overdue</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Overall Progress Bar -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Overall Progress</h6>
                <span id="overallProgress">0%</span>
            </div>
            <div class="progress" style="height: 10px;">
                <div class="progress-bar" role="progressbar" id="progressBar" style="width: 0%"></div>
            </div>
        </div>
    </div>
    
    <!-- Milestone Timeline -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Milestone Timeline</h6>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="viewMode" id="timelineView" checked>
                    <label class="btn btn-outline-secondary" for="timelineView">Timeline</label>
                    <input type="radio" class="btn-check" name="viewMode" id="listView">
                    <label class="btn btn-outline-secondary" for="listView">List</label>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="milestonesContainer">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading milestones...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Milestone Modal -->
<div class="modal fade" id="milestoneModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="milestoneModalTitle">Add Milestone</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="milestoneForm">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="milestoneTitle" class="form-label">Milestone Title *</label>
                            <input type="text" class="form-control" id="milestoneTitle" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="milestoneOrder" class="form-label">Order</label>
                            <input type="number" class="form-control" id="milestoneOrder" min="1">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="milestoneDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="milestoneDescription" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="milestoneDueDate" class="form-label">Due Date *</label>
                            <input type="date" class="form-control" id="milestoneDueDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="milestoneValue" class="form-label">Value (£)</label>
                            <input type="number" class="form-control" id="milestoneValue" min="0" step="0.01">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="milestoneStatus" class="form-label">Status</label>
                        <select class="form-select" id="milestoneStatus">
                            <option value="pending">Pending</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                            <option value="overdue">Overdue</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveMilestone()">Save Milestone</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentEngagementId = new URLSearchParams(window.location.search).get('engagementId');
let milestones = [];
let currentMilestone = null;

document.addEventListener('DOMContentLoaded', function() {
    if (currentEngagementId) {
        loadProjectDetails();
        loadMilestones();
    } else {
        showError('No project engagement specified');
    }
    
    // Setup view mode toggle
    document.querySelectorAll('input[name="viewMode"]').forEach(radio => {
        radio.addEventListener('change', function() {
            renderMilestones();
        });
    });
});

async function loadProjectDetails() {
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    query GetProjectEngagement($id: ID!) {
                        projectEngagement(id: $id) {
                            id
                            projectTitle
                            projectDescription
                            progressPercentage
                            engagementStatus
                            startDate
                            endDate
                        }
                    }
                `,
                variables: { id: currentEngagementId }
            })
        });
        
        const data = await response.json();
        if (data.data?.projectEngagement) {
            const project = data.data.projectEngagement;
            document.getElementById('projectTitle').textContent = project.projectTitle;
        }
    } catch (error) {
        console.error('Failed to load project details:', error);
    }
}

async function loadMilestones() {
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    query GetProjectMilestones($engagementId: ID!) {
                        projectMilestones(engagementId: $engagementId) {
                            id
                            milestoneTitle
                            milestoneDescription
                            milestoneOrder
                            dueDate
                            completedDate
                            milestoneValue
                            paymentStatus
                            milestoneStatus
                            completionNotes
                            createdAt
                        }
                    }
                `,
                variables: { engagementId: currentEngagementId }
            })
        });
        
        const data = await response.json();
        if (data.data?.projectMilestones) {
            milestones = data.data.projectMilestones.sort((a, b) => a.milestoneOrder - b.milestoneOrder);
            updateStatistics();
            renderMilestones();
        }
    } catch (error) {
        console.error('Failed to load milestones:', error);
        showError('Failed to load milestones');
    }
}

function updateStatistics() {
    const total = milestones.length;
    const completed = milestones.filter(m => m.milestoneStatus === 'completed').length;
    const inProgress = milestones.filter(m => m.milestoneStatus === 'in_progress').length;
    const overdue = milestones.filter(m => {
        return m.milestoneStatus !== 'completed' && new Date(m.dueDate) < new Date();
    }).length;
    
    document.getElementById('totalMilestones').textContent = total;
    document.getElementById('completedMilestones').textContent = completed;
    document.getElementById('inProgressMilestones').textContent = inProgress;
    document.getElementById('overdueMilestones').textContent = overdue;
    
    const progressPercentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    document.getElementById('overallProgress').textContent = `${progressPercentage}%`;
    document.getElementById('progressBar').style.width = `${progressPercentage}%`;
}

function renderMilestones() {
    const container = document.getElementById('milestonesContainer');
    const isTimelineView = document.getElementById('timelineView').checked;
    
    if (milestones.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-flag display-4 text-muted"></i>
                <h5 class="mt-3">No milestones yet</h5>
                <p class="text-muted">Add your first milestone to track project progress</p>
                <button class="btn btn-primary" onclick="addMilestone()">
                    <i class="bi bi-plus me-1"></i>Add Milestone
                </button>
            </div>
        `;
        return;
    }
    
    if (isTimelineView) {
        renderTimelineView(container);
    } else {
        renderListView(container);
    }
}

function renderTimelineView(container) {
    container.innerHTML = `
        <div class="timeline">
            ${milestones.map((milestone, index) => `
                <div class="timeline-item">
                    <div class="timeline-marker bg-${getStatusColor(milestone.milestoneStatus)}"></div>
                    <div class="timeline-content">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${milestone.milestoneTitle}</h6>
                                        <p class="text-muted small mb-2">${milestone.milestoneDescription || 'No description'}</p>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-${getStatusColor(milestone.milestoneStatus)} me-2">${milestone.milestoneStatus.replace('_', ' ')}</span>
                                            <small class="text-muted">Due: ${formatDate(milestone.dueDate)}</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        ${milestone.milestoneValue ? `<div class="fw-bold">£${milestone.milestoneValue}</div>` : ''}
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="editMilestone('${milestone.id}')">Edit</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="completeMilestone('${milestone.id}')">Mark Complete</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteMilestone('${milestone.id}')">Delete</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function renderListView(container) {
    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Milestone</th>
                        <th>Due Date</th>
                        <th>Status</th>
                        <th>Value</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${milestones.map(milestone => `
                        <tr>
                            <td>
                                <div>
                                    <h6 class="mb-1">${milestone.milestoneTitle}</h6>
                                    <small class="text-muted">${milestone.milestoneDescription || 'No description'}</small>
                                </div>
                            </td>
                            <td>
                                <span class="${isOverdue(milestone) ? 'text-danger' : ''}">${formatDate(milestone.dueDate)}</span>
                            </td>
                            <td>
                                <span class="badge bg-${getStatusColor(milestone.milestoneStatus)}">${milestone.milestoneStatus.replace('_', ' ')}</span>
                            </td>
                            <td>${milestone.milestoneValue ? `£${milestone.milestoneValue}` : '-'}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editMilestone('${milestone.id}')">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="completeMilestone('${milestone.id}')">
                                        <i class="bi bi-check"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteMilestone('${milestone.id}')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function getStatusColor(status) {
    switch (status) {
        case 'completed': return 'success';
        case 'in_progress': return 'warning';
        case 'overdue': return 'danger';
        default: return 'secondary';
    }
}

function isOverdue(milestone) {
    return milestone.milestoneStatus !== 'completed' && new Date(milestone.dueDate) < new Date();
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

function addMilestone() {
    currentMilestone = null;
    document.getElementById('milestoneModalTitle').textContent = 'Add Milestone';
    document.getElementById('milestoneForm').reset();
    document.getElementById('milestoneOrder').value = milestones.length + 1;
    
    const modal = new bootstrap.Modal(document.getElementById('milestoneModal'));
    modal.show();
}

function editMilestone(milestoneId) {
    currentMilestone = milestones.find(m => m.id === milestoneId);
    if (!currentMilestone) return;
    
    document.getElementById('milestoneModalTitle').textContent = 'Edit Milestone';
    document.getElementById('milestoneTitle').value = currentMilestone.milestoneTitle;
    document.getElementById('milestoneDescription').value = currentMilestone.milestoneDescription || '';
    document.getElementById('milestoneOrder').value = currentMilestone.milestoneOrder;
    document.getElementById('milestoneDueDate').value = currentMilestone.dueDate.split('T')[0];
    document.getElementById('milestoneValue').value = currentMilestone.milestoneValue || '';
    document.getElementById('milestoneStatus').value = currentMilestone.milestoneStatus;
    
    const modal = new bootstrap.Modal(document.getElementById('milestoneModal'));
    modal.show();
}

async function saveMilestone() {
    const formData = {
        title: document.getElementById('milestoneTitle').value,
        description: document.getElementById('milestoneDescription').value,
        dueDate: document.getElementById('milestoneDueDate').value,
        value: parseFloat(document.getElementById('milestoneValue').value) || null
    };
    
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    mutation AddProjectMilestone($engagementId: ID!, $title: String!, $description: String, $dueDate: Time!, $value: Float) {
                        addProjectMilestone(engagementId: $engagementId, title: $title, description: $description, dueDate: $dueDate, value: $value) {
                            id
                            milestoneTitle
                            milestoneDescription
                            dueDate
                            milestoneValue
                            milestoneStatus
                        }
                    }
                `,
                variables: {
                    engagementId: currentEngagementId,
                    ...formData
                }
            })
        });
        
        const data = await response.json();
        if (data.data?.addProjectMilestone) {
            bootstrap.Modal.getInstance(document.getElementById('milestoneModal')).hide();
            showAlert('Milestone saved successfully!', 'success');
            loadMilestones();
        } else {
            throw new Error(data.errors?.[0]?.message || 'Failed to save milestone');
        }
    } catch (error) {
        console.error('Failed to save milestone:', error);
        showAlert('Failed to save milestone: ' + error.message, 'error');
    }
}

async function completeMilestone(milestoneId) {
    const notes = prompt('Add completion notes (optional):');
    
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    mutation CompleteProjectMilestone($id: ID!, $notes: String) {
                        completeProjectMilestone(id: $id, notes: $notes) {
                            id
                            milestoneStatus
                            completedDate
                            completionNotes
                        }
                    }
                `,
                variables: { id: milestoneId, notes }
            })
        });
        
        const data = await response.json();
        if (data.data?.completeProjectMilestone) {
            showAlert('Milestone marked as complete!', 'success');
            loadMilestones();
        }
    } catch (error) {
        console.error('Failed to complete milestone:', error);
        showAlert('Failed to complete milestone', 'error');
    }
}

function exportMilestones() {
    // TODO: Implement milestone export functionality
    showAlert('Export functionality coming soon!', 'info');
}

function getAuthToken() {
    return localStorage.getItem('authToken') || sessionStorage.getItem('authToken') || '';
}

function showAlert(message, type) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    document.body.insertAdjacentHTML('afterbegin', alertHTML);
    
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 3000);
}

function showError(message) {
    document.getElementById('milestonesContainer').innerHTML = `
        <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 10px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    margin-left: 20px;
}
</style>
{{end}}

{{define "title"}}Project Milestones{{end}}