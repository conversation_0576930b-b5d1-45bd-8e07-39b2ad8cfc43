-- Enhanced External API Configuration System
CREATE TABLE IF NOT EXISTS external_api_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    base_url TEXT NOT NULL,
    version VARCHAR(50),
    auth_type VARCHAR(50) NOT NULL CHECK (auth_type IN ('NONE', 'API_KEY', 'BEARER', 'BASIC', 'OAUTH2', 'CUSTOM')),
    endpoints JSONB NOT NULL DEFAULT '{}',
    headers JSONB DEFAULT '{}',
    rate_limit JSONB,
    security_config JSONB,
    documentation TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'DEPRECATED', 'MAINTENANCE')),
    cache_ttl INTEGER DEFAULT 300,
    metadata JSONB DEFAULT '{}',
    created_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(name, created_by, tenant_id)
);

-- Enhanced API Credentials with encryption support
CREATE TABLE IF NOT EXISTS external_api_credentials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    api_config_id UUID NOT NULL REFERENCES external_api_configurations(id) ON DELETE CASCADE,
    auth_type VARCHAR(50) NOT NULL CHECK (auth_type IN ('NONE', 'API_KEY', 'BEARER', 'BASIC', 'OAUTH2', 'CUSTOM')),
    encrypted_credentials JSONB NOT NULL, -- Contains encrypted credential data
    is_active BOOLEAN NOT NULL DEFAULT true,
    expires_at TIMESTAMPTZ,
    last_used TIMESTAMPTZ,
    created_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(name, api_config_id, created_by)
);

-- API Connection tracking and monitoring
CREATE TABLE IF NOT EXISTS external_api_connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_config_id UUID NOT NULL REFERENCES external_api_configurations(id) ON DELETE CASCADE,
    credentials_id UUID NOT NULL REFERENCES external_api_credentials(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'TESTING' CHECK (status IN ('CONNECTED', 'DISCONNECTED', 'ERROR', 'TESTING')),
    last_ping TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    response_time_ms INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    total_requests BIGINT DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- API Call audit log
CREATE TABLE IF NOT EXISTS external_api_call_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_config_id UUID NOT NULL REFERENCES external_api_configurations(id) ON DELETE CASCADE,
    credentials_id UUID NOT NULL REFERENCES external_api_credentials(id) ON DELETE CASCADE,
    connection_id UUID REFERENCES external_api_connections(id) ON DELETE SET NULL,
    method VARCHAR(10) NOT NULL,
    endpoint TEXT NOT NULL,
    status_code INTEGER,
    response_time_ms INTEGER,
    request_size_bytes INTEGER DEFAULT 0,
    response_size_bytes INTEGER DEFAULT 0,
    error_message TEXT,
    user_agent TEXT,
    ip_address INET,
    created_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Rate limiting tracking
CREATE TABLE IF NOT EXISTS external_api_rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_config_id UUID NOT NULL REFERENCES external_api_configurations(id) ON DELETE CASCADE,
    credentials_id UUID NOT NULL REFERENCES external_api_credentials(id) ON DELETE CASCADE,
    window_type VARCHAR(10) NOT NULL CHECK (window_type IN ('SECOND', 'MINUTE', 'HOUR', 'DAY')),
    window_start TIMESTAMPTZ NOT NULL,
    request_count INTEGER NOT NULL DEFAULT 0,
    limit_exceeded BOOLEAN DEFAULT false,
    created_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(api_config_id, credentials_id, window_type, window_start)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_external_api_configurations_created_by ON external_api_configurations(created_by);
CREATE INDEX IF NOT EXISTS idx_external_api_configurations_tenant_id ON external_api_configurations(tenant_id);
CREATE INDEX IF NOT EXISTS idx_external_api_configurations_status ON external_api_configurations(status);
CREATE INDEX IF NOT EXISTS idx_external_api_configurations_auth_type ON external_api_configurations(auth_type);

CREATE INDEX IF NOT EXISTS idx_external_api_credentials_api_config_id ON external_api_credentials(api_config_id);
CREATE INDEX IF NOT EXISTS idx_external_api_credentials_created_by ON external_api_credentials(created_by);
CREATE INDEX IF NOT EXISTS idx_external_api_credentials_tenant_id ON external_api_credentials(tenant_id);
CREATE INDEX IF NOT EXISTS idx_external_api_credentials_is_active ON external_api_credentials(is_active);
CREATE INDEX IF NOT EXISTS idx_external_api_credentials_expires_at ON external_api_credentials(expires_at);

CREATE INDEX IF NOT EXISTS idx_external_api_connections_api_config_id ON external_api_connections(api_config_id);
CREATE INDEX IF NOT EXISTS idx_external_api_connections_credentials_id ON external_api_connections(credentials_id);
CREATE INDEX IF NOT EXISTS idx_external_api_connections_status ON external_api_connections(status);
CREATE INDEX IF NOT EXISTS idx_external_api_connections_created_by ON external_api_connections(created_by);

CREATE INDEX IF NOT EXISTS idx_external_api_call_logs_api_config_id ON external_api_call_logs(api_config_id);
CREATE INDEX IF NOT EXISTS idx_external_api_call_logs_credentials_id ON external_api_call_logs(credentials_id);
CREATE INDEX IF NOT EXISTS idx_external_api_call_logs_created_at ON external_api_call_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_external_api_call_logs_created_by ON external_api_call_logs(created_by);
CREATE INDEX IF NOT EXISTS idx_external_api_call_logs_status_code ON external_api_call_logs(status_code);

CREATE INDEX IF NOT EXISTS idx_external_api_rate_limits_api_config_id ON external_api_rate_limits(api_config_id);
CREATE INDEX IF NOT EXISTS idx_external_api_rate_limits_credentials_id ON external_api_rate_limits(credentials_id);
CREATE INDEX IF NOT EXISTS idx_external_api_rate_limits_window_start ON external_api_rate_limits(window_start);

-- Update triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_external_api_configurations_updated_at 
    BEFORE UPDATE ON external_api_configurations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_external_api_credentials_updated_at 
    BEFORE UPDATE ON external_api_credentials 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_external_api_connections_updated_at 
    BEFORE UPDATE ON external_api_connections 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_external_api_rate_limits_updated_at 
    BEFORE UPDATE ON external_api_rate_limits 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions to application role (adjust role name as needed)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON external_api_configurations TO app_role;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON external_api_credentials TO app_role;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON external_api_connections TO app_role;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON external_api_call_logs TO app_role;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON external_api_rate_limits TO app_role;