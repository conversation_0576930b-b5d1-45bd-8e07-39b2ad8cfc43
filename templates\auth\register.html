<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Velocity Platform</title>
    <meta name="description" content="Join Velocity Platform - Start building dynamic websites today">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <link href="/static/css/platform.css" rel="stylesheet">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-velocity">
        <div class="container">
            <a class="navbar-brand text-white fw-bold" href="/">
                <i class="bi bi-lightning-charge-fill me-2"></i>Velocity Platform
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="/auth/login">
                    <i class="bi bi-box-arrow-in-right me-1"></i>Login
                </a>
            </div>
        </div>
    </nav>

    <!-- Registration Form -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card card-velocity border-0 shadow">
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <i class="bi bi-lightning-charge-fill display-4 text-velocity-primary mb-3"></i>
                            <h4 class="fw-bold">Join Velocity Platform</h4>
                            <p class="text-muted">Start building amazing websites in minutes</p>
                        </div>

                        <!-- Registration Form -->
                        <form id="register-form" hx-post="/api/auth/register" hx-target="#register-result" hx-swap="innerHTML">
                            <div id="register-result"></div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="firstName" class="form-label">First Name</label>
                                        <input type="text" class="form-control" id="firstName" name="firstName" 
                                               placeholder="John" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lastName" class="form-label">Last Name</label>
                                        <input type="text" class="form-control" id="lastName" name="lastName" 
                                               placeholder="Doe" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="<EMAIL>" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="Create a strong password" required minlength="8">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                        <i class="bi bi-eye" id="password-toggle"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <small class="text-muted">Password must be at least 8 characters long</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">Confirm Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock-fill"></i>
                                    </span>
                                    <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" 
                                           placeholder="Confirm your password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirmPassword')">
                                        <i class="bi bi-eye" id="confirm-password-toggle"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="company" class="form-label">Company (Optional)</label>
                                <input type="text" class="form-control" id="company" name="company" 
                                       placeholder="Your company name">
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="/terms" target="_blank">Terms of Service</a> 
                                    and <a href="/privacy" target="_blank">Privacy Policy</a>
                                </label>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    Send me updates about new features and platform news
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-velocity">
                                    <i class="bi bi-person-plus me-2"></i>Create Account
                                </button>
                            </div>
                        </form>

                        <!-- Social Registration -->
                        <div class="text-center my-4">
                            <small class="text-muted">Or sign up with</small>
                        </div>

                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-dark">
                                <i class="bi bi-github me-2"></i>Continue with GitHub
                            </button>
                            <button class="btn btn-outline-primary">
                                <i class="bi bi-google me-2"></i>Continue with Google
                            </button>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent text-center">
                        <small class="text-muted">
                            Already have an account? 
                            <a href="/auth/login" class="text-decoration-none fw-medium">Sign in here</a>
                        </small>
                    </div>
                </div>

                <!-- Platform Benefits -->
                <div class="row g-3 mt-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="bi bi-lightning-charge text-primary mb-2" style="font-size: 2rem;"></i>
                            <h6>Lightning Fast</h6>
                            <small class="text-muted">Build websites in minutes, not hours</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="bi bi-shield-check text-success mb-2" style="font-size: 2rem;"></i>
                            <h6>Secure & Reliable</h6>
                            <small class="text-muted">Enterprise-grade security and uptime</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="bi bi-puzzle text-warning mb-2" style="font-size: 2rem;"></i>
                            <h6>Extensible</h6>
                            <small class="text-muted">Create custom addons and components</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId === 'password' ? 'password-toggle' : 'confirm-password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        // Password validation
        document.getElementById('confirmPassword').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });

        // Handle form submission
        document.getElementById('register-form').addEventListener('htmx:responseError', function(event) {
            document.getElementById('register-result').innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Registration failed. Please check your information and try again.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>`;
        });

        document.getElementById('register-form').addEventListener('htmx:afterRequest', function(event) {
            if (event.detail.successful) {
                document.getElementById('register-result').innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle me-2"></i>
                        Account created successfully! Redirecting to dashboard...
                    </div>`;
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 2000);
            }
        });
    </script>
</body>
</html>