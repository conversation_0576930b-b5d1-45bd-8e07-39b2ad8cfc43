package crypto

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"os"
	
	"golang.org/x/crypto/bcrypt"
)

type CryptoService struct{}

func NewCryptoService() *CryptoService {
	return &CryptoService{}
}

func (cs *CryptoService) HashAPIKey(key string) string {
	pepper := os.Getenv("API_KEY_PEPPER")
	h := hmac.New(sha256.New, []byte(pepper))
	h.Write([]byte(key))
	return hex.EncodeToString(h.Sum(nil))
}

func (cs *CryptoService) VerifyAPIKey(key, hash string) bool {
	return cs.Hash<PERSON>IKey(key) == hash
}

func HashAPIKey(key string) string {
	pepper := os.Getenv("API_KEY_PEPPER")
	h := hmac.New(sha256.New, []byte(pepper))
	h.Write([]byte(key))
	return hex.EncodeToString(h.Sum(nil))
}

func VerifyAPIKey(key, hash string) bool {
	return HashAPIKey(key) == hash
}

// Service provides cryptographic operations
type Service struct {
	key []byte
}

// NewService creates a new crypto service
func NewService(key []byte) *Service {
	return &Service{key: key}
}

// EncryptCredentials encrypts credential data
func (s *Service) EncryptCredentials(data []byte) ([]byte, error) {
	// Simple XOR encryption for demo purposes
	result := make([]byte, len(data))
	for i, b := range data {
		result[i] = b ^ s.key[i%len(s.key)]
	}
	return result, nil
}

// DecryptCredentials decrypts credential data
func (s *Service) DecryptCredentials(data []byte) ([]byte, error) {
	// XOR decryption (same as encryption for XOR)
	return s.EncryptCredentials(data)
}

// Encrypt encrypts data
func (s *Service) Encrypt(data []byte) ([]byte, error) {
	return s.EncryptCredentials(data)
}

// Decrypt decrypts data
func (s *Service) Decrypt(data []byte) ([]byte, error) {
	return s.DecryptCredentials(data)
}

// HashPassword hashes a password using bcrypt
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// CheckPasswordHash compares a password with its hash
func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}
