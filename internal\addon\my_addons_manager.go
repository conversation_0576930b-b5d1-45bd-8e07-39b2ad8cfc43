package addon

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// MyAddonsManager handles user addon management
type MyAddonsManager struct {
	installationSystem *InstallationSystem
	marketplace        *Marketplace
	versioningSystem   *VersioningSystem
	userAddons         map[string]*UserAddonCollection
	mutex              sync.RWMutex
}

// UserAddonCollection represents a user's addon collection
type UserAddonCollection struct {
	UserID          string              `json:"userId"`
	InstalledAddons []*UserAddon        `json:"installedAddons"`
	WishlistAddons  []string            `json:"wishlistAddons"`
	RecentlyViewed  []string            `json:"recentlyViewed"`
	Categories      map[string][]string `json:"categories"`
	Settings        UserAddonSettings   `json:"settings"`
	LastUpdated     time.Time           `json:"lastUpdated"`
}

// UserAddon represents an addon from user's perspective
type UserAddon struct {
	Installation    *Installation          `json:"installation"`
	AddonInfo       *MarketplaceAddon      `json:"addonInfo"`
	UserSettings    map[string]interface{} `json:"userSettings"`
	LastUsed        *time.Time             `json:"lastUsed,omitempty"`
	UsageStats      *AddonUsageStats       `json:"usageStats"`
	UpdateAvailable bool                   `json:"updateAvailable"`
	LatestVersion   string                 `json:"latestVersion"`
	UserRating      int                    `json:"userRating"`
	UserNotes       string                 `json:"userNotes"`
	Favorite        bool                   `json:"favorite"`
}

// UserAddonSettings contains user preferences for addon management
type UserAddonSettings struct {
	AutoUpdate      bool `json:"autoUpdate"`
	Notifications   bool `json:"notifications"`
	UsageTracking   bool `json:"usageTracking"`
	ShowBeta        bool `json:"showBeta"`
	GroupByCategory bool `json:"groupByCategory"`
}

// AddonUsageStats contains detailed usage statistics
type AddonUsageStats struct {
	TotalLaunches  int64            `json:"totalLaunches"`
	TotalUsageTime time.Duration    `json:"totalUsageTime"`
	LastLaunch     time.Time        `json:"lastLaunch"`
	AverageSession time.Duration    `json:"averageSession"`
	WeeklyUsage    []int64          `json:"weeklyUsage"`
	MonthlyUsage   []int64          `json:"monthlyUsage"`
	FeatureUsage   map[string]int64 `json:"featureUsage"`
	ErrorCount     int64            `json:"errorCount"`
	CrashCount     int64            `json:"crashCount"`
}

// NewMyAddonsManager creates a new my addons manager
func NewMyAddonsManager() *MyAddonsManager {
	return &MyAddonsManager{
		installationSystem: NewInstallationSystem(InstallationConfig{}),
		marketplace:        NewMarketplace(MarketplaceConfig{}),
		versioningSystem:   NewVersioningSystem(VersioningConfig{}),
		userAddons:         make(map[string]*UserAddonCollection),
	}
}

// GetUserAddons returns all addons for a user
func (mam *MyAddonsManager) GetUserAddons(ctx context.Context, userID string) (*UserAddonCollection, error) {
	mam.mutex.RLock()
	defer mam.mutex.RUnlock()

	collection, exists := mam.userAddons[userID]
	if !exists {
		// Initialize new collection
		collection = &UserAddonCollection{
			UserID:          userID,
			InstalledAddons: []*UserAddon{},
			WishlistAddons:  []string{},
			RecentlyViewed:  []string{},
			Categories:      make(map[string][]string),
			Settings: UserAddonSettings{
				AutoUpdate:      true,
				Notifications:   true,
				UsageTracking:   true,
				ShowBeta:        false,
				GroupByCategory: true,
			},
			LastUpdated: time.Now(),
		}
		mam.userAddons[userID] = collection
	}

	// Refresh addon data
	err := mam.refreshUserAddons(ctx, collection)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh user addons: %w", err)
	}

	return collection, nil
}

// refreshUserAddons updates the user addon collection with latest data
func (mam *MyAddonsManager) refreshUserAddons(ctx context.Context, collection *UserAddonCollection) error {
	// Get user installations
	installations, err := mam.installationSystem.GetUserInstallations(collection.UserID)
	if err != nil {
		return err
	}

	// Update user addons
	var userAddons []*UserAddon
	for _, installation := range installations {
		userAddon := mam.createUserAddon(installation)
		userAddons = append(userAddons, userAddon)
	}

	collection.InstalledAddons = userAddons
	collection.LastUpdated = time.Now()

	return nil
}

// createUserAddon creates a UserAddon from an Installation
func (mam *MyAddonsManager) createUserAddon(installation *Installation) *UserAddon {
	// Get addon info from marketplace
	addonInfo, exists := mam.marketplace.addons[installation.AddonID]
	if !exists {
		// Create minimal addon info if not found
		addonInfo = &MarketplaceAddon{
			ID:   installation.AddonID,
			Name: "Unknown Addon",
		}
	}

	// Check for updates
	updateAvailable := false
	latestVersion := installation.Version
	if versions, err := mam.versioningSystem.ListVersions(installation.AddonID, false); err == nil && len(versions) > 0 {
		latest := versions[len(versions)-1]
		latestVersion = latest.Version
		updateAvailable = latest.Version != installation.Version
	}

	return &UserAddon{
		Installation:    installation,
		AddonInfo:       addonInfo,
		UserSettings:    make(map[string]interface{}),
		UsageStats:      mam.generateUsageStats(installation),
		UpdateAvailable: updateAvailable,
		LatestVersion:   latestVersion,
		UserRating:      0,
		UserNotes:       "",
		Favorite:        false,
	}
}

// AddToWishlist adds an addon to user's wishlist
func (mam *MyAddonsManager) AddToWishlist(ctx context.Context, userID, addonID string) error {
	mam.mutex.Lock()
	defer mam.mutex.Unlock()

	collection, err := mam.GetUserAddons(ctx, userID)
	if err != nil {
		return err
	}

	// Check if already in wishlist
	for _, id := range collection.WishlistAddons {
		if id == addonID {
			return fmt.Errorf("addon already in wishlist")
		}
	}

	collection.WishlistAddons = append(collection.WishlistAddons, addonID)
	collection.LastUpdated = time.Now()

	return nil
}

// RemoveFromWishlist removes an addon from user's wishlist
func (mam *MyAddonsManager) RemoveFromWishlist(ctx context.Context, userID, addonID string) error {
	mam.mutex.Lock()
	defer mam.mutex.Unlock()

	collection, err := mam.GetUserAddons(ctx, userID)
	if err != nil {
		return err
	}

	for i, id := range collection.WishlistAddons {
		if id == addonID {
			collection.WishlistAddons = append(collection.WishlistAddons[:i], collection.WishlistAddons[i+1:]...)
			collection.LastUpdated = time.Now()
			return nil
		}
	}

	return fmt.Errorf("addon not in wishlist")
}

// UpdateUserSettings updates user addon settings
func (mam *MyAddonsManager) UpdateUserSettings(ctx context.Context, userID string, settings UserAddonSettings) error {
	mam.mutex.Lock()
	defer mam.mutex.Unlock()

	collection, err := mam.GetUserAddons(ctx, userID)
	if err != nil {
		return err
	}

	collection.Settings = settings
	collection.LastUpdated = time.Now()

	return nil
}

// generateUsageStats generates usage statistics for an installation
func (mam *MyAddonsManager) generateUsageStats(installation *Installation) *AddonUsageStats {
	return &AddonUsageStats{
		TotalLaunches:  installation.Usage.TotalSessions,
		TotalUsageTime: installation.Usage.TotalDuration,
		LastLaunch:     installation.Usage.LastSession,
		AverageSession: time.Minute * 15, // Default average
		WeeklyUsage:    []int64{10, 15, 8, 12, 20, 5, 3},
		MonthlyUsage:   []int64{100, 120, 95, 110},
		FeatureUsage:   installation.Usage.FeatureUsage,
		ErrorCount:     installation.Usage.ErrorCount,
		CrashCount:     installation.Usage.CrashCount,
	}
}
