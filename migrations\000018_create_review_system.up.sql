-- Create expert reviews table
CREATE TABLE IF NOT EXISTS expert_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    expert_profile_id UUID NOT NULL REFERENCES expert_profiles(id) ON DELETE CASCADE,
    client_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    project_engagement_id UUID REFERENCES project_engagements(id) ON DELETE SET NULL,
    
    -- Review details
    overall_rating DECIMAL(3,2) NOT NULL CHECK (overall_rating >= 1.0 AND overall_rating <= 5.0),
    communication_rating DECIMAL(3,2) CHECK (communication_rating >= 1.0 AND communication_rating <= 5.0),
    expertise_rating DECIMAL(3,2) CHECK (expertise_rating >= 1.0 AND expertise_rating <= 5.0),
    timeliness_rating DECIMAL(3,2) CHECK (timeliness_rating >= 1.0 AND timeliness_rating <= 5.0),
    value_rating DECIMAL(3,2) CHECK (value_rating >= 1.0 AND value_rating <= 5.0),
    
    -- Review content
    review_title VARCHAR(200),
    review_text TEXT NOT NULL,
    pros TEXT,
    cons TEXT,
    
    -- Recommendations
    would_recommend BOOLEAN DEFAULT true,
    would_hire_again BOOLEAN DEFAULT true,
    
    -- Review status
    review_status VARCHAR(50) DEFAULT 'published', -- draft, published, flagged, removed
    is_verified BOOLEAN DEFAULT false,
    verified_at TIMESTAMP WITH TIME ZONE,
    
    -- Response from expert
    expert_response TEXT,
    expert_responded_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ensure one review per client per expert per project
    UNIQUE(expert_profile_id, client_user_id, project_engagement_id)
);

-- Create review helpfulness table (for review voting)
CREATE TABLE IF NOT EXISTS review_helpfulness (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    review_id UUID NOT NULL REFERENCES expert_reviews(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_helpful BOOLEAN NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ensure one vote per user per review
    UNIQUE(review_id, user_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_expert_reviews_expert_profile ON expert_reviews(expert_profile_id);
CREATE INDEX IF NOT EXISTS idx_expert_reviews_client_user ON expert_reviews(client_user_id);
CREATE INDEX IF NOT EXISTS idx_expert_reviews_engagement ON expert_reviews(project_engagement_id);
CREATE INDEX IF NOT EXISTS idx_expert_reviews_rating ON expert_reviews(overall_rating);
CREATE INDEX IF NOT EXISTS idx_expert_reviews_status ON expert_reviews(review_status);
CREATE INDEX IF NOT EXISTS idx_expert_reviews_created ON expert_reviews(created_at);

CREATE INDEX IF NOT EXISTS idx_review_helpfulness_review ON review_helpfulness(review_id);
CREATE INDEX IF NOT EXISTS idx_review_helpfulness_user ON review_helpfulness(user_id);

-- Add trigger for updated_at
CREATE TRIGGER update_expert_reviews_updated_at BEFORE UPDATE ON expert_reviews FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();