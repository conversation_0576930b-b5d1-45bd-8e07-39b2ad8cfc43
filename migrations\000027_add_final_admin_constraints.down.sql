-- Drop partial indexes
DROP INDEX IF EXISTS idx_support_tickets_open;
DROP INDEX IF EXISTS idx_expert_profiles_active;
DROP INDEX IF EXISTS idx_users_active;

-- Drop composite indexes
DROP INDEX IF EXISTS idx_support_tickets_status_priority;
DROP INDEX IF EXISTS idx_expert_applications_status_created_at;
DROP INDEX IF EXISTS idx_users_role_status;

-- Drop performance indexes
DROP INDEX IF EXISTS idx_system_metrics_recorded_at;
DROP INDEX IF EXISTS idx_system_metrics_type_name;

DROP INDEX IF EXISTS idx_admin_notifications_severity;
DROP INDEX IF EXISTS idx_admin_notifications_created_at;
DROP INDEX IF EXISTS idx_admin_notifications_is_read;
DROP INDEX IF EXISTS idx_admin_notifications_target_admin_id;

DROP INDEX IF EXISTS idx_admin_dashboard_cache_expires_at;
DROP INDEX IF EXISTS idx_admin_dashboard_cache_key;

-- Drop update triggers
DROP TRIGGER IF EXISTS update_admin_notifications_updated_at ON admin_notifications;
DROP TRIGGER IF EXISTS update_admin_dashboard_cache_updated_at ON admin_dashboard_cache;
DROP TRIGGER IF EXISTS update_platform_configurations_updated_at ON platform_configurations;
DROP TRIGGER IF EXISTS update_user_subscriptions_updated_at ON user_subscriptions;
DROP TRIGGER IF EXISTS update_expert_profiles_updated_at ON expert_profiles;
DROP TRIGGER IF EXISTS update_expert_applications_updated_at ON expert_applications;
DROP TRIGGER IF EXISTS update_support_tickets_updated_at ON support_tickets;

-- Drop new tables
DROP TABLE IF EXISTS system_metrics;
DROP TABLE IF EXISTS admin_notifications;
DROP TABLE IF EXISTS admin_dashboard_cache;

-- Remove added indexes from existing tables
DROP INDEX IF EXISTS idx_invoices_payment_method;
DROP INDEX IF EXISTS idx_invoices_created_at;
DROP INDEX IF EXISTS idx_invoices_status;
DROP INDEX IF EXISTS idx_invoices_user_id;

DROP INDEX IF EXISTS idx_pages_created_at;
DROP INDEX IF EXISTS idx_pages_status;
DROP INDEX IF EXISTS idx_pages_created_by;

DROP INDEX IF EXISTS idx_addon_configs_created_at;
DROP INDEX IF EXISTS idx_addon_configs_created_by;
DROP INDEX IF EXISTS idx_addon_configs_state;

-- Remove added columns from existing tables
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invoices') THEN
        ALTER TABLE invoices DROP COLUMN IF EXISTS refund_reason;
        ALTER TABLE invoices DROP COLUMN IF EXISTS refunded_at;
        ALTER TABLE invoices DROP COLUMN IF EXISTS currency;
        ALTER TABLE invoices DROP COLUMN IF EXISTS payment_method;
        ALTER TABLE invoices DROP COLUMN IF EXISTS user_id;
    END IF;
END $$;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'pages') THEN
        ALTER TABLE pages DROP COLUMN IF EXISTS updated_at;
        ALTER TABLE pages DROP COLUMN IF EXISTS is_hidden;
        ALTER TABLE pages DROP COLUMN IF EXISTS status;
        ALTER TABLE pages DROP COLUMN IF EXISTS created_by;
    END IF;
END $$;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'addon_configs') THEN
        ALTER TABLE addon_configs DROP COLUMN IF EXISTS updated_at;
        ALTER TABLE addon_configs DROP COLUMN IF EXISTS created_by;
    END IF;
END $$;