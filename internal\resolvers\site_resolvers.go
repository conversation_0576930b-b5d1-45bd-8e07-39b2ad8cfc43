package resolvers

import (
	"context"
	"fmt"
	"goVwPlatformAPI/graph/model"
	"goVwPlatformAPI/internal/auth"
	"goVwPlatformAPI/internal/database"
)

// SiteResolver handles site-related GraphQL operations
type SiteResolver struct {
	db *database.DB
}

// NewSiteResolver creates a new site resolver
func NewSiteResolver(db *database.DB) *SiteResolver {
	return &SiteResolver{db: db}
}

// MySites returns the current user's sites
func (r *SiteResolver) MySites(ctx context.Context) ([]*model.Site, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	query := `
		SELECT id, name, COALESCE(description, ''), COALESCE(domain, ''), 
		       COALESCE(custom_domain, ''), COALESCE(is_published, false),
		       COALESCE(theme, 'default'), created_at, updated_at
		FROM sites 
		WHERE user_id = $1 
		ORDER BY created_at DESC
	`

	rows, err := r.db.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query sites: %w", err)
	}
	defer rows.Close()

	var sites []*model.Site
	for rows.Next() {
		var site model.Site

		err := rows.Scan(
			&site.ID, &site.Name, &site.Description, 
			&site.CustomDomain, &site.IsPublished,
			&site.CreatedAt, &site.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan site: %w", err)
		}

		// Note: PublishedAt field is not available in the Site model

		// Note: Owner field is not available in the Site model
		// This would need to be added to the GraphQL schema

		sites = append(sites, &site)
	}

	return sites, nil
}

// Site returns a specific site by ID
func (r *SiteResolver) Site(ctx context.Context, id string) (*model.Site, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	query := `
		SELECT s.id, s.name, COALESCE(s.description, ''), COALESCE(s.domain, ''), 
		       COALESCE(s.custom_domain, ''), COALESCE(s.is_published, false),
		       COALESCE(s.theme, 'default'), s.user_id, s.created_at, s.updated_at
		FROM sites s
		WHERE s.id = $1 AND s.user_id = $2
	`

	var site model.Site
	var ownerID string

	err = r.db.Pool.QueryRow(ctx, query, id, userID).Scan(
		&site.ID, &site.Name, &site.Description,
		&site.CustomDomain, &site.IsPublished,
		&ownerID, &site.CreatedAt, &site.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get site: %w", err)
	}

	// Note: PublishedAt field is not available in the Site model

	// Note: Owner field is not available in the Site model
	// This would need to be added to the GraphQL schema

	// Get pages for this site
	// Note: Pages field type mismatch - []*model.Page vs []*model.SitePage
	// pages, err := r.getSitePages(ctx, id)
	// if err == nil {
	//     site.Pages = pages
	// }

	// Note: Settings field is not available in the Site model
	// settings, err := r.getSiteSettings(ctx, id)
	// if err == nil {
	//     site.Settings = settings
	// }

	return &site, nil
}

// SitePages returns pages for a specific site
func (r *SiteResolver) SitePages(ctx context.Context, siteID string) ([]*model.Page, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Verify user owns the site
	var count int
	err = r.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM sites WHERE id = $1 AND user_id = $2", siteID, userID).Scan(&count)
	if err != nil || count == 0 {
		return nil, fmt.Errorf("site not found or access denied")
	}

	return r.getSitePages(ctx, siteID)
}

// Page returns a specific page by ID
func (r *SiteResolver) Page(ctx context.Context, id string) (*model.Page, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	query := `
		SELECT p.id, p.title, p.slug, COALESCE(p.content, ''),
		       COALESCE(p.is_hidden, false),
		       p.created_by, p.created_at, p.updated_at
		FROM pages p
		JOIN sites s ON p.site_id = s.id
		WHERE p.id = $1 AND s.user_id = $2
	`

	var page model.Page
	var authorID string

	err = r.db.Pool.QueryRow(ctx, query, id, userID).Scan(
		&page.ID, &page.Title, &page.Slug, &page.Content,
		&page.IsHidden, &authorID,
		&page.CreatedAt, &page.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get page: %w", err)
	}

	// Note: PublishedAt field is not available in the Page model

	// Note: Author field is not available in the Page model
	// author, err := r.getUserByID(ctx, authorID)
	// if err == nil {
	//     page.Author = author
	// }

	return &page, nil
}

// CreateSite creates a new site
func (r *SiteResolver) CreateSite(ctx context.Context, input model.CreateSiteInput) (*model.Site, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Generate site ID
	siteID := generateID()
	// Note: Domain and Theme fields are not available in CreateSiteInput

	query := `
		INSERT INTO sites (id, name, description, user_id, is_published, created_at, updated_at)
		VALUES ($1, $2, $3, $4, false, NOW(), NOW())
	`

	description := ""
	if input.Description != nil {
		description = *input.Description
	}

	_, err = r.db.Pool.Exec(ctx, query, siteID, input.Name, description, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to create site: %w", err)
	}

	// Return the created site
	return r.Site(ctx, siteID)
}

// UpdateSite updates an existing site
func (r *SiteResolver) UpdateSite(ctx context.Context, id string, input model.UpdateSiteInput) (*model.Site, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Verify user owns the site
	var count int
	err = r.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM sites WHERE id = $1 AND user_id = $2", id, userID).Scan(&count)
	if err != nil || count == 0 {
		return nil, fmt.Errorf("site not found or access denied")
	}

	// Build update query
	query := "UPDATE sites SET updated_at = NOW()"
	args := []interface{}{}
	argCount := 0

	if input.Name != nil {
		argCount++
		query += fmt.Sprintf(", name = $%d", argCount)
		args = append(args, *input.Name)
	}

	if input.Description != nil {
		argCount++
		query += fmt.Sprintf(", description = $%d", argCount)
		args = append(args, *input.Description)
	}

	// Note: Domain field is not available in UpdateSiteInput
	// if input.Domain != nil {
	//     argCount++
	//     query += fmt.Sprintf(", domain = $%d", argCount)
	//     args = append(args, *input.Domain)
	// }

	if input.CustomDomain != nil {
		argCount++
		query += fmt.Sprintf(", custom_domain = $%d", argCount)
		args = append(args, *input.CustomDomain)
	}

	// Note: Theme field is not available in UpdateSiteInput
	// if input.Theme != nil {
	//     argCount++
	//     query += fmt.Sprintf(", theme = $%d", argCount)
	//     args = append(args, *input.Theme)
	// }

	// Note: Favicon field is not available in UpdateSiteInput
	// if input.Favicon != nil {
	//     argCount++
	//     query += fmt.Sprintf(", favicon = $%d", argCount)
	//     args = append(args, *input.Favicon)
	// }

	// Note: Logo field is not available in UpdateSiteInput
	// if input.Logo != nil {
	//     argCount++
	//     query += fmt.Sprintf(", logo = $%d", argCount)
	//     args = append(args, *input.Logo)
	// }

	argCount++
	query += fmt.Sprintf(" WHERE id = $%d", argCount)
	args = append(args, id)

	_, err = r.db.Pool.Exec(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to update site: %w", err)
	}

	return r.Site(ctx, id)
}

// DeleteSite deletes a site
func (r *SiteResolver) DeleteSite(ctx context.Context, id string) (bool, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return false, fmt.Errorf("user not authenticated")
	}

	// Delete site (this will cascade to pages due to foreign key constraints)
	result, err := r.db.Pool.Exec(ctx, "DELETE FROM sites WHERE id = $1 AND user_id = $2", id, userID)
	if err != nil {
		return false, fmt.Errorf("failed to delete site: %w", err)
	}

	rowsAffected := result.RowsAffected()
	return rowsAffected > 0, nil
}

// PublishSite publishes a site
func (r *SiteResolver) PublishSite(ctx context.Context, id string) (*model.Site, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	_, err = r.db.Pool.Exec(ctx, `
		UPDATE sites 
		SET is_published = true, updated_at = NOW() 
		WHERE id = $1 AND user_id = $2
	`, id, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to publish site: %w", err)
	}

	return r.Site(ctx, id)
}

// UnpublishSite unpublishes a site
func (r *SiteResolver) UnpublishSite(ctx context.Context, id string) (*model.Site, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	_, err = r.db.Pool.Exec(ctx, `
		UPDATE sites 
		SET is_published = false, updated_at = NOW() 
		WHERE id = $1 AND user_id = $2
	`, id, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to unpublish site: %w", err)
	}

	return r.Site(ctx, id)
}

// Helper methods
func (r *SiteResolver) getSitePages(ctx context.Context, siteID string) ([]*model.Page, error) {
	query := `
		SELECT id, title, slug, COALESCE(content, ''), COALESCE(excerpt, ''),
		       COALESCE(status, 'draft'), COALESCE(is_hidden, false),
		       COALESCE(template, 'default'), created_by, created_at, updated_at
		FROM pages 
		WHERE site_id = $1 
		ORDER BY created_at DESC
	`

	rows, err := r.db.Pool.Query(ctx, query, siteID)
	if err != nil {
		return nil, fmt.Errorf("failed to query pages: %w", err)
	}
	defer rows.Close()

	var pages []*model.Page
	for rows.Next() {
		var page model.Page
		var authorID string
		// Note: publishedAt variable removed as PublishedAt field is not available

		err := rows.Scan(
			&page.ID, &page.Title, &page.Slug, &page.Content,
			&page.IsHidden, &authorID,
			&page.CreatedAt, &page.UpdatedAt,
		)
		if err != nil {
			continue
		}

		// Note: PublishedAt field is not available in the Page model

		// Note: Author field is not available in the Page model
		// author, err := r.getUserByID(ctx, authorID)
		// if err == nil {
		//     page.Author = author
		// }

		pages = append(pages, &page)
	}

	return pages, nil
}

func (r *SiteResolver) getSiteSettings(ctx context.Context, siteID string) (*model.SiteSettings, error) {
	// Mock implementation - in real app would query site_settings table
	return &model.SiteSettings{
		ID:              siteID,
		SiteName:        "My Site",
		SiteDescription: stringPtr("A great website"),
		CustomDomain:    stringPtr("mysite.com"),
		SslEnabled:      false,
		IsPublished:     true,
		ThemeConfig:     map[string]interface{}{"theme": "default"},
		SeoConfig:       map[string]interface{}{"title": "My Site"},
	}, nil
}

func (r *SiteResolver) getUserByID(ctx context.Context, userID string) (*model.User, error) {
	query := `
		SELECT id, email, COALESCE(role, 'client'), COALESCE(status, 'active'),
		       COALESCE(first_name, ''), COALESCE(last_name, ''), 
		       COALESCE(email_verified, false), created_at, updated_at
		FROM users WHERE id = $1
	`

	var user model.User

	err := r.db.Pool.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Email, &user.Role,
		&user.FirstName, &user.LastName, &user.EmailVerified,
		&user.CreatedAt, &user.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return &user, nil
}

