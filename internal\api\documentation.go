package api

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// DocumentationGenerator generates API documentation
type DocumentationGenerator struct {
	config *DocumentationConfig
}

// DocumentationConfig configures documentation generation
type DocumentationConfig struct {
	Title       string       `json:"title"`
	Version     string       `json:"version"`
	Description string       `json:"description"`
	BaseURL     string       `json:"base_url"`
	Contact     *ContactInfo `json:"contact"`
	License     *LicenseInfo `json:"license"`
}

// ContactInfo represents contact information
type ContactInfo struct {
	Name  string `json:"name"`
	Email string `json:"email"`
	URL   string `json:"url"`
}

// LicenseInfo represents license information
type LicenseInfo struct {
	Name string `json:"name"`
	URL  string `json:"url"`
}

// APIDocumentation represents the complete API documentation
type APIDocumentation struct {
	Info         *DocumentationConfig  `json:"info"`
	Servers      []ServerInfo          `json:"servers"`
	Paths        map[string]*PathItem  `json:"paths"`
	Components   *Components           `json:"components"`
	Security     []SecurityRequirement `json:"security"`
	Tags         []Tag                 `json:"tags"`
	ExternalDocs *ExternalDocs         `json:"external_docs"`
	GeneratedAt  time.Time             `json:"generated_at"`
}

// ServerInfo represents server information
type ServerInfo struct {
	URL         string                    `json:"url"`
	Description string                    `json:"description"`
	Variables   map[string]ServerVariable `json:"variables"`
}

// ServerVariable represents a server variable
type ServerVariable struct {
	Enum        []string `json:"enum"`
	Default     string   `json:"default"`
	Description string   `json:"description"`
}

// PathItem represents a path and its operations
type PathItem struct {
	Summary     string      `json:"summary"`
	Description string      `json:"description"`
	Get         *Operation  `json:"get,omitempty"`
	Post        *Operation  `json:"post,omitempty"`
	Put         *Operation  `json:"put,omitempty"`
	Delete      *Operation  `json:"delete,omitempty"`
	Patch       *Operation  `json:"patch,omitempty"`
	Head        *Operation  `json:"head,omitempty"`
	Options     *Operation  `json:"options,omitempty"`
	Parameters  []Parameter `json:"parameters,omitempty"`
}

// Operation represents an API operation
type Operation struct {
	Tags        []string              `json:"tags"`
	Summary     string                `json:"summary"`
	Description string                `json:"description"`
	OperationID string                `json:"operation_id"`
	Parameters  []Parameter           `json:"parameters"`
	RequestBody *RequestBody          `json:"request_body,omitempty"`
	Responses   map[string]*Response  `json:"responses"`
	Security    []SecurityRequirement `json:"security"`
	Deprecated  bool                  `json:"deprecated"`
	Examples    map[string]*Example   `json:"examples"`
}

// Parameter represents an operation parameter
type Parameter struct {
	Name            string              `json:"name"`
	In              string              `json:"in"` // query, header, path, cookie
	Description     string              `json:"description"`
	Required        bool                `json:"required"`
	Deprecated      bool                `json:"deprecated"`
	AllowEmptyValue bool                `json:"allow_empty_value"`
	Schema          *Schema             `json:"schema"`
	Example         interface{}         `json:"example"`
	Examples        map[string]*Example `json:"examples"`
}

// RequestBody represents a request body
type RequestBody struct {
	Description string                `json:"description"`
	Content     map[string]*MediaType `json:"content"`
	Required    bool                  `json:"required"`
}

// Response represents an API response
type Response struct {
	Description string                `json:"description"`
	Headers     map[string]*Header    `json:"headers"`
	Content     map[string]*MediaType `json:"content"`
	Links       map[string]*Link      `json:"links"`
}

// MediaType represents a media type
type MediaType struct {
	Schema   *Schema              `json:"schema"`
	Example  interface{}          `json:"example"`
	Examples map[string]*Example  `json:"examples"`
	Encoding map[string]*Encoding `json:"encoding"`
}

// Schema represents a data schema
type Schema struct {
	Type                 string             `json:"type"`
	Format               string             `json:"format"`
	Title                string             `json:"title"`
	Description          string             `json:"description"`
	Default              interface{}        `json:"default"`
	Example              interface{}        `json:"example"`
	Enum                 []interface{}      `json:"enum"`
	Properties           map[string]*Schema `json:"properties"`
	Items                *Schema            `json:"items"`
	Required             []string           `json:"required"`
	AdditionalProperties interface{}        `json:"additional_properties"`
	MinLength            *int               `json:"min_length"`
	MaxLength            *int               `json:"max_length"`
	Minimum              *float64           `json:"minimum"`
	Maximum              *float64           `json:"maximum"`
	Pattern              string             `json:"pattern"`
	Nullable             bool               `json:"nullable"`
	ReadOnly             bool               `json:"read_only"`
	WriteOnly            bool               `json:"write_only"`
	Deprecated           bool               `json:"deprecated"`
}

// Header represents a response header
type Header struct {
	Description string      `json:"description"`
	Required    bool        `json:"required"`
	Deprecated  bool        `json:"deprecated"`
	Schema      *Schema     `json:"schema"`
	Example     interface{} `json:"example"`
}

// Link represents a response link
type Link struct {
	OperationRef string                 `json:"operation_ref"`
	OperationID  string                 `json:"operation_id"`
	Parameters   map[string]interface{} `json:"parameters"`
	RequestBody  interface{}            `json:"request_body"`
	Description  string                 `json:"description"`
	Server       *ServerInfo            `json:"server"`
}

// Example represents an example
type Example struct {
	Summary       string      `json:"summary"`
	Description   string      `json:"description"`
	Value         interface{} `json:"value"`
	ExternalValue string      `json:"external_value"`
}

// Encoding represents encoding information
type Encoding struct {
	ContentType   string             `json:"content_type"`
	Headers       map[string]*Header `json:"headers"`
	Style         string             `json:"style"`
	Explode       bool               `json:"explode"`
	AllowReserved bool               `json:"allow_reserved"`
}

// Components represents reusable components
type Components struct {
	Schemas         map[string]*Schema         `json:"schemas"`
	Responses       map[string]*Response       `json:"responses"`
	Parameters      map[string]*Parameter      `json:"parameters"`
	Examples        map[string]*Example        `json:"examples"`
	RequestBodies   map[string]*RequestBody    `json:"request_bodies"`
	Headers         map[string]*Header         `json:"headers"`
	SecuritySchemes map[string]*SecurityScheme `json:"security_schemes"`
	Links           map[string]*Link           `json:"links"`
	Callbacks       map[string]*Callback       `json:"callbacks"`
}

// SecurityScheme represents a security scheme
type SecurityScheme struct {
	Type             string      `json:"type"`
	Description      string      `json:"description"`
	Name             string      `json:"name"`
	In               string      `json:"in"`
	Scheme           string      `json:"scheme"`
	BearerFormat     string      `json:"bearer_format"`
	Flows            *OAuthFlows `json:"flows"`
	OpenIDConnectURL string      `json:"open_id_connect_url"`
}

// OAuthFlows represents OAuth flows
type OAuthFlows struct {
	Implicit          *OAuthFlow `json:"implicit"`
	Password          *OAuthFlow `json:"password"`
	ClientCredentials *OAuthFlow `json:"client_credentials"`
	AuthorizationCode *OAuthFlow `json:"authorization_code"`
}

// OAuthFlow represents an OAuth flow
type OAuthFlow struct {
	AuthorizationURL string            `json:"authorization_url"`
	TokenURL         string            `json:"token_url"`
	RefreshURL       string            `json:"refresh_url"`
	Scopes           map[string]string `json:"scopes"`
}

// SecurityRequirement represents a security requirement
type SecurityRequirement map[string][]string

// Callback represents a callback
type Callback map[string]*PathItem

// Tag represents a tag
type Tag struct {
	Name         string        `json:"name"`
	Description  string        `json:"description"`
	ExternalDocs *ExternalDocs `json:"external_docs"`
}

// ExternalDocs represents external documentation
type ExternalDocs struct {
	Description string `json:"description"`
	URL         string `json:"url"`
}

// NewDocumentationGenerator creates a new documentation generator
func NewDocumentationGenerator(config *DocumentationConfig) *DocumentationGenerator {
	return &DocumentationGenerator{
		config: config,
	}
}

// GenerateDocumentation generates complete API documentation
func (dg *DocumentationGenerator) GenerateDocumentation() *APIDocumentation {
	doc := &APIDocumentation{
		Info:         dg.config,
		Servers:      dg.generateServers(),
		Paths:        dg.generatePaths(),
		Components:   dg.generateComponents(),
		Security:     dg.generateSecurity(),
		Tags:         dg.generateTags(),
		ExternalDocs: dg.generateExternalDocs(),
		GeneratedAt:  time.Now(),
	}

	return doc
}

// generateServers generates server information
func (dg *DocumentationGenerator) generateServers() []ServerInfo {
	return []ServerInfo{
		{
			URL:         dg.config.BaseURL,
			Description: "Production server",
		},
		{
			URL:         strings.Replace(dg.config.BaseURL, "api.", "staging-api.", 1),
			Description: "Staging server",
		},
		{
			URL:         "http://localhost:8080",
			Description: "Development server",
		},
	}
}

// generatePaths generates API paths documentation
func (dg *DocumentationGenerator) generatePaths() map[string]*PathItem {
	paths := make(map[string]*PathItem)

	// GraphQL endpoint
	paths["/graphql"] = &PathItem{
		Summary:     "GraphQL API endpoint",
		Description: "Main GraphQL endpoint for all API operations",
		Post: &Operation{
			Tags:        []string{"GraphQL"},
			Summary:     "Execute GraphQL query or mutation",
			Description: "Execute a GraphQL query, mutation, or subscription",
			OperationID: "executeGraphQL",
			RequestBody: &RequestBody{
				Description: "GraphQL query",
				Required:    true,
				Content: map[string]*MediaType{
					"application/json": {
						Schema: &Schema{
							Type: "object",
							Properties: map[string]*Schema{
								"query": {
									Type:        "string",
									Description: "The GraphQL query string",
								},
								"variables": {
									Type:        "object",
									Description: "Variables for the GraphQL query",
								},
								"operationName": {
									Type:        "string",
									Description: "Name of the operation to execute",
								},
							},
							Required: []string{"query"},
						},
					},
				},
			},
			Responses: map[string]*Response{
				"200": {
					Description: "Successful GraphQL response",
					Content: map[string]*MediaType{
						"application/json": {
							Schema: &Schema{
								Type: "object",
								Properties: map[string]*Schema{
									"data": {
										Type:        "object",
										Description: "The result data",
									},
									"errors": {
										Type: "array",
										Items: &Schema{
											Type: "object",
											Properties: map[string]*Schema{
												"message":   {Type: "string"},
												"path":      {Type: "array"},
												"locations": {Type: "array"},
											},
										},
										Description: "Any errors that occurred",
									},
								},
							},
						},
					},
				},
			},
			Security: []SecurityRequirement{
				{"bearerAuth": []string{}},
			},
		},
	}

	// Authentication endpoints
	paths["/auth/login"] = &PathItem{
		Post: &Operation{
			Tags:        []string{"Authentication"},
			Summary:     "User login",
			Description: "Authenticate user and return access token",
			OperationID: "login",
			RequestBody: &RequestBody{
				Required: true,
				Content: map[string]*MediaType{
					"application/json": {
						Schema: &Schema{
							Type: "object",
							Properties: map[string]*Schema{
								"email":    {Type: "string", Format: "email"},
								"password": {Type: "string", Format: "password"},
							},
							Required: []string{"email", "password"},
						},
					},
				},
			},
			Responses: map[string]*Response{
				"200": {
					Description: "Login successful",
					Content: map[string]*MediaType{
						"application/json": {
							Schema: &Schema{
								Type: "object",
								Properties: map[string]*Schema{
									"token":      {Type: "string"},
									"expires_at": {Type: "string", Format: "date-time"},
									"user":       {Type: "object"},
								},
							},
						},
					},
				},
				"401": {
					Description: "Invalid credentials",
				},
			},
		},
	}

	paths["/auth/register"] = &PathItem{
		Post: &Operation{
			Tags:        []string{"Authentication"},
			Summary:     "User registration",
			Description: "Register a new user account",
			OperationID: "register",
			RequestBody: &RequestBody{
				Required: true,
				Content: map[string]*MediaType{
					"application/json": {
						Schema: &Schema{
							Type: "object",
							Properties: map[string]*Schema{
								"email":     {Type: "string", Format: "email"},
								"password":  {Type: "string", Format: "password"},
								"firstName": {Type: "string"},
								"lastName":  {Type: "string"},
							},
							Required: []string{"email", "password"},
						},
					},
				},
			},
			Responses: map[string]*Response{
				"201": {
					Description: "User created successfully",
				},
				"400": {
					Description: "Invalid input",
				},
				"409": {
					Description: "User already exists",
				},
			},
		},
	}

	// Health check endpoint
	paths["/health"] = &PathItem{
		Get: &Operation{
			Tags:        []string{"System"},
			Summary:     "Health check",
			Description: "Check API health status",
			OperationID: "healthCheck",
			Responses: map[string]*Response{
				"200": {
					Description: "API is healthy",
					Content: map[string]*MediaType{
						"application/json": {
							Schema: &Schema{
								Type: "object",
								Properties: map[string]*Schema{
									"status":    {Type: "string"},
									"timestamp": {Type: "string", Format: "date-time"},
									"version":   {Type: "string"},
								},
							},
						},
					},
				},
			},
		},
	}

	return paths
}

// generateComponents generates reusable components
func (dg *DocumentationGenerator) generateComponents() *Components {
	return &Components{
		Schemas: map[string]*Schema{
			"User": {
				Type: "object",
				Properties: map[string]*Schema{
					"id":        {Type: "string"},
					"email":     {Type: "string", Format: "email"},
					"firstName": {Type: "string"},
					"lastName":  {Type: "string"},
					"role":      {Type: "string"},
					"status":    {Type: "string"},
					"createdAt": {Type: "string", Format: "date-time"},
					"updatedAt": {Type: "string", Format: "date-time"},
				},
				Required: []string{"id", "email"},
			},
			"Error": {
				Type: "object",
				Properties: map[string]*Schema{
					"code":    {Type: "integer"},
					"message": {Type: "string"},
					"details": {Type: "object"},
				},
				Required: []string{"code", "message"},
			},
		},
		SecuritySchemes: map[string]*SecurityScheme{
			"bearerAuth": {
				Type:         "http",
				Scheme:       "bearer",
				BearerFormat: "JWT",
				Description:  "JWT Bearer token authentication",
			},
			"apiKeyAuth": {
				Type:        "apiKey",
				In:          "header",
				Name:        "X-API-Key",
				Description: "API key authentication",
			},
		},
		Responses: map[string]*Response{
			"UnauthorizedError": {
				Description: "Authentication information is missing or invalid",
				Headers: map[string]*Header{
					"WWW_Authenticate": {
						Schema: &Schema{Type: "string"},
					},
				},
			},
			"NotFoundError": {
				Description: "The specified resource was not found",
				Content: map[string]*MediaType{
					"application/json": {
						Schema: &Schema{
							Type: "object",
							Properties: map[string]*Schema{
								"error": {Type: "string"},
							},
						},
					},
				},
			},
		},
	}
}

// generateSecurity generates security requirements
func (dg *DocumentationGenerator) generateSecurity() []SecurityRequirement {
	return []SecurityRequirement{
		{"bearerAuth": []string{}},
		{"apiKeyAuth": []string{}},
	}
}

// generateTags generates API tags
func (dg *DocumentationGenerator) generateTags() []Tag {
	return []Tag{
		{
			Name:        "Authentication",
			Description: "User authentication and authorization",
		},
		{
			Name:        "GraphQL",
			Description: "GraphQL API operations",
		},
		{
			Name:        "Users",
			Description: "User management operations",
		},
		{
			Name:        "Sites",
			Description: "Website management operations",
		},
		{
			Name:        "Addons",
			Description: "Addon marketplace operations",
		},
		{
			Name:        "Experts",
			Description: "Expert network operations",
		},
		{
			Name:        "Business Tools",
			Description: "Business planning and analysis tools",
		},
		{
			Name:        "System",
			Description: "System and health check operations",
		},
	}
}

// generateExternalDocs generates external documentation links
func (dg *DocumentationGenerator) generateExternalDocs() *ExternalDocs {
	return &ExternalDocs{
		Description: "VelocityWave Platform Documentation",
		URL:         "https://docs.velocitywave.com",
	}
}

// GenerateOpenAPISpec generates OpenAPI 3.0 specification
func (dg *DocumentationGenerator) GenerateOpenAPISpec() map[string]interface{} {
	doc := dg.GenerateDocumentation()

	spec := map[string]interface{}{
		"openapi": "3.0.3",
		"info": map[string]interface{}{
			"title":       doc.Info.Title,
			"version":     doc.Info.Version,
			"description": doc.Info.Description,
			"contact":     doc.Info.Contact,
			"license":     doc.Info.License,
		},
		"servers":      doc.Servers,
		"paths":        doc.Paths,
		"components":   doc.Components,
		"security":     doc.Security,
		"tags":         doc.Tags,
		"externalDocs": doc.ExternalDocs,
	}

	return spec
}

// GenerateMarkdownDocs generates Markdown documentation
func (dg *DocumentationGenerator) GenerateMarkdownDocs() string {
	doc := dg.GenerateDocumentation()

	var md strings.Builder

	// Header
	md.WriteString(fmt.Sprintf("# %s\n\n", doc.Info.Title))
	md.WriteString(fmt.Sprintf("Version: %s\n\n", doc.Info.Version))
	md.WriteString(fmt.Sprintf("%s\n\n", doc.Info.Description))

	// Table of Contents
	md.WriteString("## Table of Contents\n\n")
	for _, tag := range doc.Tags {
		md.WriteString(fmt.Sprintf("- [%s](#%s)\n", tag.Name, strings.ToLower(strings.ReplaceAll(tag.Name, " ", "-"))))
	}
	md.WriteString("\n")

	// Authentication
	md.WriteString("## Authentication\n\n")
	md.WriteString("The API supports the following authentication methods:\n\n")
	for name, scheme := range doc.Components.SecuritySchemes {
		md.WriteString(fmt.Sprintf("### %s\n\n", name))
		md.WriteString(fmt.Sprintf("- **Type**: %s\n", scheme.Type))
		if scheme.Description != "" {
			md.WriteString(fmt.Sprintf("- **Description**: %s\n", scheme.Description))
		}
		md.WriteString("\n")
	}

	// Endpoints by tag
	for _, tag := range doc.Tags {
		md.WriteString(fmt.Sprintf("## %s\n\n", tag.Name))
		if tag.Description != "" {
			md.WriteString(fmt.Sprintf("%s\n\n", tag.Description))
		}

		// Find paths for this tag
		for path, pathItem := range doc.Paths {
			operations := map[string]*Operation{
				"GET":    pathItem.Get,
				"POST":   pathItem.Post,
				"PUT":    pathItem.Put,
				"DELETE": pathItem.Delete,
				"PATCH":  pathItem.Patch,
			}

			for method, operation := range operations {
				if operation != nil && dg.hasTag(operation.Tags, tag.Name) {
					md.WriteString(fmt.Sprintf("### %s %s\n\n", method, path))
					md.WriteString(fmt.Sprintf("**Summary**: %s\n\n", operation.Summary))
					if operation.Description != "" {
						md.WriteString(fmt.Sprintf("**Description**: %s\n\n", operation.Description))
					}

					// Parameters
					if len(operation.Parameters) > 0 {
						md.WriteString("**Parameters**:\n\n")
						for _, param := range operation.Parameters {
							required := ""
							if param.Required {
								required = " (required)"
							}
							md.WriteString(fmt.Sprintf("- `%s` (%s)%s: %s\n", param.Name, param.In, required, param.Description))
						}
						md.WriteString("\n")
					}

					// Request body
					if operation.RequestBody != nil {
						md.WriteString("**Request Body**:\n\n")
						md.WriteString(fmt.Sprintf("%s\n\n", operation.RequestBody.Description))
					}

					// Responses
					md.WriteString("**Responses**:\n\n")
					for code, response := range operation.Responses {
						md.WriteString(fmt.Sprintf("- `%s`: %s\n", code, response.Description))
					}
					md.WriteString("\n")
				}
			}
		}
	}

	return md.String()
}

// hasTag checks if operation has a specific tag
func (dg *DocumentationGenerator) hasTag(tags []string, tag string) bool {
	for _, t := range tags {
		if t == tag {
			return true
		}
	}
	return false
}

// GeneratePostmanCollection generates a Postman collection
func (dg *DocumentationGenerator) GeneratePostmanCollection() map[string]interface{} {
	doc := dg.GenerateDocumentation()

	collection := map[string]interface{}{
		"info": map[string]interface{}{
			"name":        doc.Info.Title,
			"description": doc.Info.Description,
			"version":     doc.Info.Version,
			"schema":      "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
		},
		"auth": map[string]interface{}{
			"type": "bearer",
			"bearer": []map[string]interface{}{
				{
					"key":   "token",
					"value": "{{access_token}}",
					"type":  "string",
				},
			},
		},
		"variable": []map[string]interface{}{
			{
				"key":   "base_url",
				"value": doc.Info.BaseURL,
				"type":  "string",
			},
		},
		"item": dg.generatePostmanItems(doc),
	}

	return collection
}

// generatePostmanItems generates Postman collection items
func (dg *DocumentationGenerator) generatePostmanItems(doc *APIDocumentation) []map[string]interface{} {
	var items []map[string]interface{}

	// Group by tags
	tagItems := make(map[string][]map[string]interface{})

	for path, pathItem := range doc.Paths {
		operations := map[string]*Operation{
			"GET":    pathItem.Get,
			"POST":   pathItem.Post,
			"PUT":    pathItem.Put,
			"DELETE": pathItem.Delete,
			"PATCH":  pathItem.Patch,
		}

		for method, operation := range operations {
			if operation != nil {
				tag := "General"
				if len(operation.Tags) > 0 {
					tag = operation.Tags[0]
				}

				item := map[string]interface{}{
					"name": operation.Summary,
					"request": map[string]interface{}{
						"method": method,
						"header": []map[string]interface{}{},
						"url": map[string]interface{}{
							"raw":  "{{base_url}}" + path,
							"host": []string{"{{base_url}}"},
							"path": strings.Split(strings.TrimPrefix(path, "/"), "/"),
						},
					},
					"response": []map[string]interface{}{},
				}

				if operation.RequestBody != nil {
					item["request"].(map[string]interface{})["body"] = map[string]interface{}{
						"mode": "raw",
						"raw":  "{}",
						"options": map[string]interface{}{
							"raw": map[string]interface{}{
								"language": "json",
							},
						},
					}
				}

				tagItems[tag] = append(tagItems[tag], item)
			}
		}
	}

	// Convert to folder structure
	for tag, tagItemList := range tagItems {
		folder := map[string]interface{}{
			"name": tag,
			"item": tagItemList,
		}
		items = append(items, folder)
	}

	return items
}

// ExportToJSON exports documentation as JSON
func (dg *DocumentationGenerator) ExportToJSON() ([]byte, error) {
	doc := dg.GenerateDocumentation()
	return json.MarshalIndent(doc, "", "  ")
}

// ExportOpenAPIToJSON exports OpenAPI spec as JSON
func (dg *DocumentationGenerator) ExportOpenAPIToJSON() ([]byte, error) {
	spec := dg.GenerateOpenAPISpec()
	return json.MarshalIndent(spec, "", "  ")
}
