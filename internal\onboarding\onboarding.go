package onboarding

import (
	"context"
	"fmt"
	"time"

	"goVwPlatformAPI/internal/database"
)

type OnboardingService struct {
	db *database.DB
}

type OnboardingStep struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Required    bool                   `json:"required"`
	Order       int                    `json:"order"`
	Completed   bool                   `json:"completed"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

type OnboardingProgress struct {
	UserID         string            `json:"user_id"`
	CurrentStep    int               `json:"current_step"`
	TotalSteps     int               `json:"total_steps"`
	CompletedSteps int               `json:"completed_steps"`
	IsCompleted    bool              `json:"is_completed"`
	CompletionRate float64           `json:"completion_rate"`
	Steps          []*OnboardingStep `json:"steps"`
}

type UserProfile struct {
	UserID       string `json:"user_id"`
	FirstName    string `json:"first_name"`
	LastName     string `json:"last_name"`
	Company      string `json:"company"`
	BusinessType string `json:"business_type"`
	Industry     string `json:"industry"`
	TeamSize     string `json:"team_size"`
	PrimaryGoal  string `json:"primary_goal"`
	AvatarURL    string `json:"avatar_url"`
	Timezone     string `json:"timezone"`
	Language     string `json:"language"`
}

type UserSettings struct {
	ID                   string    `json:"id"`
	UserID               string    `json:"user_id"`
	EmailNotifications   bool      `json:"email_notifications"`
	BrowserNotifications bool      `json:"browser_notifications"`
	MarketingEmails      bool      `json:"marketing_emails"`
	WeeklyDigest         bool      `json:"weekly_digest"`
	SecurityAlerts       bool      `json:"security_alerts"`
	Theme                string    `json:"theme"`
	SidebarCollapsed     bool      `json:"sidebar_collapsed"`
	DefaultDashboardView string    `json:"default_dashboard_view"`
	AutoSave             bool      `json:"auto_save"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}

func NewOnboardingService(db *database.DB) *OnboardingService {
	return &OnboardingService{db: db}
}

// GetOnboardingSteps returns the predefined onboarding steps
func (s *OnboardingService) GetOnboardingSteps() []*OnboardingStep {
	return []*OnboardingStep{
		{
			ID:          "welcome",
			Name:        "welcome",
			Title:       "Welcome to VelocityWave",
			Description: "Get started with your platform journey",
			Required:    true,
			Order:       1,
		},
		{
			ID:          "profile",
			Name:        "profile",
			Title:       "Complete Your Profile",
			Description: "Tell us about yourself and your business",
			Required:    true,
			Order:       2,
		},
		{
			ID:          "business_info",
			Name:        "business_info",
			Title:       "Business Information",
			Description: "Help us understand your business needs",
			Required:    true,
			Order:       3,
		},
		{
			ID:          "preferences",
			Name:        "preferences",
			Title:       "Set Your Preferences",
			Description: "Customize your platform experience",
			Required:    false,
			Order:       4,
		},
		{
			ID:          "first_site",
			Name:        "first_site",
			Title:       "Create Your First Site",
			Description: "Build your first website with our tools",
			Required:    false,
			Order:       5,
		},
		{
			ID:          "complete",
			Name:        "complete",
			Title:       "You're All Set!",
			Description: "Welcome to your VelocityWave dashboard",
			Required:    true,
			Order:       6,
		},
	}
}

// GetUserOnboardingProgress retrieves the current onboarding progress for a user
func (s *OnboardingService) GetUserOnboardingProgress(ctx context.Context, userID string) (*OnboardingProgress, error) {
	// Get user's current onboarding status
	var currentStep int
	var isCompleted bool

	err := s.db.Pool.QueryRow(ctx, `
		SELECT onboarding_step, onboarding_completed 
		FROM users 
		WHERE id = $1
	`, userID).Scan(&currentStep, &isCompleted)

	if err != nil {
		return nil, fmt.Errorf("failed to get user onboarding status: %w", err)
	}

	// Get all onboarding steps
	allSteps := s.GetOnboardingSteps()
	totalSteps := len(allSteps)

	// Get completed step details from database
	rows, err := s.db.Pool.Query(ctx, `
		SELECT step_name, completed, completed_at, data
		FROM user_onboarding_progress
		WHERE user_id = $1
	`, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}
	defer rows.Close()

	completedStepsMap := make(map[string]*OnboardingStep)
	completedCount := 0

	for rows.Next() {
		var stepName string
		var completed bool
		var completedAt *time.Time
		var data map[string]interface{}

		err := rows.Scan(&stepName, &completed, &completedAt, &data)
		if err != nil {
			return nil, fmt.Errorf("failed to scan onboarding step: %w", err)
		}

		if completed {
			completedCount++
		}

		completedStepsMap[stepName] = &OnboardingStep{
			Name:        stepName,
			Completed:   completed,
			CompletedAt: completedAt,
			Data:        data,
		}
	}

	// Merge with predefined steps
	for _, step := range allSteps {
		if completedStep, exists := completedStepsMap[step.Name]; exists {
			step.Completed = completedStep.Completed
			step.CompletedAt = completedStep.CompletedAt
			step.Data = completedStep.Data
		}
	}

	completionRate := float64(completedCount) / float64(totalSteps) * 100

	return &OnboardingProgress{
		UserID:         userID,
		CurrentStep:    currentStep,
		TotalSteps:     totalSteps,
		CompletedSteps: completedCount,
		IsCompleted:    isCompleted,
		CompletionRate: completionRate,
		Steps:          allSteps,
	}, nil
}

// CompleteOnboardingStep marks a specific step as completed
func (s *OnboardingService) CompleteOnboardingStep(ctx context.Context, userID string, stepName string, data map[string]interface{}) error {
	now := time.Now()

	// Start transaction
	tx, err := s.db.Pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Insert or update step progress
	_, err = tx.Exec(ctx, `
		INSERT INTO user_onboarding_progress (user_id, step_name, completed, completed_at, data, updated_at)
		VALUES ($1, $2, true, $3, $4, $3)
		ON CONFLICT (user_id, step_name)
		DO UPDATE SET completed = true, completed_at = $3, data = $4, updated_at = $3
	`, userID, stepName, now, data)

	if err != nil {
		return fmt.Errorf("failed to update onboarding step: %w", err)
	}

	// Update user's current step and check if onboarding is complete
	allSteps := s.GetOnboardingSteps()
	var nextStep int
	var isComplete bool = true

	for i, step := range allSteps {
		if step.Name == stepName {
			nextStep = i + 2 // Next step (1-indexed)
			break
		}
	}

	// Check if all required steps are completed
	completedSteps, err := s.getCompletedStepsCount(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get completed steps count: %w", err)
	}

	requiredSteps := 0
	for _, step := range allSteps {
		if step.Required {
			requiredSteps++
		}
	}

	if completedSteps < requiredSteps {
		isComplete = false
	}

	// Update user's onboarding status
	_, err = tx.Exec(ctx, `
		UPDATE users 
		SET onboarding_step = $1, onboarding_completed = $2, updated_at = $3
		WHERE id = $4
	`, nextStep, isComplete, now, userID)

	if err != nil {
		return fmt.Errorf("failed to update user onboarding status: %w", err)
	}

	return tx.Commit(ctx)
}

// UpdateUserProfile updates user profile information
func (s *OnboardingService) UpdateUserProfile(ctx context.Context, userID string, profile *UserProfile) error {
	query := `
		UPDATE users 
		SET first_name = $1, last_name = $2, company = $3, business_type = $4,
		    industry = $5, team_size = $6, primary_goal = $7, avatar_url = $8,
		    timezone = $9, language = $10, updated_at = NOW()
		WHERE id = $11
	`

	_, err := s.db.Pool.Exec(ctx, query,
		profile.FirstName, profile.LastName, profile.Company, profile.BusinessType,
		profile.Industry, profile.TeamSize, profile.PrimaryGoal, profile.AvatarURL,
		profile.Timezone, profile.Language, userID)

	if err != nil {
		return fmt.Errorf("failed to update user profile: %w", err)
	}

	return nil
}

// GetUserSettings retrieves user settings
func (s *OnboardingService) GetUserSettings(ctx context.Context, userID string) (*UserSettings, error) {
	var settings UserSettings

	query := `
		SELECT id, user_id, email_notifications, browser_notifications, marketing_emails,
		       weekly_digest, security_alerts, theme, sidebar_collapsed, 
		       default_dashboard_view, auto_save, created_at, updated_at
		FROM user_settings
		WHERE user_id = $1
	`

	err := s.db.Pool.QueryRow(ctx, query, userID).Scan(
		&settings.ID, &settings.UserID, &settings.EmailNotifications,
		&settings.BrowserNotifications, &settings.MarketingEmails,
		&settings.WeeklyDigest, &settings.SecurityAlerts, &settings.Theme,
		&settings.SidebarCollapsed, &settings.DefaultDashboardView,
		&settings.AutoSave, &settings.CreatedAt, &settings.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user settings: %w", err)
	}

	return &settings, nil
}

// UpdateUserSettings updates user settings
func (s *OnboardingService) UpdateUserSettings(ctx context.Context, userID string, settings *UserSettings) error {
	query := `
		UPDATE user_settings 
		SET email_notifications = $1, browser_notifications = $2, marketing_emails = $3,
		    weekly_digest = $4, security_alerts = $5, theme = $6, sidebar_collapsed = $7,
		    default_dashboard_view = $8, auto_save = $9, updated_at = NOW()
		WHERE user_id = $10
	`

	_, err := s.db.Pool.Exec(ctx, query,
		settings.EmailNotifications, settings.BrowserNotifications, settings.MarketingEmails,
		settings.WeeklyDigest, settings.SecurityAlerts, settings.Theme,
		settings.SidebarCollapsed, settings.DefaultDashboardView, settings.AutoSave,
		userID)

	if err != nil {
		return fmt.Errorf("failed to update user settings: %w", err)
	}

	return nil
}

// Helper function to get completed steps count
func (s *OnboardingService) getCompletedStepsCount(ctx context.Context, userID string) (int, error) {
	var count int
	err := s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM user_onboarding_progress 
		WHERE user_id = $1 AND completed = true
	`, userID).Scan(&count)

	if err != nil {
		return 0, err
	}

	return count, nil
}
