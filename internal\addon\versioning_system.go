package addon

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

// VersioningSystem handles addon version management
type VersioningSystem struct {
	metadataManager  *MetadataManager
	deploymentSystem *DeploymentSystem
	versions         map[string][]*AddonVersion // addonID -> versions
	versionHistory   map[string]*VersionHistory // addonID -> history
	mutex            sync.RWMutex
	config           VersioningConfig
}

// VersioningConfig contains configuration for versioning
type VersioningConfig struct {
	MaxVersionsPerAddon int           `json:"maxVersionsPerAddon"`
	RetentionPeriod     time.Duration `json:"retentionPeriod"`
	AutoCleanup         bool          `json:"autoCleanup"`
	EnablePrerelease    bool          `json:"enablePrerelease"`
	EnableBranching     bool          `json:"enableBranching"`
	DefaultBranch       string        `json:"defaultBranch"`
}

// AddonVersion represents a specific version of an addon
type AddonVersion struct {
	ID              string                 `json:"id"`
	AddonID         string                 `json:"addonId"`
	Version         string                 `json:"version"`
	SemanticVersion *SemanticVersion       `json:"semanticVersion"`
	Config          *AddonConfiguration    `json:"config"`
	Status          VersionStatus          `json:"status"`
	Type            VersionType            `json:"type"`
	Branch          string                 `json:"branch"`
	CreatedAt       time.Time              `json:"createdAt"`
	CreatedBy       string                 `json:"createdBy"`
	PublishedAt     *time.Time             `json:"publishedAt,omitempty"`
	PublishedBy     string                 `json:"publishedBy,omitempty"`
	DeprecatedAt    *time.Time             `json:"deprecatedAt,omitempty"`
	DeprecatedBy    string                 `json:"deprecatedBy,omitempty"`
	Changelog       string                 `json:"changelog"`
	Dependencies    []VersionDependency    `json:"dependencies"`
	Compatibility   VersionCompatibility   `json:"compatibility"`
	Metrics         VersionMetrics         `json:"metrics"`
	Tags            []string               `json:"tags"`
	Context         map[string]interface{} `json:"context"`
}

// SemanticVersion represents a semantic version
type SemanticVersion struct {
	Major      int    `json:"major"`
	Minor      int    `json:"minor"`
	Patch      int    `json:"patch"`
	Prerelease string `json:"prerelease,omitempty"`
	Build      string `json:"build,omitempty"`
}

// VersionStatus represents the status of a version
type VersionStatus string

const (
	VersionStatusDraft      VersionStatus = "draft"
	VersionStatusTesting    VersionStatus = "testing"
	VersionStatusStaging    VersionStatus = "staging"
	VersionStatusPublished  VersionStatus = "published"
	VersionStatusDeprecated VersionStatus = "deprecated"
	VersionStatusRetired    VersionStatus = "retired"
)

// VersionType represents the type of version
type VersionType string

const (
	VersionTypeMajor  VersionType = "major"
	VersionTypeMinor  VersionType = "minor"
	VersionTypePatch  VersionType = "patch"
	VersionTypeHotfix VersionType = "hotfix"
)

// VersionDependency represents a dependency on another addon version
type VersionDependency struct {
	AddonID        string `json:"addonId"`
	VersionRange   string `json:"versionRange"`
	Required       bool   `json:"required"`
	DependencyType string `json:"dependencyType"` // runtime, development, optional
}

// VersionCompatibility represents compatibility information
type VersionCompatibility struct {
	MinPlatformVersion string   `json:"minPlatformVersion"`
	MaxPlatformVersion string   `json:"maxPlatformVersion,omitempty"`
	BreakingChanges    []string `json:"breakingChanges"`
	MigrationGuide     string   `json:"migrationGuide,omitempty"`
	BackwardCompatible bool     `json:"backwardCompatible"`
}

// VersionMetrics contains metrics for a version
type VersionMetrics struct {
	Downloads      int64     `json:"downloads"`
	ActiveInstalls int64     `json:"activeInstalls"`
	LastDownload   time.Time `json:"lastDownload"`
	AverageRating  float64   `json:"averageRating"`
	ReviewCount    int       `json:"reviewCount"`
	IssueCount     int       `json:"issueCount"`
	SecurityIssues int       `json:"securityIssues"`
}

// VersionHistory tracks the history of version changes
type VersionHistory struct {
	AddonID  string                 `json:"addonId"`
	Events   []VersionEvent         `json:"events"`
	Branches map[string]*Branch     `json:"branches"`
	Tags     map[string]string      `json:"tags"` // tag -> version
	Context  map[string]interface{} `json:"context"`
}

// VersionEvent represents an event in version history
type VersionEvent struct {
	ID          string                 `json:"id"`
	Type        EventType              `json:"type"`
	Version     string                 `json:"version"`
	Timestamp   time.Time              `json:"timestamp"`
	Actor       string                 `json:"actor"`
	Description string                 `json:"description"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// EventType represents the type of version event
type EventType string

const (
	EventTypeCreated    EventType = "created"
	EventTypePublished  EventType = "published"
	EventTypeDeprecated EventType = "deprecated"
	EventTypeRetired    EventType = "retired"
	EventTypeTagged     EventType = "tagged"
	EventTypeBranched   EventType = "branched"
	EventTypeMerged     EventType = "merged"
)

// Branch represents a version branch
type Branch struct {
	Name        string    `json:"name"`
	BaseVersion string    `json:"baseVersion"`
	HeadVersion string    `json:"headVersion"`
	CreatedAt   time.Time `json:"createdAt"`
	CreatedBy   string    `json:"createdBy"`
	Active      bool      `json:"active"`
	Protected   bool      `json:"protected"`
}

// NewVersioningSystem creates a new versioning system
func NewVersioningSystem(config VersioningConfig) *VersioningSystem {
	if config.MaxVersionsPerAddon == 0 {
		config.MaxVersionsPerAddon = 50
	}
	if config.RetentionPeriod == 0 {
		config.RetentionPeriod = 365 * 24 * time.Hour // 1 year
	}
	if config.DefaultBranch == "" {
		config.DefaultBranch = "main"
	}

	return &VersioningSystem{
		metadataManager:  NewMetadataManager(),
		deploymentSystem: NewDeploymentSystem(DeploymentConfig{}),
		versions:         make(map[string][]*AddonVersion),
		versionHistory:   make(map[string]*VersionHistory),
		config:           config,
	}
}

// CreateVersion creates a new version of an addon
func (vs *VersioningSystem) CreateVersion(ctx context.Context, addonID, createdBy string, config *AddonConfiguration, versionType VersionType, changelog string) (*AddonVersion, error) {
	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	// Validate addon configuration
	if err := vs.metadataManager.ValidateMetadata(&config.Metadata); err != nil {
		return nil, fmt.Errorf("invalid addon metadata: %w", err)
	}

	// Parse semantic version
	semVer, err := vs.parseSemanticVersion(config.Metadata.Version)
	if err != nil {
		return nil, fmt.Errorf("invalid semantic version: %w", err)
	}

	// Check if version already exists
	if vs.versionExists(addonID, config.Metadata.Version) {
		return nil, fmt.Errorf("version %s already exists for addon %s", config.Metadata.Version, addonID)
	}

	// Get previous version for compatibility checking
	previousVersion := vs.getLatestVersion(addonID)

	// Create new version
	version := &AddonVersion{
		ID:              generateVersionID(),
		AddonID:         addonID,
		Version:         config.Metadata.Version,
		SemanticVersion: semVer,
		Config:          config,
		Status:          VersionStatusDraft,
		Type:            versionType,
		Branch:          vs.config.DefaultBranch,
		CreatedAt:       time.Now(),
		CreatedBy:       createdBy,
		Changelog:       changelog,
		Dependencies:    vs.extractDependencies(config),
		Compatibility:   vs.analyzeCompatibility(previousVersion, config),
		Tags:            []string{},
		Context:         make(map[string]interface{}),
		Metrics: VersionMetrics{
			LastDownload: time.Now(),
		},
	}

	// Add to versions list
	vs.versions[addonID] = append(vs.versions[addonID], version)
	vs.sortVersions(addonID)

	// Initialize history if needed
	if vs.versionHistory[addonID] == nil {
		vs.versionHistory[addonID] = &VersionHistory{
			AddonID:  addonID,
			Events:   []VersionEvent{},
			Branches: make(map[string]*Branch),
			Tags:     make(map[string]string),
			Context:  make(map[string]interface{}),
		}
	}

	// Add event to history
	vs.addVersionEvent(addonID, EventTypeCreated, version.Version, createdBy, "Version created", map[string]interface{}{
		"versionType": versionType,
		"changelog":   changelog,
	})

	// Cleanup old versions if needed
	if vs.config.AutoCleanup {
		vs.cleanupOldVersions(addonID)
	}

	return version, nil
}

// PublishVersion publishes a version
func (vs *VersioningSystem) PublishVersion(ctx context.Context, addonID, version, publishedBy string) error {
	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	addonVersion := vs.findVersion(addonID, version)
	if addonVersion == nil {
		return fmt.Errorf("version %s not found for addon %s", version, addonID)
	}

	if addonVersion.Status == VersionStatusPublished {
		return fmt.Errorf("version %s is already published", version)
	}

	// Update version status
	now := time.Now()
	addonVersion.Status = VersionStatusPublished
	addonVersion.PublishedAt = &now
	addonVersion.PublishedBy = publishedBy

	// Add event to history
	vs.addVersionEvent(addonID, EventTypePublished, version, publishedBy, "Version published", nil)

	return nil
}

// DeprecateVersion marks a version as deprecated
func (vs *VersioningSystem) DeprecateVersion(ctx context.Context, addonID, version, deprecatedBy, reason string) error {
	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	addonVersion := vs.findVersion(addonID, version)
	if addonVersion == nil {
		return fmt.Errorf("version %s not found for addon %s", version, addonID)
	}

	if addonVersion.Status == VersionStatusDeprecated {
		return fmt.Errorf("version %s is already deprecated", version)
	}

	// Update version status
	now := time.Now()
	addonVersion.Status = VersionStatusDeprecated
	addonVersion.DeprecatedAt = &now
	addonVersion.DeprecatedBy = deprecatedBy

	// Add event to history
	vs.addVersionEvent(addonID, EventTypeDeprecated, version, deprecatedBy, fmt.Sprintf("Version deprecated: %s", reason), map[string]interface{}{
		"reason": reason,
	})

	return nil
}

// GetVersion retrieves a specific version
func (vs *VersioningSystem) GetVersion(addonID, version string) (*AddonVersion, error) {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()

	addonVersion := vs.findVersion(addonID, version)
	if addonVersion == nil {
		return nil, fmt.Errorf("version %s not found for addon %s", version, addonID)
	}

	return addonVersion, nil
}

// GetLatestVersion retrieves the latest published version
func (vs *VersioningSystem) GetLatestVersion(addonID string) (*AddonVersion, error) {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()

	latest := vs.getLatestPublishedVersion(addonID)
	if latest == nil {
		return nil, fmt.Errorf("no published versions found for addon %s", addonID)
	}

	return latest, nil
}

// ListVersions lists all versions for an addon
func (vs *VersioningSystem) ListVersions(addonID string, includeUnpublished bool) ([]*AddonVersion, error) {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()

	versions, exists := vs.versions[addonID]
	if !exists {
		return []*AddonVersion{}, nil
	}

	if includeUnpublished {
		return versions, nil
	}

	// Filter to only published versions
	var publishedVersions []*AddonVersion
	for _, version := range versions {
		if version.Status == VersionStatusPublished {
			publishedVersions = append(publishedVersions, version)
		}
	}

	return publishedVersions, nil
}

// CreateBranch creates a new version branch
func (vs *VersioningSystem) CreateBranch(ctx context.Context, addonID, branchName, baseVersion, createdBy string) (*Branch, error) {
	if !vs.config.EnableBranching {
		return nil, fmt.Errorf("branching is not enabled")
	}

	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	history := vs.versionHistory[addonID]
	if history == nil {
		return nil, fmt.Errorf("addon %s not found", addonID)
	}

	// Check if branch already exists
	if _, exists := history.Branches[branchName]; exists {
		return nil, fmt.Errorf("branch %s already exists", branchName)
	}

	// Validate base version
	if baseVersion != "" && vs.findVersion(addonID, baseVersion) == nil {
		return nil, fmt.Errorf("base version %s not found", baseVersion)
	}

	// Create branch
	branch := &Branch{
		Name:        branchName,
		BaseVersion: baseVersion,
		HeadVersion: baseVersion,
		CreatedAt:   time.Now(),
		CreatedBy:   createdBy,
		Active:      true,
		Protected:   false,
	}

	history.Branches[branchName] = branch

	// Add event to history
	vs.addVersionEvent(addonID, EventTypeBranched, baseVersion, createdBy, fmt.Sprintf("Branch %s created", branchName), map[string]interface{}{
		"branchName":  branchName,
		"baseVersion": baseVersion,
	})

	return branch, nil
}

// TagVersion creates a tag for a version
func (vs *VersioningSystem) TagVersion(ctx context.Context, addonID, version, tag, createdBy string) error {
	vs.mutex.Lock()
	defer vs.mutex.Unlock()

	// Validate version exists
	if vs.findVersion(addonID, version) == nil {
		return fmt.Errorf("version %s not found for addon %s", version, addonID)
	}

	history := vs.versionHistory[addonID]
	if history == nil {
		return fmt.Errorf("addon %s not found", addonID)
	}

	// Check if tag already exists
	if existingVersion, exists := history.Tags[tag]; exists {
		return fmt.Errorf("tag %s already exists for version %s", tag, existingVersion)
	}

	// Create tag
	history.Tags[tag] = version

	// Add event to history
	vs.addVersionEvent(addonID, EventTypeTagged, version, createdBy, fmt.Sprintf("Tag %s created", tag), map[string]interface{}{
		"tag": tag,
	})

	return nil
}

// CompareVersions compares two versions and returns compatibility information
func (vs *VersioningSystem) CompareVersions(addonID, version1, version2 string) (*VersionComparison, error) {
	vs.mutex.RLock()
	defer vs.mutex.RUnlock()

	v1 := vs.findVersion(addonID, version1)
	v2 := vs.findVersion(addonID, version2)

	if v1 == nil {
		return nil, fmt.Errorf("version %s not found", version1)
	}
	if v2 == nil {
		return nil, fmt.Errorf("version %s not found", version2)
	}

	comparison := &VersionComparison{
		Version1:            version1,
		Version2:            version2,
		ComparisonResult:    vs.compareSemanticVersions(v1.SemanticVersion, v2.SemanticVersion),
		BreakingChanges:     vs.findBreakingChanges(v1, v2),
		NewFeatures:         vs.findNewFeatures(v1, v2),
		BugFixes:            vs.findBugFixes(v1, v2),
		CompatibilityIssues: vs.findCompatibilityIssues(v1, v2),
	}

	return comparison, nil
}

// VersionComparison represents a comparison between two versions
type VersionComparison struct {
	Version1            string   `json:"version1"`
	Version2            string   `json:"version2"`
	ComparisonResult    int      `json:"comparisonResult"` // -1: v1 < v2, 0: v1 == v2, 1: v1 > v2
	BreakingChanges     []string `json:"breakingChanges"`
	NewFeatures         []string `json:"newFeatures"`
	BugFixes            []string `json:"bugFixes"`
	CompatibilityIssues []string `json:"compatibilityIssues"`
}

// Helper methods

func (vs *VersioningSystem) parseSemanticVersion(version string) (*SemanticVersion, error) {
	// Regex for semantic versioning: major.minor.patch[-prerelease][+build]
	re := regexp.MustCompile(`^(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9\-\.]+))?(?:\+([a-zA-Z0-9\-\.]+))?$`)
	matches := re.FindStringSubmatch(version)

	if len(matches) < 4 {
		return nil, fmt.Errorf("invalid semantic version format: %s", version)
	}

	major, _ := strconv.Atoi(matches[1])
	minor, _ := strconv.Atoi(matches[2])
	patch, _ := strconv.Atoi(matches[3])

	semVer := &SemanticVersion{
		Major: major,
		Minor: minor,
		Patch: patch,
	}

	if len(matches) > 4 && matches[4] != "" {
		semVer.Prerelease = matches[4]
	}

	if len(matches) > 5 && matches[5] != "" {
		semVer.Build = matches[5]
	}

	return semVer, nil
}

func (vs *VersioningSystem) versionExists(addonID, version string) bool {
	versions, exists := vs.versions[addonID]
	if !exists {
		return false
	}

	for _, v := range versions {
		if v.Version == version {
			return true
		}
	}

	return false
}

func (vs *VersioningSystem) findVersion(addonID, version string) *AddonVersion {
	versions, exists := vs.versions[addonID]
	if !exists {
		return nil
	}

	for _, v := range versions {
		if v.Version == version {
			return v
		}
	}

	return nil
}

func (vs *VersioningSystem) getLatestVersion(addonID string) *AddonVersion {
	versions, exists := vs.versions[addonID]
	if !exists || len(versions) == 0 {
		return nil
	}

	// Versions are sorted, so return the last one
	return versions[len(versions)-1]
}

func (vs *VersioningSystem) getLatestPublishedVersion(addonID string) *AddonVersion {
	versions, exists := vs.versions[addonID]
	if !exists {
		return nil
	}

	// Find the latest published version
	for i := len(versions) - 1; i >= 0; i-- {
		if versions[i].Status == VersionStatusPublished {
			return versions[i]
		}
	}

	return nil
}

func (vs *VersioningSystem) sortVersions(addonID string) {
	versions := vs.versions[addonID]
	sort.Slice(versions, func(i, j int) bool {
		return vs.compareSemanticVersions(versions[i].SemanticVersion, versions[j].SemanticVersion) < 0
	})
}

func (vs *VersioningSystem) compareSemanticVersions(v1, v2 *SemanticVersion) int {
	if v1.Major != v2.Major {
		return compareInt(v1.Major, v2.Major)
	}
	if v1.Minor != v2.Minor {
		return compareInt(v1.Minor, v2.Minor)
	}
	if v1.Patch != v2.Patch {
		return compareInt(v1.Patch, v2.Patch)
	}

	// Handle prerelease versions
	if v1.Prerelease == "" && v2.Prerelease != "" {
		return 1 // v1 > v2 (release > prerelease)
	}
	if v1.Prerelease != "" && v2.Prerelease == "" {
		return -1 // v1 < v2 (prerelease < release)
	}
	if v1.Prerelease != "" && v2.Prerelease != "" {
		return strings.Compare(v1.Prerelease, v2.Prerelease)
	}

	return 0 // Equal
}

func compareInt(a, b int) int {
	if a < b {
		return -1
	}
	if a > b {
		return 1
	}
	return 0
}

func (vs *VersioningSystem) extractDependencies(config *AddonConfiguration) []VersionDependency {
	var dependencies []VersionDependency

	for _, dep := range config.Settings.Dependencies {
		dependencies = append(dependencies, VersionDependency{
			AddonID:        dep.Name,
			VersionRange:   dep.Version,
			Required:       dep.Required,
			DependencyType: dep.Type,
		})
	}

	return dependencies
}

func (vs *VersioningSystem) analyzeCompatibility(previousVersion *AddonVersion, config *AddonConfiguration) VersionCompatibility {
	compatibility := VersionCompatibility{
		MinPlatformVersion: config.Settings.Compatibility.MinPlatformVersion,
		BackwardCompatible: true,
		BreakingChanges:    []string{},
	}

	if previousVersion != nil {
		// Analyze for breaking changes
		if vs.hasBreakingChanges(previousVersion.Config, config) {
			compatibility.BackwardCompatible = false
			compatibility.BreakingChanges = vs.identifyBreakingChanges(previousVersion.Config, config)
		}
	}

	return compatibility
}

func (vs *VersioningSystem) hasBreakingChanges(oldConfig, newConfig *AddonConfiguration) bool {
	// Simple heuristic: major version bump indicates breaking changes
	oldSemVer, _ := vs.parseSemanticVersion(oldConfig.Metadata.Version)
	newSemVer, _ := vs.parseSemanticVersion(newConfig.Metadata.Version)

	if oldSemVer != nil && newSemVer != nil {
		return newSemVer.Major > oldSemVer.Major
	}

	return false
}

func (vs *VersioningSystem) identifyBreakingChanges(oldConfig, newConfig *AddonConfiguration) []string {
	var changes []string

	// Compare workflow nodes
	if len(newConfig.Workflow.Nodes) != len(oldConfig.Workflow.Nodes) {
		changes = append(changes, "Workflow structure changed")
	}

	// Compare permissions
	if len(newConfig.Settings.Permissions) != len(oldConfig.Settings.Permissions) {
		changes = append(changes, "Permission requirements changed")
	}

	return changes
}

func (vs *VersioningSystem) findBreakingChanges(v1, v2 *AddonVersion) []string {
	return vs.identifyBreakingChanges(v1.Config, v2.Config)
}

func (vs *VersioningSystem) findNewFeatures(v1, v2 *AddonVersion) []string {
	// Placeholder implementation
	return []string{}
}

func (vs *VersioningSystem) findBugFixes(v1, v2 *AddonVersion) []string {
	// Placeholder implementation
	return []string{}
}

func (vs *VersioningSystem) findCompatibilityIssues(v1, v2 *AddonVersion) []string {
	// Placeholder implementation
	return []string{}
}

func (vs *VersioningSystem) addVersionEvent(addonID string, eventType EventType, version, actor, description string, metadata map[string]interface{}) {
	history := vs.versionHistory[addonID]
	if history == nil {
		return
	}

	event := VersionEvent{
		ID:          generateEventID(),
		Type:        eventType,
		Version:     version,
		Timestamp:   time.Now(),
		Actor:       actor,
		Description: description,
		Metadata:    metadata,
	}

	history.Events = append(history.Events, event)

	// Keep only the last 1000 events
	if len(history.Events) > 1000 {
		history.Events = history.Events[len(history.Events)-1000:]
	}
}

func (vs *VersioningSystem) cleanupOldVersions(addonID string) {
	versions := vs.versions[addonID]
	if len(versions) <= vs.config.MaxVersionsPerAddon {
		return
	}

	// Keep the most recent versions and published versions
	var toKeep []*AddonVersion
	var toRemove []*AddonVersion

	// Always keep published versions
	for _, version := range versions {
		if version.Status == VersionStatusPublished || version.Status == VersionStatusDeprecated {
			toKeep = append(toKeep, version)
		} else {
			toRemove = append(toRemove, version)
		}
	}

	// Keep the most recent draft/testing versions up to the limit
	remaining := vs.config.MaxVersionsPerAddon - len(toKeep)
	if remaining > 0 && len(toRemove) > remaining {
		// Sort by creation date and keep the most recent
		sort.Slice(toRemove, func(i, j int) bool {
			return toRemove[i].CreatedAt.After(toRemove[j].CreatedAt)
		})
		toKeep = append(toKeep, toRemove[:remaining]...)
	} else {
		toKeep = append(toKeep, toRemove...)
	}

	vs.versions[addonID] = toKeep
	vs.sortVersions(addonID)
}

func generateVersionID() string {
	return fmt.Sprintf("version_%d", time.Now().UnixNano())
}

func generateEventID() string {
	return fmt.Sprintf("event_%d", time.Now().UnixNano())
}
