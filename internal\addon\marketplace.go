package addon

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"
)

// Marketplace handles addon marketplace functionality
type Marketplace struct {
	versioningSystem *VersioningSystem
	reviewSystem     *ReviewSystem
	addons           map[string]*MarketplaceAddon
	categories       map[string]*Category
	collections      map[string]*Collection
	mutex            sync.RWMutex
	config           MarketplaceConfig
}

// MarketplaceConfig contains marketplace configuration
type MarketplaceConfig struct {
	FeaturedAddonsCount int     `json:"featuredAddonsCount"`
	PopularThreshold    int64   `json:"popularThreshold"`
	TrendingDays        int     `json:"trendingDays"`
	SearchResultsLimit  int     `json:"searchResultsLimit"`
	MinRatingDisplay    float64 `json:"minRatingDisplay"`
}

// MarketplaceAddon represents an addon in the marketplace
type MarketplaceAddon struct {
	ID               string                 `json:"id"`
	Name             string                 `json:"name"`
	Description      string                 `json:"description"`
	ShortDescription string                 `json:"shortDescription"`
	Author           string                 `json:"author"`
	AuthorID         string                 `json:"authorId"`
	Category         string                 `json:"category"`
	Tags             []string               `json:"tags"`
	Version          string                 `json:"version"`
	Status           AddonStatus            `json:"status"`
	Visibility       AddonVisibility        `json:"visibility"`
	Icon             string                 `json:"icon"`
	Screenshots      []string               `json:"screenshots"`
	DemoURL          string                 `json:"demoUrl,omitempty"`
	DocumentationURL string                 `json:"documentationUrl,omitempty"`
	SourceCodeURL    string                 `json:"sourceCodeUrl,omitempty"`
	License          string                 `json:"license"`
	Pricing          AddonPricing           `json:"pricing"`
	Metrics          AddonMetrics           `json:"metrics"`
	Compatibility    AddonCompatibility     `json:"compatibility"`
	Dependencies     []string               `json:"dependencies"`
	CreatedAt        time.Time              `json:"createdAt"`
	UpdatedAt        time.Time              `json:"updatedAt"`
	PublishedAt      *time.Time             `json:"publishedAt,omitempty"`
	Context          map[string]interface{} `json:"context"`
}

// AddonStatus represents the status of an addon in marketplace
type AddonStatus string

const (
	AddonStatusDraft     AddonStatus = "draft"
	AddonStatusReview    AddonStatus = "review"
	AddonStatusPublished AddonStatus = "published"
	AddonStatusSuspended AddonStatus = "suspended"
	AddonStatusRetired   AddonStatus = "retired"
)

// AddonVisibility represents the visibility of an addon
type AddonVisibility string

const (
	AddonVisibilityPublic   AddonVisibility = "public"
	AddonVisibilityPrivate  AddonVisibility = "private"
	AddonVisibilityUnlisted AddonVisibility = "unlisted"
)

// AddonPricing contains pricing information
type AddonPricing struct {
	Model     PricingModel  `json:"model"`
	Price     float64       `json:"price"`
	Currency  string        `json:"currency"`
	Recurring bool          `json:"recurring"`
	Period    string        `json:"period,omitempty"` // monthly, yearly
	FreeTrial bool          `json:"freeTrial"`
	TrialDays int           `json:"trialDays,omitempty"`
	Tiers     []PricingTier `json:"tiers,omitempty"`
}

// PricingModel represents the pricing model
type PricingModel string

const (
	PricingModelFree         PricingModel = "free"
	PricingModelOneTime      PricingModel = "one_time"
	PricingModelSubscription PricingModel = "subscription"
	PricingModelUsageBased   PricingModel = "usage_based"
	PricingModelTiered       PricingModel = "tiered"
)

// PricingTier represents a pricing tier
type PricingTier struct {
	Name     string         `json:"name"`
	Price    float64        `json:"price"`
	Features []string       `json:"features"`
	Limits   map[string]int `json:"limits"`
	Popular  bool           `json:"popular"`
}

// AddonMetrics contains addon metrics
type AddonMetrics struct {
	Downloads       int64     `json:"downloads"`
	ActiveInstalls  int64     `json:"activeInstalls"`
	Views           int64     `json:"views"`
	Rating          float64   `json:"rating"`
	ReviewCount     int       `json:"reviewCount"`
	LastDownload    time.Time `json:"lastDownload"`
	TrendingScore   float64   `json:"trendingScore"`
	PopularityScore float64   `json:"popularityScore"`
}

// AddonCompatibility contains compatibility information
type AddonCompatibility struct {
	MinPlatformVersion string   `json:"minPlatformVersion"`
	MaxPlatformVersion string   `json:"maxPlatformVersion,omitempty"`
	SupportedBrowsers  []string `json:"supportedBrowsers"`
	RequiredFeatures   []string `json:"requiredFeatures"`
}

// Category represents an addon category
type Category struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Icon        string `json:"icon"`
	Color       string `json:"color"`
	AddonCount  int    `json:"addonCount"`
	Featured    bool   `json:"featured"`
	SortOrder   int    `json:"sortOrder"`
}

// Collection represents a curated collection of addons
type Collection struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	CuratorID   string                 `json:"curatorId"`
	CuratorName string                 `json:"curatorName"`
	AddonIDs    []string               `json:"addonIds"`
	Featured    bool                   `json:"featured"`
	Public      bool                   `json:"public"`
	CreatedAt   time.Time              `json:"createdAt"`
	UpdatedAt   time.Time              `json:"updatedAt"`
	Context     map[string]interface{} `json:"context"`
}

// SearchFilters contains search and filter options
type SearchFilters struct {
	Query         string       `json:"query"`
	Category      string       `json:"category"`
	Tags          []string     `json:"tags"`
	Author        string       `json:"author"`
	PricingModel  PricingModel `json:"pricingModel"`
	MinRating     float64      `json:"minRating"`
	Compatibility string       `json:"compatibility"`
	SortBy        SortOption   `json:"sortBy"`
	SortOrder     SortOrder    `json:"sortOrder"`
	Limit         int          `json:"limit"`
	Offset        int          `json:"offset"`
}

// SortOption represents sorting options
type SortOption string

const (
	SortByRelevance  SortOption = "relevance"
	SortByPopularity SortOption = "popularity"
	SortByRating     SortOption = "rating"
	SortByDownloads  SortOption = "downloads"
	SortByCreatedAt  SortOption = "created_at"
	SortByUpdatedAt  SortOption = "updated_at"
	SortByName       SortOption = "name"
	SortByTrending   SortOption = "trending"
)

// SortOrder represents sort order
type SortOrder string

const (
	SortOrderAsc  SortOrder = "asc"
	SortOrderDesc SortOrder = "desc"
)

// SearchResult contains search results
type SearchResult struct {
	Addons     []*MarketplaceAddon `json:"addons"`
	Total      int                 `json:"total"`
	Page       int                 `json:"page"`
	PerPage    int                 `json:"perPage"`
	TotalPages int                 `json:"totalPages"`
	Facets     SearchFacets        `json:"facets"`
	SearchTime time.Duration       `json:"searchTime"`
}

// SearchFacets contains search facets for filtering
type SearchFacets struct {
	Categories    map[string]int `json:"categories"`
	Tags          map[string]int `json:"tags"`
	Authors       map[string]int `json:"authors"`
	PricingModels map[string]int `json:"pricingModels"`
	Ratings       map[string]int `json:"ratings"`
}

// NewMarketplace creates a new marketplace instance
func NewMarketplace(config MarketplaceConfig) *Marketplace {
	if config.FeaturedAddonsCount == 0 {
		config.FeaturedAddonsCount = 10
	}
	if config.PopularThreshold == 0 {
		config.PopularThreshold = 1000
	}
	if config.TrendingDays == 0 {
		config.TrendingDays = 7
	}
	if config.SearchResultsLimit == 0 {
		config.SearchResultsLimit = 50
	}
	if config.MinRatingDisplay == 0 {
		config.MinRatingDisplay = 1.0
	}

	marketplace := &Marketplace{
		versioningSystem: NewVersioningSystem(VersioningConfig{}),
		reviewSystem:     NewReviewSystem(ReviewConfig{}),
		addons:           make(map[string]*MarketplaceAddon),
		categories:       make(map[string]*Category),
		collections:      make(map[string]*Collection),
		config:           config,
	}

	// Initialize default categories
	marketplace.initializeDefaultCategories()

	return marketplace
}

// SearchAddons searches for addons based on filters
func (m *Marketplace) SearchAddons(ctx context.Context, filters SearchFilters) (*SearchResult, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	startTime := time.Now()

	// Get all published addons
	var candidates []*MarketplaceAddon
	for _, addon := range m.addons {
		if addon.Status == AddonStatusPublished && addon.Visibility == AddonVisibilityPublic {
			candidates = append(candidates, addon)
		}
	}

	// Apply filters
	filtered := m.applyFilters(candidates, filters)

	// Sort results
	m.sortAddons(filtered, filters.SortBy, filters.SortOrder)

	// Calculate pagination
	total := len(filtered)
	limit := filters.Limit
	if limit == 0 || limit > m.config.SearchResultsLimit {
		limit = m.config.SearchResultsLimit
	}

	offset := filters.Offset
	if offset > total {
		offset = total
	}

	end := offset + limit
	if end > total {
		end = total
	}

	// Get page results
	pageResults := filtered[offset:end]

	// Generate facets
	facets := m.generateFacets(candidates)

	result := &SearchResult{
		Addons:     pageResults,
		Total:      total,
		Page:       (offset / limit) + 1,
		PerPage:    limit,
		TotalPages: (total + limit - 1) / limit,
		Facets:     facets,
		SearchTime: time.Since(startTime),
	}

	return result, nil
}

// GetFeaturedAddons returns featured addons
func (m *Marketplace) GetFeaturedAddons(ctx context.Context) ([]*MarketplaceAddon, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var featured []*MarketplaceAddon
	for _, addon := range m.addons {
		if addon.Status == AddonStatusPublished && addon.Visibility == AddonVisibilityPublic {
			if addon.Metrics.PopularityScore > 0.8 || addon.Metrics.Rating >= 4.5 {
				featured = append(featured, addon)
			}
		}
	}

	// Sort by popularity score
	sort.Slice(featured, func(i, j int) bool {
		return featured[i].Metrics.PopularityScore > featured[j].Metrics.PopularityScore
	})

	// Limit to configured count
	if len(featured) > m.config.FeaturedAddonsCount {
		featured = featured[:m.config.FeaturedAddonsCount]
	}

	return featured, nil
}

// GetTrendingAddons returns trending addons
func (m *Marketplace) GetTrendingAddons(ctx context.Context) ([]*MarketplaceAddon, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var trending []*MarketplaceAddon
	cutoffDate := time.Now().AddDate(0, 0, -m.config.TrendingDays)

	for _, addon := range m.addons {
		if addon.Status == AddonStatusPublished && addon.Visibility == AddonVisibilityPublic {
			if addon.Metrics.LastDownload.After(cutoffDate) && addon.Metrics.TrendingScore > 0.5 {
				trending = append(trending, addon)
			}
		}
	}

	// Sort by trending score
	sort.Slice(trending, func(i, j int) bool {
		return trending[i].Metrics.TrendingScore > trending[j].Metrics.TrendingScore
	})

	return trending, nil
}

// GetCategories returns all categories
func (m *Marketplace) GetCategories(ctx context.Context) ([]*Category, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var categories []*Category
	for _, category := range m.categories {
		categories = append(categories, category)
	}

	// Sort by sort order
	sort.Slice(categories, func(i, j int) bool {
		return categories[i].SortOrder < categories[j].SortOrder
	})

	return categories, nil
}

// GetCollections returns public collections
func (m *Marketplace) GetCollections(ctx context.Context) ([]*Collection, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var collections []*Collection
	for _, collection := range m.collections {
		if collection.Public {
			collections = append(collections, collection)
		}
	}

	// Sort by featured first, then by name
	sort.Slice(collections, func(i, j int) bool {
		if collections[i].Featured != collections[j].Featured {
			return collections[i].Featured
		}
		return collections[i].Name < collections[j].Name
	})

	return collections, nil
}

// Helper methods

func (m *Marketplace) applyFilters(addons []*MarketplaceAddon, filters SearchFilters) []*MarketplaceAddon {
	var filtered []*MarketplaceAddon

	for _, addon := range addons {
		if m.matchesFilters(addon, filters) {
			filtered = append(filtered, addon)
		}
	}

	return filtered
}

func (m *Marketplace) matchesFilters(addon *MarketplaceAddon, filters SearchFilters) bool {
	// Query filter
	if filters.Query != "" {
		query := strings.ToLower(filters.Query)
		if !strings.Contains(strings.ToLower(addon.Name), query) &&
			!strings.Contains(strings.ToLower(addon.Description), query) &&
			!strings.Contains(strings.ToLower(addon.Author), query) {
			// Check tags
			found := false
			for _, tag := range addon.Tags {
				if strings.Contains(strings.ToLower(tag), query) {
					found = true
					break
				}
			}
			if !found {
				return false
			}
		}
	}

	// Category filter
	if filters.Category != "" && addon.Category != filters.Category {
		return false
	}

	// Tags filter
	if len(filters.Tags) > 0 {
		hasTag := false
		for _, filterTag := range filters.Tags {
			for _, addonTag := range addon.Tags {
				if addonTag == filterTag {
					hasTag = true
					break
				}
			}
			if hasTag {
				break
			}
		}
		if !hasTag {
			return false
		}
	}

	// Author filter
	if filters.Author != "" && addon.Author != filters.Author {
		return false
	}

	// Pricing model filter
	if filters.PricingModel != "" && addon.Pricing.Model != filters.PricingModel {
		return false
	}

	// Minimum rating filter
	if filters.MinRating > 0 && addon.Metrics.Rating < filters.MinRating {
		return false
	}

	return true
}

func (m *Marketplace) sortAddons(addons []*MarketplaceAddon, sortBy SortOption, sortOrder SortOrder) {
	sort.Slice(addons, func(i, j int) bool {
		var less bool

		switch sortBy {
		case SortByPopularity:
			less = addons[i].Metrics.PopularityScore < addons[j].Metrics.PopularityScore
		case SortByRating:
			less = addons[i].Metrics.Rating < addons[j].Metrics.Rating
		case SortByDownloads:
			less = addons[i].Metrics.Downloads < addons[j].Metrics.Downloads
		case SortByCreatedAt:
			less = addons[i].CreatedAt.Before(addons[j].CreatedAt)
		case SortByUpdatedAt:
			less = addons[i].UpdatedAt.Before(addons[j].UpdatedAt)
		case SortByName:
			less = addons[i].Name < addons[j].Name
		case SortByTrending:
			less = addons[i].Metrics.TrendingScore < addons[j].Metrics.TrendingScore
		default: // SortByRelevance
			// For relevance, use a combination of rating and popularity
			scoreI := addons[i].Metrics.Rating*0.6 + addons[i].Metrics.PopularityScore*0.4
			scoreJ := addons[j].Metrics.Rating*0.6 + addons[j].Metrics.PopularityScore*0.4
			less = scoreI < scoreJ
		}

		if sortOrder == SortOrderAsc {
			return less
		}
		return !less
	})
}

func (m *Marketplace) generateFacets(addons []*MarketplaceAddon) SearchFacets {
	facets := SearchFacets{
		Categories:    make(map[string]int),
		Tags:          make(map[string]int),
		Authors: <AUTHORS>
		PricingModels: make(map[string]int),
		Ratings:       make(map[string]int),
	}

	for _, addon := range addons {
		// Categories
		facets.Categories[addon.Category]++

		// Tags
		for _, tag := range addon.Tags {
			facets.Tags[tag]++
		}

		// Authors
		facets.Authors[addon.Author]++

		// Pricing models
		facets.PricingModels[string(addon.Pricing.Model)]++

		// Ratings (grouped)
		rating := int(addon.Metrics.Rating)
		ratingGroup := fmt.Sprintf("%d+ stars", rating)
		facets.Ratings[ratingGroup]++
	}

	return facets
}

func (m *Marketplace) initializeDefaultCategories() {
	defaultCategories := []*Category{
		{ID: "productivity", Name: "Productivity", Description: "Tools to boost productivity", Icon: "briefcase", Color: "#3498db", SortOrder: 1, Featured: true},
		{ID: "communication", Name: "Communication", Description: "Communication and collaboration tools", Icon: "chat", Color: "#2ecc71", SortOrder: 2, Featured: true},
		{ID: "analytics", Name: "Analytics", Description: "Data analysis and reporting tools", Icon: "chart-bar", Color: "#e74c3c", SortOrder: 3, Featured: true},
		{ID: "marketing", Name: "Marketing", Description: "Marketing and promotion tools", Icon: "megaphone", Color: "#f39c12", SortOrder: 4, Featured: true},
		{ID: "ecommerce", Name: "E-commerce", Description: "Online store and payment tools", Icon: "shopping-cart", Color: "#9b59b6", SortOrder: 5, Featured: true},
		{ID: "social", Name: "Social Media", Description: "Social media integration tools", Icon: "share", Color: "#1abc9c", SortOrder: 6, Featured: false},
		{ID: "content", Name: "Content Management", Description: "Content creation and management", Icon: "file-text", Color: "#34495e", SortOrder: 7, Featured: false},
		{ID: "design", Name: "Design", Description: "Design and creative tools", Icon: "palette", Color: "#e67e22", SortOrder: 8, Featured: false},
		{ID: "development", Name: "Development", Description: "Developer tools and utilities", Icon: "code", Color: "#95a5a6", SortOrder: 9, Featured: false},
		{ID: "integration", Name: "Integration", Description: "Third-party service integrations", Icon: "link", Color: "#16a085", SortOrder: 10, Featured: false},
		{ID: "automation", Name: "Automation", Description: "Workflow automation tools", Icon: "cogs", Color: "#8e44ad", SortOrder: 11, Featured: false},
		{ID: "security", Name: "Security", Description: "Security and privacy tools", Icon: "shield", Color: "#c0392b", SortOrder: 12, Featured: false},
	}

	for _, category := range defaultCategories {
		m.categories[category.ID] = category
	}
}
