package main

import (
	"fmt"
	"os"

	"goVwPlatformAPI/internal/compiler"
	"goVwPlatformAPI/internal/database"

	"github.com/urfave/cli/v2"
)

func main() {
	app := &cli.App{
		Name:  "site-compiler",
		Usage: "Compile dynamic site to static files",
		Flags: []cli.Flag{
			&cli.StringFlag{
				Name:     "output",
				Aliases:  []string{"o"},
				Usage:    "Output directory for static files",
				Required: true,
			},
		},
		Action: func(c *cli.Context) error {
			db, err := database.NewConnection()
			if err != nil {
				return fmt.Errorf("failed to connect to database: %w", err)
			}
			defer db.Close()

			outputDir := c.String("output")
			if err := os.Mkdir<PERSON>ll(outputDir, 0755); err != nil {
				return fmt.Errorf("failed to create output directory: %w", err)
			}

			compiler := compiler.NewCompiler(db, outputDir)
			if err := compiler.CompileSite(); err != nil {
				return fmt.Errorf("compilation failed: %w", err)
			}

			fmt.Printf("Successfully compiled site to %s\n", outputDir)
			return nil
		},
	}

	if err := app.Run(os.Args); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
