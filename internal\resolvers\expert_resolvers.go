package resolvers

import (
	"context"
	"fmt"
	"goVwPlatformAPI/graph/model"
	"goVwPlatformAPI/internal/auth"
	"goVwPlatformAPI/internal/database"
	"time"
)

// ExpertResolver handles expert network GraphQL operations
type ExpertResolver struct {
	db *database.DB
}

// NewExpertResolver creates a new expert resolver
func NewExpertResolver(db *database.DB) *ExpertResolver {
	return &ExpertResolver{db: db}
}

// ExpertProfiles returns expert profiles with filtering
func (r *ExpertResolver) ExpertProfiles(ctx context.Context, filters *model.ExpertFiltersInput, limit *int, offset *int) ([]*model.ExpertProfile, error) {
	limitVal := 20
	if limit != nil && *limit > 0 && *limit <= 100 {
		limitVal = *limit
	}

	offsetVal := 0
	if offset != nil && *offset >= 0 {
		offsetVal = *offset
	}

	query := `
		SELECT ep.id, ep.user_id, ep.status, ep.tier, ep.expertise_areas,
		       ep.years_of_experience, ep.hourly_rate, ep.currency,
		       COALESCE(ep.bio, ''), COALESCE(ep.rating, 0), COALESCE(ep.review_count, 0),
		       ep.verification_status, ep.created_at, ep.updated_at
		FROM expert_profiles ep
		JOIN users u ON ep.user_id = u.id
		WHERE ep.status = 'ACTIVE' AND ep.verification_status = 'VERIFIED'
		ORDER BY ep.rating DESC, ep.review_count DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := r.db.Pool.Query(ctx, query, limitVal, offsetVal)
	if err != nil {
		return nil, fmt.Errorf("failed to query expert profiles: %w", err)
	}
	defer rows.Close()

	var profiles []*model.ExpertProfile
	for rows.Next() {
		var profile model.ExpertProfile
		var expertiseAreasBytes []byte

		// Note: Many fields are not available in the current model
		var status string
		var rating float64
		err := rows.Scan(
			&profile.ID, &profile.UserID, &status, &expertiseAreasBytes,
			&profile.HourlyRate, &profile.Bio, &rating,
			&profile.CreatedAt, &profile.UpdatedAt,
		)
		if err != nil {
			continue
		}

		// Note: Many fields like User, ExpertiseAreas, Languages, etc. are not available in the current model
		// These would need to be added to the GraphQL schema and regenerated models

		profiles = append(profiles, &profile)
	}

	return profiles, nil
}

// ExpertProfile returns a specific expert profile
func (r *ExpertResolver) ExpertProfile(ctx context.Context, id string) (*model.ExpertProfile, error) {
	query := `
		SELECT ep.id, ep.user_id, ep.status, ep.tier, ep.expertise_areas,
		       ep.years_of_experience, ep.hourly_rate, ep.currency,
		       COALESCE(ep.bio, ''), COALESCE(ep.rating, 0), COALESCE(ep.review_count, 0),
		       ep.verification_status, ep.created_at, ep.updated_at
		FROM expert_profiles ep
		WHERE ep.id = $1
	`

	var profile model.ExpertProfile
	// Note: Many fields are not available in the current model
	var userID string
	var status string
	var rating float64
	var expertiseAreasBytes []byte
	
	err := r.db.Pool.QueryRow(ctx, query, id).Scan(
		&profile.ID, &userID, &status, &expertiseAreasBytes,
		&profile.HourlyRate, &profile.Bio, &rating,
		&profile.CreatedAt, &profile.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get expert profile: %w", err)
	}

	// Note: Many fields like User, ExpertiseAreas, Languages, etc. are not available in the current model
	// These would need to be added to the GraphQL schema and regenerated models

	// Note: Portfolio, Certifications, Education, Skills, Reviews and other fields are not available in the current model
	// These would need to be added to the GraphQL schema and regenerated models

	return &profile, nil
}

// MyExpertProfile returns the current user's expert profile
func (r *ExpertResolver) MyExpertProfile(ctx context.Context) (*model.ExpertProfile, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	query := `
		SELECT ep.id
		FROM expert_profiles ep
		WHERE ep.user_id = $1
	`

	var profileID string
	err = r.db.Pool.QueryRow(ctx, query, userID).Scan(&profileID)
	if err != nil {
		return nil, fmt.Errorf("expert profile not found")
	}

	return r.ExpertProfile(ctx, profileID)
}

// MyExpertApplication returns the current user's expert application
func (r *ExpertResolver) MyExpertApplication(ctx context.Context) (*model.ExpertApplication, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	query := `
		SELECT ea.id, ea.status, ea.expertise_areas, ea.years_of_experience,
		       ea.hourly_rate, ea.currency, COALESCE(ea.bio, ''),
		       COALESCE(ea.cover_letter, ''), COALESCE(ea.review_notes, ''),
		       ea.reviewed_by, ea.reviewed_at, ea.approved_by, ea.approved_at,
		       COALESCE(ea.rejection_reason, ''), ea.created_at, ea.updated_at
		FROM expert_applications ea
		WHERE ea.user_id = $1
		ORDER BY ea.created_at DESC
		LIMIT 1
	`

	var app model.ExpertApplication

	// Note: Many fields are not available in the current model
	err = r.db.Pool.QueryRow(ctx, query, userID).Scan(
		&app.ID, &app.Status,
	)
	if err != nil {
		return nil, fmt.Errorf("expert application not found")
	}

	// Note: User, ExpertiseAreas, Languages, Availability, PreferredProjectTypes fields are not available in the current model
	// These would need to be added to the GraphQL schema and regenerated models

	return &app, nil
}

// SubmitExpertApplication submits an expert application
func (r *ExpertResolver) SubmitExpertApplication(ctx context.Context, input model.ExpertApplicationInput) (*model.ExpertApplication, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Check if user already has an application
	var count int
	err = r.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM expert_applications WHERE user_id = $1", userID).Scan(&count)
	if err == nil && count > 0 {
		return nil, fmt.Errorf("expert application already exists")
	}

	appID := generateID()

	query := `
		INSERT INTO expert_applications (
			id, user_id, status, expertise_areas, years_of_experience,
			hourly_rate, currency, bio, cover_letter, created_at, updated_at
		) VALUES ($1, $2, 'PENDING', $3, $4, $5, $6, $7, $8, NOW(), NOW())
	`

	// Note: ExpertiseAreas and other fields are not available in the current model
	_, err = r.db.Pool.Exec(ctx, query, appID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to create expert application: %w", err)
	}

	return r.MyExpertApplication(ctx)
}

// CreateExpertProfile creates a new expert profile
// Note: CreateExpertProfileInput type is not available in the current model
/*
func (r *ExpertResolver) CreateExpertProfile(ctx context.Context, input model.CreateExpertProfileInput) (*model.ExpertProfile, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	profile := &model.ExpertProfile{
		ID:              generateID(),
		UserID:          userID,
		Status:          "pending",
		Tier:            "basic",
		Specializations: input.Specializations,
		Bio:             input.Bio,
		HourlyRate:      input.HourlyRate,
		Availability:    input.Availability,
		Languages:       input.Languages,
		TimeZone:        input.TimeZone,
		CreatedAt:       model.Time(time.Now()),
		UpdatedAt:       model.Time(time.Now()),
	}

	return profile, nil
}
*/

// UpdateExpertProfile updates the expert profile
func (r *ExpertResolver) UpdateExpertProfile(ctx context.Context, input model.UpdateExpertProfileInput) (*model.ExpertProfile, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Get expert profile ID
	var profileID string
	err = r.db.Pool.QueryRow(ctx, "SELECT id FROM expert_profiles WHERE user_id = $1", userID).Scan(&profileID)
	if err != nil {
		return nil, fmt.Errorf("expert profile not found")
	}

	// Build update query
	query := "UPDATE expert_profiles SET updated_at = NOW()"
	args := []interface{}{}
	argCount := 0

	// Note: ExpertiseAreas, Currency, Availability, LinkedinURL, WebsiteURL fields are not available in the current model
	if input.HourlyRate != nil {
		argCount++
		query += fmt.Sprintf(", hourly_rate = $%d", argCount)
		args = append(args, *input.HourlyRate)
	}

	if input.Bio != nil {
		argCount++
		query += fmt.Sprintf(", bio = $%d", argCount)
		args = append(args, *input.Bio)
	}

	argCount++
	query += fmt.Sprintf(" WHERE id = $%d", argCount)
	args = append(args, profileID)

	_, err = r.db.Pool.Exec(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to update expert profile: %w", err)
	}

	return r.ExpertProfile(ctx, profileID)
}

// Projects returns projects for the current user
func (r *ExpertResolver) Projects(ctx context.Context, status *string, limit *int) ([]*model.Project, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	limitVal := 20
	if limit != nil && *limit > 0 && *limit <= 100 {
		limitVal = *limit
	}

	// Mock implementation - in real app would query projects table
	projects := []*model.Project{
		{
			ID:             generateID(),
			Title:          "E-commerce Website Development",
			Description:    "Build a modern e-commerce website with React and Node.js",
			Status:         "IN_PROGRESS",
			// Note: Category, HourlyRate, EstimatedHours, ActualHours, StartDate, EndDate, Deliverables, Milestones, Messages, Attachments fields are not available in the current model
			Budget:         func() *float64 { v := 5000.0; return &v }(),
			CreatedAt: time.Now().Add(-15 * 24 * time.Hour),
			UpdatedAt: time.Now().Add(-1 * time.Hour),
		}, {
			ID:             generateID(),
			Title:          "Mobile App UI Design",
			Description:    "Design user interface for iOS and Android mobile application",
			Status:         "COMPLETED",
			// Note: Category, HourlyRate, EstimatedHours, ActualHours, StartDate, EndDate, CompletedAt, Deliverables, Milestones, Messages, Attachments fields are not available in the current model
			Budget:         func() *float64 { v := 3000.0; return &v }(),
			CreatedAt: time.Now().Add(-45 * 24 * time.Hour),
			UpdatedAt: time.Now().Add(-15 * 24 * time.Hour),
		},
	}

	// Filter by status if provided
	if status != nil {
		var filtered []*model.Project
		for _, project := range projects {
			if project.Status == *status {
				filtered = append(filtered, project)
			}
		}
		projects = filtered
	}

	if len(projects) > limitVal {
		projects = projects[:limitVal]
	}

	// Note: Client and Expert fields are not available in the current model
	// These would need to be added to the GraphQL schema and regenerated models

	return projects, nil
}

// Project returns a specific project
func (r *ExpertResolver) Project(ctx context.Context, id string) (*model.Project, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Mock implementation - in real app would query projects table
	project := &model.Project{
		ID:             id,
		Title:          "E-commerce Website Development",
		Description:    "Build a modern e-commerce website with React and Node.js",
		Status:         "IN_PROGRESS",
		// Note: Category, HourlyRate, EstimatedHours, ActualHours, StartDate, EndDate fields are not available in the current model
		Budget:         func() *float64 { v := 5000.0; return &v }(),
		CreatedAt: time.Now().Add(-15 * 24 * time.Hour),
		UpdatedAt: time.Now().Add(-1 * time.Hour),
	}

	// Note: Client and Expert fields are not available in the current model
	// These would need to be added to the GraphQL schema and regenerated models

	return project, nil
}

// Helper methods
func (r *ExpertResolver) getUserByID(ctx context.Context, userID string) (*model.User, error) {
	query := `
		SELECT id, email, COALESCE(role, 'client'), COALESCE(status, 'active'),
		       COALESCE(first_name, ''), COALESCE(last_name, ''), 
		       COALESCE(email_verified, false), created_at, updated_at
		FROM users WHERE id = $1
	`

	var user model.User

	err := r.db.Pool.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Email, &user.Role, 
		&user.FirstName, &user.LastName, &user.EmailVerified,
		&user.CreatedAt, &user.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return &user, nil
}

func (r *ExpertResolver) getExpertProfileByUserID(ctx context.Context, userID string) (*model.ExpertProfile, error) {
	var profileID string
	err := r.db.Pool.QueryRow(ctx, "SELECT id FROM expert_profiles WHERE user_id = $1", userID).Scan(&profileID)
	if err != nil {
		return nil, err
	}

	return r.ExpertProfile(ctx, profileID)
}

func (r *ExpertResolver) getExpertPortfolio(ctx context.Context, expertID string) []*model.PortfolioItem {
	// Mock implementation
	return []*model.PortfolioItem{
		{
			ID:           generateID(),
			Title:        "E-commerce Platform",
			Description:  "Modern e-commerce platform built with React and Node.js",
			// Note: URL, ImageURL, Technologies, Year, Category, Featured fields are not available in the current model
		},
	}

	return []*model.PortfolioItem{}
}

func (r *ExpertResolver) getExpertCertifications(ctx context.Context, expertID string) []*model.Certification {
	// Mock implementation
	return []*model.Certification{
		{
			ID:           generateID(),
			Name:         "AWS Certified Solutions Architect",
			Issuer:       "Amazon Web Services",
			// Note: IssueDate, ExpiryDate, CredentialID, URL, Verified fields are not available in the current model
		},
	}
}

func (r *ExpertResolver) getExpertEducation(ctx context.Context, expertID string) []*model.Education {
	// Mock implementation
	return []*model.Education{
		{
			ID:           generateID(),
			Institution:  "Stanford University",
			Degree:       "Bachelor of Science",
			FieldOfStudy: "Computer Science",
			// Note: StartYear, EndYear, GPA, Description fields are not available in the current model
		},
	}
}

func (r *ExpertResolver) getExpertSkills(ctx context.Context, expertID string) []*model.Skill {
	// Mock implementation
	return []*model.Skill{
		{
			ID:                generateID(),
			Name:              "React",
			// Note: Category, Level, YearsOfExperience, Endorsed, Endorsements fields are not available in the current model
		}, {
			ID:                generateID(),
			Name:              "Node.js",
			// Note: Category, Level, YearsOfExperience, Endorsed, Endorsements fields are not available in the current model
		},
	}
}

// CreateProject creates a new project
// Note: CreateProjectInput type is not available in the current model
/*
func (r *ExpertResolver) CreateProject(ctx context.Context, input model.CreateProjectInput) (*model.Project, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	project := &model.Project{
		ID:          generateID(),
		Title:       input.Title,
		Description: input.Description,
		Status:      "active",
		Budget:      input.Budget,
		StartDate:   input.StartDate,
		EndDate:     input.EndDate,
		CreatedAt:   model.Time(time.Now()),
		UpdatedAt:   model.Time(time.Now()),
	}

	return project, nil
}
*/

// UpdateProject updates an existing project
// Note: UpdateProjectInput type is not available in the current model
/*
func (r *ExpertResolver) UpdateProject(ctx context.Context, id string, input model.UpdateProjectInput) (*model.Project, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Mock implementation - get existing project
	project := &model.Project{
		ID:          id,
		Title:       "Updated Project",
		Description: "Updated project description",
		Status:      "active",
		Budget:      50000.0,
		CreatedAt:   model.Time(time.Now().Add(-7 * 24 * time.Hour)),
		UpdatedAt:   model.Time(time.Now()),
	}

	// Update fields if provided
	if input.Title != nil {
		project.Title = *input.Title
	}
	if input.Description != nil {
		project.Description = *input.Description
	}
	if input.Status != nil {
		project.Status = *input.Status
	}
	if input.Budget != nil {
		project.Budget = *input.Budget
	}

	return project, nil
}
*/


func (r *ExpertResolver) getExpertReviews(ctx context.Context, expertID string, limit int) []*model.ExpertReview {
	// Mock implementation
	return []*model.ExpertReview{
		{
			ID:             generateID(),
			Rating:         5.0,
			// Note: Title field is not available in the ExpertReview model
			Comment:        "The expert delivered high-quality work on time and exceeded expectations.",
			// Note: Pros, Cons, WouldRecommend, IsVerified fields are not available in the current model
			CreatedAt: time.Now().Add(-30 * 24 * time.Hour),
			// Note: UpdatedAt field is not available in the ExpertReview model
		},
	}
}






