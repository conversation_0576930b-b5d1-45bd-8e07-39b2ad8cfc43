<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Addon Builder - Velocity Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <!-- Rete.js -->
    <script src="https://cdn.jsdelivr.net/npm/rete@2.0.3/build/rete.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-area-plugin@2.0.3/build/index.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-connection-plugin@2.0.2/build/index.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-render-utils@2.0.2/build/index.min.js"></script>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-velocity">
        <div class="container-fluid">
            <a class="navbar-brand text-white fw-bold" href="/">
                <i class="bi bi-lightning-charge-fill me-2"></i>Velocity Platform
            </a>
            <div class="d-flex align-items-center">
                <span class="text-white me-3">
                    <i class="bi bi-puzzle me-1"></i>Addon Builder
                </span>
                <div class="btn-group me-3">
                    <button class="btn btn-outline-light btn-sm" onclick="addonBuilder.testAddon()">
                        <i class="bi bi-play me-1"></i>Test
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="addonBuilder.saveAddon()">
                        <i class="bi bi-floppy me-1"></i>Save
                    </button>
                    <button class="btn btn-success btn-sm" onclick="addonBuilder.submitForReview()">
                        <i class="bi bi-check-circle me-1"></i>Submit for Review
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid p-0">
        <div class="row g-0">
            <!-- Node Palette -->
            <div class="col-md-2 component-palette">
                <div class="p-3">
                    <h6 class="fw-bold mb-3">
                        <i class="bi bi-diagram-3 me-2"></i>Nodes
                    </h6>
                    
                    <!-- Input Nodes -->
                    <div class="mb-4">
                        <h6 class="text-muted small mb-2">INPUTS</h6>
                        <div class="component-item" data-node="text-input">
                            <i class="bi bi-input-cursor me-2"></i>Text Input
                        </div>
                        <div class="component-item" data-node="number-input">
                            <i class="bi bi-123 me-2"></i>Number Input
                        </div>
                        <div class="component-item" data-node="api-input">
                            <i class="bi bi-cloud-download me-2"></i>API Input
                        </div>
                    </div>

                    <!-- Processing Nodes -->
                    <div class="mb-4">
                        <h6 class="text-muted small mb-2">PROCESSING</h6>
                        <div class="component-item" data-node="transform">
                            <i class="bi bi-arrow-repeat me-2"></i>Transform
                        </div>
                        <div class="component-item" data-node="filter">
                            <i class="bi bi-funnel me-2"></i>Filter
                        </div>
                        <div class="component-item" data-node="condition">
                            <i class="bi bi-question-diamond me-2"></i>Condition
                        </div>
                    </div>

                    <!-- Output Nodes -->
                    <div class="mb-4">
                        <h6 class="text-muted small mb-2">OUTPUTS</h6>
                        <div class="component-item" data-node="html-output">
                            <i class="bi bi-code me-2"></i>HTML Output
                        </div>
                        <div class="component-item" data-node="api-output">
                            <i class="bi bi-cloud-upload me-2"></i>API Output
                        </div>
                        <div class="component-item" data-node="display">
                            <i class="bi bi-display me-2"></i>Display
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rete.js Canvas -->
            <div class="col-md-8">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="fw-bold mb-0">
                            <i class="bi bi-diagram-2 me-2"></i>Addon Flow
                        </h6>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary" onclick="addonBuilder.zoomIn()">
                                <i class="bi bi-zoom-in"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="addonBuilder.zoomOut()">
                                <i class="bi bi-zoom-out"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="addonBuilder.resetView()">
                                <i class="bi bi-arrows-fullscreen"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div id="rete-container" class="addon-builder-canvas border rounded"></div>
                </div>
            </div>

            <!-- Properties Panel -->
            <div class="col-md-2 theme-editor">
                <div class="p-3">
                    <h6 class="fw-bold mb-3">
                        <i class="bi bi-gear me-2"></i>Properties
                    </h6>
                    
                    <div id="node-properties">
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-cursor me-2"></i>
                            <small>Select a node to edit properties</small>
                        </div>
                    </div>

                    <!-- Addon Settings -->
                    <div class="mt-4">
                        <h6 class="fw-bold mb-3">
                            <i class="bi bi-sliders me-2"></i>Addon Settings
                        </h6>
                        
                        <div class="mb-3">
                            <label class="form-label">Name</label>
                            <input type="text" class="form-control" id="addon-name" placeholder="My Addon">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" id="addon-description" rows="3" 
                                      placeholder="Describe what your addon does..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Version</label>
                            <input type="text" class="form-control" id="addon-version" value="1.0.0">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Category</label>
                            <select class="form-select" id="addon-category">
                                <option value="utility">Utility</option>
                                <option value="display">Display</option>
                                <option value="form">Form</option>
                                <option value="api">API Integration</option>
                                <option value="analytics">Analytics</option>
                            </select>
                        </div>
                    </div>

                    <!-- External APIs -->
                    <div class="mt-4">
                        <h6 class="fw-bold mb-3">
                            <i class="bi bi-cloud me-2"></i>External APIs
                        </h6>
                        
                        <div id="api-configs">
                            <button class="btn btn-outline-primary btn-sm w-100" onclick="addonBuilder.addAPIConfig()">
                                <i class="bi bi-plus me-1"></i>Add API
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Output Modal -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-play-circle me-2"></i>Addon Test Results
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="test-output">
                        <div class="text-center py-4">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">Running addon test...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/addon-builder.js"></script>
</body>
</html>