package resolvers

import (
	"context"
	"fmt"
	"goVwPlatformAPI/graph/model"
	"goVwPlatformAPI/internal/auth"
	"goVwPlatformAPI/internal/database"
	"time"
)

// AddonResolver handles addon-related GraphQL operations
type AddonResolver struct {
	db *database.DB
}

// NewAddonResolver creates a new addon resolver
func NewAddonResolver(db *database.DB) *AddonResolver {
	return &AddonResolver{db: db}
}

// MyAddons returns the current user's addons
func (r *AddonResolver) MyAddons(ctx context.Context) ([]*model.Addon, error) {
	userID, _ := auth.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	query := `
		SELECT id, name, COALESCE(config, '{}'), COALESCE(state, 'DEVELOPMENT'),
		       created_at, updated_at
		FROM addon_configs 
		WHERE created_by = $1 
		ORDER BY created_at DESC
	`

	rows, err := r.db.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query addons: %w", err)
	}
	defer rows.Close()

	var addons []*model.Addon
	for rows.Next() {
		var addon model.Addon
		var config string

		var state string
		err := rows.Scan(
			&addon.ID, &addon.Name, &config, &state,
			&addon.CreatedAt, &addon.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan addon: %w", err)
		}

		// Note: These fields may not exist in current model, commenting out for now
		// addon.Config = config
		// if author, err := r.getUserByID(ctx, userID); err == nil {
		//     addon.Author = author.Email
		// }
		// addon.Version = "1.0.0"
		// addon.Description = "Custom addon"
		// addon.Category = "custom"
		// addon.Tags = []string{"custom"}
		// addon.IsPublic = false
		// addon.Downloads = 0
		// addon.Rating = 0.0
		// addon.ReviewCount = 0

		addons = append(addons, &addon)
	}

	return addons, nil
}

// Addon returns a specific addon by ID
func (r *AddonResolver) Addon(ctx context.Context, id string) (*model.Addon, error) {
	userID, _ := auth.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	query := `
		SELECT ac.id, ac.name, COALESCE(ac.config, '{}'), COALESCE(ac.state, 'DEVELOPMENT'),
		       ac.created_by, ac.created_at, ac.updated_at
		FROM addon_configs ac
		WHERE ac.id = $1 AND ac.created_by = $2
	`

	var addon model.Addon
	var config string
	var authorID string

	var state string
	err := r.db.Pool.QueryRow(ctx, query, id, userID).Scan(
		&addon.ID, &addon.Name, &config, &state,
		&authorID, &addon.CreatedAt, &addon.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get addon: %w", err)
	}

	// Note: These fields may not exist in current model, commenting out for now
	// addon.Config = config
	// if author, err := r.getUserByID(ctx, authorID); err == nil {
	//     addon.Author = author.Email
	// }
	// addon.Version = "1.0.0"
	// addon.Description = "Custom addon"
	// addon.Category = "custom"
	// addon.Tags = []string{"custom"}
	// addon.IsPublic = false
	// addon.Downloads = 0
	// addon.Rating = 0.0
	// addon.ReviewCount = 0

	return &addon, nil
}

// MarketplaceAddons returns public addons from the marketplace
func (r *AddonResolver) MarketplaceAddons(ctx context.Context, category *string, limit *int, offset *int) ([]*model.Addon, error) {
	limitVal := 20
	if limit != nil && *limit > 0 && *limit <= 100 {
		limitVal = *limit
	}

	offsetVal := 0
	if offset != nil && *offset >= 0 {
		offsetVal = *offset
	}

	query := `
		SELECT ac.id, ac.name, COALESCE(ac.config, '{}'), COALESCE(ac.state, 'DEVELOPMENT'),
		       ac.created_by, ac.created_at, ac.updated_at
		FROM addon_configs ac
		WHERE ac.state = 'PRODUCTION'
		ORDER BY ac.created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := r.db.Pool.Query(ctx, query, limitVal, offsetVal)
	if err != nil {
		return nil, fmt.Errorf("failed to query marketplace addons: %w", err)
	}
	defer rows.Close()

	var addons []*model.Addon
	for rows.Next() {
		var addon model.Addon
		var config string
		var authorID string

		var state string
		err := rows.Scan(
			&addon.ID, &addon.Name, &config, &state,
			&authorID, &addon.CreatedAt, &addon.UpdatedAt,
		)
		if err != nil {
			continue
		}

		// Note: These fields may not exist in current model, commenting out for now
		// addon.Config = config
		// if author, err := r.getUserByID(ctx, authorID); err == nil {
		//     addon.Author = author.Email
		// }
		// addon.Version = "1.0.0"
		// addon.Description = "Marketplace addon"
		// addon.Category = "utility"
		// addon.Tags = []string{"marketplace", "utility"}
		// addon.IsPublic = true
		// addon.Downloads = 100 + len(addons)*50
		// addon.Rating = 4.5
		// addon.ReviewCount = 10 + len(addons)*5

		addons = append(addons, &addon)
	}

	return addons, nil
}

// CreateAddon creates a new addon
// Note: Commented out until model.CreateAddonInput is generated
/*
func (r *AddonResolver) CreateAddon(ctx context.Context, input model.CreateAddonInput) (*model.Addon, error) {
	userID, _ := auth.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	addonID := generateID()

	query := `
		INSERT INTO addon_configs (id, name, config, state, created_by, created_at, updated_at)
		VALUES ($1, $2, $3, 'DEVELOPMENT', $4, NOW(), NOW())
	`

	config := ""
	if input.Config != nil {
		config = *input.Config
	}
	if config == "" {
		config = "{}"
	}

	_, err := r.db.Pool.Exec(ctx, query, addonID, input.Name, config, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to create addon: %w", err)
	}

	// Return the created addon
	return r.Addon(ctx, addonID)
}
*/

// UpdateAddon updates an existing addon
// Note: Commented out until model.UpdateAddonInput is generated
/*
func (r *AddonResolver) UpdateAddon(ctx context.Context, id string, input model.UpdateAddonInput) (*model.Addon, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Verify user owns the addon
	var count int
	err = r.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM addon_configs WHERE id = $1 AND created_by = $2", id, userID).Scan(&count)
	if err != nil || count == 0 {
		return nil, fmt.Errorf("addon not found or access denied")
	}

	// Build update query
	query := "UPDATE addon_configs SET updated_at = NOW()"
	args := []interface{}{}
	argCount := 0

	if input.Name != nil {
		argCount++
		query += fmt.Sprintf(", name = $%d", argCount)
		args = append(args, *input.Name)
	}

	if input.Config != nil && *input.Config != "" {
		argCount++
		query += fmt.Sprintf(", config = $%d", argCount)
		args = append(args, *input.Config)
	}

	if input.State != nil {
		argCount++
		query += fmt.Sprintf(", state = $%d", argCount)
		args = append(args, *input.State)
	}

	argCount++
	query += fmt.Sprintf(" WHERE id = $%d", argCount)
	args = append(args, id)

	_, err = r.db.Pool.Exec(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to update addon: %w", err)
	}

	return r.Addon(ctx, id)
}
*/

// DeleteAddon deletes an addon
func (r *AddonResolver) DeleteAddon(ctx context.Context, id string) (bool, error) {
	userID, _ := auth.GetUserIDFromContext(ctx)
	if userID == "" {
		return false, fmt.Errorf("user not authenticated")
	}

	result, err := r.db.Pool.Exec(ctx, "DELETE FROM addon_configs WHERE id = $1 AND created_by = $2", id, userID)
	if err != nil {
		return false, fmt.Errorf("failed to delete addon: %w", err)
	}

	rowsAffected := result.RowsAffected()
	return rowsAffected > 0, nil
}

// PublishAddon publishes an addon to the marketplace
func (r *AddonResolver) PublishAddon(ctx context.Context, id string) (*model.Addon, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Update addon state to REVIEW (requires admin approval)
	_, err = r.db.Pool.Exec(ctx, `
		UPDATE addon_configs 
		SET state = 'REVIEW', updated_at = NOW() 
		WHERE id = $1 AND created_by = $2
	`, id, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to publish addon: %w", err)
	}

	return r.Addon(ctx, id)
}

// UnpublishAddon unpublishes an addon from the marketplace
func (r *AddonResolver) UnpublishAddon(ctx context.Context, id string) (*model.Addon, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	_, err = r.db.Pool.Exec(ctx, `
		UPDATE addon_configs 
		SET state = 'DEVELOPMENT', updated_at = NOW() 
		WHERE id = $1 AND created_by = $2
	`, id, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to unpublish addon: %w", err)
	}

	return r.Addon(ctx, id)
}

// InstallAddon installs an addon for the user
func (r *AddonResolver) InstallAddon(ctx context.Context, addonID string) (*model.AddonInstallation, error) {
	userID, _ := auth.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Verify addon exists and is published
	var count int
	err := r.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM addon_configs WHERE id = $1 AND state = 'PRODUCTION'", addonID).Scan(&count)
	if err != nil || count == 0 {
		return nil, fmt.Errorf("addon not found or not available")
	}

	// Create installation record (simplified)
	installation := &model.AddonInstallation{
		ID:      generateID(),
		UserID:  userID,
		AddonID: addonID,
		Status:  "installed",
		Config:  "{}",
	}

	return installation, nil
}

// UninstallAddon uninstalls an addon for the user
func (r *AddonResolver) UninstallAddon(ctx context.Context, addonID string) (bool, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return false, fmt.Errorf("user not authenticated")
	}

	// In a real implementation, would delete from addon_installations table
	return true, nil
}

// MyAddonInstallations returns the user's installed addons
func (r *AddonResolver) MyAddonInstallations(ctx context.Context) ([]*model.AddonInstallation, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Mock implementation - in real app would query addon_installations table
	installations := []*model.AddonInstallation{
		{
			ID:      generateID(),
			UserID:  userID,
			AddonID: "addon_1",
			Status:  "active",
			Config:  "{}",
		},
		{
			ID:      generateID(),
			UserID:  userID,
			AddonID: "addon_2",
			Status:  "active",
			Config:  "{}",
		},
	}

	return installations, nil
}

// AddonReviews returns reviews for an addon
func (r *AddonResolver) AddonReviews(ctx context.Context, addonID string, limit *int) ([]*model.AddonReview, error) {
	limitVal := 10
	if limit != nil && *limit > 0 && *limit <= 50 {
		limitVal = *limit
	}

	// Mock implementation - in real app would query addon_reviews table
	reviews := []*model.AddonReview{
		{
			ID:        generateID(),
			AddonID:   addonID,
			UserID:    "user_1",
			Rating:    5,
			Comment:   "This addon works perfectly and saved me a lot of time.",
			CreatedAt: time.Now().Add(-2 * 24 * time.Hour),
			UpdatedAt: time.Now().Add(-2 * 24 * time.Hour),
		},
		{
			ID:        generateID(),
			AddonID:   addonID,
			UserID:    "user_2",
			Rating:    4,
			Comment:   "Works well but the interface could be more intuitive.",
			CreatedAt: time.Now().Add(-5 * 24 * time.Hour),
			UpdatedAt: time.Now().Add(-5 * 24 * time.Hour),
		},
	}

	if len(reviews) > limitVal {
		reviews = reviews[:limitVal]
	}

	return reviews, nil
}

// CreateAddonReview creates a review for an addon
// Note: Commented out until model.CreateAddonReviewInput and model.AddonReview are generated
/*
func (r *AddonResolver) CreateAddonReview(ctx context.Context, input model.CreateAddonReviewInput) (*model.AddonReview, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Verify addon exists
	var count int
	err = r.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM addon_configs WHERE id = $1", input.AddonID).Scan(&count)
	if err != nil || count == 0 {
		return nil, fmt.Errorf("addon not found")
	}

	// Get user info
	_, err = r.getUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}

	// Create review
	review := &model.AddonReview{
		ID:        generateID(),
		AddonID:   input.AddonID,
		UserID:    userID,
		Rating:    input.Rating,
		Comment:   input.Comment,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return review, nil
}
*/

// Helper methods
func (r *AddonResolver) getUserByID(ctx context.Context, userID string) (*model.User, error) {
	query := `
		SELECT id, email, COALESCE(role, 'client'), COALESCE(status, 'active'),
		       COALESCE(first_name, ''), COALESCE(last_name, ''), 
		       COALESCE(email_verified, false), created_at, updated_at
		FROM users WHERE id = $1
	`

	var user model.User

	var status string
	err := r.db.Pool.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Email, &user.Role, &status,
		&user.FirstName, &user.LastName, &user.EmailVerified,
		&user.CreatedAt, &user.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return &user, nil
}

