import Rete from "rete";
import ConnectionPlugin from "rete-connection-plugin";
import VueRenderPlugin from "rete-vue-render-plugin";

function saveAddonConfig(config) {
    return fetch('/graphql', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
            query: `
                mutation SaveAddonConfig($input: AddonConfigInput!) {
                    saveAddonConfig(input: $input) {
                        id
                        config
                    }
                }
            `,
            variables: {
                input: {
                    name: 'current-config',
                    config: JSON.stringify(config)
                }
            }
        })
    })
    .then(response => response.json())
    .then(data => data.data.saveAddonConfig);
}

function loadAddonConfig(id) {
    return fetch('/graphql', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
            query: `
                query LoadAddonConfig($id: ID!) {
                    loadAddonConfig(id: $id) {
                        id
                        config
                    }
                }
            `,
            variables: {
                id: id
            }
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.data?.loadAddonConfig?.config) {
            return JSON.parse(data.data.loadAddonConfig.config);
        }
        return null;
    });
}

class TriggerComponent extends Rete.Component {
    constructor() {
        super("Trigger");
        this.data.component = "trigger-node";
    }

    builder(node) {
        const out = new Rete.Output("trigger", "Trigger", "trigger");
        node.addOutput(out);
        return node;
    }
}

class ActionComponent extends Rete.Component {
    constructor() {
        super("Action");
        this.data.component = "action-node";
    }

    builder(node) {
        const inp = new Rete.Input("action", "Action", "action");
        node.addInput(inp);
        return node;
    }
}

class ConditionComponent extends Rete.Component {
    constructor() {
        super("Condition");
        this.data.component = "condition-node";
    }

    builder(node) {
        const inp = new Rete.Input("condition", "Condition", "condition");
        const out = new Rete.Output("result", "Result", "boolean");
        node.addInput(inp);
        node.addOutput(out);
        return node;
    }
}

export default async function createEditor(container) {
    const editor = new Rete.NodeEditor("addon-builder@1.0.0", container);
    editor.use(ConnectionPlugin);
    editor.use(VueRenderPlugin);

    const engine = new Rete.Engine("addon-builder@1.0.0");
    
    const components = [
        new TriggerComponent(),
        new ActionComponent(),
        new ConditionComponent()
    ];

    components.forEach(c => {
        editor.register(c);
        engine.register(c);
    });

    editor.on("process nodecreated noderemoved connectioncreated connectionremoved", async () => {
        await engine.abort();
        await engine.process(editor.toJSON());
    });

    editor.view.resize();
    editor.trigger("process");

    // Initialize default nodes
    const trigger = components[0].createNode();
    trigger.position = [80, 200];
    editor.addNode(trigger);

    const action = components[1].createNode();
    action.position = [400, 200];
    editor.addNode(action);

    // Save/Load functionality
    window.saveConfig = () => {
        const data = editor.toJSON();
        saveAddonConfig(data).then(() => {
            console.log('Config saved to server');
        });
    };

    window.loadConfig = (configId) => {
        loadAddonConfig(configId).then(data => {
            if (data) {
                editor.fromJSON(data);
            }
        });
    };

    // Export functionality
    window.generateAddon = async () => {
        const data = editor.toJSON();
        // TODO: Implement API call to generate addon code
        console.log('Generating addon from:', data);
    };

    return editor;
}