package api

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

// RateLimiter handles API rate limiting
type RateLimiter struct {
	rules   map[string]*RateLimitRule
	buckets map[string]*TokenBucket
	mutex   sync.RWMutex
	cleanup *time.Ticker
}

// RateLimitRule defines rate limiting rules
type RateLimitRule struct {
	Name              string        `json:"name"`
	RequestsPerSecond int           `json:"requests_per_second"`
	RequestsPerMinute int           `json:"requests_per_minute"`
	RequestsPerHour   int           `json:"requests_per_hour"`
	RequestsPerDay    int           `json:"requests_per_day"`
	BurstSize         int           `json:"burst_size"`
	WindowSize        time.Duration `json:"window_size"`
	Enabled           bool          `json:"enabled"`
	Paths             []string      `json:"paths"`
	Methods           []string      `json:"methods"`
	UserTiers         []string      `json:"user_tiers"`
	IPWhitelist       []string      `json:"ip_whitelist"`
	IPBlacklist       []string      `json:"ip_blacklist"`
}

// TokenBucket implements token bucket algorithm for rate limiting
type TokenBucket struct {
	Capacity     int       `json:"capacity"`
	Tokens       int       `json:"tokens"`
	RefillRate   int       `json:"refill_rate"`
	LastRefill   time.Time `json:"last_refill"`
	WindowStart  time.Time `json:"window_start"`
	RequestCount int       `json:"request_count"`
	mutex        sync.Mutex
}

// RateLimitResult represents the result of a rate limit check
type RateLimitResult struct {
	Allowed           bool          `json:"allowed"`
	RemainingRequests int           `json:"remaining_requests"`
	ResetTime         time.Time     `json:"reset_time"`
	RetryAfter        time.Duration `json:"retry_after"`
	Rule              string        `json:"rule"`
	Reason            string        `json:"reason"`
}

// RateLimitContext contains information for rate limiting decisions
type RateLimitContext struct {
	UserID    string `json:"user_id"`
	UserTier  string `json:"user_tier"`
	IPAddress string `json:"ip_address"`
	Path      string `json:"path"`
	Method    string `json:"method"`
	UserAgent string `json:"user_agent"`
	APIKey    string `json:"api_key"`
	Endpoint  string `json:"endpoint"`
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter() *RateLimiter {
	rl := &RateLimiter{
		rules:   make(map[string]*RateLimitRule),
		buckets: make(map[string]*TokenBucket),
		cleanup: time.NewTicker(5 * time.Minute),
	}

	// Set default rules
	rl.setDefaultRules()

	// Start cleanup goroutine
	go rl.cleanupExpiredBuckets()

	return rl
}

// setDefaultRules sets up default rate limiting rules
func (rl *RateLimiter) setDefaultRules() {
	// Default rule for free tier users
	rl.rules["free_tier"] = &RateLimitRule{
		Name:              "free_tier",
		RequestsPerSecond: 10,
		RequestsPerMinute: 100,
		RequestsPerHour:   1000,
		RequestsPerDay:    10000,
		BurstSize:         20,
		WindowSize:        time.Minute,
		Enabled:           true,
		Paths:             []string{"*"},
		Methods:           []string{"*"},
		UserTiers:         []string{"free"},
	}

	// Pro tier rule
	rl.rules["pro_tier"] = &RateLimitRule{
		Name:              "pro_tier",
		RequestsPerSecond: 50,
		RequestsPerMinute: 1000,
		RequestsPerHour:   10000,
		RequestsPerDay:    100000,
		BurstSize:         100,
		WindowSize:        time.Minute,
		Enabled:           true,
		Paths:             []string{"*"},
		Methods:           []string{"*"},
		UserTiers:         []string{"pro"},
	}

	// Enterprise tier rule
	rl.rules["enterprise_tier"] = &RateLimitRule{
		Name:              "enterprise_tier",
		RequestsPerSecond: 200,
		RequestsPerMinute: 5000,
		RequestsPerHour:   50000,
		RequestsPerDay:    500000,
		BurstSize:         500,
		WindowSize:        time.Minute,
		Enabled:           true,
		Paths:             []string{"*"},
		Methods:           []string{"*"},
		UserTiers:         []string{"enterprise"},
	}

	// GraphQL specific rule
	rl.rules["graphql"] = &RateLimitRule{
		Name:              "graphql",
		RequestsPerSecond: 20,
		RequestsPerMinute: 500,
		RequestsPerHour:   5000,
		RequestsPerDay:    50000,
		BurstSize:         50,
		WindowSize:        time.Minute,
		Enabled:           true,
		Paths:             []string{"/graphql", "/api/graphql"},
		Methods:           []string{"POST"},
		UserTiers:         []string{"*"},
	}

	// Authentication endpoints rule
	rl.rules["auth"] = &RateLimitRule{
		Name:              "auth",
		RequestsPerSecond: 5,
		RequestsPerMinute: 20,
		RequestsPerHour:   100,
		RequestsPerDay:    500,
		BurstSize:         10,
		WindowSize:        time.Minute,
		Enabled:           true,
		Paths:             []string{"/auth/login", "/auth/register", "/auth/reset-password"},
		Methods:           []string{"POST"},
		UserTiers:         []string{"*"},
	}

	// IP-based rule for anonymous users
	rl.rules["anonymous"] = &RateLimitRule{
		Name:              "anonymous",
		RequestsPerSecond: 5,
		RequestsPerMinute: 50,
		RequestsPerHour:   500,
		RequestsPerDay:    2000,
		BurstSize:         10,
		WindowSize:        time.Minute,
		Enabled:           true,
		Paths:             []string{"*"},
		Methods:           []string{"*"},
		UserTiers:         []string{"anonymous"},
	}
}

// CheckRateLimit checks if a request should be rate limited
func (rl *RateLimiter) CheckRateLimit(ctx *RateLimitContext) *RateLimitResult {
	rl.mutex.RLock()
	defer rl.mutex.RUnlock()

	// Find applicable rule
	rule := rl.findApplicableRule(ctx)
	if rule == nil || !rule.Enabled {
		return &RateLimitResult{
			Allowed: true,
			Rule:    "no_rule",
		}
	}

	// Check IP whitelist/blacklist
	if rl.isIPBlacklisted(ctx.IPAddress, rule) {
		return &RateLimitResult{
			Allowed: false,
			Rule:    rule.Name,
			Reason:  "IP blacklisted",
		}
	}

	if rl.isIPWhitelisted(ctx.IPAddress, rule) {
		return &RateLimitResult{
			Allowed: true,
			Rule:    rule.Name,
			Reason:  "IP whitelisted",
		}
	}

	// Generate bucket key
	bucketKey := rl.generateBucketKey(ctx, rule)

	// Get or create token bucket
	bucket := rl.getOrCreateBucket(bucketKey, rule)

	// Check rate limit
	return rl.checkTokenBucket(bucket, rule)
}

// findApplicableRule finds the most specific rule for the request
func (rl *RateLimiter) findApplicableRule(ctx *RateLimitContext) *RateLimitRule {
	var bestRule *RateLimitRule
	bestScore := -1

	for _, rule := range rl.rules {
		score := rl.calculateRuleScore(ctx, rule)
		if score > bestScore {
			bestScore = score
			bestRule = rule
		}
	}

	return bestRule
}

// calculateRuleScore calculates how well a rule matches the request context
func (rl *RateLimiter) calculateRuleScore(ctx *RateLimitContext, rule *RateLimitRule) int {
	score := 0

	// Check user tier match
	if rl.matchesUserTier(ctx.UserTier, rule.UserTiers) {
		score += 10
	}

	// Check path match
	if rl.matchesPath(ctx.Path, rule.Paths) {
		score += 5
	}

	// Check method match
	if rl.matchesMethod(ctx.Method, rule.Methods) {
		score += 3
	}

	// If no matches, return -1 to indicate rule doesn't apply
	if score == 0 {
		return -1
	}

	return score
}

// matchesUserTier checks if user tier matches rule
func (rl *RateLimiter) matchesUserTier(userTier string, ruleTiers []string) bool {
	for _, tier := range ruleTiers {
		if tier == "*" || tier == userTier {
			return true
		}
	}
	return false
}

// matchesPath checks if path matches rule
func (rl *RateLimiter) matchesPath(path string, rulePaths []string) bool {
	for _, rulePath := range rulePaths {
		if rulePath == "*" || rulePath == path {
			return true
		}
		// Simple wildcard matching
		if rl.wildcardMatch(path, rulePath) {
			return true
		}
	}
	return false
}

// matchesMethod checks if method matches rule
func (rl *RateLimiter) matchesMethod(method string, ruleMethods []string) bool {
	for _, ruleMethod := range ruleMethods {
		if ruleMethod == "*" || ruleMethod == method {
			return true
		}
	}
	return false
}

// wildcardMatch performs simple wildcard matching
func (rl *RateLimiter) wildcardMatch(text, pattern string) bool {
	if pattern == "*" {
		return true
	}

	// Simple prefix/suffix matching
	if len(pattern) > 0 && pattern[len(pattern)-1] == '*' {
		prefix := pattern[:len(pattern)-1]
		return len(text) >= len(prefix) && text[:len(prefix)] == prefix
	}

	if len(pattern) > 0 && pattern[0] == '*' {
		suffix := pattern[1:]
		return len(text) >= len(suffix) && text[len(text)-len(suffix):] == suffix
	}

	return text == pattern
}

// isIPBlacklisted checks if IP is blacklisted
func (rl *RateLimiter) isIPBlacklisted(ip string, rule *RateLimitRule) bool {
	for _, blacklistedIP := range rule.IPBlacklist {
		if ip == blacklistedIP {
			return true
		}
	}
	return false
}

// isIPWhitelisted checks if IP is whitelisted
func (rl *RateLimiter) isIPWhitelisted(ip string, rule *RateLimitRule) bool {
	for _, whitelistedIP := range rule.IPWhitelist {
		if ip == whitelistedIP {
			return true
		}
	}
	return false
}

// generateBucketKey generates a unique key for the token bucket
func (rl *RateLimiter) generateBucketKey(ctx *RateLimitContext, rule *RateLimitRule) string {
	if ctx.UserID != "" {
		return fmt.Sprintf("user:%s:rule:%s", ctx.UserID, rule.Name)
	}
	if ctx.APIKey != "" {
		return fmt.Sprintf("apikey:%s:rule:%s", ctx.APIKey, rule.Name)
	}
	return fmt.Sprintf("ip:%s:rule:%s", ctx.IPAddress, rule.Name)
}

// getOrCreateBucket gets or creates a token bucket
func (rl *RateLimiter) getOrCreateBucket(key string, rule *RateLimitRule) *TokenBucket {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	bucket, exists := rl.buckets[key]
	if !exists {
		bucket = &TokenBucket{
			Capacity:     rule.BurstSize,
			Tokens:       rule.BurstSize,
			RefillRate:   rule.RequestsPerSecond,
			LastRefill:   time.Now(),
			WindowStart:  time.Now(),
			RequestCount: 0,
		}
		rl.buckets[key] = bucket
	}

	return bucket
}

// checkTokenBucket checks the token bucket and updates it
func (rl *RateLimiter) checkTokenBucket(bucket *TokenBucket, rule *RateLimitRule) *RateLimitResult {
	bucket.mutex.Lock()
	defer bucket.mutex.Unlock()

	now := time.Now()

	// Refill tokens based on time elapsed
	rl.refillTokens(bucket, now)

	// Check window-based limits
	if rl.exceedsWindowLimit(bucket, rule, now) {
		return &RateLimitResult{
			Allowed:           false,
			RemainingRequests: 0,
			ResetTime:         bucket.WindowStart.Add(rule.WindowSize),
			RetryAfter:        bucket.WindowStart.Add(rule.WindowSize).Sub(now),
			Rule:              rule.Name,
			Reason:            "Window limit exceeded",
		}
	}

	// Check if we have tokens available
	if bucket.Tokens <= 0 {
		nextRefill := bucket.LastRefill.Add(time.Second / time.Duration(bucket.RefillRate))
		return &RateLimitResult{
			Allowed:           false,
			RemainingRequests: 0,
			ResetTime:         nextRefill,
			RetryAfter:        nextRefill.Sub(now),
			Rule:              rule.Name,
			Reason:            "Rate limit exceeded",
		}
	}

	// Consume a token
	bucket.Tokens--
	bucket.RequestCount++

	return &RateLimitResult{
		Allowed:           true,
		RemainingRequests: bucket.Tokens,
		ResetTime:         bucket.LastRefill.Add(time.Second),
		Rule:              rule.Name,
	}
}

// refillTokens refills the token bucket based on elapsed time
func (rl *RateLimiter) refillTokens(bucket *TokenBucket, now time.Time) {
	elapsed := now.Sub(bucket.LastRefill)
	tokensToAdd := int(elapsed.Seconds()) * bucket.RefillRate

	if tokensToAdd > 0 {
		bucket.Tokens += tokensToAdd
		if bucket.Tokens > bucket.Capacity {
			bucket.Tokens = bucket.Capacity
		}
		bucket.LastRefill = now
	}
}

// exceedsWindowLimit checks if the request exceeds window-based limits
func (rl *RateLimiter) exceedsWindowLimit(bucket *TokenBucket, rule *RateLimitRule, now time.Time) bool {
	// Reset window if expired
	if now.Sub(bucket.WindowStart) >= rule.WindowSize {
		bucket.WindowStart = now
		bucket.RequestCount = 0
	}

	// Check minute limit
	if rule.RequestsPerMinute > 0 && bucket.RequestCount >= rule.RequestsPerMinute {
		return true
	}

	return false
}

// cleanupExpiredBuckets removes old token buckets
func (rl *RateLimiter) cleanupExpiredBuckets() {
	for range rl.cleanup.C {
		rl.mutex.Lock()
		now := time.Now()

		for key, bucket := range rl.buckets {
			// Remove buckets that haven't been used in the last hour
			if now.Sub(bucket.LastRefill) > time.Hour {
				delete(rl.buckets, key)
			}
		}

		rl.mutex.Unlock()
	}
}

// AddRule adds a new rate limiting rule
func (rl *RateLimiter) AddRule(rule *RateLimitRule) {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	rl.rules[rule.Name] = rule
}

// RemoveRule removes a rate limiting rule
func (rl *RateLimiter) RemoveRule(ruleName string) {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	delete(rl.rules, ruleName)
}

// GetRules returns all rate limiting rules
func (rl *RateLimiter) GetRules() map[string]*RateLimitRule {
	rl.mutex.RLock()
	defer rl.mutex.RUnlock()

	rules := make(map[string]*RateLimitRule)
	for name, rule := range rl.rules {
		rules[name] = rule
	}

	return rules
}

// RateLimitMiddleware creates HTTP middleware for rate limiting
func (rl *RateLimiter) RateLimitMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := &RateLimitContext{
				IPAddress: rl.getClientIP(r),
				Path:      r.URL.Path,
				Method:    r.Method,
				UserAgent: r.UserAgent(),
			}

			// Extract user information from context if available
			if userID := r.Header.Get("X-User-ID"); userID != "" {
				ctx.UserID = userID
			}

			if userTier := r.Header.Get("X-User-Tier"); userTier != "" {
				ctx.UserTier = userTier
			} else {
				ctx.UserTier = "anonymous"
			}

			if apiKey := r.Header.Get("X-API-Key"); apiKey != "" {
				ctx.APIKey = apiKey
			}

			// Check rate limit
			result := rl.CheckRateLimit(ctx)

			// Set rate limit headers
			w.Header().Set("X-RateLimit-Rule", result.Rule)
			w.Header().Set("X-RateLimit-Remaining", strconv.Itoa(result.RemainingRequests))
			w.Header().Set("X-RateLimit-Reset", strconv.FormatInt(result.ResetTime.Unix(), 10))

			if !result.Allowed {
				w.Header().Set("Retry-After", strconv.FormatInt(int64(result.RetryAfter.Seconds()), 10))
				http.Error(w, fmt.Sprintf("Rate limit exceeded: %s", result.Reason), http.StatusTooManyRequests)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// getClientIP extracts the client IP address from the request
func (rl *RateLimiter) getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// Take the first IP in the list
		if idx := strings.Index(xff, ","); idx != -1 {
			return strings.TrimSpace(xff[:idx])
		}
		return strings.TrimSpace(xff)
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// Fall back to RemoteAddr
	if idx := strings.LastIndex(r.RemoteAddr, ":"); idx != -1 {
		return r.RemoteAddr[:idx]
	}

	return r.RemoteAddr
}

// Stop stops the rate limiter and cleanup goroutine
func (rl *RateLimiter) Stop() {
	if rl.cleanup != nil {
		rl.cleanup.Stop()
	}
}
