package api

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"goVwPlatformAPI/internal/database"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"
)

// ExternalAPIProxy handles proxying requests to external APIs with authentication, rate limiting, and monitoring
type ExternalAPIProxy struct {
	db          *database.DB
	client      *http.Client
	rateLimiter *RateLimiter
	cache       *ProxyCache
	mu          sync.RWMutex
	configs     map[string]*ExternalAPIConfig
}

// ExternalAPIConfig represents configuration for an external API
type ExternalAPIConfig struct {
	ID                string                 `json:"id"`
	Name              string                 `json:"name"`
	BaseURL           string                 `json:"baseUrl"`
	AuthType          string                 `json:"authType"` // none, api_key, bearer, oauth2, basic
	AuthConfig        map[string]interface{} `json:"authConfig"`
	DefaultHeaders    map[string]string      `json:"defaultHeaders"`
	RateLimit         *APIRateLimit          `json:"rateLimit"`
	Timeout           time.Duration          `json:"timeout"`
	RetryPolicy       *APIRetryPolicy        `json:"retryPolicy"`
	CachePolicy       *APICachePolicy        `json:"cachePolicy"`
	AllowedMethods    []string               `json:"allowedMethods"`
	AllowedPaths      []string               `json:"allowedPaths"`
	BlockedPaths      []string               `json:"blockedPaths"`
	TransformRules    []*TransformRule       `json:"transformRules"`
	MonitoringEnabled bool                   `json:"monitoringEnabled"`
	CreatedAt         time.Time              `json:"createdAt"`
	UpdatedAt         time.Time              `json:"updatedAt"`
}

// APIRateLimit defines rate limiting for external API calls
type APIRateLimit struct {
	RequestsPerSecond int `json:"requestsPerSecond"`
	RequestsPerMinute int `json:"requestsPerMinute"`
	RequestsPerHour   int `json:"requestsPerHour"`
	RequestsPerDay    int `json:"requestsPerDay"`
}

// APIRetryPolicy defines retry behavior for failed API calls
type APIRetryPolicy struct {
	MaxRetries    int           `json:"maxRetries"`
	RetryDelay    time.Duration `json:"retryDelay"`
	BackoffFactor float64       `json:"backoffFactor"`
	RetryOn       []int         `json:"retryOn"` // HTTP status codes to retry on
}

// APICachePolicy defines caching behavior for API responses
type APICachePolicy struct {
	Enabled  bool          `json:"enabled"`
	TTL      time.Duration `json:"ttl"`
	CacheKey string        `json:"cacheKey"` // template for cache key generation
	VaryBy   []string      `json:"varyBy"`   // headers/params to vary cache by
}

// TransformRule defines request/response transformation
type TransformRule struct {
	Type      string                 `json:"type"`      // request_header, response_header, request_body, response_body
	Operation string                 `json:"operation"` // add, remove, replace, transform
	Target    string                 `json:"target"`    // header name, json path, etc.
	Value     interface{}            `json:"value"`
	Condition map[string]interface{} `json:"condition,omitempty"`
}

// ProxyRequest represents a request to be proxied
type ProxyRequest struct {
	ConfigID    string            `json:"configId"`
	Method      string            `json:"method"`
	Path        string            `json:"path"`
	Headers     map[string]string `json:"headers"`
	QueryParams map[string]string `json:"queryParams"`
	Body        interface{}       `json:"body"`
	UserID      string            `json:"userId"`
	TenantID    string            `json:"tenantId"`
}

// ProxyResponse represents the response from a proxied request
type ProxyResponse struct {
	StatusCode    int               `json:"statusCode"`
	Headers       map[string]string `json:"headers"`
	Body          interface{}       `json:"body"`
	Duration      time.Duration     `json:"duration"`
	FromCache     bool              `json:"fromCache"`
	RequestID     string            `json:"requestId"`
	ExternalAPIID string            `json:"externalApiId"`
}

// ProxyCache handles caching of API responses
type ProxyCache struct {
	mu    sync.RWMutex
	cache map[string]*CacheEntry
}

// CacheEntry represents a cached API response
type CacheEntry struct {
	Response  *ProxyResponse `json:"response"`
	ExpiresAt time.Time      `json:"expiresAt"`
	CreatedAt time.Time      `json:"createdAt"`
}

// NewExternalAPIProxy creates a new external API proxy
func NewExternalAPIProxy(db *database.DB, rateLimiter *RateLimiter) *ExternalAPIProxy {
	return &ExternalAPIProxy{
		db:          db,
		client:      &http.Client{Timeout: 30 * time.Second},
		rateLimiter: rateLimiter,
		cache:       NewProxyCache(),
		configs:     make(map[string]*ExternalAPIConfig),
	}
}

// NewProxyCache creates a new proxy cache
func NewProxyCache() *ProxyCache {
	cache := &ProxyCache{
		cache: make(map[string]*CacheEntry),
	}

	// Start cleanup goroutine
	go cache.cleanup()

	return cache
}

// cleanup removes expired cache entries
func (pc *ProxyCache) cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		pc.mu.Lock()
		now := time.Now()
		for key, entry := range pc.cache {
			if now.After(entry.ExpiresAt) {
				delete(pc.cache, key)
			}
		}
		pc.mu.Unlock()
	}
}

// RegisterAPIConfig registers a new external API configuration
func (proxy *ExternalAPIProxy) RegisterAPIConfig(config *ExternalAPIConfig) error {
	if config.ID == "" {
		config.ID = generateID()
	}

	config.CreatedAt = time.Now()
	config.UpdatedAt = time.Now()

	// Set defaults
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	if config.AllowedMethods == nil {
		config.AllowedMethods = []string{"GET", "POST", "PUT", "DELETE", "PATCH"}
	}

	if config.RetryPolicy == nil {
		config.RetryPolicy = &APIRetryPolicy{
			MaxRetries:    3,
			RetryDelay:    1 * time.Second,
			BackoffFactor: 2.0,
			RetryOn:       []int{500, 502, 503, 504},
		}
	}

	proxy.mu.Lock()
	proxy.configs[config.ID] = config
	proxy.mu.Unlock()

	// In a real implementation, save to database
	return nil
}

// ProxyRequest proxies a request to an external API
func (proxy *ExternalAPIProxy) ProxyRequest(ctx context.Context, req *ProxyRequest) (*ProxyResponse, error) {
	startTime := time.Now()
	requestID := generateID()

	// Get API configuration
	config, err := proxy.getAPIConfig(req.ConfigID)
	if err != nil {
		return nil, fmt.Errorf("failed to get API config: %w", err)
	}

	// Validate request
	if err := proxy.validateRequest(req, config); err != nil {
		return nil, fmt.Errorf("request validation failed: %w", err)
	}

	// Check rate limits
	if err := proxy.checkRateLimit(req, config); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	// Check cache
	if config.CachePolicy != nil && config.CachePolicy.Enabled {
		if cached := proxy.getCachedResponse(req, config); cached != nil {
			cached.RequestID = requestID
			return cached, nil
		}
	}

	// Transform request
	transformedReq, err := proxy.transformRequest(req, config)
	if err != nil {
		return nil, fmt.Errorf("request transformation failed: %w", err)
	}

	// Make the actual request with retries
	response, err := proxy.makeRequestWithRetries(ctx, transformedReq, config)
	if err != nil {
		return nil, fmt.Errorf("external API request failed: %w", err)
	}

	// Transform response
	transformedResp, err := proxy.transformResponse(response, config)
	if err != nil {
		return nil, fmt.Errorf("response transformation failed: %w", err)
	}

	transformedResp.Duration = time.Since(startTime)
	transformedResp.RequestID = requestID
	transformedResp.ExternalAPIID = config.ID

	// Cache response if enabled
	if config.CachePolicy != nil && config.CachePolicy.Enabled {
		proxy.cacheResponse(req, transformedResp, config)
	}

	// Log request if monitoring is enabled
	if config.MonitoringEnabled {
		proxy.logRequest(req, transformedResp, config)
	}

	return transformedResp, nil
}

// getAPIConfig retrieves API configuration
func (proxy *ExternalAPIProxy) getAPIConfig(configID string) (*ExternalAPIConfig, error) {
	proxy.mu.RLock()
	defer proxy.mu.RUnlock()

	config, exists := proxy.configs[configID]
	if !exists {
		return nil, fmt.Errorf("API config not found: %s", configID)
	}

	return config, nil
}

// validateRequest validates the proxy request against API configuration
func (proxy *ExternalAPIProxy) validateRequest(req *ProxyRequest, config *ExternalAPIConfig) error {
	// Check allowed methods
	methodAllowed := false
	for _, method := range config.AllowedMethods {
		if strings.EqualFold(req.Method, method) {
			methodAllowed = true
			break
		}
	}
	if !methodAllowed {
		return fmt.Errorf("method %s not allowed", req.Method)
	}

	// Check allowed paths
	if len(config.AllowedPaths) > 0 {
		pathAllowed := false
		for _, allowedPath := range config.AllowedPaths {
			if proxy.matchPath(req.Path, allowedPath) {
				pathAllowed = true
				break
			}
		}
		if !pathAllowed {
			return fmt.Errorf("path %s not allowed", req.Path)
		}
	}

	// Check blocked paths
	for _, blockedPath := range config.BlockedPaths {
		if proxy.matchPath(req.Path, blockedPath) {
			return fmt.Errorf("path %s is blocked", req.Path)
		}
	}

	return nil
}

// matchPath checks if a path matches a pattern (supports wildcards)
func (proxy *ExternalAPIProxy) matchPath(path, pattern string) bool {
	if pattern == "*" {
		return true
	}

	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(path, prefix)
	}

	return path == pattern
}

// checkRateLimit checks if the request is within rate limits
func (proxy *ExternalAPIProxy) checkRateLimit(req *ProxyRequest, config *ExternalAPIConfig) error {
	if config.RateLimit == nil {
		return nil
	}

	// Create rate limit context
	rateLimitCtx := &RateLimitContext{
		UserID:   req.UserID,
		Endpoint: fmt.Sprintf("external_api:%s", config.ID),
		Method:   req.Method,
		Path:     req.Path,
	}

	// Check rate limits using the existing rate limiter
	result := proxy.rateLimiter.CheckRateLimit(rateLimitCtx)
	if !result.Allowed {
		return fmt.Errorf("rate limit exceeded: %s", result.Reason)
	}

	return nil
}

// getCachedResponse retrieves a cached response if available
func (proxy *ExternalAPIProxy) getCachedResponse(req *ProxyRequest, config *ExternalAPIConfig) *ProxyResponse {
	cacheKey := proxy.generateCacheKey(req, config)

	proxy.cache.mu.RLock()
	defer proxy.cache.mu.RUnlock()

	entry, exists := proxy.cache.cache[cacheKey]
	if !exists || time.Now().After(entry.ExpiresAt) {
		return nil
	}

	// Clone response to avoid mutation
	response := *entry.Response
	response.FromCache = true
	return &response
}

// generateCacheKey generates a cache key for the request
func (proxy *ExternalAPIProxy) generateCacheKey(req *ProxyRequest, config *ExternalAPIConfig) string {
	key := fmt.Sprintf("%s:%s:%s", config.ID, req.Method, req.Path)

	// Add query parameters to key
	if len(req.QueryParams) > 0 {
		params := url.Values{}
		for k, v := range req.QueryParams {
			params.Add(k, v)
		}
		key += "?" + params.Encode()
	}

	// Add vary-by headers
	if config.CachePolicy != nil {
		for _, header := range config.CachePolicy.VaryBy {
			if value, exists := req.Headers[header]; exists {
				key += fmt.Sprintf(":%s=%s", header, value)
			}
		}
	}

	return key
}

// transformRequest applies request transformations
func (proxy *ExternalAPIProxy) transformRequest(req *ProxyRequest, config *ExternalAPIConfig) (*http.Request, error) {
	// Build URL
	fullURL := config.BaseURL + req.Path
	if len(req.QueryParams) > 0 {
		params := url.Values{}
		for k, v := range req.QueryParams {
			params.Add(k, v)
		}
		fullURL += "?" + params.Encode()
	}

	// Prepare body
	var bodyReader io.Reader
	if req.Body != nil {
		bodyBytes, err := json.Marshal(req.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}

	// Create HTTP request
	httpReq, err := http.NewRequest(req.Method, fullURL, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set default headers
	for key, value := range config.DefaultHeaders {
		httpReq.Header.Set(key, value)
	}

	// Set request headers
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// Apply authentication
	if err := proxy.applyAuthentication(httpReq, config); err != nil {
		return nil, fmt.Errorf("failed to apply authentication: %w", err)
	}

	// Apply transformation rules
	for _, rule := range config.TransformRules {
		if rule.Type == "request_header" {
			proxy.applyHeaderTransform(httpReq.Header, rule)
		}
	}

	return httpReq, nil
}

// applyAuthentication applies authentication to the request
func (proxy *ExternalAPIProxy) applyAuthentication(req *http.Request, config *ExternalAPIConfig) error {
	switch config.AuthType {
	case "api_key":
		if apiKey, exists := config.AuthConfig["api_key"].(string); exists {
			if headerName, exists := config.AuthConfig["header_name"].(string); exists {
				req.Header.Set(headerName, apiKey)
			} else {
				req.Header.Set("X-API-Key", apiKey)
			}
		}
	case "bearer":
		if token, exists := config.AuthConfig["token"].(string); exists {
			req.Header.Set("Authorization", "Bearer "+token)
		}
	case "basic":
		if username, exists := config.AuthConfig["username"].(string); exists {
			if password, exists := config.AuthConfig["password"].(string); exists {
				req.SetBasicAuth(username, password)
			}
		}
	case "oauth2":
		// OAuth2 implementation would go here
		return fmt.Errorf("OAuth2 authentication not implemented")
	}

	return nil
}

// applyHeaderTransform applies header transformation rules
func (proxy *ExternalAPIProxy) applyHeaderTransform(headers http.Header, rule *TransformRule) {
	switch rule.Operation {
	case "add":
		if value, ok := rule.Value.(string); ok {
			headers.Add(rule.Target, value)
		}
	case "remove":
		headers.Del(rule.Target)
	case "replace":
		if value, ok := rule.Value.(string); ok {
			headers.Set(rule.Target, value)
		}
	}
}

// makeRequestWithRetries makes the HTTP request with retry logic
func (proxy *ExternalAPIProxy) makeRequestWithRetries(ctx context.Context, req *http.Request, config *ExternalAPIConfig) (*ProxyResponse, error) {
	var lastErr error

	for attempt := 0; attempt <= config.RetryPolicy.MaxRetries; attempt++ {
		if attempt > 0 {
			// Wait before retry
			delay := time.Duration(float64(config.RetryPolicy.RetryDelay) *
				float64(attempt) * config.RetryPolicy.BackoffFactor)
			time.Sleep(delay)
		}

		// Set timeout
		reqCtx, cancel := context.WithTimeout(ctx, config.Timeout)
		req = req.WithContext(reqCtx)

		resp, err := proxy.client.Do(req)
		cancel()

		if err != nil {
			lastErr = err
			continue
		}

		// Read response body
		bodyBytes, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			lastErr = err
			continue
		}

		// Check if we should retry based on status code
		shouldRetry := false
		for _, retryCode := range config.RetryPolicy.RetryOn {
			if resp.StatusCode == retryCode {
				shouldRetry = true
				break
			}
		}

		if !shouldRetry || attempt == config.RetryPolicy.MaxRetries {
			// Parse response body
			var body interface{}
			if len(bodyBytes) > 0 {
				if err := json.Unmarshal(bodyBytes, &body); err != nil {
					// If JSON parsing fails, return as string
					body = string(bodyBytes)
				}
			}

			// Convert headers
			headers := make(map[string]string)
			for key, values := range resp.Header {
				if len(values) > 0 {
					headers[key] = values[0]
				}
			}

			return &ProxyResponse{
				StatusCode: resp.StatusCode,
				Headers:    headers,
				Body:       body,
			}, nil
		}

		lastErr = fmt.Errorf("HTTP %d", resp.StatusCode)
	}

	return nil, fmt.Errorf("request failed after %d attempts: %w",
		config.RetryPolicy.MaxRetries+1, lastErr)
}

// transformResponse applies response transformations
func (proxy *ExternalAPIProxy) transformResponse(response *ProxyResponse, config *ExternalAPIConfig) (*ProxyResponse, error) {
	// Apply transformation rules
	for _, rule := range config.TransformRules {
		if rule.Type == "response_header" {
			// Apply header transformations
		} else if rule.Type == "response_body" {
			// Apply body transformations
		}
	}

	return response, nil
}

// cacheResponse caches the response
func (proxy *ExternalAPIProxy) cacheResponse(req *ProxyRequest, response *ProxyResponse, config *ExternalAPIConfig) {
	if config.CachePolicy == nil || !config.CachePolicy.Enabled {
		return
	}

	cacheKey := proxy.generateCacheKey(req, config)

	entry := &CacheEntry{
		Response:  response,
		ExpiresAt: time.Now().Add(config.CachePolicy.TTL),
		CreatedAt: time.Now(),
	}

	proxy.cache.mu.Lock()
	proxy.cache.cache[cacheKey] = entry
	proxy.cache.mu.Unlock()
}

// logRequest logs the API request for monitoring
func (proxy *ExternalAPIProxy) logRequest(req *ProxyRequest, response *ProxyResponse, config *ExternalAPIConfig) {
	// In a real implementation, save to database or send to monitoring system
	fmt.Printf("External API call: %s %s -> %d (%v)\n",
		req.Method, req.Path, response.StatusCode, response.Duration)
}

// GetAPIConfigs returns all API configurations for a user/tenant
func (proxy *ExternalAPIProxy) GetAPIConfigs(userID, tenantID string) ([]*ExternalAPIConfig, error) {
	proxy.mu.RLock()
	defer proxy.mu.RUnlock()

	var configs []*ExternalAPIConfig
	for _, config := range proxy.configs {
		// In a real implementation, filter by user/tenant permissions
		configs = append(configs, config)
	}

	return configs, nil
}

// UpdateAPIConfig updates an existing API configuration
func (proxy *ExternalAPIProxy) UpdateAPIConfig(configID string, updates *ExternalAPIConfig) error {
	proxy.mu.Lock()
	defer proxy.mu.Unlock()

	config, exists := proxy.configs[configID]
	if !exists {
		return fmt.Errorf("API config not found: %s", configID)
	}

	// Update fields
	if updates.Name != "" {
		config.Name = updates.Name
	}
	if updates.BaseURL != "" {
		config.BaseURL = updates.BaseURL
	}
	if updates.AuthType != "" {
		config.AuthType = updates.AuthType
	}
	if updates.AuthConfig != nil {
		config.AuthConfig = updates.AuthConfig
	}
	if updates.DefaultHeaders != nil {
		config.DefaultHeaders = updates.DefaultHeaders
	}
	if updates.RateLimit != nil {
		config.RateLimit = updates.RateLimit
	}
	if updates.RetryPolicy != nil {
		config.RetryPolicy = updates.RetryPolicy
	}
	if updates.CachePolicy != nil {
		config.CachePolicy = updates.CachePolicy
	}
	if updates.AllowedMethods != nil {
		config.AllowedMethods = updates.AllowedMethods
	}
	if updates.AllowedPaths != nil {
		config.AllowedPaths = updates.AllowedPaths
	}
	if updates.BlockedPaths != nil {
		config.BlockedPaths = updates.BlockedPaths
	}
	if updates.TransformRules != nil {
		config.TransformRules = updates.TransformRules
	}
	config.MonitoringEnabled = updates.MonitoringEnabled
	config.UpdatedAt = time.Now()

	// In a real implementation, update in database
	return nil
}

// DeleteAPIConfig deletes an API configuration
func (proxy *ExternalAPIProxy) DeleteAPIConfig(configID string) error {
	proxy.mu.Lock()
	defer proxy.mu.Unlock()

	if _, exists := proxy.configs[configID]; !exists {
		return fmt.Errorf("API config not found: %s", configID)
	}

	delete(proxy.configs, configID)

	// In a real implementation, delete from database
	return nil
}

// generateID generates a random ID
func generateID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
