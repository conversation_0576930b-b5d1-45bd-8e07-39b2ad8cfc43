package dashboard

import (
	"context"
	"fmt"
	"time"

	"goVwPlatformAPI/internal/database"
	"goVwPlatformAPI/internal/subscription"
)

type DashboardService struct {
	db                  *database.DB
	subscriptionService *subscription.SubscriptionService
}

type DashboardStats struct {
	TotalSites       int                    `json:"total_sites"`
	TotalPages       int                    `json:"total_pages"`
	TotalAddons      int                    `json:"total_addons"`
	CurrentUsage     map[string]interface{} `json:"current_usage"`
	SubscriptionTier string                 `json:"subscription_tier"`
	StorageUsed      float64                `json:"storage_used"`
	BandwidthUsed    float64                `json:"bandwidth_used"`
	APICallsUsed     int                    `json:"api_calls_used"`
	RecentActivity   []*ActivityItem        `json:"recent_activity"`
}

type ActivityItem struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

func NewDashboardService(db *database.DB) *DashboardService {
	return &DashboardService{
		db:                  db,
		subscriptionService: subscription.NewSubscriptionService(db),
	}
}

// GetDashboardStats retrieves comprehensive dashboard statistics for a user
func (s *DashboardService) GetDashboardStats(ctx context.Context, userID string, tenantID string) (*DashboardStats, error) {
	stats := &DashboardStats{
		CurrentUsage:   make(map[string]interface{}),
		RecentActivity: []*ActivityItem{},
	}

	// Get total sites count
	err := s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM sites WHERE user_id = $1 AND tenant_id = $2
	`, userID, tenantID).Scan(&stats.TotalSites)
	if err != nil {
		return nil, fmt.Errorf("failed to get sites count: %w", err)
	}

	// Get total pages count
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM pages WHERE tenant_id = $1
	`, tenantID).Scan(&stats.TotalPages)
	if err != nil {
		// Pages table might not exist yet, set to 0
		stats.TotalPages = 0
	}

	// Get total addons count
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM addons WHERE tenant_id = $1
	`, tenantID).Scan(&stats.TotalAddons)
	if err != nil {
		// Addons table might not exist yet, set to 0
		stats.TotalAddons = 0
	}

	// Get subscription tier
	userSub, err := s.subscriptionService.GetUserSubscription(ctx, userID)
	if err == nil && userSub != nil {
		stats.SubscriptionTier = userSub.Tier.Name
	} else {
		stats.SubscriptionTier = "Free"
	}

	// Get current usage statistics
	stats.CurrentUsage = s.getCurrentUsageStats(ctx, userID)

	// Get storage usage (mock data for now)
	stats.StorageUsed = s.getStorageUsage(ctx, userID, tenantID)

	// Get bandwidth usage (mock data for now)
	stats.BandwidthUsed = s.getBandwidthUsage(ctx, userID, tenantID)

	// Get API calls usage
	apiCalls, err := s.subscriptionService.GetCurrentUsage(ctx, userID, "api_calls")
	if err == nil {
		stats.APICallsUsed = apiCalls
	}

	// Get recent activity
	stats.RecentActivity, err = s.getRecentActivity(ctx, userID, tenantID)
	if err != nil {
		// If we can't get activity, just return empty array
		stats.RecentActivity = []*ActivityItem{}
	}

	return stats, nil
}

// getCurrentUsageStats gets current usage for various resources
func (s *DashboardService) getCurrentUsageStats(ctx context.Context, userID string) map[string]interface{} {
	usage := make(map[string]interface{})

	// Get usage for different resource types
	resourceTypes := []string{"sites", "pages", "addons", "storage", "bandwidth", "api_calls"}

	for _, resourceType := range resourceTypes {
		currentUsage, err := s.subscriptionService.GetCurrentUsage(ctx, userID, resourceType)
		if err == nil {
			usage[resourceType] = currentUsage
		} else {
			usage[resourceType] = 0
		}
	}

	return usage
}

// getStorageUsage calculates storage usage for a user (mock implementation)
func (s *DashboardService) getStorageUsage(ctx context.Context, userID string, tenantID string) float64 {
	// This would typically calculate actual file storage usage
	// For now, return a mock value based on sites and pages
	var siteCount int
	s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM sites WHERE user_id = $1 AND tenant_id = $2
	`, userID, tenantID).Scan(&siteCount)

	// Mock calculation: 10MB per site on average
	return float64(siteCount) * 10.0
}

// getBandwidthUsage calculates bandwidth usage for a user (mock implementation)
func (s *DashboardService) getBandwidthUsage(ctx context.Context, userID string, tenantID string) float64 {
	// This would typically calculate actual bandwidth usage from logs
	// For now, return a mock value
	var siteCount int
	s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM sites WHERE user_id = $1 AND tenant_id = $2
	`, userID, tenantID).Scan(&siteCount)

	// Mock calculation: 100MB per site per month on average
	return float64(siteCount) * 100.0
}

// getRecentActivity gets recent user activity
func (s *DashboardService) getRecentActivity(ctx context.Context, userID string, tenantID string) ([]*ActivityItem, error) {
	activities := []*ActivityItem{}

	// Get recent site creations
	siteRows, err := s.db.Pool.Query(ctx, `
		SELECT id, name, created_at 
		FROM sites 
		WHERE user_id = $1 AND tenant_id = $2 
		ORDER BY created_at DESC 
		LIMIT 5
	`, userID, tenantID)

	if err == nil {
		defer siteRows.Close()
		for siteRows.Next() {
			var siteID, siteName string
			var createdAt time.Time

			if err := siteRows.Scan(&siteID, &siteName, &createdAt); err == nil {
				activities = append(activities, &ActivityItem{
					ID:          fmt.Sprintf("site_%s", siteID),
					Type:        "site_created",
					Description: fmt.Sprintf("Created site: %s", siteName),
					Timestamp:   createdAt,
					Metadata: map[string]interface{}{
						"site_id":   siteID,
						"site_name": siteName,
					},
				})
			}
		}
	}

	// Get recent subscription changes
	subRows, err := s.db.Pool.Query(ctx, `
		SELECT us.id, st.name, us.created_at
		FROM user_subscriptions us
		JOIN subscription_tiers st ON us.tier_id = st.id
		WHERE us.user_id = $1
		ORDER BY us.created_at DESC
		LIMIT 3
	`, userID)

	if err == nil {
		defer subRows.Close()
		for subRows.Next() {
			var subID, tierName string
			var createdAt time.Time

			if err := subRows.Scan(&subID, &tierName, &createdAt); err == nil {
				activities = append(activities, &ActivityItem{
					ID:          fmt.Sprintf("sub_%s", subID),
					Type:        "subscription_changed",
					Description: fmt.Sprintf("Subscription changed to: %s", tierName),
					Timestamp:   createdAt,
					Metadata: map[string]interface{}{
						"subscription_id": subID,
						"tier_name":       tierName,
					},
				})
			}
		}
	}

	// Sort activities by timestamp (most recent first)
	for i := 0; i < len(activities)-1; i++ {
		for j := i + 1; j < len(activities); j++ {
			if activities[i].Timestamp.Before(activities[j].Timestamp) {
				activities[i], activities[j] = activities[j], activities[i]
			}
		}
	}

	// Limit to 10 most recent activities
	if len(activities) > 10 {
		activities = activities[:10]
	}

	return activities, nil
}

// TrackActivity records a new activity for the user
func (s *DashboardService) TrackActivity(ctx context.Context, userID string, tenantID string, activityType string, description string, metadata map[string]interface{}) error {
	// This could be implemented to store activities in a dedicated table
	// For now, we'll rely on existing table changes to generate activities

	// Track usage for the activity
	err := s.subscriptionService.TrackUsage(ctx, userID, activityType, 1)
	if err != nil {
		return fmt.Errorf("failed to track activity usage: %w", err)
	}

	return nil
}
