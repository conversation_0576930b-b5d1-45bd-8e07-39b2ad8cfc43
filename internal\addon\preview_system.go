package addon

import (
	"encoding/json"
	"fmt"
	"html/template"
	"time"
)

// PreviewSystem handles addon preview generation and rendering
type PreviewSystem struct {
	metadataManager *MetadataManager
	serializer      *AddonSerializer
}

// NewPreviewSystem creates a new preview system
func NewPreviewSystem() *PreviewSystem {
	return &PreviewSystem{
		metadataManager: NewMetadataManager(),
		serializer:      NewAddonSerializer(),
	}
}

// PreviewData contains all data needed for addon preview
type PreviewData struct {
	Metadata    *AddonMetadata         `json:"metadata"`
	Workflow    *WorkflowGraph         `json:"workflow"`
	Settings    *AddonSettings         `json:"settings"`
	GeneratedAt time.Time              `json:"generatedAt"`
	PreviewHTML string                 `json:"previewHTML"`
	PreviewCSS  string                 `json:"previewCSS"`
	PreviewJS   string                 `json:"previewJS"`
	Screenshots []string               `json:"screenshots"`
	DemoData    map[string]interface{} `json:"demoData"`
}

// GeneratePreview creates a preview of the addon
func (p *PreviewSystem) GeneratePreview(config *AddonConfiguration) (*PreviewData, error) {
	if config == nil {
		return nil, fmt.Errorf("addon configuration is nil")
	}

	// Validate configuration
	if err := p.serializer.ValidateConfiguration(config); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	preview := &PreviewData{
		Metadata:    &config.Metadata,
		Workflow:    &config.Workflow,
		Settings:    &config.Settings,
		GeneratedAt: time.Now(),
		Screenshots: []string{},
		DemoData:    make(map[string]interface{}),
	}

	// Generate HTML preview
	html, err := p.generateHTMLPreview(config)
	if err != nil {
		return nil, fmt.Errorf("failed to generate HTML preview: %w", err)
	}
	preview.PreviewHTML = html

	// Generate CSS preview
	css, err := p.generateCSSPreview(config)
	if err != nil {
		return nil, fmt.Errorf("failed to generate CSS preview: %w", err)
	}
	preview.PreviewCSS = css

	// Generate JavaScript preview
	js, err := p.generateJSPreview(config)
	if err != nil {
		return nil, fmt.Errorf("failed to generate JS preview: %w", err)
	}
	preview.PreviewJS = js

	// Generate demo data
	demoData, err := p.generateDemoData(config)
	if err != nil {
		return nil, fmt.Errorf("failed to generate demo data: %w", err)
	}
	preview.DemoData = demoData

	return preview, nil
}

// GenerateInteractivePreview creates an interactive preview that can be embedded
func (p *PreviewSystem) GenerateInteractivePreview(config *AddonConfiguration) (string, error) {
	preview, err := p.GeneratePreview(config)
	if err != nil {
		return "", err
	}

	// Create interactive preview HTML
	interactiveHTML := fmt.Sprintf(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>%s - Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .preview-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
        }
        .preview-title {
            margin: 0;
            font-size: 24px;
        }
        .preview-description {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .preview-content {
            padding: 20px;
        }
        .preview-section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .workflow-preview {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            background: #fafafa;
            min-height: 200px;
        }
        .demo-controls {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .addon-output {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background: white;
            min-height: 100px;
            font-family: monospace;
        }
        %s
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1 class="preview-title">%s</h1>
            <p class="preview-description">%s</p>
        </div>
        <div class="preview-content">
            <div class="preview-section">
                <h2 class="section-title">Interactive Demo</h2>
                <div class="demo-controls">
                    <button class="btn" onclick="runAddonDemo()">Run Demo</button>
                    <button class="btn" onclick="resetDemo()">Reset</button>
                    <button class="btn" onclick="showWorkflow()">Show Workflow</button>
                </div>
                <div id="addon-output" class="addon-output">
                    Click "Run Demo" to see the addon in action...
                </div>
            </div>
            
            <div class="preview-section">
                <h2 class="section-title">Workflow Visualization</h2>
                <div id="workflow-preview" class="workflow-preview">
                    %s
                </div>
            </div>
            
            %s
        </div>
    </div>
    
    <script>
        const addonConfig = %s;
        const demoData = %s;
        
        %s
        
        function runAddonDemo() {
            const output = document.getElementById('addon-output');
            output.innerHTML = '<div style="color: #27ae60;">Running addon demo...</div>';
            
            // Simulate addon execution
            setTimeout(() => {
                executeAddonDemo();
            }, 1000);
        }
        
        function resetDemo() {
            const output = document.getElementById('addon-output');
            output.innerHTML = 'Click "Run Demo" to see the addon in action...';
        }
        
        function showWorkflow() {
            const workflow = document.getElementById('workflow-preview');
            workflow.innerHTML = generateWorkflowVisualization(addonConfig.workflow);
        }
        
        function executeAddonDemo() {
            const output = document.getElementById('addon-output');
            const result = simulateAddonExecution(addonConfig, demoData);
            output.innerHTML = result;
        }
        
        function generateWorkflowVisualization(workflow) {
            let html = '<div style="display: flex; flex-wrap: wrap; gap: 20px;">';
            
            for (const [nodeId, node] of Object.entries(workflow.nodes || {})) {
                html += '<div style="border: 1px solid #3498db; border-radius: 4px; padding: 10px; background: white; min-width: 120px;">';
                html += '<div style="font-weight: bold; color: #2c3e50;">' + node.label + '</div>';
                html += '<div style="font-size: 12px; color: #7f8c8d;">' + node.type + '</div>';
                html += '</div>';
            }
            
            html += '</div>';
            return html;
        }
        
        function simulateAddonExecution(config, data) {
            // Simple simulation of addon execution
            let result = '<div style="color: #27ae60; font-weight: bold;">✓ Addon executed successfully!</div>';
            result += '<div style="margin-top: 10px;"><strong>Execution Summary:</strong></div>';
            result += '<ul>';
            result += '<li>Processed ' + Object.keys(config.workflow.nodes || {}).length + ' workflow nodes</li>';
            result += '<li>Used ' + Object.keys(data).length + ' demo data points</li>';
            result += '<li>Execution time: ' + Math.floor(Math.random() * 500 + 100) + 'ms</li>';
            result += '</ul>';
            
            if (data.sampleOutput) {
                result += '<div style="margin-top: 15px;"><strong>Sample Output:</strong></div>';
                result += '<pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">';
                result += JSON.stringify(data.sampleOutput, null, 2);
                result += '</pre>';
            }
            
            return result;
        }
        
        // Initialize preview
        document.addEventListener('DOMContentLoaded', function() {
            showWorkflow();
        });
    </script>
</body>
</html>`,
		preview.Metadata.Name,
		preview.PreviewCSS,
		preview.Metadata.Name,
		preview.Metadata.Description,
		p.generateWorkflowHTML(&config.Workflow),
		preview.PreviewHTML,
		p.configToJSON(config),
		p.demoDataToJSON(preview.DemoData),
		preview.PreviewJS,
	)

	return interactiveHTML, nil
}

// generateHTMLPreview creates HTML content for the addon preview
func (p *PreviewSystem) generateHTMLPreview(config *AddonConfiguration) (string, error) {
	html := `<div class="preview-section">
        <h2 class="section-title">Addon Information</h2>
        <div class="info-grid">
            <div class="info-item">
                <strong>Version:</strong> ` + config.Metadata.Version + `
            </div>
            <div class="info-item">
                <strong>Category:</strong> ` + config.Metadata.Category + `
            </div>
            <div class="info-item">
                <strong>Author:</strong> ` + config.Metadata.Author + `
            </div>
        </div>
    </div>`

	if len(config.Metadata.Tags) > 0 {
		html += `<div class="preview-section">
            <h2 class="section-title">Tags</h2>
            <div class="tags">`
		for _, tag := range config.Metadata.Tags {
			html += `<span class="tag">` + tag + `</span>`
		}
		html += `</div></div>`
	}

	return html, nil
}

// generateCSSPreview creates CSS for the addon preview
func (p *PreviewSystem) generateCSSPreview(config *AddonConfiguration) (string, error) {
	css := `
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .info-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .tag {
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
    `
	return css, nil
}

// generateJSPreview creates JavaScript for the addon preview
func (p *PreviewSystem) generateJSPreview(config *AddonConfiguration) (string, error) {
	js := `
        // Addon preview JavaScript
        console.log('Addon preview loaded:', '` + config.Metadata.Name + `');
        
        // Add any addon-specific preview functionality here
    `
	return js, nil
}

// generateDemoData creates sample data for addon demonstration
func (p *PreviewSystem) generateDemoData(config *AddonConfiguration) (map[string]interface{}, error) {
	demoData := map[string]interface{}{
		"timestamp": time.Now().Format(time.RFC3339),
		"addonId":   config.Metadata.ID,
		"version":   config.Metadata.Version,
	}

	// Generate sample data based on workflow nodes
	if len(config.Workflow.Nodes) > 0 {
		demoData["sampleInput"] = map[string]interface{}{
			"message": "Hello from " + config.Metadata.Name,
			"value":   42,
			"enabled": true,
		}

		demoData["sampleOutput"] = map[string]interface{}{
			"result":    "success",
			"processed": len(config.Workflow.Nodes),
			"message":   "Addon executed successfully",
		}
	}

	return demoData, nil
}

// generateWorkflowHTML creates HTML representation of the workflow
func (p *PreviewSystem) generateWorkflowHTML(workflow *WorkflowGraph) string {
	if len(workflow.Nodes) == 0 {
		return "<p>No workflow nodes defined</p>"
	}

	html := `<div class="workflow-nodes">`
	for _, node := range workflow.Nodes {
		html += fmt.Sprintf(`
            <div class="workflow-node" data-node-id="%s">
                <div class="node-header">%s</div>
                <div class="node-type">%s</div>
            </div>
        `, node.ID, node.Label, node.Type)
	}
	html += `</div>`

	return html
}

// Helper methods

func (p *PreviewSystem) configToJSON(config *AddonConfiguration) string {
	data, _ := json.Marshal(config)
	return string(data)
}

func (p *PreviewSystem) demoDataToJSON(demoData map[string]interface{}) string {
	data, _ := json.Marshal(demoData)
	return string(data)
}

// GetPreviewTemplate returns a template for preview customization
func (p *PreviewSystem) GetPreviewTemplate() *template.Template {
	tmplStr := `
<!DOCTYPE html>
<html>
<head>
    <title>{{.Metadata.Name}} - Preview</title>
    <style>{{.PreviewCSS}}</style>
</head>
<body>
    <div class="addon-preview">
        <h1>{{.Metadata.Name}}</h1>
        <p>{{.Metadata.Description}}</p>
        {{.PreviewHTML}}
    </div>
    <script>{{.PreviewJS}}</script>
</body>
</html>`

	tmpl, _ := template.New("preview").Parse(tmplStr)
	return tmpl
}
