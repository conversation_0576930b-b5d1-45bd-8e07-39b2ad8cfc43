<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Snippets - Velocity Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-velocity">
        <div class="container-fluid">
            <a class="navbar-brand text-white fw-bold" href="/">
                <i class="bi bi-lightning-charge-fill me-2"></i>Velocity Platform
            </a>
            <div class="d-flex align-items-center">
                <span class="text-white me-3">
                    <i class="bi bi-code-square me-1"></i>Code Snippets
                </span>
                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createSnippetModal">
                    <i class="bi bi-plus me-1"></i>Create Snippet
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-md-3">
                <div class="card card-velocity">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-funnel me-2"></i>Filters
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Search -->
                        <div class="mb-3">
                            <label class="form-label">Search</label>
                            <input type="text" class="form-control" id="search-input" placeholder="Search snippets...">
                        </div>

                        <!-- Category Filter -->
                        <div class="mb-3">
                            <label class="form-label">Category</label>
                            <select class="form-select" id="category-filter">
                                <option value="">All Categories</option>
                                <option value="buttons">Buttons</option>
                                <option value="cards">Cards</option>
                                <option value="forms">Forms</option>
                                <option value="navigation">Navigation</option>
                                <option value="layout">Layout</option>
                                <option value="interactive">Interactive</option>
                            </select>
                        </div>

                        <!-- Type Filter -->
                        <div class="mb-3">
                            <label class="form-label">Type</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="html-filter" checked>
                                <label class="form-check-label" for="html-filter">HTML</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="css-filter" checked>
                                <label class="form-check-label" for="css-filter">CSS</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="js-filter" checked>
                                <label class="form-check-label" for="js-filter">JavaScript</label>
                            </div>
                        </div>

                        <button class="btn btn-velocity w-100" onclick="snippetManager.applyFilters()">
                            Apply Filters
                        </button>
                    </div>
                </div>
            </div>

            <!-- Snippets Grid -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Code Snippets Library</h4>
                    <div class="btn-group">
                        <button class="btn btn-outline-secondary active" data-view="grid">
                            <i class="bi bi-grid"></i>
                        </button>
                        <button class="btn btn-outline-secondary" data-view="list">
                            <i class="bi bi-list"></i>
                        </button>
                    </div>
                </div>

                <div id="snippets-container" class="row g-4">
                    <!-- Snippets will be loaded here -->
                    <div class="col-12 text-center py-5">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading snippets...</p>
                    </div>
                </div>

                <!-- Pagination -->
                <nav class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Create Snippet Modal -->
    <div class="modal fade" id="createSnippetModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>Create New Snippet
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="snippet-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Name</label>
                                    <input type="text" class="form-control" id="snippet-name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Category</label>
                                    <select class="form-select" id="snippet-category" required>
                                        <option value="buttons">Buttons</option>
                                        <option value="cards">Cards</option>
                                        <option value="forms">Forms</option>
                                        <option value="navigation">Navigation</option>
                                        <option value="layout">Layout</option>
                                        <option value="interactive">Interactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" id="snippet-description" rows="2"></textarea>
                        </div>

                        <!-- Code Tabs -->
                        <ul class="nav nav-tabs" id="code-tabs">
                            <li class="nav-item">
                                <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#html-tab">HTML</button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-bs-toggle="tab" data-bs-target="#css-tab">CSS</button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-bs-toggle="tab" data-bs-target="#js-tab">JavaScript</button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-bs-toggle="tab" data-bs-target="#preview-tab">Preview</button>
                            </li>
                        </ul>

                        <div class="tab-content border border-top-0 p-3">
                            <div class="tab-pane fade show active" id="html-tab">
                                <textarea class="form-control" id="snippet-html" rows="10" 
                                          placeholder="Enter HTML code..."></textarea>
                            </div>
                            <div class="tab-pane fade" id="css-tab">
                                <textarea class="form-control" id="snippet-css" rows="10" 
                                          placeholder="Enter CSS code..."></textarea>
                            </div>
                            <div class="tab-pane fade" id="js-tab">
                                <textarea class="form-control" id="snippet-js" rows="10" 
                                          placeholder="Enter JavaScript code..."></textarea>
                            </div>
                            <div class="tab-pane fade" id="preview-tab">
                                <div id="snippet-preview" class="border rounded p-3 bg-light">
                                    <p class="text-muted">Preview will appear here...</p>
                                </div>
                                <button type="button" class="btn btn-outline-primary mt-2" onclick="snippetManager.updatePreview()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Refresh Preview
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-velocity" onclick="snippetManager.saveSnippet()">
                        <i class="bi bi-floppy me-1"></i>Save Snippet
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Snippet Detail Modal -->
    <div class="modal fade" id="snippetDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detail-title">Snippet Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="detail-body">
                    <!-- Snippet details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-velocity" onclick="snippetManager.copyToClipboard()">
                        <i class="bi bi-clipboard me-1"></i>Copy Code
                    </button>
                    <button type="button" class="btn btn-success" onclick="snippetManager.useInBuilder()">
                        <i class="bi bi-plus-circle me-1"></i>Use in Builder
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/snippet-manager.js"></script>
</body>
</html>