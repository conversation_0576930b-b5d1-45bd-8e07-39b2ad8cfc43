{{define "content"}}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold">Become a VelocityWave Expert</h1>
                <p class="lead text-muted">Join our network of professional advisors and help UK businesses succeed</p>
            </div>
            
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form id="expertApplicationForm">
                        <!-- Professional Information -->
                        <div class="mb-4">
                            <h5 class="card-title mb-3">
                                <i class="bi bi-person-badge me-2"></i>Professional Information
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="professionalTitle" class="form-label">Professional Title *</label>
                                    <input type="text" class="form-control" id="professionalTitle" required 
                                           placeholder="e.g., Senior Business Consultant, Financial Advisor">
                                    <div class="form-text">Your current professional title or role</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="yearsExperience" class="form-label">Years of Experience *</label>
                                    <input type="number" class="form-control" id="yearsExperience" min="1" max="50" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="expertiseAreas" class="form-label">Areas of Expertise *</label>
                                <select class="form-select" id="expertiseAreas" multiple required>
                                    <option value="Business Strategy">Business Strategy</option>
                                    <option value="Financial Planning">Financial Planning</option>
                                    <option value="Marketing">Marketing & Sales</option>
                                    <option value="Operations">Operations Management</option>
                                    <option value="HR">Human Resources</option>
                                    <option value="Legal">Legal & Compliance</option>
                                    <option value="Technology">Technology & IT</option>
                                    <option value="Accounting">Accounting & Bookkeeping</option>
                                    <option value="Tax">Tax Advisory</option>
                                    <option value="Insurance">Insurance & Risk</option>
                                </select>
                                <div class="form-text">Hold Ctrl/Cmd to select multiple areas</div>
                            </div>
                        </div>
                        
                        <!-- Rates and Availability -->
                        <div class="mb-4">
                            <h5 class="card-title mb-3">
                                <i class="bi bi-currency-pound me-2"></i>Rates & Availability
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="hourlyRate" class="form-label">Hourly Rate (£)</label>
                                    <input type="number" class="form-control" id="hourlyRate" min="25" max="500" step="5"
                                           placeholder="e.g., 75">
                                    <div class="form-text">Your preferred hourly rate in GBP</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="availabilityHours" class="form-label">Available Hours/Week</label>
                                    <select class="form-select" id="availabilityHours">
                                        <option value="10">10 hours/week</option>
                                        <option value="20" selected>20 hours/week</option>
                                        <option value="30">30 hours/week</option>
                                        <option value="40">40+ hours/week</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Qualifications -->
                        <div class="mb-4">
                            <h5 class="card-title mb-3">
                                <i class="bi bi-award me-2"></i>Qualifications & Experience
                            </h5>
                            
                            <div class="mb-3">
                                <label for="qualifications" class="form-label">Professional Qualifications</label>
                                <textarea class="form-control" id="qualifications" rows="3"
                                          placeholder="List your relevant qualifications, certifications, and professional memberships..."></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="portfolioUrl" class="form-label">Portfolio URL</label>
                                    <input type="url" class="form-control" id="portfolioUrl" 
                                           placeholder="https://yourportfolio.com">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="linkedinUrl" class="form-label">LinkedIn Profile</label>
                                    <input type="url" class="form-control" id="linkedinUrl" 
                                           placeholder="https://linkedin.com/in/yourprofile">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="websiteUrl" class="form-label">Website</label>
                                    <input type="url" class="form-control" id="websiteUrl" 
                                           placeholder="https://yourwebsite.com">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Motivation -->
                        <div class="mb-4">
                            <h5 class="card-title mb-3">
                                <i class="bi bi-chat-text me-2"></i>About You
                            </h5>
                            
                            <div class="mb-3">
                                <label for="motivationStatement" class="form-label">Why do you want to join VelocityWave? *</label>
                                <textarea class="form-control" id="motivationStatement" rows="4" required
                                          placeholder="Tell us about your motivation to help UK businesses, your approach to consulting, and what makes you a great advisor..."></textarea>
                                <div class="form-text">Minimum 100 characters</div>
                            </div>
                        </div>
                        
                        <!-- Project Preferences -->
                        <div class="mb-4">
                            <h5 class="card-title mb-3">
                                <i class="bi bi-briefcase me-2"></i>Project Preferences
                            </h5>
                            
                            <div class="mb-3">
                                <label for="preferredProjectTypes" class="form-label">Preferred Project Types</label>
                                <select class="form-select" id="preferredProjectTypes" multiple>
                                    <option value="Strategy">Strategic Planning</option>
                                    <option value="Startup">Startup Consulting</option>
                                    <option value="Growth">Growth & Scaling</option>
                                    <option value="Turnaround">Business Turnaround</option>
                                    <option value="Compliance">Regulatory Compliance</option>
                                    <option value="Digital">Digital Transformation</option>
                                    <option value="Financial">Financial Restructuring</option>
                                    <option value="Operational">Operational Efficiency</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Terms and Submit -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    I agree to the <a href="/terms" target="_blank">Terms of Service</a> and 
                                    <a href="/experts/agreement" target="_blank">Expert Agreement</a>
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-send me-2"></i>Submit Application
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Application Process Info -->
            <div class="row mt-5">
                <div class="col-md-4 text-center mb-4">
                    <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-file-earmark-text fs-2 text-primary"></i>
                    </div>
                    <h5>1. Submit Application</h5>
                    <p class="text-muted">Complete the application form with your professional details</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-search fs-2 text-warning"></i>
                    </div>
                    <h5>2. Review Process</h5>
                    <p class="text-muted">Our team reviews your application within 3-5 business days</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-check-circle fs-2 text-success"></i>
                    </div>
                    <h5>3. Start Helping</h5>
                    <p class="text-muted">Once approved, start connecting with clients and growing your practice</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('expertApplicationForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = {
        professionalTitle: document.getElementById('professionalTitle').value,
        yearsExperience: parseInt(document.getElementById('yearsExperience').value),
        expertiseAreas: Array.from(document.getElementById('expertiseAreas').selectedOptions).map(option => option.value),
        hourlyRate: parseFloat(document.getElementById('hourlyRate').value) || null,
        qualifications: document.getElementById('qualifications').value ? JSON.stringify([document.getElementById('qualifications').value]) : null,
        motivationStatement: document.getElementById('motivationStatement').value,
        portfolioUrl: document.getElementById('portfolioUrl').value || null,
        linkedinUrl: document.getElementById('linkedinUrl').value || null,
        websiteUrl: document.getElementById('websiteUrl').value || null,
        availabilityHours: parseInt(document.getElementById('availabilityHours').value),
        preferredProjectTypes: Array.from(document.getElementById('preferredProjectTypes').selectedOptions).map(option => option.value)
    };
    
    try {
        const response = await fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                query: `
                    mutation CreateExpertApplication($input: CreateExpertApplicationInput!) {
                        createExpertApplication(input: $input) {
                            id
                            applicationStatus
                            submittedAt
                            professionalTitle
                        }
                    }
                `,
                variables: { input: formData }
            })
        });
        
        const data = await response.json();
        if (data.data?.createExpertApplication) {
            showSuccessMessage();
        } else {
            throw new Error(data.errors?.[0]?.message || 'Failed to submit application');
        }
    } catch (error) {
        console.error('Application submission failed:', error);
        showErrorMessage(error.message);
    }
});

function showSuccessMessage() {
    const form = document.getElementById('expertApplicationForm');
    form.innerHTML = `
        <div class="text-center py-5">
            <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 100px; height: 100px;">
                <i class="bi bi-check-circle fs-1 text-success"></i>
            </div>
            <h3 class="text-success mb-3">Application Submitted!</h3>
            <p class="lead mb-4">Thank you for your interest in joining VelocityWave as an expert advisor.</p>
            <p class="text-muted mb-4">We'll review your application within 3-5 business days and get back to you via email.</p>
            <a href="/dashboard" class="btn btn-primary">Return to Dashboard</a>
        </div>
    `;
}

function showErrorMessage(message) {
    const alertHTML = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Application Failed:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.querySelector('.card-body').insertAdjacentHTML('afterbegin', alertHTML);
}

function getAuthToken() {
    return localStorage.getItem('authToken') || sessionStorage.getItem('authToken') || '';
}
</script>
{{end}}

{{define "title"}}Become an Expert - VelocityWave{{end}}