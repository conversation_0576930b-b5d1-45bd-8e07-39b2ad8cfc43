<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - Velocity Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom Theme CSS -->
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <link href="/static/css/platform.css" rel="stylesheet">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <!-- Handlebars.js -->
    <script src="https://cdn.jsdelivr.net/npm/handlebars@4.7.8/dist/handlebars.min.js"></script>
    
    {{if .AdditionalCSS}}
    {{range .AdditionalCSS}}
    <link href="{{.}}" rel="stylesheet">
    {{end}}
    {{end}}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-velocity">
        <div class="container-fluid">
            <a class="navbar-brand text-white fw-bold" href="/">
                <i class="bi bi-lightning-charge-fill me-2"></i>
                Velocity Platform
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/builder">
                            <i class="bi bi-tools me-1"></i>Website Builder
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/addon-builder">
                            <i class="bi bi-puzzle me-1"></i>Addon Builder
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/snippets">
                            <i class="bi bi-code-square me-1"></i>Code Snippets
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {{if .User}}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>{{.User.Email}}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="/settings">
                                <i class="bi bi-gear me-2"></i>Settings
                            </a></li>
                            {{if .User.IsAdmin}}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin">
                                <i class="bi bi-shield-check me-2"></i>Admin Panel
                            </a></li>
                            {{end}}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout">
                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                    {{else}}
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/auth/login">
                            <i class="bi bi-box-arrow-in-right me-1"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/auth/register">
                            <i class="bi bi-person-plus me-1"></i>Register
                        </a>
                    </li>
                    {{end}}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Alert Messages -->
    <div id="alert-container" class="container-fluid mt-2">
        {{if .SuccessMessage}}
        <div class="alert alert-velocity-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>{{.SuccessMessage}}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {{end}}
        
        {{if .ErrorMessage}}
        <div class="alert alert-velocity-error alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>{{.ErrorMessage}}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {{end}}
        
        {{if .WarningMessage}}
        <div class="alert alert-velocity-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-circle-fill me-2"></i>{{.WarningMessage}}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {{end}}
    </div>

    <!-- Main Content -->
    <main class="flex-grow-1">
        {{.Content}}
    </main>

    <!-- Footer -->
    <footer class="bg-light border-top mt-auto">
        <div class="container-fluid py-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <small class="text-muted">
                        © 2024 Velocity Platform. Built with Bootstrap, HTMX, and Go.
                    </small>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        <a href="/docs" class="text-decoration-none me-3">Documentation</a>
                        <a href="/support" class="text-decoration-none me-3">Support</a>
                        <a href="/api/playground" class="text-decoration-none">GraphQL API</a>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Platform Configuration -->
    <script>
        window.VelocityPlatform = {
            apiEndpoint: '/query',
            version: '1.0.0',
            features: {
                addonBuilder: true,
                websiteBuilder: true,
                themeEditor: true,
                codeSnippets: true
            }
        };
    </script>
    
    <!-- Additional JavaScript -->
    {{if .AdditionalJS}}
    {{range .AdditionalJS}}
    <script src="{{.}}"></script>
    {{end}}
    {{end}}
    
    <!-- Page-specific JavaScript -->
    {{if .PageJS}}
    <script>{{.PageJS}}</script>
    {{end}}
</body>
</html>