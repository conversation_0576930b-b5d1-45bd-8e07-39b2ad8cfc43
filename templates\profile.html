<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Settings - VelocityWave Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/platform.css" rel="stylesheet">
    <link href="/api/theme.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/dashboard">
                <i class="bi bi-lightning-charge me-2"></i>VelocityWave
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/profile">
                            <i class="bi bi-person me-1"></i>Profile
                        </a>
                    </li>
                </ul>
                
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>Account
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">Profile Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form hx-post="/api/auth/logout" hx-swap="innerHTML" hx-target="body" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right me-1"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">Profile Settings</h6>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="#personal-info" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                            <i class="bi bi-person me-2"></i>Personal Information
                        </a>
                        <a href="#account-settings" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="bi bi-gear me-2"></i>Account Settings
                        </a>
                        <a href="#security" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="bi bi-shield-check me-2"></i>Security
                        </a>
                        <a href="#notifications" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="bi bi-bell me-2"></i>Notifications
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="tab-content">
                    <!-- Personal Information -->
                    <div class="tab-pane fade show active" id="personal-info">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white border-0 py-3">
                                <h5 class="mb-0 fw-bold">Personal Information</h5>
                            </div>
                            <div class="card-body">
                                <form id="personalInfoForm" hx-post="/api/profile/update" hx-swap="innerHTML" hx-target="#form-response">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="firstName" class="form-label">First Name *</label>
                                            <input type="text" class="form-control" id="firstName" name="firstName" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="lastName" class="form-label">Last Name *</label>
                                            <input type="text" class="form-control" id="lastName" name="lastName" required>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <input type="email" class="form-control" id="email" name="email" required readonly>
                                        <div class="form-text">Contact support to change your email address</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="company" class="form-label">Company Name</label>
                                        <input type="text" class="form-control" id="company" name="company">
                                    </div>

                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone">
                                    </div>

                                    <div class="mb-3">
                                        <label for="bio" class="form-label">Bio</label>
                                        <textarea class="form-control" id="bio" name="bio" rows="4" placeholder="Tell us about yourself..."></textarea>
                                    </div>

                                    <div id="form-response" class="mb-3"></div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Save Changes
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Account Settings -->
                    <div class="tab-pane fade" id="account-settings">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white border-0 py-3">
                                <h5 class="mb-0 fw-bold">Account Settings</h5>
                            </div>
                            <div class="card-body">
                                <form id="accountSettingsForm" hx-post="/api/profile/account-settings" hx-swap="innerHTML" hx-target="#account-response">
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">Timezone</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="Europe/London">London (GMT)</option>
                                            <option value="Europe/Dublin">Dublin (GMT)</option>
                                            <option value="Europe/Edinburgh">Edinburgh (GMT)</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="language" class="form-label">Language</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="en">English</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="marketingEmails" name="marketingEmails">
                                            <label class="form-check-label" for="marketingEmails">
                                                Receive marketing emails and product updates
                                            </label>
                                        </div>
                                    </div>

                                    <div id="account-response" class="mb-3"></div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Save Settings
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Security -->
                    <div class="tab-pane fade" id="security">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white border-0 py-3">
                                <h5 class="mb-0 fw-bold">Security Settings</h5>
                            </div>
                            <div class="card-body">
                                <!-- Change Password -->
                                <h6 class="fw-bold mb-3">Change Password</h6>
                                <form id="changePasswordForm" hx-post="/api/profile/change-password" hx-swap="innerHTML" hx-target="#password-response">
                                    <div class="mb-3">
                                        <label for="currentPassword" class="form-label">Current Password *</label>
                                        <input type="password" class="form-control" id="currentPassword" name="currentPassword" required>
                                    </div>

                                    <div class="row g-3 mb-3">
                                        <div class="col-md-6">
                                            <label for="newPassword" class="form-label">New Password *</label>
                                            <input type="password" class="form-control" id="newPassword" name="newPassword" required minlength="8">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="confirmNewPassword" class="form-label">Confirm New Password *</label>
                                            <input type="password" class="form-control" id="confirmNewPassword" name="confirmNewPassword" required>
                                        </div>
                                    </div>

                                    <div id="password-response" class="mb-3"></div>

                                    <button type="submit" class="btn btn-warning">
                                        <i class="bi bi-key me-2"></i>Change Password
                                    </button>
                                </form>

                                <hr class="my-4">

                                <!-- Two-Factor Authentication -->
                                <h6 class="fw-bold mb-3">Two-Factor Authentication</h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="mb-1">Add an extra layer of security to your account</p>
                                        <small class="text-muted">Status: Not enabled</small>
                                    </div>
                                    <button class="btn btn-outline-success" disabled>
                                        <i class="bi bi-shield-plus me-2"></i>Enable 2FA (Coming Soon)
                                    </button>
                                </div>

                                <hr class="my-4">

                                <!-- Active Sessions -->
                                <h6 class="fw-bold mb-3">Active Sessions</h6>
                                <div class="list-group">
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Current Session</h6>
                                                <small class="text-muted">Chrome on Windows • Active now</small>
                                            </div>
                                            <span class="badge bg-success">Current</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications -->
                    <div class="tab-pane fade" id="notifications">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white border-0 py-3">
                                <h5 class="mb-0 fw-bold">Notification Preferences</h5>
                            </div>
                            <div class="card-body">
                                <form id="notificationForm" hx-post="/api/profile/notifications" hx-swap="innerHTML" hx-target="#notification-response">
                                    <h6 class="fw-bold mb-3">Email Notifications</h6>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="emailSecurity" name="emailSecurity" checked>
                                            <label class="form-check-label" for="emailSecurity">
                                                Security alerts and login notifications
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="emailUpdates" name="emailUpdates">
                                            <label class="form-check-label" for="emailUpdates">
                                                Product updates and new features
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="emailMarketing" name="emailMarketing">
                                            <label class="form-check-label" for="emailMarketing">
                                                Marketing emails and promotions
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="emailWeekly" name="emailWeekly">
                                            <label class="form-check-label" for="emailWeekly">
                                                Weekly summary and tips
                                            </label>
                                        </div>
                                    </div>

                                    <div id="notification-response" class="mb-3"></div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Save Preferences
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script>
        // Password confirmation validation
        document.getElementById('confirmNewPassword').addEventListener('input', function() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>