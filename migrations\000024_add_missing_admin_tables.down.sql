-- Drop tables in reverse order of creation
DROP TABLE IF EXISTS project_engagements;
DROP TABLE IF EXISTS expert_profiles;
DROP TABLE IF EXISTS expert_applications;
DROP TABLE IF EXISTS user_subscriptions;
DROP TABLE IF EXISTS subscription_tiers;

-- Remove added columns from users table
ALTER TABLE users DROP COLUMN IF EXISTS notes;
ALTER TABLE users DROP COLUMN IF EXISTS last_login_at;
ALTER TABLE users DROP COLUMN IF EXISTS email_verified;
ALTER TABLE users DROP COLUMN IF EXISTS company;
ALTER TABLE users DROP COLUMN IF EXISTS last_name;
ALTER TABLE users DROP COLUMN IF EXISTS first_name;
ALTER TABLE users DROP COLUMN IF EXISTS status;
ALTER TABLE users DROP COLUMN IF EXISTS role;

-- Remove added columns from sites table (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'sites') THEN
        ALTER TABLE sites DROP COLUMN IF EXISTS user_id;
        ALTER TABLE sites DROP COLUMN IF EXISTS is_published;
    END IF;
END $$;