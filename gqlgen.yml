schema:
  - "graph/schema.graphqls"
exec:
  filename: "graph/generated/generated.go"
  package: "generated"
model:
  filename: "graph/model/models_gen.go"
  package: "model"
resolver:
  filename: "graph/resolver.go"
  package: "graph"
  type: "Resolver"

models:
  Time:
    model:
      - time.Time
  JSON:
    model:
      - interface{}
  Upload:
    model:
      - github.com/99designs/gqlgen/graphql.Upload

# Autobind tells gqlgen to search for any type names in the schema in these go packages
# If they match it will use them, otherwise it will generate them.
autobind:
  - "github.com/99designs/gqlgen/graphql"