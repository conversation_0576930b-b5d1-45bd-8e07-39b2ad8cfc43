schema:
  - "graph/schema.graphqls"
exec:
  filename: "graph/generated/generated.go"
  package: "generated"
model:
  filename: "graph/model/models_gen.go"
  package: "model"
resolver:
  filename: "graph/resolver.go"
  package: "graph"
  type: "Resolver"

models:
  Time:
    model:
      - time.Time
  JSON:
    model:
      - interface{}
  Upload:
    model:
      - github.com/99designs/gqlgen/graphql.Upload