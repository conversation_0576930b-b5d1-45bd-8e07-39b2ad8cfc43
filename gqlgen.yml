# Where are all the schema files located? globs are supported eg  src/**/*.graphqls
schema:
  - "graph/schema.graphqls"

# Where should the generated server code go?
exec:
  filename: "graph/generated/generated.go"
  package: "generated"

# Where should any generated models go?
model:
  filename: "graph/model/models_gen.go"
  package: "model"

# Where should the resolver implementations go?
resolver:
  filename: "graph/resolver.go"
  package: "graph"
  type: "Resolver"

# Optional: turn on use `gqlgen:"fieldName"` tags in your models
# struct_tag: json

# Optional: turn on to use []Thing instead of []*Thing
# omit_slice_element_pointers: false

# Optional: turn on to omit Is<Name>() methods to interface and unions
# omit_interface_checks: true

# Optional: turn on to skip generation of ComplexityRoot struct content and Complexity function
# omit_complexity: false

# Optional: turn on to not generate any file notice comments in generated files
# omit_gqlgen_file_notice: false

# Optional: turn on to exclude the gqlgen version in the generated file notice
# omit_gqlgen_version_in_file_notice: false

# Optional: turn off to make struct-type struct fields not use pointers
# struct_fields_always_pointers: true

# Optional: turn off to make resolvers return values instead of pointers for structs
# resolvers_always_return_pointers: true

# Optional: turn on to return pointers instead of values in unmarshalInput
# return_pointers_in_unmarshalinput: false

# Optional: wrap nullable input fields with Omittable
# nullable_input_omittable: true

# Optional: set to speed up generation time by not performing a final validation pass
# skip_validation: false

# Optional: set to skip running `go mod tidy` when generating server code
# skip_mod_tidy: true

# gqlgen will search for any type names in the schema in these go packages
# if they match it will use them, otherwise it will generate them.
autobind:
  - "github.com/99designs/gqlgen/graphql"

# This section declares type mapping between the GraphQL and go type systems
models:
  ID:
    model:
      - github.com/99designs/gqlgen/graphql.ID
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  UUID:
    model:
      - github.com/99designs/gqlgen/graphql.UUID
  # Use Int32 for better GraphQL spec compliance
  Int:
    model:
      - github.com/99designs/gqlgen/graphql.Int32
  Time:
    model:
      - time.Time
  JSON:
    model:
      - interface{}
  Upload:
    model:
      - github.com/99designs/gqlgen/graphql.Upload