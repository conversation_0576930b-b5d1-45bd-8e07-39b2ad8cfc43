{{define "content"}}
<div class="container py-4">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-5 fw-bold">Competitor Research Tool</h1>
        <p class="lead text-muted">Analyze your competition and identify market opportunities</p>
    </div>
    
    <!-- Research Setup -->
    <div class="row mb-5" id="researchSetup">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Start Your Competitor Analysis</h5>
                </div>
                <div class="card-body">
                    <form id="researchForm">
                        <div class="mb-3">
                            <label for="businessName" class="form-label">Your Business Name</label>
                            <input type="text" class="form-control" id="businessName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="industry" class="form-label">Industry/Sector</label>
                            <select class="form-select" id="industry" required>
                                <option value="">Select your industry</option>
                                <option value="technology">Technology</option>
                                <option value="retail">Retail</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="finance">Financial Services</option>
                                <option value="manufacturing">Manufacturing</option>
                                <option value="hospitality">Hospitality</option>
                                <option value="education">Education</option>
                                <option value="consulting">Consulting</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="location" class="form-label">Primary Location</label>
                            <input type="text" class="form-control" id="location" placeholder="e.g., London, UK" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="competitors" class="form-label">Known Competitors (optional)</label>
                            <textarea class="form-control" id="competitors" rows="3" 
                                      placeholder="List any competitors you're aware of, one per line..."></textarea>
                            <div class="form-text">We'll help you discover additional competitors</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="researchFocus" class="form-label">Research Focus</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="pricing" checked>
                                        <label class="form-check-label" for="pricing">Pricing Analysis</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="products" checked>
                                        <label class="form-check-label" for="products">Product/Service Comparison</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="marketing" checked>
                                        <label class="form-check-label" for="marketing">Marketing Strategies</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="online" checked>
                                        <label class="form-check-label" for="online">Online Presence</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="strengths">
                                        <label class="form-check-label" for="strengths">Strengths & Weaknesses</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="opportunities">
                                        <label class="form-check-label" for="opportunities">Market Opportunities</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-search me-2"></i>Start Research
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Research Results -->
    <div id="researchResults" style="display: none;">
        <div class="row">
            <!-- Research Navigation -->
            <div class="col-md-3">
                <div class="card sticky-top">
                    <div class="card-header">
                        <h6 class="mb-0">Research Sections</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <a href="#overview" class="list-group-item list-group-item-action active" onclick="showSection('overview')">
                                <i class="bi bi-graph-up me-2"></i>Market Overview
                            </a>
                            <a href="#competitors" class="list-group-item list-group-item-action" onclick="showSection('competitors')">
                                <i class="bi bi-people me-2"></i>Competitor Profiles
                            </a>
                            <a href="#pricing" class="list-group-item list-group-item-action" onclick="showSection('pricing')">
                                <i class="bi bi-currency-pound me-2"></i>Pricing Analysis
                            </a>
                            <a href="#swot" class="list-group-item list-group-item-action" onclick="showSection('swot')">
                                <i class="bi bi-grid me-2"></i>SWOT Analysis
                            </a>
                            <a href="#opportunities" class="list-group-item list-group-item-action" onclick="showSection('opportunities')">
                                <i class="bi bi-lightbulb me-2"></i>Opportunities
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0" id="sectionTitle">Market Overview</h5>
                            <button class="btn btn-outline-primary btn-sm" onclick="exportReport()">
                                <i class="bi bi-download me-1"></i>Export Report
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="sectionContent">
                            <!-- Content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let researchData = {};
let currentSection = 'overview';

document.getElementById('researchForm').addEventListener('submit', function(e) {
    e.preventDefault();
    startResearch();
});

function startResearch() {
    // Collect form data
    researchData = {
        businessName: document.getElementById('businessName').value,
        industry: document.getElementById('industry').value,
        location: document.getElementById('location').value,
        knownCompetitors: document.getElementById('competitors').value.split('\n').filter(c => c.trim()),
        researchFocus: {
            pricing: document.getElementById('pricing').checked,
            products: document.getElementById('products').checked,
            marketing: document.getElementById('marketing').checked,
            online: document.getElementById('online').checked,
            strengths: document.getElementById('strengths').checked,
            opportunities: document.getElementById('opportunities').checked
        },
        createdAt: new Date().toISOString()
    };
    
    // Hide setup and show results
    document.getElementById('researchSetup').style.display = 'none';
    document.getElementById('researchResults').style.display = 'block';
    
    // Generate mock research data
    generateResearchData();
    
    // Show first section
    showSection('overview');
}

function generateResearchData() {
    // Mock competitor data based on industry
    const mockCompetitors = {
        technology: [
            { name: 'TechCorp Ltd', website: 'techcorp.co.uk', employees: '50-100', revenue: '£2-5M' },
            { name: 'InnovateTech', website: 'innovatetech.com', employees: '20-50', revenue: '£1-2M' },
            { name: 'Digital Solutions UK', website: 'digitalsolutions.co.uk', employees: '100-200', revenue: '£5-10M' }
        ],
        retail: [
            { name: 'RetailMax', website: 'retailmax.co.uk', employees: '200-500', revenue: '£10-20M' },
            { name: 'ShopSmart', website: 'shopsmart.com', employees: '100-200', revenue: '£5-10M' },
            { name: 'Local Retail Co', website: 'localretail.co.uk', employees: '20-50', revenue: '£1-2M' }
        ]
    };
    
    researchData.competitors = mockCompetitors[researchData.industry] || mockCompetitors.technology;
    
    // Mock market data
    researchData.marketData = {
        size: '£2.5B',
        growth: '8.5%',
        segments: ['Enterprise', 'SME', 'Consumer'],
        trends: ['Digital transformation', 'Remote work', 'Sustainability']
    };
}

function showSection(sectionId) {
    currentSection = sectionId;
    
    // Update navigation
    document.querySelectorAll('.list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Update content
    const content = getSectionContent(sectionId);
    document.getElementById('sectionContent').innerHTML = content;
    document.getElementById('sectionTitle').textContent = getSectionTitle(sectionId);
}

function getSectionTitle(sectionId) {
    const titles = {
        overview: 'Market Overview',
        competitors: 'Competitor Profiles',
        pricing: 'Pricing Analysis',
        swot: 'SWOT Analysis',
        opportunities: 'Market Opportunities'
    };
    return titles[sectionId] || 'Research Section';
}

function getSectionContent(sectionId) {
    switch(sectionId) {
        case 'overview':
            return `
                <div class="row mb-4">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary">${researchData.marketData.size}</h4>
                        <small class="text-muted">Market Size</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success">${researchData.marketData.growth}</h4>
                        <small class="text-muted">Annual Growth</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info">${researchData.competitors.length}</h4>
                        <small class="text-muted">Key Competitors</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning">${researchData.marketData.segments.length}</h4>
                        <small class="text-muted">Market Segments</small>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Market Segments</h6>
                        <ul class="list-group">
                            ${researchData.marketData.segments.map(segment => `
                                <li class="list-group-item d-flex justify-content-between">
                                    ${segment}
                                    <span class="badge bg-primary">Active</span>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Key Trends</h6>
                        <ul class="list-group">
                            ${researchData.marketData.trends.map(trend => `
                                <li class="list-group-item">
                                    <i class="bi bi-arrow-up-right text-success me-2"></i>${trend}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                </div>
            `;
            
        case 'competitors':
            return `
                <div class="row">
                    ${researchData.competitors.map(competitor => `
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">${competitor.name}</h6>
                                    <p class="card-text">
                                        <strong>Website:</strong> ${competitor.website}<br>
                                        <strong>Employees:</strong> ${competitor.employees}<br>
                                        <strong>Est. Revenue:</strong> ${competitor.revenue}
                                    </p>
                                    <div class="d-flex justify-content-between">
                                        <span class="badge bg-success">Direct Competitor</span>
                                        <button class="btn btn-sm btn-outline-primary" onclick="analyzeCompetitor('${competitor.name}')">
                                            Analyze
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            
        case 'pricing':
            return `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Competitor</th>
                                <th>Basic Plan</th>
                                <th>Standard Plan</th>
                                <th>Premium Plan</th>
                                <th>Market Position</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${researchData.competitors.map(competitor => `
                                <tr>
                                    <td><strong>${competitor.name}</strong></td>
                                    <td>£${Math.floor(Math.random() * 50) + 10}/month</td>
                                    <td>£${Math.floor(Math.random() * 100) + 50}/month</td>
                                    <td>£${Math.floor(Math.random() * 200) + 100}/month</td>
                                    <td><span class="badge bg-info">Mid-market</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <div class="alert alert-info mt-4">
                    <h6 class="alert-heading">Pricing Insights</h6>
                    <ul class="mb-0">
                        <li>Average market pricing ranges from £20-300/month</li>
                        <li>Premium features command 2-3x price premium</li>
                        <li>Annual billing typically offers 15-20% discount</li>
                    </ul>
                </div>
            `;
            
        case 'swot':
            return `
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">Strengths</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Strong local presence</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Competitive pricing</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Agile decision making</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">Weaknesses</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-x-circle text-warning me-2"></i>Limited brand recognition</li>
                                    <li><i class="bi bi-x-circle text-warning me-2"></i>Smaller marketing budget</li>
                                    <li><i class="bi bi-x-circle text-warning me-2"></i>Limited resources</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">Opportunities</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-arrow-up-right text-primary me-2"></i>Digital transformation trend</li>
                                    <li><i class="bi bi-arrow-up-right text-primary me-2"></i>Underserved market segments</li>
                                    <li><i class="bi bi-arrow-up-right text-primary me-2"></i>Partnership opportunities</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">Threats</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-exclamation-triangle text-danger me-2"></i>Large competitor expansion</li>
                                    <li><i class="bi bi-exclamation-triangle text-danger me-2"></i>Economic uncertainty</li>
                                    <li><i class="bi bi-exclamation-triangle text-danger me-2"></i>Regulatory changes</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
        case 'opportunities':
            return `
                <div class="row">
                    <div class="col-md-8">
                        <h6>Identified Opportunities</h6>
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Niche Market Specialization</h6>
                                    <span class="badge bg-success">High Impact</span>
                                </div>
                                <p class="mb-1">Focus on underserved SME segment with tailored solutions</p>
                                <small>Estimated market size: £500M</small>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Digital-First Approach</h6>
                                    <span class="badge bg-warning">Medium Impact</span>
                                </div>
                                <p class="mb-1">Leverage digital channels for cost-effective customer acquisition</p>
                                <small>Potential cost savings: 30-40%</small>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Strategic Partnerships</h6>
                                    <span class="badge bg-info">Medium Impact</span>
                                </div>
                                <p class="mb-1">Partner with complementary service providers</p>
                                <small>Potential reach: 2x current market</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6>Action Plan</h6>
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Next Steps</h6>
                                <ol class="list-group list-group-numbered">
                                    <li class="list-group-item">Validate niche market demand</li>
                                    <li class="list-group-item">Develop digital marketing strategy</li>
                                    <li class="list-group-item">Identify potential partners</li>
                                    <li class="list-group-item">Create competitive positioning</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
        default:
            return '<p class="text-muted">Content not available</p>';
    }
}

function analyzeCompetitor(competitorName) {
    showAlert(`Detailed analysis for ${competitorName} coming soon!`, 'info');
}

function exportReport() {
    // Create export content
    let exportContent = `Competitor Research Report\n`;
    exportContent += `Business: ${researchData.businessName}\n`;
    exportContent += `Industry: ${researchData.industry}\n`;
    exportContent += `Generated: ${new Date().toLocaleDateString()}\n\n`;
    
    exportContent += `MARKET OVERVIEW\n`;
    exportContent += `===============\n`;
    exportContent += `Market Size: ${researchData.marketData.size}\n`;
    exportContent += `Growth Rate: ${researchData.marketData.growth}\n\n`;
    
    exportContent += `KEY COMPETITORS\n`;
    exportContent += `===============\n`;
    researchData.competitors.forEach(competitor => {
        exportContent += `${competitor.name}\n`;
        exportContent += `Website: ${competitor.website}\n`;
        exportContent += `Employees: ${competitor.employees}\n`;
        exportContent += `Revenue: ${competitor.revenue}\n\n`;
    });
    
    // Download as text file
    const blob = new Blob([exportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'competitor-research-report.txt';
    a.click();
    URL.revokeObjectURL(url);
    
    showAlert('Report exported successfully!', 'success');
}

function showAlert(message, type) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    document.body.insertAdjacentHTML('afterbegin', alertHTML);
    
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 3000);
}
</script>

<style>
.sticky-top {
    top: 20px;
}

.list-group-item-action:hover {
    background-color: rgba(0,0,0,0.05);
}
</style>
{{end}}

{{define "title"}}Competitor Research - VelocityWave{{end}}