CREATE TABLE IF NOT EXISTS api_keys (
    key_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    site_id INT NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    permissions BIGINT NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    revoked <PERSON><PERSON><PERSON><PERSON><PERSON> NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_api_keys_site ON api_keys(site_id);