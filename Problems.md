[{"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidIfaceAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidIfaceAssign"}}, "severity": 8, "message": "cannot use &executableSchema{…} (value of type *executableSchema) as graphql.ExecutableSchema value in return statement: *executableSchema does not implement graphql.ExecutableSchema (wrong type for method Complexity)\n\t\thave Complexity(context.Context, string, string, int, map[string]any) (int, bool)\n\t\twant Complexity(string, string, int, map[string]interface{}) (int, bool)", "source": "compiler", "startLineNumber": 26, "startColumn": 9, "endLineNumber": 31, "endColumn": 3}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "duplicate method GenerateSiteAPIKey", "source": "compiler", "startLineNumber": 1936, "startColumn": 2, "endLineNumber": 1936, "endColumn": 20, "relatedInformation": [{"startLineNumber": 2031, "startColumn": 2, "endLineNumber": 2031, "endColumn": 20, "message": "other declaration of method GenerateSiteAPIKey", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "duplicate method GenerateSiteAPIKey (see details)", "source": "compiler", "startLineNumber": 2031, "startColumn": 2, "endLineNumber": 2031, "endColumn": 20, "relatedInformation": [{"startLineNumber": 1936, "startColumn": 2, "endLineNumber": 1936, "endColumn": 20, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 123333, "startColumn": 17, "endLineNumber": 123333, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 123344, "startColumn": 12, "endLineNumber": 123344, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 125254, "startColumn": 17, "endLineNumber": 125254, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 125259, "startColumn": 12, "endLineNumber": 125259, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 126496, "startColumn": 17, "endLineNumber": 126496, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 126504, "startColumn": 12, "endLineNumber": 126504, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 126864, "startColumn": 17, "endLineNumber": 126864, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 126872, "startColumn": 12, "endLineNumber": 126872, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidRangeExpr", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidRangeExpr"}}, "severity": 8, "message": "cannot range over input.Settings (variable of interface type any)", "source": "compiler", "startLineNumber": 876, "startColumn": 22, "endLineNumber": 876, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.Name undefined (type model.UpdateSiteSettingsInput has no field or method Name)", "source": "compiler", "startLineNumber": 991, "startColumn": 23, "endLineNumber": 991, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.Description undefined (type model.UpdateSiteSettingsInput has no field or method Description)", "source": "compiler", "startLineNumber": 992, "startColumn": 23, "endLineNumber": 992, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.CustomDomain undefined (type model.UpdateSiteSettingsInput has no field or method CustomDomain)", "source": "compiler", "startLineNumber": 993, "startColumn": 23, "endLineNumber": 993, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.ThemeConfig undefined (type model.UpdateSiteSettingsInput has no field or method ThemeConfig)", "source": "compiler", "startLineNumber": 995, "startColumn": 13, "endLineNumber": 995, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.ThemeConfig undefined (type model.UpdateSiteSettingsInput has no field or method ThemeConfig)", "source": "compiler", "startLineNumber": 999, "startColumn": 28, "endLineNumber": 999, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.SeoConfig undefined (type model.UpdateSiteSettingsInput has no field or method SeoConfig)", "source": "compiler", "startLineNumber": 1005, "startColumn": 13, "endLineNumber": 1005, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.SeoConfig undefined (type model.UpdateSiteSettingsInput has no field or method SeoConfig)", "source": "compiler", "startLineNumber": 1009, "startColumn": 28, "endLineNumber": 1009, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.JSON", "source": "compiler", "startLineNumber": 1235, "startColumn": 21, "endLineNumber": 1235, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1260, "startColumn": 20, "endLineNumber": 1260, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1261, "startColumn": 20, "endLineNumber": 1261, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidRangeExpr", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidRangeExpr"}}, "severity": 8, "message": "cannot range over input.Features (variable of interface type any)", "source": "compiler", "startLineNumber": 1297, "startColumn": 26, "endLineNumber": 1297, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use input.Limits (variable of interface type any) as map[string]interface{} value in argument to subscriptionService.CreateTier: need type assertion", "source": "compiler", "startLineNumber": 1300, "startColumn": 96, "endLineNumber": 1300, "endColumn": 108}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1312, "startColumn": 23, "endLineNumber": 1312, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1313, "startColumn": 23, "endLineNumber": 1313, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1350, "startColumn": 31, "endLineNumber": 1350, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1351, "startColumn": 31, "endLineNumber": 1351, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1353, "startColumn": 31, "endLineNumber": 1353, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1354, "startColumn": 31, "endLineNumber": 1354, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use input.Data (variable of interface type any) as map[string]interface{} value in assignment: need type assertion", "source": "compiler", "startLineNumber": 1399, "startColumn": 10, "endLineNumber": 1399, "endColumn": 20}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GenerateSiteAPIKey already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:325:28", "source": "compiler", "startLineNumber": 1566, "startColumn": 28, "endLineNumber": 1566, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1828, "startColumn": 21, "endLineNumber": 1828, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1829, "startColumn": 21, "endLineNumber": 1829, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1864, "startColumn": 20, "endLineNumber": 1864, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1865, "startColumn": 20, "endLineNumber": 1865, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1936, "startColumn": 24, "endLineNumber": 1936, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1937, "startColumn": 24, "endLineNumber": 1937, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1966, "startColumn": 31, "endLineNumber": 1966, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1967, "startColumn": 31, "endLineNumber": 1967, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1969, "startColumn": 31, "endLineNumber": 1969, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1970, "startColumn": 31, "endLineNumber": 1970, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2112, "startColumn": 31, "endLineNumber": 2112, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2113, "startColumn": 31, "endLineNumber": 2113, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2136, "startColumn": 23, "endLineNumber": 2136, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2184, "startColumn": 24, "endLineNumber": 2184, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2185, "startColumn": 24, "endLineNumber": 2185, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2236, "startColumn": 22, "endLineNumber": 2236, "endColumn": 26}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2266, "startColumn": 23, "endLineNumber": 2266, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2267, "startColumn": 23, "endLineNumber": 2267, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2305, "startColumn": 22, "endLineNumber": 2305, "endColumn": 26}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UnexportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UnexportedName"}}, "severity": 8, "message": "name __InputValueResolver not exported by package generated", "source": "compiler", "startLineNumber": 2719, "startColumn": 45, "endLineNumber": 2719, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UnexportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UnexportedName"}}, "severity": 8, "message": "name __TypeResolver not exported by package generated", "source": "compiler", "startLineNumber": 2722, "startColumn": 39, "endLineNumber": 2722, "endColumn": 53}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Config in struct literal of type model.AddonInstallation", "source": "compiler", "startLineNumber": 344, "startColumn": 3, "endLineNumber": 344, "endColumn": 9}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Config in struct literal of type model.AddonInstallation", "source": "compiler", "startLineNumber": 375, "startColumn": 4, "endLineNumber": 375, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Config in struct literal of type model.AddonInstallation", "source": "compiler", "startLineNumber": 382, "startColumn": 4, "endLineNumber": 382, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Title in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 33, "startColumn": 3, "endLineNumber": 33, "endColumn": 8}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Content in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 35, "startColumn": 3, "endLineNumber": 35, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Title in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 55, "startColumn": 3, "endLineNumber": 55, "endColumn": 8}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.Title undefined (type model.CreateBusinessPlanInput has no field or method Title)", "source": "compiler", "startLineNumber": 55, "startColumn": 22, "endLineNumber": 55, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Content in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 57, "startColumn": 3, "endLineNumber": 57, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.Content undefined (type model.CreateBusinessPlanInput has no field or method Content)", "source": "compiler", "startLineNumber": 57, "startColumn": 22, "endLineNumber": 57, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 104, "startColumn": 25, "endLineNumber": 104, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 226, "startColumn": 21, "endLineNumber": 226, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 227, "startColumn": 21, "endLineNumber": 227, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 231, "startColumn": 20, "endLineNumber": 231, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 232, "startColumn": 20, "endLineNumber": 232, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/expert_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "default", "target": {"$mid": 1, "path": "/golang.org/x/tools/go/analysis/passes/unreachable", "scheme": "https", "authority": "pkg.go.dev"}}, "severity": 4, "message": "unreachable code", "source": "unreachable", "startLineNumber": 400, "startColumn": 2, "endLineNumber": 400, "endColumn": 33, "tags": [1]}]