[{"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "duplicate method GenerateSiteAPIKey", "source": "compiler", "startLineNumber": 1932, "startColumn": 2, "endLineNumber": 1932, "endColumn": 20, "relatedInformation": [{"startLineNumber": 2027, "startColumn": 2, "endLineNumber": 2027, "endColumn": 20, "message": "other declaration of method GenerateSiteAPIKey", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "duplicate method GenerateSiteAPIKey (see details)", "source": "compiler", "startLineNumber": 2027, "startColumn": 2, "endLineNumber": 2027, "endColumn": 20, "relatedInformation": [{"startLineNumber": 1932, "startColumn": 2, "endLineNumber": 1932, "endColumn": 20, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 119845, "startColumn": 17, "endLineNumber": 119845, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 119856, "startColumn": 12, "endLineNumber": 119856, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 121767, "startColumn": 17, "endLineNumber": 121767, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 121772, "startColumn": 12, "endLineNumber": 121772, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 123006, "startColumn": 17, "endLineNumber": 123006, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 123014, "startColumn": 12, "endLineNumber": 123014, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 123378, "startColumn": 17, "endLineNumber": 123378, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 123386, "startColumn": 12, "endLineNumber": 123386, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidConversion", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidConversion"}}, "severity": 8, "message": "cannot convert input.ThemeConfig (variable of type interface{}) to type map[string]interface{}: need type assertion", "source": "compiler", "startLineNumber": 716, "startColumn": 41, "endLineNumber": 716, "endColumn": 58}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use seoConfig (variable of type interface{}) as map[string]interface{} value in assignment: need type assertion", "source": "compiler", "startLineNumber": 721, "startColumn": 27, "endLineNumber": 721, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 744, "startColumn": 23, "endLineNumber": 744, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 745, "startColumn": 23, "endLineNumber": 745, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidRangeExpr", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidRangeExpr"}}, "severity": 8, "message": "cannot range over input.ThemeConfig (variable of type interface{})", "source": "compiler", "startLineNumber": 777, "startColumn": 22, "endLineNumber": 777, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidRangeExpr", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidRangeExpr"}}, "severity": 8, "message": "cannot range over input.SeoConfig (variable of type interface{})", "source": "compiler", "startLineNumber": 787, "startColumn": 22, "endLineNumber": 787, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 814, "startColumn": 23, "endLineNumber": 814, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 815, "startColumn": 23, "endLineNumber": 815, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidRangeExpr", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidRangeExpr"}}, "severity": 8, "message": "cannot range over input.Settings (variable of type interface{})", "source": "compiler", "startLineNumber": 941, "startColumn": 22, "endLineNumber": 941, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidRangeExpr", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidRangeExpr"}}, "severity": 8, "message": "cannot range over input.ThemeConfig (variable of type interface{})", "source": "compiler", "startLineNumber": 1064, "startColumn": 22, "endLineNumber": 1064, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidRangeExpr", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidRangeExpr"}}, "severity": 8, "message": "cannot range over input.SeoConfig (variable of type interface{})", "source": "compiler", "startLineNumber": 1074, "startColumn": 22, "endLineNumber": 1074, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1261, "startColumn": 20, "endLineNumber": 1261, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1262, "startColumn": 20, "endLineNumber": 1262, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.JSON", "source": "compiler", "startLineNumber": 1300, "startColumn": 21, "endLineNumber": 1300, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1325, "startColumn": 20, "endLineNumber": 1325, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1326, "startColumn": 20, "endLineNumber": 1326, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidRangeExpr", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidRangeExpr"}}, "severity": 8, "message": "cannot range over input.Features (variable of type interface{})", "source": "compiler", "startLineNumber": 1362, "startColumn": 26, "endLineNumber": 1362, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use input.Limits (variable of type interface{}) as map[string]interface{} value in argument to subscriptionService.CreateTier: need type assertion", "source": "compiler", "startLineNumber": 1365, "startColumn": 96, "endLineNumber": 1365, "endColumn": 108}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1377, "startColumn": 23, "endLineNumber": 1377, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1378, "startColumn": 23, "endLineNumber": 1378, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1415, "startColumn": 31, "endLineNumber": 1415, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1416, "startColumn": 31, "endLineNumber": 1416, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1418, "startColumn": 31, "endLineNumber": 1418, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1419, "startColumn": 31, "endLineNumber": 1419, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use input.Data (variable of type interface{}) as map[string]interface{} value in assignment: need type assertion", "source": "compiler", "startLineNumber": 1464, "startColumn": 10, "endLineNumber": 1464, "endColumn": 20}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1883, "startColumn": 21, "endLineNumber": 1883, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1884, "startColumn": 21, "endLineNumber": 1884, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1919, "startColumn": 20, "endLineNumber": 1919, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1920, "startColumn": 20, "endLineNumber": 1920, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1991, "startColumn": 24, "endLineNumber": 1991, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 1992, "startColumn": 24, "endLineNumber": 1992, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2021, "startColumn": 31, "endLineNumber": 2021, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2022, "startColumn": 31, "endLineNumber": 2022, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2024, "startColumn": 31, "endLineNumber": 2024, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2025, "startColumn": 31, "endLineNumber": 2025, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2167, "startColumn": 31, "endLineNumber": 2167, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2168, "startColumn": 31, "endLineNumber": 2168, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2191, "startColumn": 23, "endLineNumber": 2191, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2239, "startColumn": 24, "endLineNumber": 2239, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2240, "startColumn": 24, "endLineNumber": 2240, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2291, "startColumn": 22, "endLineNumber": 2291, "endColumn": 26}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2321, "startColumn": 23, "endLineNumber": 2321, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2322, "startColumn": 23, "endLineNumber": 2322, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 2360, "startColumn": 22, "endLineNumber": 2360, "endColumn": 26}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidIfaceAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidIfaceAssign"}}, "severity": 8, "message": "cannot use &mutationResolver{…} (value of type *mutationResolver) as generated.MutationResolver value in return statement: *mutationResolver does not implement generated.MutationResolver (missing method CreateWebAddon)", "source": "compiler", "startLineNumber": 2753, "startColumn": 67, "endLineNumber": 2753, "endColumn": 87}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidIfaceAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidIfaceAssign"}}, "severity": 8, "message": "cannot use &queryResolver{…} (value of type *queryResolver) as generated.QueryResolver value in return statement: *queryResolver does not implement generated.QueryResolver (wrong type for method AdminExpertProfiles)\n\t\thave AdminExpertProfiles(context.Context, map[string]interface{}, *int, *int) (*model.ExpertProfileListResponse, error)\n\t\twant AdminExpertProfiles(context.Context, interface{}, *int, *int) (*model.ExpertProfileListResponse, error)", "source": "compiler", "startLineNumber": 2756, "startColumn": 61, "endLineNumber": 2756, "endColumn": 78}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Config in struct literal of type model.AddonInstallation", "source": "compiler", "startLineNumber": 344, "startColumn": 3, "endLineNumber": 344, "endColumn": 9}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Config in struct literal of type model.AddonInstallation", "source": "compiler", "startLineNumber": 375, "startColumn": 4, "endLineNumber": 375, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/addon_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Config in struct literal of type model.AddonInstallation", "source": "compiler", "startLineNumber": 382, "startColumn": 4, "endLineNumber": 382, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Title in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 33, "startColumn": 3, "endLineNumber": 33, "endColumn": 8}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Content in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 35, "startColumn": 3, "endLineNumber": 35, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Title in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 55, "startColumn": 3, "endLineNumber": 55, "endColumn": 8}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.Title undefined (type model.CreateBusinessPlanInput has no field or method Title)", "source": "compiler", "startLineNumber": 55, "startColumn": 22, "endLineNumber": 55, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Content in struct literal of type model.BusinessPlan", "source": "compiler", "startLineNumber": 57, "startColumn": 3, "endLineNumber": 57, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "input.Content undefined (type model.CreateBusinessPlanInput has no field or method Content)", "source": "compiler", "startLineNumber": 57, "startColumn": 22, "endLineNumber": 57, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 104, "startColumn": 25, "endLineNumber": 104, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/expert_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "app.Status undefined (type model.ExpertApplication has no field or method Status)", "source": "compiler", "startLineNumber": 159, "startColumn": 17, "endLineNumber": 159, "endColumn": 23}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/expert_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Rating in struct literal of type model.ExpertReview", "source": "compiler", "startLineNumber": 512, "startColumn": 4, "endLineNumber": 512, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/expert_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field Comment in struct literal of type model.ExpertReview", "source": "compiler", "startLineNumber": 514, "startColumn": 4, "endLineNumber": 514, "endColumn": 11}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/expert_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "default", "target": {"$mid": 1, "path": "/golang.org/x/tools/go/analysis/passes/unreachable", "scheme": "https", "authority": "pkg.go.dev"}}, "severity": 4, "message": "unreachable code", "source": "unreachable", "startLineNumber": 400, "startColumn": 2, "endLineNumber": 400, "endColumn": 33, "tags": [1]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 226, "startColumn": 21, "endLineNumber": 226, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 227, "startColumn": 21, "endLineNumber": 227, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 231, "startColumn": 20, "endLineNumber": 231, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/user_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredImportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredImportedName"}}, "severity": 8, "message": "undefined: model.Time", "source": "compiler", "startLineNumber": 232, "startColumn": 20, "endLineNumber": 232, "endColumn": 24}]