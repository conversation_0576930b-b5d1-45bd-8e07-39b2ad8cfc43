[{"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "error while importing github.com/99designs/gqlgen/graphql/handler: module lookup disabled by GOPROXY=off", "source": "compiler", "startLineNumber": 18, "startColumn": 2, "endLineNumber": 18, "endColumn": 47}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "error while importing github.com/99designs/gqlgen/graphql/handler/extension: module lookup disabled by GOPROXY=off", "source": "compiler", "startLineNumber": 19, "startColumn": 2, "endLineNumber": 19, "endColumn": 57}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "error while importing github.com/99designs/gqlgen/graphql/handler/lru: module lookup disabled by GOPROXY=off", "source": "compiler", "startLineNumber": 20, "startColumn": 2, "endLineNumber": 20, "endColumn": 51}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "error while importing github.com/99designs/gqlgen/graphql/handler/transport: module lookup disabled by GOPROXY=off", "source": "compiler", "startLineNumber": 21, "startColumn": 2, "endLineNumber": 21, "endColumn": 57}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "NonIndexableOperand", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "NonIndexableOperand"}}, "severity": 8, "message": "invalid operation: cannot index lru.New (value of type func(size int) *lru.LRU)", "source": "compiler", "startLineNumber": 1298, "startColumn": 10, "endLineNumber": 1298, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/go.mod", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "error while importing github.com/99designs/gqlgen/graphql/handler/extension: module lookup disabled by GOPROXY=off", "source": "compiler", "startLineNumber": 8, "startColumn": 2, "endLineNumber": 8, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/go.mod", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "error while importing github.com/99designs/gqlgen/graphql/handler/lru: module lookup disabled by GOPROXY=off", "source": "compiler", "startLineNumber": 8, "startColumn": 2, "endLineNumber": 8, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/go.mod", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "error while importing github.com/99designs/gqlgen/graphql/handler/transport: module lookup disabled by GOPROXY=off", "source": "compiler", "startLineNumber": 8, "startColumn": 2, "endLineNumber": 8, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "duplicate method GenerateSiteAPIKey", "source": "compiler", "startLineNumber": 1934, "startColumn": 2, "endLineNumber": 1934, "endColumn": 20, "relatedInformation": [{"startLineNumber": 2029, "startColumn": 2, "endLineNumber": 2029, "endColumn": 20, "message": "other declaration of method GenerateSiteAPIKey", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "duplicate method GenerateSiteAPIKey (see details)", "source": "compiler", "startLineNumber": 2029, "startColumn": 2, "endLineNumber": 2029, "endColumn": 20, "relatedInformation": [{"startLineNumber": 1934, "startColumn": 2, "endLineNumber": 1934, "endColumn": 20, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 118807, "startColumn": 17, "endLineNumber": 118807, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 118818, "startColumn": 12, "endLineNumber": 118818, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 120729, "startColumn": 17, "endLineNumber": 120729, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 120734, "startColumn": 12, "endLineNumber": 120734, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 121968, "startColumn": 17, "endLineNumber": 121968, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 121976, "startColumn": 12, "endLineNumber": 121976, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 122340, "startColumn": 17, "endLineNumber": 122340, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 122348, "startColumn": 12, "endLineNumber": 122348, "endColumn": 17}]