package api

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"goVwPlatformAPI/internal/database"
	"net/http"
	"sync"
	"time"
)

// WebhookManager handles webhook registration, delivery, and management
type WebhookManager struct {
	db            *database.DB
	client        *http.Client
	deliveryQueue chan *WebhookDelivery
	workers       int
	stopChan      chan struct{}
	wg            sync.WaitGroup
	mu            sync.RWMutex
	webhooks      map[string]*Webhook
}

// Webhook represents a webhook configuration
type Webhook struct {
	ID          string            `json:"id"`
	UserID      string            `json:"userId"`
	TenantID    string            `json:"tenantId"`
	Name        string            `json:"name"`
	URL         string            `json:"url"`
	Events      []string          `json:"events"`
	Secret      string            `json:"secret"`
	Active      bool              `json:"active"`
	Headers     map[string]string `json:"headers"`
	RetryPolicy *RetryPolicy      `json:"retryPolicy"`
	CreatedAt   time.Time         `json:"createdAt"`
	UpdatedAt   time.Time         `json:"updatedAt"`
	LastUsed    *time.Time        `json:"lastUsed,omitempty"`
}

// RetryPolicy defines webhook retry behavior
type RetryPolicy struct {
	MaxRetries    int           `json:"maxRetries"`
	RetryInterval time.Duration `json:"retryInterval"`
	BackoffFactor float64       `json:"backoffFactor"`
	MaxInterval   time.Duration `json:"maxInterval"`
}

// WebhookEvent represents an event to be sent via webhook
type WebhookEvent struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Source    string                 `json:"source"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	UserID    string                 `json:"userId"`
	TenantID  string                 `json:"tenantId"`
}

// WebhookDelivery represents a webhook delivery attempt
type WebhookDelivery struct {
	ID          string        `json:"id"`
	WebhookID   string        `json:"webhookId"`
	Event       *WebhookEvent `json:"event"`
	Attempt     int           `json:"attempt"`
	ScheduledAt time.Time     `json:"scheduledAt"`
	DeliveredAt *time.Time    `json:"deliveredAt,omitempty"`
	StatusCode  int           `json:"statusCode"`
	Response    string        `json:"response"`
	Error       string        `json:"error,omitempty"`
	Duration    time.Duration `json:"duration"`
}

// WebhookDeliveryLog represents the delivery history
type WebhookDeliveryLog struct {
	WebhookID   string             `json:"webhookId"`
	EventID     string             `json:"eventId"`
	Deliveries  []*WebhookDelivery `json:"deliveries"`
	Status      string             `json:"status"` // pending, delivered, failed, exhausted
	CreatedAt   time.Time          `json:"createdAt"`
	LastAttempt *time.Time         `json:"lastAttempt,omitempty"`
}

// NewWebhookManager creates a new webhook manager
func NewWebhookManager(db *database.DB, workers int) *WebhookManager {
	if workers <= 0 {
		workers = 5 // Default number of workers
	}

	return &WebhookManager{
		db:            db,
		client:        &http.Client{Timeout: 30 * time.Second},
		deliveryQueue: make(chan *WebhookDelivery, 1000),
		workers:       workers,
		stopChan:      make(chan struct{}),
		webhooks:      make(map[string]*Webhook),
	}
}

// Start starts the webhook manager workers
func (wm *WebhookManager) Start() {
	for i := 0; i < wm.workers; i++ {
		wm.wg.Add(1)
		go wm.worker()
	}
}

// Stop stops the webhook manager
func (wm *WebhookManager) Stop() {
	close(wm.stopChan)
	wm.wg.Wait()
}

// worker processes webhook deliveries
func (wm *WebhookManager) worker() {
	defer wm.wg.Done()

	for {
		select {
		case delivery := <-wm.deliveryQueue:
			wm.processDelivery(delivery)
		case <-wm.stopChan:
			return
		}
	}
}

// RegisterWebhook registers a new webhook
func (wm *WebhookManager) RegisterWebhook(webhook *Webhook) error {
	if webhook.ID == "" {
		webhook.ID = generateID()
	}

	if webhook.Secret == "" {
		webhook.Secret = generateSecret()
	}

	webhook.CreatedAt = time.Now()
	webhook.UpdatedAt = time.Now()

	if webhook.RetryPolicy == nil {
		webhook.RetryPolicy = &RetryPolicy{
			MaxRetries:    3,
			RetryInterval: 5 * time.Second,
			BackoffFactor: 2.0,
			MaxInterval:   5 * time.Minute,
		}
	}

	wm.mu.Lock()
	wm.webhooks[webhook.ID] = webhook
	wm.mu.Unlock()

	// In a real implementation, save to database
	return nil
}

// UpdateWebhook updates an existing webhook
func (wm *WebhookManager) UpdateWebhook(webhookID string, updates *Webhook) error {
	wm.mu.Lock()
	defer wm.mu.Unlock()

	webhook, exists := wm.webhooks[webhookID]
	if !exists {
		return fmt.Errorf("webhook not found: %s", webhookID)
	}

	// Update fields
	if updates.Name != "" {
		webhook.Name = updates.Name
	}
	if updates.URL != "" {
		webhook.URL = updates.URL
	}
	if updates.Events != nil {
		webhook.Events = updates.Events
	}
	if updates.Headers != nil {
		webhook.Headers = updates.Headers
	}
	if updates.RetryPolicy != nil {
		webhook.RetryPolicy = updates.RetryPolicy
	}
	webhook.Active = updates.Active
	webhook.UpdatedAt = time.Now()

	// In a real implementation, update in database
	return nil
}

// DeleteWebhook deletes a webhook
func (wm *WebhookManager) DeleteWebhook(webhookID string) error {
	wm.mu.Lock()
	defer wm.mu.Unlock()

	if _, exists := wm.webhooks[webhookID]; !exists {
		return fmt.Errorf("webhook not found: %s", webhookID)
	}

	delete(wm.webhooks, webhookID)

	// In a real implementation, delete from database
	return nil
}

// GetWebhook retrieves a webhook by ID
func (wm *WebhookManager) GetWebhook(webhookID string) (*Webhook, error) {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	webhook, exists := wm.webhooks[webhookID]
	if !exists {
		return nil, fmt.Errorf("webhook not found: %s", webhookID)
	}

	return webhook, nil
}

// ListWebhooks lists webhooks for a user/tenant
func (wm *WebhookManager) ListWebhooks(userID, tenantID string) ([]*Webhook, error) {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	var webhooks []*Webhook
	for _, webhook := range wm.webhooks {
		if (userID == "" || webhook.UserID == userID) &&
			(tenantID == "" || webhook.TenantID == tenantID) {
			webhooks = append(webhooks, webhook)
		}
	}

	return webhooks, nil
}

// TriggerEvent triggers webhook deliveries for an event
func (wm *WebhookManager) TriggerEvent(event *WebhookEvent) error {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	for _, webhook := range wm.webhooks {
		if !webhook.Active {
			continue
		}

		// Check if webhook is interested in this event type
		if !wm.isEventMatched(webhook.Events, event.Type) {
			continue
		}

		// Check tenant/user scope
		if webhook.TenantID != "" && webhook.TenantID != event.TenantID {
			continue
		}
		if webhook.UserID != "" && webhook.UserID != event.UserID {
			continue
		}

		// Create delivery
		delivery := &WebhookDelivery{
			ID:          generateID(),
			WebhookID:   webhook.ID,
			Event:       event,
			Attempt:     1,
			ScheduledAt: time.Now(),
		}

		// Queue for delivery
		select {
		case wm.deliveryQueue <- delivery:
		default:
			// Queue is full, log error
			fmt.Printf("Webhook delivery queue is full, dropping delivery for webhook %s\n", webhook.ID)
		}
	}

	return nil
}

// isEventMatched checks if an event type matches webhook event patterns
func (wm *WebhookManager) isEventMatched(patterns []string, eventType string) bool {
	for _, pattern := range patterns {
		if pattern == "*" || pattern == eventType {
			return true
		}
		// Support wildcard patterns like "user.*"
		if len(pattern) > 1 && pattern[len(pattern)-1] == '*' {
			prefix := pattern[:len(pattern)-1]
			if len(eventType) >= len(prefix) && eventType[:len(prefix)] == prefix {
				return true
			}
		}
	}
	return false
}

// processDelivery processes a webhook delivery
func (wm *WebhookManager) processDelivery(delivery *WebhookDelivery) {
	webhook, err := wm.GetWebhook(delivery.WebhookID)
	if err != nil {
		fmt.Printf("Failed to get webhook %s: %v\n", delivery.WebhookID, err)
		return
	}

	startTime := time.Now()

	// Prepare payload
	payload, err := json.Marshal(delivery.Event)
	if err != nil {
		delivery.Error = fmt.Sprintf("Failed to marshal event: %v", err)
		wm.logDelivery(delivery)
		return
	}

	// Create request
	req, err := http.NewRequest("POST", webhook.URL, bytes.NewBuffer(payload))
	if err != nil {
		delivery.Error = fmt.Sprintf("Failed to create request: %v", err)
		wm.logDelivery(delivery)
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "VelocityWave-Webhooks/1.0")
	req.Header.Set("X-Webhook-ID", webhook.ID)
	req.Header.Set("X-Event-Type", delivery.Event.Type)
	req.Header.Set("X-Event-ID", delivery.Event.ID)
	req.Header.Set("X-Delivery-ID", delivery.ID)

	// Add custom headers
	for key, value := range webhook.Headers {
		req.Header.Set(key, value)
	}

	// Add signature
	if webhook.Secret != "" {
		signature := wm.generateSignature(payload, webhook.Secret)
		req.Header.Set("X-Webhook-Signature", signature)
	}

	// Make request
	resp, err := wm.client.Do(req)
	delivery.Duration = time.Since(startTime)
	delivery.DeliveredAt = &startTime

	if err != nil {
		delivery.Error = fmt.Sprintf("Request failed: %v", err)
		wm.handleFailedDelivery(delivery, webhook)
		return
	}
	defer resp.Body.Close()

	delivery.StatusCode = resp.StatusCode

	// Read response
	buf := make([]byte, 1024)
	n, _ := resp.Body.Read(buf)
	delivery.Response = string(buf[:n])

	// Check if delivery was successful
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		// Success
		webhook.LastUsed = &startTime
		wm.logDelivery(delivery)
	} else {
		// Failed
		delivery.Error = fmt.Sprintf("HTTP %d: %s", resp.StatusCode, delivery.Response)
		wm.handleFailedDelivery(delivery, webhook)
	}
}

// handleFailedDelivery handles failed webhook deliveries with retry logic
func (wm *WebhookManager) handleFailedDelivery(delivery *WebhookDelivery, webhook *Webhook) {
	wm.logDelivery(delivery)

	if delivery.Attempt < webhook.RetryPolicy.MaxRetries {
		// Schedule retry
		retryDelay := wm.calculateRetryDelay(delivery.Attempt, webhook.RetryPolicy)

		retryDelivery := &WebhookDelivery{
			ID:          generateID(),
			WebhookID:   delivery.WebhookID,
			Event:       delivery.Event,
			Attempt:     delivery.Attempt + 1,
			ScheduledAt: time.Now().Add(retryDelay),
		}

		// Schedule retry (in a real implementation, use a proper scheduler)
		go func() {
			time.Sleep(retryDelay)
			select {
			case wm.deliveryQueue <- retryDelivery:
			case <-wm.stopChan:
			}
		}()
	}
}

// calculateRetryDelay calculates the delay for the next retry attempt
func (wm *WebhookManager) calculateRetryDelay(attempt int, policy *RetryPolicy) time.Duration {
	delay := policy.RetryInterval

	// Apply exponential backoff
	for i := 1; i < attempt; i++ {
		delay = time.Duration(float64(delay) * policy.BackoffFactor)
	}

	// Cap at maximum interval
	if delay > policy.MaxInterval {
		delay = policy.MaxInterval
	}

	return delay
}

// generateSignature generates HMAC signature for webhook payload
func (wm *WebhookManager) generateSignature(payload []byte, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(payload)
	return "sha256=" + hex.EncodeToString(h.Sum(nil))
}

// VerifySignature verifies webhook signature
func (wm *WebhookManager) VerifySignature(payload []byte, signature, secret string) bool {
	expectedSignature := wm.generateSignature(payload, secret)
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// logDelivery logs webhook delivery (in a real implementation, save to database)
func (wm *WebhookManager) logDelivery(delivery *WebhookDelivery) {
	// In a real implementation, save delivery log to database
	fmt.Printf("Webhook delivery: %s -> %s (attempt %d, status %d)\n",
		delivery.WebhookID, delivery.Event.Type, delivery.Attempt, delivery.StatusCode)
}

// GetDeliveryLogs retrieves delivery logs for a webhook
func (wm *WebhookManager) GetDeliveryLogs(webhookID string, limit int) ([]*WebhookDeliveryLog, error) {
	// In a real implementation, query from database
	return nil, fmt.Errorf("not implemented")
}

// TestWebhook tests a webhook by sending a test event
func (wm *WebhookManager) TestWebhook(webhookID string) error {
	webhook, err := wm.GetWebhook(webhookID)
	if err != nil {
		return err
	}

	testEvent := &WebhookEvent{
		ID:     generateID(),
		Type:   "webhook.test",
		Source: "webhook_manager",
		Data: map[string]interface{}{
			"message":    "This is a test webhook delivery",
			"webhook_id": webhookID,
		},
		Timestamp: time.Now(),
		UserID:    webhook.UserID,
		TenantID:  webhook.TenantID,
	}

	delivery := &WebhookDelivery{
		ID:          generateID(),
		WebhookID:   webhook.ID,
		Event:       testEvent,
		Attempt:     1,
		ScheduledAt: time.Now(),
	}

	wm.processDelivery(delivery)
	return nil
}

// GetWebhookStats returns statistics for a webhook
func (wm *WebhookManager) GetWebhookStats(webhookID string, period time.Duration) (*WebhookStats, error) {
	// In a real implementation, query delivery logs from database
	return &WebhookStats{
		WebhookID:            webhookID,
		TotalDeliveries:      0,
		SuccessfulDeliveries: 0,
		FailedDeliveries:     0,
		AverageResponseTime:  0,
		LastDelivery:         nil,
	}, nil
}

// WebhookStats represents webhook statistics
type WebhookStats struct {
	WebhookID            string        `json:"webhookId"`
	TotalDeliveries      int           `json:"totalDeliveries"`
	SuccessfulDeliveries int           `json:"successfulDeliveries"`
	FailedDeliveries     int           `json:"failedDeliveries"`
	AverageResponseTime  time.Duration `json:"averageResponseTime"`
	LastDelivery         *time.Time    `json:"lastDelivery,omitempty"`
}

// Helper functions
func generateSecret() string {
	// In a real implementation, use crypto/rand
	return "webhook_secret_" + generateID()
}
