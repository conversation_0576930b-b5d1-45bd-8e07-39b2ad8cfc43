{{define "content"}}
<div class="container py-4">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-5 fw-bold">Financial Planning Tools</h1>
        <p class="lead text-muted">Plan your business finances with professional-grade tools</p>
    </div>
    
    <!-- Tool Selection -->
    <div class="row mb-5" id="toolSelection">
        <div class="col-12">
            <h4 class="mb-4">Choose Your Financial Planning Tool</h4>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 tool-card" onclick="selectTool('cashflow')">
                        <div class="card-body text-center">
                            <i class="bi bi-graph-up display-4 text-primary mb-3"></i>
                            <h5>Cash Flow Forecast</h5>
                            <p class="text-muted">Project your cash inflows and outflows</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="bi bi-check text-success me-2"></i>Monthly projections</li>
                                <li><i class="bi bi-check text-success me-2"></i>Revenue forecasting</li>
                                <li><i class="bi bi-check text-success me-2"></i>Expense tracking</li>
                                <li><i class="bi bi-check text-success me-2"></i>Break-even analysis</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card h-100 tool-card" onclick="selectTool('budget')">
                        <div class="card-body text-center">
                            <i class="bi bi-calculator display-4 text-success mb-3"></i>
                            <h5>Budget Planner</h5>
                            <p class="text-muted">Create and manage your business budget</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="bi bi-check text-success me-2"></i>Income planning</li>
                                <li><i class="bi bi-check text-success me-2"></i>Expense categories</li>
                                <li><i class="bi bi-check text-success me-2"></i>Variance analysis</li>
                                <li><i class="bi bi-check text-success me-2"></i>Budget vs actual</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card h-100 tool-card" onclick="selectTool('startup')">
                        <div class="card-body text-center">
                            <i class="bi bi-currency-pound display-4 text-warning mb-3"></i>
                            <h5>Startup Costs Calculator</h5>
                            <p class="text-muted">Calculate initial investment requirements</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="bi bi-check text-success me-2"></i>One-time costs</li>
                                <li><i class="bi bi-check text-success me-2"></i>Working capital</li>
                                <li><i class="bi bi-check text-success me-2"></i>Equipment & assets</li>
                                <li><i class="bi bi-check text-success me-2"></i>Funding gap analysis</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Financial Planning Tool -->
    <div id="financialTool" style="display: none;">
        <div class="row">
            <!-- Tool Navigation -->
            <div class="col-md-3">
                <div class="card sticky-top">
                    <div class="card-header">
                        <h6 class="mb-0" id="toolTitle">Financial Tool</h6>
                    </div>
                    <div class="card-body">
                        <div id="toolNavigation">
                            <!-- Navigation will be populated here -->
                        </div>
                        
                        <!-- Quick Summary -->
                        <div class="mt-4">
                            <h6>Quick Summary</h6>
                            <div id="quickSummary" class="small">
                                <!-- Summary will be updated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0" id="currentStepTitle">Step 1</h5>
                            <div>
                                <button class="btn btn-outline-secondary btn-sm me-2" onclick="saveData()">
                                    <i class="bi bi-save me-1"></i>Save
                                </button>
                                <button class="btn btn-success btn-sm" onclick="exportResults()">
                                    <i class="bi bi-download me-1"></i>Export
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="stepContent">
                            <!-- Step content will be loaded here -->
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button class="btn btn-outline-secondary" id="prevStepButton" onclick="previousStep()" disabled>
                                <i class="bi bi-arrow-left me-1"></i>Previous
                            </button>
                            <button class="btn btn-primary" id="nextStepButton" onclick="nextStep()">
                                Next<i class="bi bi-arrow-right ms-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Results Section -->
                <div class="card mt-4" id="resultsSection" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">Results & Analysis</h6>
                    </div>
                    <div class="card-body">
                        <div id="resultsContent">
                            <!-- Results will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentTool = null;
let currentStep = 0;
let toolData = {};
let toolSteps = {};

// Define tool structures
const tools = {
    cashflow: {
        name: 'Cash Flow Forecast',
        steps: [
            { id: 'business_info', title: 'Business Information' },
            { id: 'revenue_streams', title: 'Revenue Streams' },
            { id: 'expenses', title: 'Business Expenses' },
            { id: 'timing', title: 'Timing & Seasonality' },
            { id: 'forecast', title: 'Cash Flow Forecast' }
        ]
    },
    budget: {
        name: 'Budget Planner',
        steps: [
            { id: 'budget_period', title: 'Budget Period' },
            { id: 'income_sources', title: 'Income Sources' },
            { id: 'fixed_costs', title: 'Fixed Costs' },
            { id: 'variable_costs', title: 'Variable Costs' },
            { id: 'budget_summary', title: 'Budget Summary' }
        ]
    },
    startup: {
        name: 'Startup Costs Calculator',
        steps: [
            { id: 'business_type', title: 'Business Type' },
            { id: 'equipment_assets', title: 'Equipment & Assets' },
            { id: 'initial_expenses', title: 'Initial Expenses' },
            { id: 'working_capital', title: 'Working Capital' },
            { id: 'funding_analysis', title: 'Funding Analysis' }
        ]
    }
};

function selectTool(toolId) {
    currentTool = toolId;
    toolSteps = tools[toolId];
    
    // Hide tool selection and show tool
    document.getElementById('toolSelection').style.display = 'none';
    document.getElementById('financialTool').style.display = 'block';
    
    // Initialize tool data
    toolData = {
        tool: toolId,
        toolName: toolSteps.name,
        steps: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    
    // Setup tool
    setupTool();
    loadStep(0);
}

function setupTool() {
    document.getElementById('toolTitle').textContent = toolSteps.name;
    
    // Setup navigation
    const navigation = document.getElementById('toolNavigation');
    navigation.innerHTML = toolSteps.steps.map((step, index) => `
        <div class="d-flex align-items-center mb-2 step-nav" data-step="${index}" onclick="loadStep(${index})">
            <div class="step-indicator me-2" id="indicator-${index}">
                <i class="bi bi-circle"></i>
            </div>
            <small class="step-title">${step.title}</small>
        </div>
    `).join('');
}

function loadStep(stepIndex) {
    currentStep = stepIndex;
    const step = toolSteps.steps[stepIndex];
    
    // Update navigation
    document.querySelectorAll('.step-nav').forEach((nav, index) => {
        nav.classList.toggle('active', index === stepIndex);
    });
    
    // Update step title
    document.getElementById('currentStepTitle').textContent = step.title;
    
    // Load step content
    const content = getStepContent(step.id);
    document.getElementById('stepContent').innerHTML = content;
    
    // Update navigation buttons
    document.getElementById('prevStepButton').disabled = stepIndex === 0;
    document.getElementById('nextStepButton').textContent = 
        stepIndex === toolSteps.steps.length - 1 ? 'Calculate' : 'Next';
    
    // Load existing data if available
    if (toolData.steps[step.id]) {
        populateStepData(step.id, toolData.steps[step.id]);
    }
}

function getStepContent(stepId) {
    const contentTemplates = {
        // Cash Flow Forecast
        business_info: `
            <div class="mb-3">
                <label class="form-label">Business Name</label>
                <input type="text" class="form-control" id="business_name" placeholder="Your business name">
            </div>
            <div class="mb-3">
                <label class="form-label">Forecast Period</label>
                <select class="form-select" id="forecast_period">
                    <option value="12">12 months</option>
                    <option value="24">24 months</option>
                    <option value="36">36 months</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Starting Cash Balance (£)</label>
                <input type="number" class="form-control" id="starting_cash" placeholder="0" min="0">
            </div>
        `,
        revenue_streams: `
            <div class="mb-3">
                <label class="form-label">Primary Revenue Source</label>
                <input type="text" class="form-control" id="primary_revenue" placeholder="e.g., Product sales, Service fees">
            </div>
            <div class="mb-3">
                <label class="form-label">Monthly Revenue (£)</label>
                <input type="number" class="form-control" id="monthly_revenue" placeholder="0" min="0">
            </div>
            <div class="mb-3">
                <label class="form-label">Revenue Growth Rate (%/month)</label>
                <input type="number" class="form-control" id="revenue_growth" placeholder="0" min="0" max="100" step="0.1">
            </div>
            <div class="mb-3">
                <label class="form-label">Secondary Revenue Sources</label>
                <textarea class="form-control" id="secondary_revenue" rows="3" 
                          placeholder="List any additional revenue streams..."></textarea>
            </div>
        `,
        expenses: `
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">Rent/Premises (£/month)</label>
                    <input type="number" class="form-control" id="rent" placeholder="0" min="0">
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Salaries & Wages (£/month)</label>
                    <input type="number" class="form-control" id="salaries" placeholder="0" min="0">
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Utilities (£/month)</label>
                    <input type="number" class="form-control" id="utilities" placeholder="0" min="0">
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Marketing (£/month)</label>
                    <input type="number" class="form-control" id="marketing" placeholder="0" min="0">
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Insurance (£/month)</label>
                    <input type="number" class="form-control" id="insurance" placeholder="0" min="0">
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Other Expenses (£/month)</label>
                    <input type="number" class="form-control" id="other_expenses" placeholder="0" min="0">
                </div>
            </div>
        `,
        
        // Budget Planner
        budget_period: `
            <div class="mb-3">
                <label class="form-label">Budget Period</label>
                <select class="form-select" id="budget_period_type">
                    <option value="monthly">Monthly Budget</option>
                    <option value="quarterly">Quarterly Budget</option>
                    <option value="annual">Annual Budget</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Budget Year</label>
                <input type="number" class="form-control" id="budget_year" value="${new Date().getFullYear()}" min="2020" max="2030">
            </div>
        `,
        
        // Startup Costs
        business_type: `
            <div class="mb-3">
                <label class="form-label">Business Type</label>
                <select class="form-select" id="business_type_select">
                    <option value="">Select business type</option>
                    <option value="retail">Retail Store</option>
                    <option value="restaurant">Restaurant/Cafe</option>
                    <option value="service">Service Business</option>
                    <option value="manufacturing">Manufacturing</option>
                    <option value="technology">Technology/Software</option>
                    <option value="consulting">Consulting</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Business Location</label>
                <select class="form-select" id="business_location">
                    <option value="home">Home-based</option>
                    <option value="rented">Rented premises</option>
                    <option value="purchased">Purchased premises</option>
                    <option value="online">Online only</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Number of Employees (initially)</label>
                <input type="number" class="form-control" id="initial_employees" placeholder="0" min="0">
            </div>
        `
    };
    
    return contentTemplates[stepId] || `
        <div class="mb-3">
            <label class="form-label">${stepId.replace('_', ' ').toUpperCase()}</label>
            <textarea class="form-control" id="${stepId}_content" rows="6" 
                      placeholder="Enter information for this step..."></textarea>
        </div>
    `;
}

function populateStepData(stepId, data) {
    Object.keys(data).forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.value = data[fieldId];
        }
    });
}

function saveStepData() {
    const step = toolSteps.steps[currentStep];
    const stepData = {};
    
    // Get all form inputs in the current step
    const inputs = document.querySelectorAll('#stepContent input, #stepContent textarea, #stepContent select');
    inputs.forEach(input => {
        if (input.value.trim()) {
            stepData[input.id] = input.value.trim();
        }
    });
    
    toolData.steps[step.id] = stepData;
    toolData.updatedAt = new Date().toISOString();
    
    // Update step indicator
    updateStepIndicator(currentStep, Object.keys(stepData).length > 0);
    updateQuickSummary();
}

function updateStepIndicator(stepIndex, completed) {
    const indicator = document.getElementById(`indicator-${stepIndex}`);
    if (completed) {
        indicator.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i>';
    } else {
        indicator.innerHTML = '<i class="bi bi-circle"></i>';
    }
}

function updateQuickSummary() {
    const summary = document.getElementById('quickSummary');
    const completedSteps = Object.keys(toolData.steps).length;
    const totalSteps = toolSteps.steps.length;
    
    summary.innerHTML = `
        <div class="mb-2">
            <strong>Progress:</strong> ${completedSteps}/${totalSteps} steps
        </div>
        <div class="progress mb-2" style="height: 6px;">
            <div class="progress-bar" style="width: ${(completedSteps/totalSteps)*100}%"></div>
        </div>
    `;
}

function nextStep() {
    saveStepData();
    
    if (currentStep < toolSteps.steps.length - 1) {
        loadStep(currentStep + 1);
    } else {
        // Calculate results
        calculateResults();
    }
}

function previousStep() {
    saveStepData();
    
    if (currentStep > 0) {
        loadStep(currentStep - 1);
    }
}

function calculateResults() {
    saveStepData();
    
    let resultsHTML = '';
    
    if (currentTool === 'cashflow') {
        resultsHTML = generateCashFlowResults();
    } else if (currentTool === 'budget') {
        resultsHTML = generateBudgetResults();
    } else if (currentTool === 'startup') {
        resultsHTML = generateStartupResults();
    }
    
    document.getElementById('resultsContent').innerHTML = resultsHTML;
    document.getElementById('resultsSection').style.display = 'block';
    
    // Scroll to results
    document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
}

function generateCashFlowResults() {
    const data = toolData.steps;
    const monthlyRevenue = parseFloat(data.revenue_streams?.monthly_revenue || 0);
    const growthRate = parseFloat(data.revenue_streams?.revenue_growth || 0) / 100;
    const monthlyExpenses = calculateMonthlyExpenses(data.expenses);
    const startingCash = parseFloat(data.business_info?.starting_cash || 0);
    
    let cashFlow = [];
    let runningCash = startingCash;
    
    for (let month = 1; month <= 12; month++) {
        const revenue = monthlyRevenue * Math.pow(1 + growthRate, month - 1);
        const netCashFlow = revenue - monthlyExpenses;
        runningCash += netCashFlow;
        
        cashFlow.push({
            month: month,
            revenue: revenue,
            expenses: monthlyExpenses,
            netCashFlow: netCashFlow,
            runningCash: runningCash
        });
    }
    
    return `
        <div class="row mb-4">
            <div class="col-md-3 text-center">
                <h4 class="text-primary">£${monthlyRevenue.toLocaleString()}</h4>
                <small>Starting Monthly Revenue</small>
            </div>
            <div class="col-md-3 text-center">
                <h4 class="text-danger">£${monthlyExpenses.toLocaleString()}</h4>
                <small>Monthly Expenses</small>
            </div>
            <div class="col-md-3 text-center">
                <h4 class="text-${cashFlow[11].runningCash > 0 ? 'success' : 'danger'}">£${cashFlow[11].runningCash.toLocaleString()}</h4>
                <small>Year-end Cash Position</small>
            </div>
            <div class="col-md-3 text-center">
                <h4 class="text-info">${findBreakEvenMonth(cashFlow)}</h4>
                <small>Break-even Month</small>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Month</th>
                        <th>Revenue</th>
                        <th>Expenses</th>
                        <th>Net Cash Flow</th>
                        <th>Running Cash</th>
                    </tr>
                </thead>
                <tbody>
                    ${cashFlow.map(row => `
                        <tr>
                            <td>Month ${row.month}</td>
                            <td>£${row.revenue.toLocaleString()}</td>
                            <td>£${row.expenses.toLocaleString()}</td>
                            <td class="text-${row.netCashFlow >= 0 ? 'success' : 'danger'}">£${row.netCashFlow.toLocaleString()}</td>
                            <td class="text-${row.runningCash >= 0 ? 'success' : 'danger'}">£${row.runningCash.toLocaleString()}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function calculateMonthlyExpenses(expensesData) {
    if (!expensesData) return 0;
    
    return (parseFloat(expensesData.rent || 0) +
            parseFloat(expensesData.salaries || 0) +
            parseFloat(expensesData.utilities || 0) +
            parseFloat(expensesData.marketing || 0) +
            parseFloat(expensesData.insurance || 0) +
            parseFloat(expensesData.other_expenses || 0));
}

function findBreakEvenMonth(cashFlow) {
    for (let i = 0; i < cashFlow.length; i++) {
        if (cashFlow[i].netCashFlow > 0) {
            return `Month ${i + 1}`;
        }
    }
    return 'Not achieved';
}

function generateBudgetResults() {
    return `
        <div class="text-center py-4">
            <h5>Budget Analysis</h5>
            <p class="text-muted">Budget analysis will be displayed here</p>
        </div>
    `;
}

function generateStartupResults() {
    return `
        <div class="text-center py-4">
            <h5>Startup Costs Analysis</h5>
            <p class="text-muted">Startup costs breakdown will be displayed here</p>
        </div>
    `;
}

function saveData() {
    saveStepData();
    showAlert('Data saved successfully!', 'success');
}

function exportResults() {
    // Create export content
    let exportContent = `${toolData.toolName} Results\n`;
    exportContent += `Generated on: ${new Date().toLocaleDateString()}\n\n`;
    
    // Add step data
    toolSteps.steps.forEach(step => {
        const stepData = toolData.steps[step.id];
        if (stepData && Object.keys(stepData).length > 0) {
            exportContent += `${step.title.toUpperCase()}\n`;
            exportContent += '='.repeat(step.title.length) + '\n\n';
            
            Object.entries(stepData).forEach(([key, value]) => {
                exportContent += `${key.replace('_', ' ').toUpperCase()}: ${value}\n`;
            });
            exportContent += '\n';
        }
    });
    
    // Download as text file
    const blob = new Blob([exportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentTool}-analysis.txt`;
    a.click();
    URL.revokeObjectURL(url);
    
    showAlert('Results exported successfully!', 'success');
}

function showAlert(message, type) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    document.body.insertAdjacentHTML('afterbegin', alertHTML);
    
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 3000);
}
</script>

<style>
.tool-card {
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.step-nav {
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.step-nav:hover {
    background-color: rgba(0,0,0,0.05);
}

.step-nav.active {
    background-color: rgba(13, 110, 253, 0.1);
}

.step-indicator {
    width: 20px;
    text-align: center;
}

.sticky-top {
    top: 20px;
}
</style>
{{end}}

{{define "title"}}Financial Planning Tools - VelocityWave{{end}}