-- Create client requirements table
CREATE TABLE IF NOT EXISTS client_requirements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Project details
    project_title VARCHAR(200) NOT NULL,
    project_description TEXT NOT NULL,
    project_type VARCHAR(100) NOT NULL,
    required_expertise TEXT[] NOT NULL,
    project_scope VARCHAR(50) NOT NULL, -- small, medium, large
    
    -- Budget and timeline
    budget_min DECIMAL(10,2),
    budget_max DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'GBP',
    timeline_weeks INTEGER,
    start_date DATE,
    urgency VARCHAR(50) DEFAULT 'normal', -- low, normal, high, urgent
    
    -- Preferences
    preferred_expert_experience INTEGER, -- minimum years
    preferred_communication VARCHAR(50) DEFAULT 'email', -- email, phone, video, in-person
    location_preference VARCHAR(100),
    remote_work_acceptable BOOLEAN DEFAULT true,
    
    -- Status
    status VARCHAR(50) DEFAULT 'active', -- active, matched, completed, cancelled
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create expert-client matches table
CREATE TABLE IF NOT EXISTS expert_client_matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_requirement_id UUID NOT NULL REFERENCES client_requirements(id) ON DELETE CASCADE,
    expert_profile_id UUID NOT NULL REFERENCES expert_profiles(id) ON DELETE CASCADE,
    
    -- Match scoring
    compatibility_score DECIMAL(5,2) NOT NULL, -- 0.00 to 100.00
    expertise_match_score DECIMAL(5,2) NOT NULL,
    budget_match_score DECIMAL(5,2) NOT NULL,
    availability_match_score DECIMAL(5,2) NOT NULL,
    experience_match_score DECIMAL(5,2) NOT NULL,
    
    -- Match status
    match_status VARCHAR(50) DEFAULT 'suggested', -- suggested, contacted, accepted, declined, expired
    suggested_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    contacted_at TIMESTAMP WITH TIME ZONE,
    responded_at TIMESTAMP WITH TIME ZONE,
    
    -- Communication
    client_message TEXT,
    expert_response TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create project engagements table
CREATE TABLE IF NOT EXISTS project_engagements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_requirement_id UUID NOT NULL REFERENCES client_requirements(id) ON DELETE CASCADE,
    expert_profile_id UUID NOT NULL REFERENCES expert_profiles(id) ON DELETE CASCADE,
    match_id UUID REFERENCES expert_client_matches(id),
    
    -- Project details
    project_title VARCHAR(200) NOT NULL,
    project_description TEXT NOT NULL,
    agreed_scope TEXT,
    deliverables JSONB DEFAULT '[]',
    
    -- Financial terms
    agreed_rate DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'GBP',
    rate_type VARCHAR(50) DEFAULT 'hourly', -- hourly, fixed, milestone
    estimated_hours INTEGER,
    total_budget DECIMAL(12,2),
    
    -- Timeline
    start_date DATE NOT NULL,
    end_date DATE,
    estimated_duration_weeks INTEGER,
    
    -- Status and progress
    engagement_status VARCHAR(50) DEFAULT 'pending', -- pending, active, paused, completed, cancelled, disputed
    progress_percentage INTEGER DEFAULT 0,
    
    -- Contract details
    contract_signed_at TIMESTAMP WITH TIME ZONE,
    contract_document_url VARCHAR(500),
    terms_agreed BOOLEAN DEFAULT false,
    
    -- Communication preferences
    communication_frequency VARCHAR(50) DEFAULT 'weekly',
    preferred_meeting_day VARCHAR(20),
    preferred_meeting_time TIME,
    
    -- Performance tracking
    client_satisfaction_rating DECIMAL(3,2),
    expert_performance_rating DECIMAL(3,2),
    on_time_delivery BOOLEAN,
    within_budget BOOLEAN,
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create project milestones table
CREATE TABLE IF NOT EXISTS project_milestones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    engagement_id UUID NOT NULL REFERENCES project_engagements(id) ON DELETE CASCADE,
    
    -- Milestone details
    milestone_title VARCHAR(200) NOT NULL,
    milestone_description TEXT,
    milestone_order INTEGER NOT NULL,
    
    -- Timeline
    due_date DATE NOT NULL,
    completed_date DATE,
    
    -- Financial
    milestone_value DECIMAL(10,2),
    payment_status VARCHAR(50) DEFAULT 'pending', -- pending, paid, overdue
    
    -- Status
    milestone_status VARCHAR(50) DEFAULT 'pending', -- pending, in_progress, completed, overdue, cancelled
    completion_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create project communications table
CREATE TABLE IF NOT EXISTS project_communications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    engagement_id UUID NOT NULL REFERENCES project_engagements(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Message details
    message_type VARCHAR(50) DEFAULT 'message', -- message, update, milestone, payment, dispute
    subject VARCHAR(200),
    message_content TEXT NOT NULL,
    
    -- Attachments
    attachments JSONB DEFAULT '[]',
    
    -- Status
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    priority VARCHAR(20) DEFAULT 'normal', -- low, normal, high, urgent
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_client_requirements_user_id ON client_requirements(user_id);
CREATE INDEX IF NOT EXISTS idx_client_requirements_tenant_id ON client_requirements(tenant_id);
CREATE INDEX IF NOT EXISTS idx_client_requirements_status ON client_requirements(status);
CREATE INDEX IF NOT EXISTS idx_client_requirements_expertise ON client_requirements USING GIN(required_expertise);

CREATE INDEX IF NOT EXISTS idx_expert_client_matches_client_req ON expert_client_matches(client_requirement_id);
CREATE INDEX IF NOT EXISTS idx_expert_client_matches_expert ON expert_client_matches(expert_profile_id);
CREATE INDEX IF NOT EXISTS idx_expert_client_matches_status ON expert_client_matches(match_status);
CREATE INDEX IF NOT EXISTS idx_expert_client_matches_score ON expert_client_matches(compatibility_score);

CREATE INDEX IF NOT EXISTS idx_project_engagements_client_req ON project_engagements(client_requirement_id);
CREATE INDEX IF NOT EXISTS idx_project_engagements_expert ON project_engagements(expert_profile_id);
CREATE INDEX IF NOT EXISTS idx_project_engagements_status ON project_engagements(engagement_status);
CREATE INDEX IF NOT EXISTS idx_project_engagements_dates ON project_engagements(start_date, end_date);

CREATE INDEX IF NOT EXISTS idx_project_milestones_engagement ON project_milestones(engagement_id);
CREATE INDEX IF NOT EXISTS idx_project_milestones_due_date ON project_milestones(due_date);
CREATE INDEX IF NOT EXISTS idx_project_milestones_status ON project_milestones(milestone_status);

CREATE INDEX IF NOT EXISTS idx_project_communications_engagement ON project_communications(engagement_id);
CREATE INDEX IF NOT EXISTS idx_project_communications_sender ON project_communications(sender_id);
CREATE INDEX IF NOT EXISTS idx_project_communications_created ON project_communications(created_at);

-- Add triggers for updated_at
CREATE TRIGGER update_client_requirements_updated_at BEFORE UPDATE ON client_requirements FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();
CREATE TRIGGER update_expert_client_matches_updated_at BEFORE UPDATE ON expert_client_matches FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();
CREATE TRIGGER update_project_engagements_updated_at BEFORE UPDATE ON project_engagements FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();
CREATE TRIGGER update_project_milestones_updated_at BEFORE UPDATE ON project_milestones FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();