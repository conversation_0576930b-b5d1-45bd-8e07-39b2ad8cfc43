package admin

import (
	"context"
	"goVwPlatformAPI/internal/database"
	"time"
)

// RevenueReportsService handles revenue and billing reports
type RevenueReportsService struct {
	db *database.DB
}

// NewRevenueReportsService creates a new revenue reports service
func NewRevenueReportsService(db *database.DB) *RevenueReportsService {
	return &RevenueReportsService{db: db}
}

// RevenueReport represents a comprehensive revenue report
type RevenueReport struct {
	TimeRange          TimeRange              `json:"time_range"`
	Summary            *RevenueSummary        `json:"summary"`
	BySubscriptionTier map[string]TierRevenue `json:"by_subscription_tier"`
	ByPaymentMethod    map[string]float64     `json:"by_payment_method"`
	ByRegion           map[string]float64     `json:"by_region"`
	MonthlyTrend       []MonthlyRevenueData   `json:"monthly_trend"`
	TopCustomers       []CustomerRevenue      `json:"top_customers"`
	ChurnAnalysis      *ChurnAnalysis         `json:"churn_analysis"`
	Forecasting        *RevenueForecast       `json:"forecasting"`
	GeneratedAt        time.Time              `json:"generated_at"`
}

// RevenueSummary represents summary revenue metrics
type RevenueSummary struct {
	TotalRevenue      float64 `json:"total_revenue"`
	RecurringRevenue  float64 `json:"recurring_revenue"`
	OneTimeRevenue    float64 `json:"one_time_revenue"`
	RefundsTotal      float64 `json:"refunds_total"`
	NetRevenue        float64 `json:"net_revenue"`
	AverageOrderValue float64 `json:"average_order_value"`
	ARPU              float64 `json:"arpu"`
	LTV               float64 `json:"ltv"` // Lifetime Value
	GrowthRate        float64 `json:"growth_rate"`
	ConversionRate    float64 `json:"conversion_rate"`
}

// TierRevenue represents revenue by subscription tier
type TierRevenue struct {
	TierName       string  `json:"tier_name"`
	Revenue        float64 `json:"revenue"`
	UserCount      int64   `json:"user_count"`
	AverageRevenue float64 `json:"average_revenue"`
	GrowthRate     float64 `json:"growth_rate"`
}

// MonthlyRevenueData represents monthly revenue data
type MonthlyRevenueData struct {
	Month            time.Time `json:"month"`
	Revenue          float64   `json:"revenue"`
	NewCustomers     int64     `json:"new_customers"`
	ChurnedCustomers int64     `json:"churned_customers"`
	NetCustomers     int64     `json:"net_customers"`
}

// CustomerRevenue represents top customer revenue data
type CustomerRevenue struct {
	UserID       string    `json:"user_id"`
	Email        string    `json:"email"`
	TotalRevenue float64   `json:"total_revenue"`
	LastPayment  time.Time `json:"last_payment"`
	TierName     string    `json:"tier_name"`
	Status       string    `json:"status"`
}

// ChurnAnalysis represents churn analysis data
type ChurnAnalysis struct {
	ChurnRate       float64            `json:"churn_rate"`
	ChurnedRevenue  float64            `json:"churned_revenue"`
	ChurnReasons    map[string]int64   `json:"churn_reasons"`
	ChurnByTier     map[string]float64 `json:"churn_by_tier"`
	RetentionRate   float64            `json:"retention_rate"`
	AverageLifespan time.Duration      `json:"average_lifespan"`
}

// RevenueForecast represents revenue forecasting data
type RevenueForecast struct {
	NextMonthForecast   float64   `json:"next_month_forecast"`
	NextQuarterForecast float64   `json:"next_quarter_forecast"`
	YearEndForecast     float64   `json:"year_end_forecast"`
	Confidence          float64   `json:"confidence"`
	Methodology         string    `json:"methodology"`
	LastUpdated         time.Time `json:"last_updated"`
}

// GenerateRevenueReport generates a comprehensive revenue report
func (s *RevenueReportsService) GenerateRevenueReport(ctx context.Context, timeRange TimeRange) (*RevenueReport, error) {
	report := &RevenueReport{
		TimeRange:          timeRange,
		GeneratedAt:        time.Now(),
		BySubscriptionTier: make(map[string]TierRevenue),
		ByPaymentMethod:    make(map[string]float64),
		ByRegion:           make(map[string]float64),
	}

	// Generate summary
	summary, err := s.generateRevenueSummary(ctx, timeRange)
	if err != nil {
		return nil, err
	}
	report.Summary = summary

	// Generate tier breakdown
	tierRevenue := s.generateTierRevenue(ctx, timeRange)
	report.BySubscriptionTier = tierRevenue

	// Generate monthly trend
	monthlyTrend := s.generateMonthlyTrend(ctx, timeRange)
	report.MonthlyTrend = monthlyTrend

	// Generate top customers
	topCustomers := s.generateTopCustomers(ctx, timeRange)
	report.TopCustomers = topCustomers

	// Generate churn analysis
	churnAnalysis := s.generateChurnAnalysis(ctx, timeRange)
	report.ChurnAnalysis = churnAnalysis

	// Generate forecasting
	forecast := s.generateRevenueForecast(ctx)
	report.Forecasting = forecast

	// Mock payment method and region data
	report.ByPaymentMethod = map[string]float64{
		"Credit Card":   summary.TotalRevenue * 0.7,
		"PayPal":        summary.TotalRevenue * 0.2,
		"Bank Transfer": summary.TotalRevenue * 0.1,
	}

	report.ByRegion = map[string]float64{
		"North America": summary.TotalRevenue * 0.5,
		"Europe":        summary.TotalRevenue * 0.3,
		"Asia Pacific":  summary.TotalRevenue * 0.15,
		"Other":         summary.TotalRevenue * 0.05,
	}

	return report, nil
}

// generateRevenueSummary generates revenue summary metrics
func (s *RevenueReportsService) generateRevenueSummary(ctx context.Context, timeRange TimeRange) (*RevenueSummary, error) {
	summary := &RevenueSummary{}

	// Get total revenue
	err := s.db.Pool.QueryRow(ctx, `
		SELECT COALESCE(SUM(amount), 0) FROM invoices 
		WHERE status = 'PAID' AND created_at >= $1 AND created_at <= $2
	`, timeRange.StartDate, timeRange.EndDate).Scan(&summary.TotalRevenue)
	if err != nil {
		summary.TotalRevenue = 0
	}

	// Get recurring vs one-time revenue (simplified)
	summary.RecurringRevenue = summary.TotalRevenue * 0.8 // 80% recurring
	summary.OneTimeRevenue = summary.TotalRevenue * 0.2   // 20% one-time

	// Get refunds
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COALESCE(SUM(amount), 0) FROM invoices 
		WHERE status = 'REFUNDED' AND created_at >= $1 AND created_at <= $2
	`, timeRange.StartDate, timeRange.EndDate).Scan(&summary.RefundsTotal)
	if err != nil {
		summary.RefundsTotal = 0
	}

	summary.NetRevenue = summary.TotalRevenue - summary.RefundsTotal

	// Calculate other metrics
	var userCount, invoiceCount int64
	s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM users").Scan(&userCount)
	s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM invoices 
		WHERE status = 'PAID' AND created_at >= $1 AND created_at <= $2
	`, timeRange.StartDate, timeRange.EndDate).Scan(&invoiceCount)

	if userCount > 0 {
		summary.ARPU = summary.TotalRevenue / float64(userCount)
		summary.LTV = summary.ARPU * 24 // Simplified LTV calculation
	}

	if invoiceCount > 0 {
		summary.AverageOrderValue = summary.TotalRevenue / float64(invoiceCount)
	}

	// Mock growth rate and conversion rate
	summary.GrowthRate = 15.5
	summary.ConversionRate = 3.2

	return summary, nil
}

// generateTierRevenue generates revenue breakdown by subscription tier
func (s *RevenueReportsService) generateTierRevenue(ctx context.Context, timeRange TimeRange) map[string]TierRevenue {
	tierRevenue := make(map[string]TierRevenue)

	// Mock tier revenue data
	tierRevenue["Free"] = TierRevenue{
		TierName:       "Free",
		Revenue:        0,
		UserCount:      500,
		AverageRevenue: 0,
		GrowthRate:     5.0,
	}

	tierRevenue["Pro"] = TierRevenue{
		TierName:       "Pro",
		Revenue:        25000,
		UserCount:      250,
		AverageRevenue: 100,
		GrowthRate:     20.0,
	}

	tierRevenue["Enterprise"] = TierRevenue{
		TierName:       "Enterprise",
		Revenue:        50000,
		UserCount:      50,
		AverageRevenue: 1000,
		GrowthRate:     15.0,
	}

	return tierRevenue
}

// generateMonthlyTrend generates monthly revenue trend data
func (s *RevenueReportsService) generateMonthlyTrend(ctx context.Context, timeRange TimeRange) []MonthlyRevenueData {
	var trend []MonthlyRevenueData

	// Generate mock monthly data for the last 12 months
	now := time.Now()
	for i := 11; i >= 0; i-- {
		month := now.AddDate(0, -i, 0)
		baseRevenue := 20000.0 + float64(i)*1500 // Growing trend

		trend = append(trend, MonthlyRevenueData{
			Month:            month,
			Revenue:          baseRevenue,
			NewCustomers:     int64(50 + i*5),
			ChurnedCustomers: int64(10 + i*2),
			NetCustomers:     int64(40 + i*3),
		})
	}

	return trend
}

// generateTopCustomers generates top customer revenue data
func (s *RevenueReportsService) generateTopCustomers(ctx context.Context, timeRange TimeRange) []CustomerRevenue {
	return []CustomerRevenue{
		{
			UserID:       "user_1",
			Email:        "<EMAIL>",
			TotalRevenue: 12000.00,
			LastPayment:  time.Now().Add(-15 * 24 * time.Hour),
			TierName:     "Enterprise",
			Status:       "active",
		},
		{
			UserID:       "user_2",
			Email:        "<EMAIL>",
			TotalRevenue: 8500.00,
			LastPayment:  time.Now().Add(-10 * 24 * time.Hour),
			TierName:     "Pro",
			Status:       "active",
		},
		{
			UserID:       "user_3",
			Email:        "<EMAIL>",
			TotalRevenue: 6000.00,
			LastPayment:  time.Now().Add(-5 * 24 * time.Hour),
			TierName:     "Pro",
			Status:       "active",
		},
	}
}

// generateChurnAnalysis generates churn analysis data
func (s *RevenueReportsService) generateChurnAnalysis(ctx context.Context, timeRange TimeRange) *ChurnAnalysis {
	return &ChurnAnalysis{
		ChurnRate:      5.2,
		ChurnedRevenue: 2500.00,
		ChurnReasons: map[string]int64{
			"Price too high":    15,
			"Missing features":  8,
			"Poor support":      5,
			"Competitor switch": 12,
			"Business closure":  3,
		},
		ChurnByTier: map[string]float64{
			"Free":       2.0,
			"Pro":        4.5,
			"Enterprise": 1.2,
		},
		RetentionRate:   94.8,
		AverageLifespan: 18 * 30 * 24 * time.Hour, // 18 months
	}
}

// generateRevenueForecast generates revenue forecasting data
func (s *RevenueReportsService) generateRevenueForecast(ctx context.Context) *RevenueForecast {
	return &RevenueForecast{
		NextMonthForecast:   28000.00,
		NextQuarterForecast: 85000.00,
		YearEndForecast:     350000.00,
		Confidence:          85.5,
		Methodology:         "Linear regression with seasonal adjustments",
		LastUpdated:         time.Now(),
	}
}
