-- Remove indexes
DROP INDEX IF EXISTS idx_tenants_slug;
DROP INDEX IF EXISTS idx_tenants_domain;
DROP INDEX IF EXISTS idx_tenants_status;
DROP INDEX IF EXISTS idx_users_tenant_id;
DROP INDEX IF EXISTS idx_sites_tenant_id;
DROP INDEX IF EXISTS idx_pages_tenant_id;
DROP INDEX IF EXISTS idx_addons_tenant_id;
DROP INDEX IF EXISTS idx_api_keys_tenant_id;
DROP INDEX IF EXISTS idx_subscriptions_tenant_id;
DROP INDEX IF EXISTS idx_page_content_tenant_id;

-- Remove tenant_id columns from tables (if they exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_content' AND column_name = 'tenant_id') THEN
        ALTER TABLE page_content DROP COLUMN tenant_id;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscriptions' AND column_name = 'tenant_id') THEN
        ALTER TABLE subscriptions DROP COLUMN tenant_id;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'api_keys' AND column_name = 'tenant_id') THEN
        ALTER TABLE api_keys DROP COLUMN tenant_id;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'addons' AND column_name = 'tenant_id') THEN
        ALTER TABLE addons DROP COLUMN tenant_id;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'pages' AND column_name = 'tenant_id') THEN
        ALTER TABLE pages DROP COLUMN tenant_id;
    END IF;
END $$;

-- Remove new columns from tenants table
ALTER TABLE tenants DROP COLUMN IF EXISTS settings;
ALTER TABLE tenants DROP COLUMN IF EXISTS max_sites;
ALTER TABLE tenants DROP COLUMN IF EXISTS max_users;
ALTER TABLE tenants DROP COLUMN IF EXISTS plan_type;
ALTER TABLE tenants DROP COLUMN IF EXISTS status;
ALTER TABLE tenants DROP COLUMN IF EXISTS domain;
ALTER TABLE tenants DROP COLUMN IF EXISTS slug;