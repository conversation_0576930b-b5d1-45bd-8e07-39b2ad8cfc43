{{ define "content" }}
<div>
    <h1>Admin Dashboard</h1>
    
    <div class="grid">
        <!-- API Key Management Card -->
        <div class="card">
            <h3>API Key Management</h3>
            <div class="card-body">
                <form hx-post="/admin/generate-api-key" hx-swap="outerHTML">
                    <div class="form-group">
                        <label>Site</label>
                        <select name="site_id" class="form-select" required>
                            {{ range .Sites }}
                            <option value="{{ .ID }}">{{ .Domain }}</option>
                            {{ end }}
                        </select>
                    </div>
                    <div class="form-group mt-2">
                        <label>Permissions</label>
                        <input type="number" name="permissions"
                            class="form-control"
                            value="15"
                            min="1" max="255"
                            required>
                    </div>
                    <button type="submit" class="btn btn-success mt-2">
                        Generate New Key
                    </button>
                </form>

                <div class="mt-4">
                    <h4>Active Keys</h4>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Key ID</th>
                                    <th>Site</th>
                                    <th>Expires</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{ range .APIKeys }}
                                <tr>
                                    <td class="font-monospace">{{ .KeyID }}</td>
                                    <td>{{ .Site.Domain }}</td>
                                    <td>{{ .ExpiresAt.Format "Jan 02, 2006" }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-danger"
                                                hx-delete="/admin/api-keys/{{ .KeyID }}">
                                            Revoke
                                        </button>
                                    </td>
                                </tr>
                                {{ end }}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>User Management</h3>
            <div hx-get="/admin/users" hx-trigger="load">
                Loading users...
            </div>
        </div>
        
        <div class="card">
            <h3>Vehicle Statistics</h3>
            <div hx-get="/admin/vehicle-stats" hx-trigger="load">
                Loading statistics...
            </div>
        </div>
    </div>
</div>
{{ end }}