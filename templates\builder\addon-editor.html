<!DOCTYPE html>
<html>
<head>
    <title>Addon Builder</title>
    <style>
        #editor-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
        }
        .node-palette {
            position: absolute;
            left: 20px;
            top: 20px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .node-item {
            padding: 8px;
            margin: 4px 0;
            background: #f5f5f5;
            cursor: move;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="node-palette">
        <div class="node-item" data-type="trigger">Trigger</div>
        <div class="node-item" data-type="action">Action</div>
        <div class="node-item" data-type="condition">Condition</div>
    </div>
    <div id="editor-container"></div>

    <div class="controls">
        <button id="save-btn">Save Configuration</button>
        <button id="load-btn">Load Configuration</button>
        <button id="export-btn">Generate Addon</button>
    </div>
</body>
</html>