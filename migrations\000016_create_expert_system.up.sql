-- Create expert applications table
CREATE TABLE IF NOT EXISTS expert_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Application details
    application_status VARCHAR(50) NOT NULL DEFAULT 'pending',
    submitted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES users(id),
    
    -- Professional information
    professional_title VARCHAR(200) NOT NULL,
    years_experience INTEGER NOT NULL,
    expertise_areas TEXT[] NOT NULL,
    hourly_rate DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'GBP',
    
    -- Qualifications
    qualifications JSONB DEFAULT '[]',
    certifications JSONB DEFAULT '[]',
    education JSONB DEFAULT '[]',
    
    -- Portfolio and experience
    portfolio_url VARCHAR(500),
    linkedin_url VARCHAR(500),
    website_url VARCHAR(500),
    work_samples JSONB DEFAULT '[]',
    
    -- Application essay/motivation
    motivation_statement TEXT,
    availability_hours INTEGER DEFAULT 20,
    preferred_project_types TEXT[],
    
    -- Admin notes
    admin_notes TEXT,
    rejection_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create expert profiles table (for approved experts)
CREATE TABLE IF NOT EXISTS expert_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    application_id UUID REFERENCES expert_applications(id),
    
    -- Profile status
    profile_status VARCHAR(50) NOT NULL DEFAULT 'active',
    verified_at TIMESTAMP WITH TIME ZONE,
    
    -- Professional details
    professional_title VARCHAR(200) NOT NULL,
    bio TEXT,
    expertise_areas TEXT[] NOT NULL,
    years_experience INTEGER NOT NULL,
    
    -- Rates and availability
    hourly_rate DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'GBP',
    availability_status VARCHAR(50) DEFAULT 'available',
    max_concurrent_projects INTEGER DEFAULT 3,
    
    -- Contact and links
    portfolio_url VARCHAR(500),
    linkedin_url VARCHAR(500),
    website_url VARCHAR(500),
    
    -- Performance metrics
    total_projects INTEGER DEFAULT 0,
    completed_projects INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    total_earnings DECIMAL(12,2) DEFAULT 0.00,
    
    -- Profile settings
    profile_visibility VARCHAR(50) DEFAULT 'public',
    accepts_new_projects BOOLEAN DEFAULT true,
    response_time_hours INTEGER DEFAULT 24,
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create expert specializations table
CREATE TABLE IF NOT EXISTS expert_specializations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    expert_profile_id UUID NOT NULL REFERENCES expert_profiles(id) ON DELETE CASCADE,
    specialization_name VARCHAR(100) NOT NULL,
    proficiency_level VARCHAR(50) NOT NULL, -- beginner, intermediate, advanced, expert
    years_experience INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_expert_applications_user_id ON expert_applications(user_id);
CREATE INDEX IF NOT EXISTS idx_expert_applications_tenant_id ON expert_applications(tenant_id);
CREATE INDEX IF NOT EXISTS idx_expert_applications_status ON expert_applications(application_status);
CREATE INDEX IF NOT EXISTS idx_expert_applications_submitted_at ON expert_applications(submitted_at);

CREATE INDEX IF NOT EXISTS idx_expert_profiles_user_id ON expert_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_expert_profiles_tenant_id ON expert_profiles(tenant_id);
CREATE INDEX IF NOT EXISTS idx_expert_profiles_status ON expert_profiles(profile_status);
CREATE INDEX IF NOT EXISTS idx_expert_profiles_expertise ON expert_profiles USING GIN(expertise_areas);
CREATE INDEX IF NOT EXISTS idx_expert_profiles_rating ON expert_profiles(average_rating);
CREATE INDEX IF NOT EXISTS idx_expert_profiles_availability ON expert_profiles(availability_status);

CREATE INDEX IF NOT EXISTS idx_expert_specializations_profile_id ON expert_specializations(expert_profile_id);
CREATE INDEX IF NOT EXISTS idx_expert_specializations_name ON expert_specializations(specialization_name);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_expert_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_expert_applications_updated_at BEFORE UPDATE ON expert_applications FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();
CREATE TRIGGER update_expert_profiles_updated_at BEFORE UPDATE ON expert_profiles FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();