package admin

import (
	"context"
	"fmt"
	"goVwPlatformAPI/internal/database"
	"strings"
	"time"
)

// AddonReviewService handles admin addon review and approval operations
type AddonReviewService struct {
	db *database.DB
}

// NewAddonReviewService creates a new addon review service
func NewAddonReviewService(db *database.DB) *AddonReviewService {
	return &AddonReviewService{db: db}
}

// AdminAddonReview represents an addon from admin review perspective
type AdminAddonReview struct {
	ID                  string                 `json:"id"`
	Name                string                 `json:"name"`
	Description         string                 `json:"description"`
	Version             string                 `json:"version"`
	AuthorID            string                 `json:"author_id"`
	AuthorEmail         string                 `json:"author_email"`
	AuthorName          string                 `json:"author_name"`
	State               string                 `json:"state"`
	Category            string                 `json:"category"`
	Tags                []string               `json:"tags"`
	Config              string                 `json:"config"`
	Dependencies        []string               `json:"dependencies"`
	Permissions         []string               `json:"permissions"`
	SecurityScore       float64                `json:"security_score"`
	QualityScore        float64                `json:"quality_score"`
	OverallScore        float64                `json:"overall_score"`
	ReviewNotes         string                 `json:"review_notes"`
	ReviewedBy          string                 `json:"reviewed_by"`
	ReviewedAt          *time.Time             `json:"reviewed_at"`
	ApprovedBy          string                 `json:"approved_by"`
	ApprovedAt          *time.Time             `json:"approved_at"`
	RejectionReason     string                 `json:"rejection_reason"`
	SecurityFlags       []SecurityFlag         `json:"security_flags"`
	QualityFlags        []QualityFlag          `json:"quality_flags"`
	TestResults         []TestResult           `json:"test_results"`
	PerformanceMetrics  *PerformanceMetrics    `json:"performance_metrics"`
	CreatedAt           time.Time              `json:"created_at"`
	UpdatedAt           time.Time              `json:"updated_at"`
	SubmittedAt         *time.Time             `json:"submitted_at"`
	Metadata            map[string]interface{} `json:"metadata"`
	PreviewURL          string                 `json:"preview_url"`
	DownloadCount       int64                  `json:"download_count"`
	Rating              float64                `json:"rating"`
	ReviewCount         int64                  `json:"review_count"`
	RevenueGenerated    float64                `json:"revenue_generated"`
	InstallationCount   int64                  `json:"installation_count"`
	UninstallationCount int64                  `json:"uninstallation_count"`
	ErrorReports        int64                  `json:"error_reports"`
	SupportTickets      int64                  `json:"support_tickets"`
}

// SecurityFlag represents a security concern with an addon
type SecurityFlag struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"`     // xss, injection, unsafe_eval, external_requests, etc.
	Severity     string                 `json:"severity"` // low, medium, high, critical
	Description  string                 `json:"description"`
	Location     string                 `json:"location"` // file/line where issue was found
	Suggestion   string                 `json:"suggestion"`
	AutoDetected bool                   `json:"auto_detected"`
	Status       string                 `json:"status"` // pending, acknowledged, fixed, dismissed
	CreatedAt    time.Time              `json:"created_at"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// QualityFlag represents a quality concern with an addon
type QualityFlag struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"` // performance, usability, documentation, etc.
	Severity     string                 `json:"severity"`
	Description  string                 `json:"description"`
	Location     string                 `json:"location"`
	Suggestion   string                 `json:"suggestion"`
	AutoDetected bool                   `json:"auto_detected"`
	Status       string                 `json:"status"`
	CreatedAt    time.Time              `json:"created_at"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// TestResult represents the result of an automated test
type TestResult struct {
	ID       string                 `json:"id"`
	TestType string                 `json:"test_type"` // security, performance, compatibility, etc.
	Status   string                 `json:"status"`    // passed, failed, warning, skipped
	Score    float64                `json:"score"`
	Details  string                 `json:"details"`
	Duration time.Duration          `json:"duration"`
	RunAt    time.Time              `json:"run_at"`
	Metadata map[string]interface{} `json:"metadata"`
}

// AddonReviewFilters represents filters for addon review
type AddonReviewFilters struct {
	State            string     `json:"state"`
	Category         string     `json:"category"`
	AuthorID         string     `json:"author_id"`
	MinSecurityScore *float64   `json:"min_security_score"`
	MaxSecurityScore *float64   `json:"max_security_score"`
	MinQualityScore  *float64   `json:"min_quality_score"`
	MaxQualityScore  *float64   `json:"max_quality_score"`
	HasSecurityFlags *bool      `json:"has_security_flags"`
	HasQualityFlags  *bool      `json:"has_quality_flags"`
	SubmittedAfter   *time.Time `json:"submitted_after"`
	SubmittedBefore  *time.Time `json:"submitted_before"`
	ReviewedBy       string     `json:"reviewed_by"`
	Priority         string     `json:"priority"` // high, medium, low
}

// AddonReviewListResponse represents paginated addon review list
type AddonReviewListResponse struct {
	Addons     []AdminAddonReview `json:"addons"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"page_size"`
	TotalPages int                `json:"total_pages"`
}

// GetAddonsForReview retrieves addons that need review
func (s *AddonReviewService) GetAddonsForReview(ctx context.Context, filters *AddonReviewFilters, page, pageSize int) (*AddonReviewListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 25
	}

	offset := (page - 1) * pageSize

	// Build query with filters
	query := `
		SELECT 
			ac.id, ac.name, ac.config, ac.state, ac.created_by, 
			u.email, CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as author_name,
			ac.created_at, ac.updated_at
		FROM addon_configs ac
		JOIN users u ON ac.created_by = u.id
		WHERE ac.state IN ('REVIEW', 'DEVELOPMENT', 'REJECTED', 'PRODUCTION')
	`

	args := []interface{}{}
	argCount := 0

	// Apply filters
	if filters != nil {
		if filters.State != "" {
			argCount++
			query += fmt.Sprintf(" AND ac.state = $%d", argCount)
			args = append(args, filters.State)
		}

		if filters.AuthorID != "" {
			argCount++
			query += fmt.Sprintf(" AND ac.created_by = $%d", argCount)
			args = append(args, filters.AuthorID)
		}

		if filters.SubmittedAfter != nil {
			argCount++
			query += fmt.Sprintf(" AND ac.created_at >= $%d", argCount)
			args = append(args, *filters.SubmittedAfter)
		}

		if filters.SubmittedBefore != nil {
			argCount++
			query += fmt.Sprintf(" AND ac.created_at <= $%d", argCount)
			args = append(args, *filters.SubmittedBefore)
		}
	}

	// Add ordering and pagination
	query += " ORDER BY ac.created_at DESC"
	argCount++
	query += fmt.Sprintf(" LIMIT $%d", argCount)
	args = append(args, pageSize)
	argCount++
	query += fmt.Sprintf(" OFFSET $%d", argCount)
	args = append(args, offset)

	// Execute query
	rows, err := s.db.Pool.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query addons for review: %w", err)
	}
	defer rows.Close()

	var addons []AdminAddonReview
	for rows.Next() {
		var addon AdminAddonReview
		var config *string

		err := rows.Scan(
			&addon.ID, &addon.Name, &config, &addon.State, &addon.AuthorID,
			&addon.AuthorEmail, &addon.AuthorName, &addon.CreatedAt, &addon.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan addon: %w", err)
		}

		if config != nil {
			addon.Config = *config
		}

		// Enrich addon data
		if err := s.enrichAddonReviewData(ctx, &addon); err != nil {
			// Log error but continue
			fmt.Printf("Warning: failed to enrich addon data for %s: %v\n", addon.ID, err)
		}

		addons = append(addons, addon)
	}

	// Get total count
	countQuery := `
		SELECT COUNT(*) 
		FROM addon_configs ac
		JOIN users u ON ac.created_by = u.id
		WHERE ac.state IN ('REVIEW', 'DEVELOPMENT', 'REJECTED', 'PRODUCTION')
	`

	var total int64
	err = s.db.Pool.QueryRow(ctx, countQuery).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("failed to count addons: %w", err)
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &AddonReviewListResponse{
		Addons:     addons,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// enrichAddonReviewData adds additional data to addon review
func (s *AddonReviewService) enrichAddonReviewData(ctx context.Context, addon *AdminAddonReview) error {
	// Calculate security score
	addon.SecurityScore = s.calculateSecurityScore(addon.Config)

	// Calculate quality score
	addon.QualityScore = s.calculateQualityScore(addon.Config, addon.Name)

	// Calculate overall score
	addon.OverallScore = (addon.SecurityScore + addon.QualityScore) / 2

	// Get security flags
	addon.SecurityFlags = s.getSecurityFlags(addon.Config)

	// Get quality flags
	addon.QualityFlags = s.getQualityFlags(addon.Config, addon.Name)

	// Run automated tests
	addon.TestResults = s.runAutomatedTests(addon.Config)

	// Calculate performance metrics
	addon.PerformanceMetrics = s.calculatePerformanceMetrics(addon.Config)

	// Set preview URL
	addon.PreviewURL = fmt.Sprintf("/admin/addon-preview/%s", addon.ID)

	// Get usage statistics (simplified)
	addon.DownloadCount = 0
	addon.Rating = 0.0
	addon.ReviewCount = 0
	addon.RevenueGenerated = 0.0
	addon.InstallationCount = 0
	addon.UninstallationCount = 0
	addon.ErrorReports = 0
	addon.SupportTickets = 0

	// Initialize metadata
	addon.Metadata = make(map[string]interface{})
	addon.Metadata["last_analyzed"] = time.Now()
	addon.Metadata["analysis_version"] = "1.0"

	return nil
}

// calculateSecurityScore calculates a security score for an addon
func (s *AddonReviewService) calculateSecurityScore(config string) float64 {
	score := 100.0 // Start with perfect score

	configLower := strings.ToLower(config)

	// Deduct points for security risks
	securityRisks := map[string]float64{
		"eval(":          -30.0, // Very dangerous
		"function(":      -5.0,  // Dynamic function creation
		"document.write": -20.0, // DOM manipulation
		"innerhtml":      -15.0, // XSS risk
		"outerhtml":      -15.0, // XSS risk
		"script":         -25.0, // Script injection
		"iframe":         -20.0, // Embedding risk
		"object":         -20.0, // Object embedding
		"embed":          -20.0, // Content embedding
		"fetch":          -10.0, // External requests
		"xmlhttprequest": -10.0, // AJAX requests
		"websocket":      -15.0, // Network connections
		"localstorage":   -5.0,  // Data storage
		"sessionstorage": -5.0,  // Session data
		"cookie":         -5.0,  // Cookie access
		"window.open":    -10.0, // Popup windows
		"location.href":  -10.0, // Navigation
		"history.":       -5.0,  // History manipulation
	}

	for risk, penalty := range securityRisks {
		if strings.Contains(configLower, risk) {
			score += penalty
		}
	}

	// Ensure score is between 0 and 100
	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	return score
}

// calculateQualityScore calculates a quality score for an addon
func (s *AddonReviewService) calculateQualityScore(config, name string) float64 {
	score := 50.0 // Start with neutral score

	// Add points for good practices
	if len(name) > 5 && len(name) < 50 {
		score += 10.0 // Good name length
	}

	if strings.Contains(config, "description") {
		score += 10.0 // Has description
	}

	if strings.Contains(config, "version") {
		score += 5.0 // Has version
	}

	if strings.Contains(config, "dependencies") {
		score += 5.0 // Declares dependencies
	}

	// Check for documentation
	if strings.Contains(strings.ToLower(config), "readme") ||
		strings.Contains(strings.ToLower(config), "documentation") {
		score += 15.0
	}

	// Check for error handling
	if strings.Contains(config, "try") && strings.Contains(config, "catch") {
		score += 10.0
	}

	// Check for code organization
	if strings.Contains(config, "function") || strings.Contains(config, "class") {
		score += 10.0
	}

	// Deduct points for quality issues
	configLower := strings.ToLower(config)

	// Check for excessive complexity
	if len(config) > 10000 {
		score -= 10.0 // Very large config
	}

	// Check for poor naming
	if strings.Contains(configLower, "test") || strings.Contains(configLower, "temp") {
		score -= 5.0
	}

	// Ensure score is between 0 and 100
	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	return score
}

// getSecurityFlags identifies security flags in addon config
func (s *AddonReviewService) getSecurityFlags(config string) []SecurityFlag {
	var flags []SecurityFlag

	configLower := strings.ToLower(config)

	// Check for dangerous patterns
	dangerousPatterns := map[string]SecurityFlag{
		"eval(": {
			Type:        "unsafe_eval",
			Severity:    "critical",
			Description: "Uses eval() which can execute arbitrary code",
			Suggestion:  "Replace eval() with safer alternatives like JSON.parse()",
		},
		"document.write": {
			Type:        "dom_manipulation",
			Severity:    "high",
			Description: "Uses document.write which can cause XSS vulnerabilities",
			Suggestion:  "Use safer DOM manipulation methods",
		},
		"innerhtml": {
			Type:        "xss_risk",
			Severity:    "high",
			Description: "Uses innerHTML which can lead to XSS attacks",
			Suggestion:  "Use textContent or sanitize HTML input",
		},
		"script": {
			Type:        "script_injection",
			Severity:    "critical",
			Description: "Contains script tags which may indicate code injection",
			Suggestion:  "Remove script tags and use proper JavaScript modules",
		},
	}

	for pattern, flagTemplate := range dangerousPatterns {
		if strings.Contains(configLower, pattern) {
			flag := flagTemplate
			flag.ID = fmt.Sprintf("sec_%d", time.Now().UnixNano())
			flag.Location = fmt.Sprintf("Config contains: %s", pattern)
			flag.AutoDetected = true
			flag.Status = "pending"
			flag.CreatedAt = time.Now()
			flag.Metadata = make(map[string]interface{})

			flags = append(flags, flag)
		}
	}

	return flags
}

// getQualityFlags identifies quality flags in addon config
func (s *AddonReviewService) getQualityFlags(config, name string) []QualityFlag {
	var flags []QualityFlag

	// Check for quality issues
	if len(name) < 3 {
		flags = append(flags, QualityFlag{
			ID:           fmt.Sprintf("qual_%d", time.Now().UnixNano()),
			Type:         "naming",
			Severity:     "medium",
			Description:  "Addon name is too short",
			Suggestion:   "Use a more descriptive name (at least 3 characters)",
			AutoDetected: true,
			Status:       "pending",
			CreatedAt:    time.Now(),
			Metadata:     make(map[string]interface{}),
		})
	}

	if len(config) < 100 {
		flags = append(flags, QualityFlag{
			ID:           fmt.Sprintf("qual_%d", time.Now().UnixNano()),
			Type:         "completeness",
			Severity:     "medium",
			Description:  "Addon configuration seems incomplete",
			Suggestion:   "Add more functionality and configuration",
			AutoDetected: true,
			Status:       "pending",
			CreatedAt:    time.Now(),
			Metadata:     make(map[string]interface{}),
		})
	}

	if !strings.Contains(strings.ToLower(config), "description") {
		flags = append(flags, QualityFlag{
			ID:           fmt.Sprintf("qual_%d", time.Now().UnixNano()),
			Type:         "documentation",
			Severity:     "low",
			Description:  "Missing description",
			Suggestion:   "Add a description to help users understand the addon",
			AutoDetected: true,
			Status:       "pending",
			CreatedAt:    time.Now(),
			Metadata:     make(map[string]interface{}),
		})
	}

	return flags
}

// runAutomatedTests runs automated tests on addon config
func (s *AddonReviewService) runAutomatedTests(config string) []TestResult {
	var results []TestResult

	// Security test
	securityScore := s.calculateSecurityScore(config)
	securityStatus := "passed"
	if securityScore < 70 {
		securityStatus = "failed"
	} else if securityScore < 85 {
		securityStatus = "warning"
	}

	results = append(results, TestResult{
		ID:       fmt.Sprintf("test_%d", time.Now().UnixNano()),
		TestType: "security",
		Status:   securityStatus,
		Score:    securityScore,
		Details:  fmt.Sprintf("Security score: %.1f/100", securityScore),
		Duration: 100 * time.Millisecond,
		RunAt:    time.Now(),
		Metadata: make(map[string]interface{}),
	})

	// Quality test
	qualityScore := s.calculateQualityScore(config, "")
	qualityStatus := "passed"
	if qualityScore < 60 {
		qualityStatus = "failed"
	} else if qualityScore < 80 {
		qualityStatus = "warning"
	}

	results = append(results, TestResult{
		ID:       fmt.Sprintf("test_%d", time.Now().UnixNano()),
		TestType: "quality",
		Status:   qualityStatus,
		Score:    qualityScore,
		Details:  fmt.Sprintf("Quality score: %.1f/100", qualityScore),
		Duration: 150 * time.Millisecond,
		RunAt:    time.Now(),
		Metadata: make(map[string]interface{}),
	})

	// Performance test (simplified)
	results = append(results, TestResult{
		ID:       fmt.Sprintf("test_%d", time.Now().UnixNano()),
		TestType: "performance",
		Status:   "passed",
		Score:    85.0,
		Details:  "Performance within acceptable limits",
		Duration: 200 * time.Millisecond,
		RunAt:    time.Now(),
		Metadata: make(map[string]interface{}),
	})

	return results
}

// calculatePerformanceMetrics calculates performance metrics for addon
func (s *AddonReviewService) calculatePerformanceMetrics(config string) *PerformanceMetrics {
	return &PerformanceMetrics{
		ResponseTimes: map[string]time.Duration{
			"addon": 50 * time.Millisecond,
		},
		ThroughputRPS:  100.0,
		ErrorRate:      0.0,
		ActiveSessions: 1,
		QueueDepth:     0,
	}
}

// GetAddonReview retrieves a single addon for review
func (s *AddonReviewService) GetAddonReview(ctx context.Context, addonID string) (*AdminAddonReview, error) {
	query := `
		SELECT 
			ac.id, ac.name, ac.config, ac.state, ac.created_by, 
			u.email, CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as author_name,
			ac.created_at, ac.updated_at
		FROM addon_configs ac
		JOIN users u ON ac.created_by = u.id
		WHERE ac.id = $1
	`

	var addon AdminAddonReview
	var config *string

	err := s.db.Pool.QueryRow(ctx, query, addonID).Scan(
		&addon.ID, &addon.Name, &config, &addon.State, &addon.AuthorID,
		&addon.AuthorEmail, &addon.AuthorName, &addon.CreatedAt, &addon.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get addon for review: %w", err)
	}

	if config != nil {
		addon.Config = *config
	}

	// Enrich addon data
	if err := s.enrichAddonReviewData(ctx, &addon); err != nil {
		return nil, fmt.Errorf("failed to enrich addon data: %w", err)
	}

	return &addon, nil
}

// ApproveAddon approves an addon after review
func (s *AddonReviewService) ApproveAddon(ctx context.Context, addonID, reviewerID, notes string) error {
	_, err := s.db.Pool.Exec(ctx, `
		UPDATE addon_configs 
		SET state = 'PRODUCTION', updated_at = NOW()
		WHERE id = $1
	`, addonID)

	if err != nil {
		return fmt.Errorf("failed to approve addon: %w", err)
	}

	// Log the review action
	return s.logAddonReviewAction(ctx, addonID, reviewerID, "approve", notes)
}

// RejectAddon rejects an addon after review
func (s *AddonReviewService) RejectAddon(ctx context.Context, addonID, reviewerID, reason string) error {
	_, err := s.db.Pool.Exec(ctx, `
		UPDATE addon_configs 
		SET state = 'REJECTED', updated_at = NOW()
		WHERE id = $1
	`, addonID)

	if err != nil {
		return fmt.Errorf("failed to reject addon: %w", err)
	}

	// Log the review action
	return s.logAddonReviewAction(ctx, addonID, reviewerID, "reject", reason)
}

// RequestChanges requests changes to an addon
func (s *AddonReviewService) RequestChanges(ctx context.Context, addonID, reviewerID, feedback string) error {
	_, err := s.db.Pool.Exec(ctx, `
		UPDATE addon_configs 
		SET state = 'DEVELOPMENT', updated_at = NOW()
		WHERE id = $1
	`, addonID)

	if err != nil {
		return fmt.Errorf("failed to request changes for addon: %w", err)
	}

	// Log the review action
	return s.logAddonReviewAction(ctx, addonID, reviewerID, "request_changes", feedback)
}

// logAddonReviewAction logs a review action
func (s *AddonReviewService) logAddonReviewAction(ctx context.Context, addonID, reviewerID, action, notes string) error {
	// For now, just log to stdout (in production, would store in database)
	fmt.Printf("ADDON_REVIEW_ACTION: reviewer=%s addon=%s action=%s notes=%s\n",
		reviewerID, addonID, action, notes)
	return nil
}

// GetAddonReviewStats returns addon review statistics
func (s *AddonReviewService) GetAddonReviewStats(ctx context.Context) (*AddonReviewStats, error) {
	stats := &AddonReviewStats{
		ByState: make(map[string]int64),
	}

	// Count addons by state
	rows, err := s.db.Pool.Query(ctx, `
		SELECT state, COUNT(*) 
		FROM addon_configs 
		GROUP BY state
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to get addon stats by state: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var state string
		var count int64
		if err := rows.Scan(&state, &count); err != nil {
			return nil, err
		}
		stats.ByState[state] = count
	}

	// Calculate other metrics
	stats.PendingReview = stats.ByState["REVIEW"]
	stats.InDevelopment = stats.ByState["DEVELOPMENT"]
	stats.Approved = stats.ByState["PRODUCTION"]
	stats.Rejected = stats.ByState["REJECTED"]

	// Average review time (simplified)
	stats.AverageReviewTimeDays = 2.5

	return stats, nil
}

// AddonReviewStats represents addon review statistics
type AddonReviewStats struct {
	PendingReview         int64            `json:"pending_review"`
	InDevelopment         int64            `json:"in_development"`
	Approved              int64            `json:"approved"`
	Rejected              int64            `json:"rejected"`
	ByState               map[string]int64 `json:"by_state"`
	AverageReviewTimeDays float64          `json:"average_review_time_days"`
	HighSecurityRiskCount int64            `json:"high_security_risk_count"`
	LowQualityCount       int64            `json:"low_quality_count"`
}
