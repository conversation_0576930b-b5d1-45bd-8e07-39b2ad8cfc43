package addon

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// SandboxEnvironment provides a secure development environment for addons
type SandboxEnvironment struct {
	metadataManager *MetadataManager
	testingEnv      *TestingEnvironment
	activeSandboxes map[string]*SandboxInstance
	mutex           sync.RWMutex
	config          SandboxConfig
}

// SandboxConfig contains configuration for the sandbox environment
type SandboxConfig struct {
	MaxInstances     int           `json:"maxInstances"`
	DefaultTimeout   time.Duration `json:"defaultTimeout"`
	MaxMemoryMB      int           `json:"maxMemoryMB"`
	MaxCPUPercent    float64       `json:"maxCpuPercent"`
	AllowedDomains   []string      `json:"allowedDomains"`
	BlockedDomains   []string      `json:"blockedDomains"`
	EnableNetworking bool          `json:"enableNetworking"`
	EnableFileSystem bool          `json:"enableFileSystem"`
	WorkspaceDir     string        `json:"workspaceDir"`
}

// SandboxInstance represents an active sandbox instance
type SandboxInstance struct {
	ID          string                 `json:"id"`
	AddonID     string                 `json:"addonId"`
	UserID      string                 `json:"userId"`
	Status      SandboxStatus          `json:"status"`
	CreatedAt   time.Time              `json:"createdAt"`
	LastUsed    time.Time              `json:"lastUsed"`
	ExpiresAt   time.Time              `json:"expiresAt"`
	Config      *AddonConfiguration    `json:"config"`
	Workspace   string                 `json:"workspace"`
	Resources   SandboxResources       `json:"resources"`
	Permissions SandboxPermissions     `json:"permissions"`
	Logs        []SandboxLog           `json:"logs"`
	Context     map[string]interface{} `json:"context"`
}

// SandboxStatus represents the status of a sandbox instance
type SandboxStatus string

const (
	SandboxStatusCreating   SandboxStatus = "creating"
	SandboxStatusActive     SandboxStatus = "active"
	SandboxStatusSuspended  SandboxStatus = "suspended"
	SandboxStatusTerminated SandboxStatus = "terminated"
	SandboxStatusError      SandboxStatus = "error"
)

// SandboxResources tracks resource usage
type SandboxResources struct {
	MemoryUsageMB     int       `json:"memoryUsageMB"`
	CPUUsagePercent   float64   `json:"cpuUsagePercent"`
	DiskUsageMB       int       `json:"diskUsageMB"`
	NetworkCallsOut   int       `json:"networkCallsOut"`
	NetworkCallsIn    int       `json:"networkCallsIn"`
	ExecutionTime     int64     `json:"executionTime"`
	LastResourceCheck time.Time `json:"lastResourceCheck"`
}

// SandboxPermissions defines what the sandbox can access
type SandboxPermissions struct {
	AllowNetworkAccess bool     `json:"allowNetworkAccess"`
	AllowFileSystem    bool     `json:"allowFileSystem"`
	AllowedAPIs        []string `json:"allowedApis"`
	AllowedDomains     []string `json:"allowedDomains"`
	MaxExecutionTime   int      `json:"maxExecutionTime"`
	MaxMemoryMB        int      `json:"maxMemoryMB"`
	MaxDiskMB          int      `json:"maxDiskMB"`
}

// SandboxLog represents a log entry from sandbox execution
type SandboxLog struct {
	Timestamp time.Time              `json:"timestamp"`
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Source    string                 `json:"source"`
	Data      map[string]interface{} `json:"data,omitempty"`
}

// NewSandboxEnvironment creates a new sandbox environment
func NewSandboxEnvironment(config SandboxConfig) *SandboxEnvironment {
	if config.MaxInstances == 0 {
		config.MaxInstances = 10
	}
	if config.DefaultTimeout == 0 {
		config.DefaultTimeout = time.Hour
	}
	if config.MaxMemoryMB == 0 {
		config.MaxMemoryMB = 512
	}
	if config.MaxCPUPercent == 0 {
		config.MaxCPUPercent = 50.0
	}
	if config.WorkspaceDir == "" {
		config.WorkspaceDir = "/tmp/addon-sandboxes"
	}

	return &SandboxEnvironment{
		metadataManager: NewMetadataManager(),
		testingEnv:      NewTestingEnvironment(),
		activeSandboxes: make(map[string]*SandboxInstance),
		config:          config,
	}
}

// CreateSandbox creates a new sandbox instance for addon development
func (se *SandboxEnvironment) CreateSandbox(ctx context.Context, addonID, userID string, config *AddonConfiguration) (*SandboxInstance, error) {
	se.mutex.Lock()
	defer se.mutex.Unlock()

	// Check if we've reached the maximum number of instances
	if len(se.activeSandboxes) >= se.config.MaxInstances {
		return nil, fmt.Errorf("maximum number of sandbox instances reached")
	}

	// Validate addon configuration
	if err := se.metadataManager.ValidateMetadata(&config.Metadata); err != nil {
		return nil, fmt.Errorf("invalid addon metadata: %w", err)
	}

	// Create sandbox instance
	instance := &SandboxInstance{
		ID:        generateSandboxID(),
		AddonID:   addonID,
		UserID:    userID,
		Status:    SandboxStatusCreating,
		CreatedAt: time.Now(),
		LastUsed:  time.Now(),
		ExpiresAt: time.Now().Add(se.config.DefaultTimeout),
		Config:    config,
		Logs:      []SandboxLog{},
		Context:   make(map[string]interface{}),
		Resources: SandboxResources{
			LastResourceCheck: time.Now(),
		},
		Permissions: SandboxPermissions{
			AllowNetworkAccess: se.config.EnableNetworking,
			AllowFileSystem:    se.config.EnableFileSystem,
			AllowedDomains:     se.config.AllowedDomains,
			MaxExecutionTime:   int(se.config.DefaultTimeout.Seconds()),
			MaxMemoryMB:        se.config.MaxMemoryMB,
			MaxDiskMB:          100, // Default 100MB disk space
		},
	}

	// Create workspace directory
	workspace, err := se.createWorkspace(instance.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to create workspace: %w", err)
	}
	instance.Workspace = workspace

	// Initialize sandbox environment
	if err := se.initializeSandbox(instance); err != nil {
		se.cleanupWorkspace(workspace)
		return nil, fmt.Errorf("failed to initialize sandbox: %w", err)
	}

	// Store the instance
	se.activeSandboxes[instance.ID] = instance
	instance.Status = SandboxStatusActive

	se.addLog(instance, "info", "Sandbox created successfully", "system", nil)

	return instance, nil
}

// GetSandbox retrieves a sandbox instance by ID
func (se *SandboxEnvironment) GetSandbox(sandboxID string) (*SandboxInstance, error) {
	se.mutex.RLock()
	defer se.mutex.RUnlock()

	instance, exists := se.activeSandboxes[sandboxID]
	if !exists {
		return nil, fmt.Errorf("sandbox not found: %s", sandboxID)
	}

	return instance, nil
}

// ExecuteInSandbox executes addon code within the sandbox
func (se *SandboxEnvironment) ExecuteInSandbox(ctx context.Context, sandboxID string, input map[string]interface{}) (*SandboxExecutionResult, error) {
	instance, err := se.GetSandbox(sandboxID)
	if err != nil {
		return nil, err
	}

	if instance.Status != SandboxStatusActive {
		return nil, fmt.Errorf("sandbox is not active: %s", instance.Status)
	}

	// Update last used timestamp
	se.mutex.Lock()
	instance.LastUsed = time.Now()
	se.mutex.Unlock()

	se.addLog(instance, "info", "Starting addon execution", "executor", map[string]interface{}{
		"inputSize": len(input),
	})

	// Create execution context with timeout
	execCtx, cancel := context.WithTimeout(ctx, time.Duration(instance.Permissions.MaxExecutionTime)*time.Second)
	defer cancel()

	// Execute the addon
	result, err := se.executeAddonWorkflow(execCtx, instance, input)
	if err != nil {
		se.addLog(instance, "error", fmt.Sprintf("Execution failed: %s", err.Error()), "executor", nil)
		return nil, fmt.Errorf("execution failed: %w", err)
	}

	se.addLog(instance, "info", "Addon execution completed", "executor", map[string]interface{}{
		"executionTime": result.ExecutionTime.String(),
		"success":       result.Success,
	})

	return result, nil
}

// SandboxExecutionResult contains the result of sandbox execution
type SandboxExecutionResult struct {
	Success       bool                   `json:"success"`
	Output        map[string]interface{} `json:"output"`
	ExecutionTime time.Duration          `json:"executionTime"`
	ResourceUsage SandboxResources       `json:"resourceUsage"`
	Logs          []SandboxLog           `json:"logs"`
	Errors        []string               `json:"errors"`
	Warnings      []string               `json:"warnings"`
}

// executeAddonWorkflow executes the addon workflow in the sandbox
func (se *SandboxEnvironment) executeAddonWorkflow(ctx context.Context, instance *SandboxInstance, input map[string]interface{}) (*SandboxExecutionResult, error) {
	startTime := time.Now()

	result := &SandboxExecutionResult{
		Success:  true,
		Output:   make(map[string]interface{}),
		Logs:     []SandboxLog{},
		Errors:   []string{},
		Warnings: []string{},
	}

	// Simulate workflow execution (in a real implementation, this would execute the actual workflow)
	for nodeID, node := range instance.Config.Workflow.Nodes {
		select {
		case <-ctx.Done():
			result.Success = false
			result.Errors = append(result.Errors, "Execution timed out")
			return result, fmt.Errorf("execution timed out")
		default:
		}

		// Simulate node execution
		if err := se.executeNode(instance, node, input); err != nil {
			result.Success = false
			result.Errors = append(result.Errors, fmt.Sprintf("Node %s failed: %s", nodeID, err.Error()))
		}

		// Simulate some processing time
		time.Sleep(time.Millisecond * 10)
	}

	result.ExecutionTime = time.Since(startTime)

	// Update resource usage
	se.updateResourceUsage(instance)
	result.ResourceUsage = instance.Resources

	// Simulate output generation
	result.Output = map[string]interface{}{
		"processed":     true,
		"timestamp":     time.Now().Format(time.RFC3339),
		"nodesExecuted": len(instance.Config.Workflow.Nodes),
		"inputData":     input,
	}

	return result, nil
}

// executeNode simulates execution of a single workflow node
func (se *SandboxEnvironment) executeNode(instance *SandboxInstance, node WorkflowNode, input map[string]interface{}) error {
	// Check permissions and resource limits
	if err := se.checkResourceLimits(instance); err != nil {
		return fmt.Errorf("resource limit exceeded: %w", err)
	}

	// Simulate different node types
	switch node.Type {
	case "trigger":
		se.addLog(instance, "debug", fmt.Sprintf("Executing trigger node: %s", node.Label), "executor", nil)
		return nil
	case "action":
		se.addLog(instance, "debug", fmt.Sprintf("Executing action node: %s", node.Label), "executor", nil)
		// Simulate action execution
		if node.Config.Timeout > 0 && node.Config.Timeout < 100 {
			return fmt.Errorf("action timeout too low")
		}
		return nil
	case "condition":
		se.addLog(instance, "debug", fmt.Sprintf("Executing condition node: %s", node.Label), "executor", nil)
		// Simulate condition evaluation
		if len(node.Config.Conditions) == 0 {
			return fmt.Errorf("condition node missing conditions")
		}
		return nil
	default:
		return fmt.Errorf("unknown node type: %s", node.Type)
	}
}

// checkResourceLimits checks if the sandbox is within resource limits
func (se *SandboxEnvironment) checkResourceLimits(instance *SandboxInstance) error {
	// Simulate resource checking
	if instance.Resources.MemoryUsageMB > instance.Permissions.MaxMemoryMB {
		return fmt.Errorf("memory limit exceeded: %d MB > %d MB",
			instance.Resources.MemoryUsageMB, instance.Permissions.MaxMemoryMB)
	}

	if instance.Resources.DiskUsageMB > instance.Permissions.MaxDiskMB {
		return fmt.Errorf("disk limit exceeded: %d MB > %d MB",
			instance.Resources.DiskUsageMB, instance.Permissions.MaxDiskMB)
	}

	return nil
}

// updateResourceUsage updates the resource usage metrics
func (se *SandboxEnvironment) updateResourceUsage(instance *SandboxInstance) {
	// Simulate resource usage (in a real implementation, this would query actual system metrics)
	instance.Resources.MemoryUsageMB = 64 + len(instance.Config.Workflow.Nodes)*8
	instance.Resources.CPUUsagePercent = float64(len(instance.Config.Workflow.Nodes)) * 2.5
	instance.Resources.DiskUsageMB = 10 + len(instance.Logs)/10
	instance.Resources.ExecutionTime += 100 // milliseconds
	instance.Resources.LastResourceCheck = time.Now()
}

// TerminateSandbox terminates a sandbox instance
func (se *SandboxEnvironment) TerminateSandbox(sandboxID string) error {
	se.mutex.Lock()
	defer se.mutex.Unlock()

	instance, exists := se.activeSandboxes[sandboxID]
	if !exists {
		return fmt.Errorf("sandbox not found: %s", sandboxID)
	}

	// Clean up workspace
	if err := se.cleanupWorkspace(instance.Workspace); err != nil {
		se.addLog(instance, "warning", fmt.Sprintf("Failed to cleanup workspace: %s", err.Error()), "system", nil)
	}

	// Update status and remove from active sandboxes
	instance.Status = SandboxStatusTerminated
	delete(se.activeSandboxes, sandboxID)

	se.addLog(instance, "info", "Sandbox terminated", "system", nil)

	return nil
}

// ListSandboxes returns all active sandboxes for a user
func (se *SandboxEnvironment) ListSandboxes(userID string) []*SandboxInstance {
	se.mutex.RLock()
	defer se.mutex.RUnlock()

	var userSandboxes []*SandboxInstance
	for _, instance := range se.activeSandboxes {
		if instance.UserID == userID {
			userSandboxes = append(userSandboxes, instance)
		}
	}

	return userSandboxes
}

// CleanupExpiredSandboxes removes expired sandbox instances
func (se *SandboxEnvironment) CleanupExpiredSandboxes() {
	se.mutex.Lock()
	defer se.mutex.Unlock()

	now := time.Now()
	var expiredIDs []string

	for id, instance := range se.activeSandboxes {
		if now.After(instance.ExpiresAt) {
			expiredIDs = append(expiredIDs, id)
		}
	}

	for _, id := range expiredIDs {
		instance := se.activeSandboxes[id]
		se.cleanupWorkspace(instance.Workspace)
		instance.Status = SandboxStatusTerminated
		delete(se.activeSandboxes, id)
	}
}

// Helper methods

func (se *SandboxEnvironment) createWorkspace(sandboxID string) (string, error) {
	workspaceDir := filepath.Join(se.config.WorkspaceDir, sandboxID)

	if err := os.MkdirAll(workspaceDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create workspace directory: %w", err)
	}

	// Create basic sandbox structure
	dirs := []string{"src", "dist", "logs", "temp"}
	for _, dir := range dirs {
		if err := os.MkdirAll(filepath.Join(workspaceDir, dir), 0755); err != nil {
			return "", fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}

	return workspaceDir, nil
}

func (se *SandboxEnvironment) cleanupWorkspace(workspace string) error {
	if workspace == "" {
		return nil
	}
	return os.RemoveAll(workspace)
}

func (se *SandboxEnvironment) initializeSandbox(instance *SandboxInstance) error {
	// Create initial configuration files
	configPath := filepath.Join(instance.Workspace, "addon-config.json")
	configData, err := json.MarshalIndent(instance.Config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := ioutil.WriteFile(configPath, configData, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	// Create sandbox metadata
	metadataPath := filepath.Join(instance.Workspace, "sandbox-metadata.json")
	metadata := map[string]interface{}{
		"sandboxId":   instance.ID,
		"addonId":     instance.AddonID,
		"userId":      instance.UserID,
		"createdAt":   instance.CreatedAt,
		"permissions": instance.Permissions,
	}

	metadataData, err := json.MarshalIndent(metadata, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal metadata: %w", err)
	}

	if err := ioutil.WriteFile(metadataPath, metadataData, 0644); err != nil {
		return fmt.Errorf("failed to write metadata file: %w", err)
	}

	return nil
}

func (se *SandboxEnvironment) addLog(instance *SandboxInstance, level, message, source string, data map[string]interface{}) {
	log := SandboxLog{
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		Source:    source,
		Data:      data,
	}

	se.mutex.Lock()
	instance.Logs = append(instance.Logs, log)
	// Keep only the last 1000 logs to prevent memory issues
	if len(instance.Logs) > 1000 {
		instance.Logs = instance.Logs[len(instance.Logs)-1000:]
	}
	se.mutex.Unlock()
}

func generateSandboxID() string {
	return fmt.Sprintf("sandbox_%d", time.Now().UnixNano())
}
