/* Website Builder Specific Styles */

.component-palette {
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    height: 100vh;
    overflow-y: auto;
}

.component-item {
    padding: 8px 12px;
    margin: 4px 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s ease;
}

.component-item:hover {
    background-color: #e9ecef;
    border-color: #0d6efd;
    transform: translateY(-1px);
}

.component-item:active {
    cursor: grabbing;
}

.website-canvas {
    min-height: 600px;
    background: white;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    position: relative;
}

.component-wrapper {
    border: 2px solid transparent;
    border-radius: 4px;
    transition: all 0.2s ease;
    position: relative;
}

.component-wrapper:hover {
    border-color: #0d6efd;
    box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.1);
}

.component-wrapper.selected {
    border-color: #0d6efd;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.2);
}

.component-controls {
    opacity: 0;
    transition: opacity 0.2s ease;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 4px;
}

.component-wrapper:hover .component-controls,
.component-wrapper.selected .component-controls {
    opacity: 1;
}

.properties-panel {
    background-color: #f8f9fa;
    border-left: 1px solid #dee2e6;
    height: 100vh;
    overflow-y: auto;
}

/* Device preview modes */
.device-desktop {
    max-width: 100%;
}

.device-tablet {
    max-width: 768px;
    margin: 0 auto;
}

.device-mobile {
    max-width: 375px;
    margin: 0 auto;
}

/* Enhanced Sortable.js styles */
.sortable-ghost {
    opacity: 0.4;
    background: #e9ecef;
    border: 2px dashed #0d6efd;
    border-radius: 4px;
}

.sortable-chosen {
    opacity: 0.8;
    transform: rotate(5deg);
    z-index: 1000;
}

.sortable-drag {
    opacity: 0.6;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

/* Drop zones */
.drop-zone {
    min-height: 50px;
    border: 2px dashed transparent;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
}

.drop-zone.drag-over {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

.drop-zone.drag-over::before {
    content: "Drop component here";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #0d6efd;
    font-weight: 500;
    pointer-events: none;
}

/* Nested component support */
.component-wrapper.nested {
    margin-left: 20px;
    border-left: 2px solid #dee2e6;
    padding-left: 15px;
}

.component-wrapper.can-drop {
    border: 2px dashed #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

/* Drag handle */
.drag-handle {
    cursor: grab;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.component-wrapper:hover .drag-handle {
    opacity: 1;
}

.drag-handle:active {
    cursor: grabbing;
}

/* Builder toolbar */
.builder-toolbar {
    background: white;
    border-bottom: 1px solid #dee2e6;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.device-toggle {
    display: flex;
    gap: 4px;
}

.device-toggle .btn {
    padding: 6px 12px;
    font-size: 14px;
}

/* Theme customizer */
.theme-customizer {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.color-input {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* Canvas empty state */
.canvas-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #6c757d;
    font-size: 18px;
}

/* Component property editor */
.property-group {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #dee2e6;
}

.property-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.property-label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .component-palette,
    .properties-panel {
        height: auto;
        max-height: 300px;
    }
    
    .builder-toolbar {
        flex-wrap: wrap;
        gap: 8px;
    }
}

/* Animation for component addition */
@keyframes componentAdded {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.component-wrapper.newly-added {
    animation: componentAdded 0.3s ease-out;
}

/* Loading states */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error states */
.save-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    padding: 8px 16px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.save-indicator.show {
    opacity: 1;
}

.save-indicator.success {
    background-color: #198754;
}

.save-indicator.error {
    background-color: #dc3545;
}

.save-indicator.info {
    background-color: #0dcaf0;
}