<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Addon Marketplace - Velocity Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-lightning-charge"></i>
                VelocityWave
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/marketplace/my-addons">
                    <i class="bi bi-collection"></i> My Addons
                </a>
                <a class="nav-link" href="/builder/addon-builder">
                    <i class="bi bi-plus-circle"></i> Create Addon
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="bg-gradient-primary text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">Addon Marketplace</h1>
                    <p class="lead mb-4">Discover powerful addons to extend your platform capabilities. From productivity tools to integrations, find everything you need to supercharge your workflow.</p>
                </div>
                <div class="col-lg-4">
                    <div class="text-center">
                        <i class="bi bi-shop display-1 opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- Search and Filters -->
        <div class="row">
            <div class="col-lg-3">
                <!-- Sidebar Filters -->
                <div class="card sticky-top" style="top: 20px;">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-funnel"></i>
                            Filters
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="filter-form">
                            <!-- Search -->
                            <div class="mb-3">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search-query" placeholder="Search addons...">
                                    <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Categories -->
                            <div class="mb-3">
                                <label class="form-label">Category</label>
                                <select class="form-select" id="category-filter" onchange="applyFilters()">
                                    <option value="">All Categories</option>
                                    <option value="productivity">Productivity</option>
                                    <option value="communication">Communication</option>
                                    <option value="analytics">Analytics</option>
                                    <option value="marketing">Marketing</option>
                                    <option value="ecommerce">E-commerce</option>
                                    <option value="social">Social Media</option>
                                    <option value="content">Content Management</option>
                                    <option value="design">Design</option>
                                    <option value="development">Development</option>
                                    <option value="integration">Integration</option>
                                    <option value="automation">Automation</option>
                                    <option value="security">Security</option>
                                </select>
                            </div>

                            <!-- Pricing -->
                            <div class="mb-3">
                                <label class="form-label">Pricing</label>
                                <select class="form-select" id="pricing-filter" onchange="applyFilters()">
                                    <option value="">All Pricing</option>
                                    <option value="free">Free</option>
                                    <option value="one_time">One-time Purchase</option>
                                    <option value="subscription">Subscription</option>
                                    <option value="usage_based">Usage-based</option>
                                </select>
                            </div>

                            <!-- Rating -->
                            <div class="mb-3">
                                <label class="form-label">Minimum Rating</label>
                                <select class="form-select" id="rating-filter" onchange="applyFilters()">
                                    <option value="">Any Rating</option>
                                    <option value="4">4+ Stars</option>
                                    <option value="3">3+ Stars</option>
                                    <option value="2">2+ Stars</option>
                                    <option value="1">1+ Stars</option>
                                </select>
                            </div>

                            <!-- Tags -->
                            <div class="mb-3">
                                <label class="form-label">Popular Tags</label>
                                <div id="popular-tags" class="d-flex flex-wrap gap-1">
                                    <!-- Tags will be populated dynamically -->
                                </div>
                            </div>

                            <button type="button" class="btn btn-outline-secondary w-100" onclick="clearAllFilters()">
                                <i class="bi bi-arrow-clockwise"></i> Clear All Filters
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-9">
                <!-- Search Bar -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="input-group input-group-lg">
                            <input type="text" class="form-control" id="main-search" placeholder="Search for addons..." onkeypress="handleSearchKeypress(event)">
                            <button class="btn btn-primary" type="button" onclick="performSearch()">
                                <i class="bi bi-search"></i> Search
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select form-select-lg" id="sort-by" onchange="applyFilters()">
                            <option value="relevance">Sort by Relevance</option>
                            <option value="popularity">Most Popular</option>
                            <option value="rating">Highest Rated</option>
                            <option value="downloads">Most Downloaded</option>
                            <option value="updated_at">Recently Updated</option>
                            <option value="created_at">Newest</option>
                            <option value="name">Name A-Z</option>
                            <option value="trending">Trending</option>
                        </select>
                    </div>
                </div>

                <!-- Quick Categories -->
                <div class="mb-4">
                    <h5 class="mb-3">Browse by Category</h5>
                    <div class="row" id="category-grid">
                        <!-- Categories will be populated dynamically -->
                    </div>
                </div>

                <!-- Featured Addons -->
                <div class="mb-4" id="featured-section">
                    <h5 class="mb-3">
                        <i class="bi bi-star-fill text-warning"></i>
                        Featured Addons
                    </h5>
                    <div class="row" id="featured-addons">
                        <!-- Featured addons will be populated dynamically -->
                    </div>
                </div>

                <!-- Search Results -->
                <div id="search-results-section" class="d-none">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0" id="results-title">Search Results</h5>
                        <div class="text-muted" id="results-count">0 results</div>
                    </div>
                    <div id="search-results">
                        <!-- Search results will be populated dynamically -->
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Search results pagination" class="mt-4">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- Pagination will be populated dynamically -->
                        </ul>
                    </nav>
                </div>

                <!-- Trending Addons -->
                <div class="mb-4" id="trending-section">
                    <h5 class="mb-3">
                        <i class="bi bi-graph-up text-success"></i>
                        Trending This Week
                    </h5>
                    <div class="row" id="trending-addons">
                        <!-- Trending addons will be populated dynamically -->
                    </div>
                </div>

                <!-- Loading State -->
                <div id="loading-state" class="text-center py-5 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading addons...</p>
                </div>

                <!-- Empty State -->
                <div id="empty-state" class="text-center py-5 d-none">
                    <i class="bi bi-search display-1 text-muted"></i>
                    <h4 class="mt-3">No addons found</h4>
                    <p class="text-muted">Try adjusting your search criteria or browse our categories.</p>
                    <button class="btn btn-primary" onclick="clearAllFilters()">
                        <i class="bi bi-arrow-clockwise"></i> Reset Filters
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let currentFilters = {};
        let searchTimeout = null;

        // Initialize marketplace
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
            loadFeaturedAddons();
            loadTrendingAddons();
            loadPopularTags();
        });

        function loadCategories() {
            fetch('/api/marketplace/categories')
                .then(response => response.json())
                .then(categories => {
                    displayCategories(categories);
                })
                .catch(error => {
                    console.error('Error loading categories:', error);
                });
        }

        function displayCategories(categories) {
            const grid = document.getElementById('category-grid');
            grid.innerHTML = '';

            categories.slice(0, 6).forEach(category => {
                const categoryCard = `
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 category-card" onclick="filterByCategory('${category.id}')" style="cursor: pointer;">
                            <div class="card-body text-center">
                                <i class="bi bi-${category.icon} display-6 mb-2" style="color: ${category.color};"></i>
                                <h6 class="card-title">${category.name}</h6>
                                <p class="card-text small text-muted">${category.addonCount} addons</p>
                            </div>
                        </div>
                    </div>
                `;
                grid.innerHTML += categoryCard;
            });
        }

        function loadFeaturedAddons() {
            fetch('/api/marketplace/featured')
                .then(response => response.json())
                .then(addons => {
                    displayAddons(addons, 'featured-addons', true);
                })
                .catch(error => {
                    console.error('Error loading featured addons:', error);
                });
        }

        function loadTrendingAddons() {
            fetch('/api/marketplace/trending')
                .then(response => response.json())
                .then(addons => {
                    displayAddons(addons, 'trending-addons', true);
                })
                .catch(error => {
                    console.error('Error loading trending addons:', error);
                });
        }

        function displayAddons(addons, containerId, isHorizontal = false) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            if (addons.length === 0) {
                container.innerHTML = '<div class="col-12 text-center text-muted">No addons found</div>';
                return;
            }

            addons.forEach(addon => {
                const addonCard = createAddonCard(addon, isHorizontal);
                container.innerHTML += addonCard;
            });
        }

        function createAddonCard(addon, isHorizontal = false) {
            const colClass = isHorizontal ? 'col-md-6 col-lg-4' : 'col-12';
            const cardClass = isHorizontal ? 'h-100' : 'mb-3';
            
            return `
                <div class="${colClass} mb-3">
                    <div class="card ${cardClass} addon-card" onclick="viewAddon('${addon.id}')" style="cursor: pointer;">
                        <div class="card-body">
                            <div class="d-flex align-items-start">
                                <img src="${addon.icon || '/static/images/default-addon-icon.svg'}" 
                                     alt="${addon.name}" class="me-3" style="width: 48px; height: 48px;">
                                <div class="flex-grow-1">
                                    <h6 class="card-title mb-1">${addon.name}</h6>
                                    <p class="card-text small text-muted mb-2">${addon.shortDescription || addon.description}</p>
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <div class="rating me-2">
                                                ${generateStarRating(addon.metrics.rating)}
                                                <small class="text-muted">(${addon.metrics.reviewCount})</small>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-primary">${addon.category}</span>
                                            ${addon.pricing.model === 'free' ? 
                                                '<span class="badge bg-success">Free</span>' : 
                                                `<span class="badge bg-warning">$${addon.pricing.price}</span>`}
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="bi bi-download"></i> ${formatNumber(addon.metrics.downloads)} downloads
                                            <span class="ms-2">by ${addon.author}</span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateStarRating(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 >= 0.5;
            let stars = '';

            for (let i = 0; i < fullStars; i++) {
                stars += '<i class="bi bi-star-fill text-warning"></i>';
            }

            if (hasHalfStar) {
                stars += '<i class="bi bi-star-half text-warning"></i>';
            }

            const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
            for (let i = 0; i < emptyStars; i++) {
                stars += '<i class="bi bi-star text-muted"></i>';
            }

            return stars;
        }

        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        function performSearch() {
            const query = document.getElementById('main-search').value;
            document.getElementById('search-query').value = query;
            applyFilters();
        }

        function handleSearchKeypress(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }

        function applyFilters() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const filters = {
                    query: document.getElementById('search-query').value || document.getElementById('main-search').value,
                    category: document.getElementById('category-filter').value,
                    pricingModel: document.getElementById('pricing-filter').value,
                    minRating: document.getElementById('rating-filter').value,
                    sortBy: document.getElementById('sort-by').value,
                    page: currentPage
                };

                currentFilters = filters;
                searchAddons(filters);
            }, 300);
        }

        function searchAddons(filters) {
            showLoading();
            
            const queryParams = new URLSearchParams();
            Object.keys(filters).forEach(key => {
                if (filters[key]) {
                    queryParams.append(key, filters[key]);
                }
            });

            fetch(`/api/marketplace/search?${queryParams}`)
                .then(response => response.json())
                .then(result => {
                    hideLoading();
                    displaySearchResults(result);
                })
                .catch(error => {
                    hideLoading();
                    console.error('Error searching addons:', error);
                    showEmptyState();
                });
        }

        function displaySearchResults(result) {
            const hasQuery = currentFilters.query && currentFilters.query.trim() !== '';
            
            if (hasQuery) {
                // Show search results section
                document.getElementById('featured-section').classList.add('d-none');
                document.getElementById('trending-section').classList.add('d-none');
                document.getElementById('search-results-section').classList.remove('d-none');
                
                document.getElementById('results-title').textContent = `Search Results for "${currentFilters.query}"`;
                document.getElementById('results-count').textContent = `${result.total} results`;
                
                displayAddons(result.addons, 'search-results', false);
                displayPagination(result);
            } else {
                // Show filtered results in place of featured/trending
                document.getElementById('featured-section').classList.remove('d-none');
                document.getElementById('trending-section').classList.remove('d-none');
                document.getElementById('search-results-section').classList.add('d-none');
                
                displayAddons(result.addons, 'featured-addons', true);
            }

            if (result.addons.length === 0) {
                showEmptyState();
            } else {
                hideEmptyState();
            }
        }

        function displayPagination(result) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (result.totalPages <= 1) return;

            // Previous button
            const prevDisabled = result.page === 1 ? 'disabled' : '';
            pagination.innerHTML += `
                <li class="page-item ${prevDisabled}">
                    <a class="page-link" href="#" onclick="changePage(${result.page - 1})">Previous</a>
                </li>
            `;

            // Page numbers
            const startPage = Math.max(1, result.page - 2);
            const endPage = Math.min(result.totalPages, result.page + 2);

            for (let i = startPage; i <= endPage; i++) {
                const active = i === result.page ? 'active' : '';
                pagination.innerHTML += `
                    <li class="page-item ${active}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            }

            // Next button
            const nextDisabled = result.page === result.totalPages ? 'disabled' : '';
            pagination.innerHTML += `
                <li class="page-item ${nextDisabled}">
                    <a class="page-link" href="#" onclick="changePage(${result.page + 1})">Next</a>
                </li>
            `;
        }

        function changePage(page) {
            currentPage = page;
            currentFilters.page = page;
            searchAddons(currentFilters);
        }

        function filterByCategory(categoryId) {
            document.getElementById('category-filter').value = categoryId;
            applyFilters();
        }

        function clearSearch() {
            document.getElementById('search-query').value = '';
            document.getElementById('main-search').value = '';
            applyFilters();
        }

        function clearAllFilters() {
            document.getElementById('search-query').value = '';
            document.getElementById('main-search').value = '';
            document.getElementById('category-filter').value = '';
            document.getElementById('pricing-filter').value = '';
            document.getElementById('rating-filter').value = '';
            document.getElementById('sort-by').value = 'relevance';
            
            currentPage = 1;
            currentFilters = {};
            
            // Reset to default view
            document.getElementById('featured-section').classList.remove('d-none');
            document.getElementById('trending-section').classList.remove('d-none');
            document.getElementById('search-results-section').classList.add('d-none');
            hideEmptyState();
            
            loadFeaturedAddons();
            loadTrendingAddons();
        }

        function viewAddon(addonId) {
            window.location.href = `/marketplace/addon/${addonId}`;
        }

        function showLoading() {
            document.getElementById('loading-state').classList.remove('d-none');
        }

        function hideLoading() {
            document.getElementById('loading-state').classList.add('d-none');
        }

        function showEmptyState() {
            document.getElementById('empty-state').classList.remove('d-none');
        }

        function hideEmptyState() {
            document.getElementById('empty-state').classList.add('d-none');
        }

        function loadPopularTags() {
            // Simulate loading popular tags
            const popularTags = ['productivity', 'automation', 'integration', 'analytics', 'communication'];
            const tagsContainer = document.getElementById('popular-tags');
            
            popularTags.forEach(tag => {
                const tagBadge = `<span class="badge bg-light text-dark" style="cursor: pointer;" onclick="filterByTag('${tag}')">${tag}</span>`;
                tagsContainer.innerHTML += tagBadge;
            });
        }

        function filterByTag(tag) {
            document.getElementById('search-query').value = tag;
            document.getElementById('main-search').value = tag;
            applyFilters();
        }
    </script>

    <style>
        .category-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }

        .addon-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }

        .rating i {
            font-size: 0.8rem;
        }

        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</body>
</html>