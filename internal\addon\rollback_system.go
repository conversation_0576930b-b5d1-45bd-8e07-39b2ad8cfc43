package addon

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// RollbackSystem handles addon rollback functionality
type RollbackSystem struct {
	versioningSystem *VersioningSystem
	deploymentSystem *DeploymentSystem
	activeRollbacks  map[string]*RollbackOperation
	rollbackHistory  []*RollbackOperation
	mutex            sync.RWMutex
	config           RollbackConfig
}

// RollbackConfig contains configuration for rollback operations
type RollbackConfig struct {
	MaxConcurrentRollbacks int           `json:"maxConcurrentRollbacks"`
	RollbackTimeout        time.Duration `json:"rollbackTimeout"`
	AutoRollbackEnabled    bool          `json:"autoRollbackEnabled"`
	HealthCheckThreshold   float64       `json:"healthCheckThreshold"`
	ErrorRateThreshold     float64       `json:"errorRateThreshold"`
}

// RollbackOperation represents a rollback operation
type RollbackOperation struct {
	ID          string                 `json:"id"`
	AddonID     string                 `json:"addonId"`
	FromVersion string                 `json:"fromVersion"`
	ToVersion   string                 `json:"toVersion"`
	Reason      RollbackReason         `json:"reason"`
	Type        RollbackType           `json:"type"`
	Status      RollbackStatus         `json:"status"`
	StartedAt   time.Time              `json:"startedAt"`
	CompletedAt *time.Time             `json:"completedAt,omitempty"`
	InitiatedBy string                 `json:"initiatedBy"`
	Steps       []RollbackStep         `json:"steps"`
	Validation  RollbackValidation     `json:"validation"`
	Context     map[string]interface{} `json:"context"`
}

// RollbackReason represents the reason for rollback
type RollbackReason string

const (
	RollbackReasonManual      RollbackReason = "manual"
	RollbackReasonHealthCheck RollbackReason = "health_check_failure"
	RollbackReasonErrorRate   RollbackReason = "high_error_rate"
	RollbackReasonPerformance RollbackReason = "performance_degradation"
	RollbackReasonSecurity    RollbackReason = "security_issue"
	RollbackReasonDependency  RollbackReason = "dependency_failure"
)

// RollbackType represents the type of rollback
type RollbackType string

const (
	RollbackTypeImmediate RollbackType = "immediate"
	RollbackTypeGraceful  RollbackType = "graceful"
	RollbackTypeCanary    RollbackType = "canary"
)

// RollbackStatus represents the status of a rollback operation
type RollbackStatus string

const (
	RollbackStatusPending    RollbackStatus = "pending"
	RollbackStatusInProgress RollbackStatus = "in_progress"
	RollbackStatusCompleted  RollbackStatus = "completed"
	RollbackStatusFailed     RollbackStatus = "failed"
	RollbackStatusCancelled  RollbackStatus = "cancelled"
)

// RollbackStep represents a single step in the rollback process
type RollbackStep struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Status      RollbackStepStatus     `json:"status"`
	StartedAt   time.Time              `json:"startedAt"`
	CompletedAt *time.Time             `json:"completedAt,omitempty"`
	Duration    time.Duration          `json:"duration"`
	Output      string                 `json:"output"`
	Error       string                 `json:"error,omitempty"`
	Retries     int                    `json:"retries"`
	MaxRetries  int                    `json:"maxRetries"`
	Context     map[string]interface{} `json:"context"`
}

// RollbackStepStatus represents the status of a rollback step
type RollbackStepStatus string

const (
	RollbackStepStatusPending   RollbackStepStatus = "pending"
	RollbackStepStatusRunning   RollbackStepStatus = "running"
	RollbackStepStatusCompleted RollbackStepStatus = "completed"
	RollbackStepStatusFailed    RollbackStepStatus = "failed"
	RollbackStepStatusSkipped   RollbackStepStatus = "skipped"
)

// RollbackValidation contains validation information for rollback
type RollbackValidation struct {
	PreRollbackChecks  []ValidationCheck `json:"preRollbackChecks"`
	PostRollbackChecks []ValidationCheck `json:"postRollbackChecks"`
	CompatibilityCheck bool              `json:"compatibilityCheck"`
	DataMigrationCheck bool              `json:"dataMigrationCheck"`
	DependencyCheck    bool              `json:"dependencyCheck"`
}

// ValidationCheck represents a validation check
type ValidationCheck struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Status    ValidationStatus       `json:"status"`
	Result    bool                   `json:"result"`
	Message   string                 `json:"message"`
	CheckedAt time.Time              `json:"checkedAt"`
	Context   map[string]interface{} `json:"context"`
}

// ValidationStatus represents the status of a validation check
type ValidationStatus string

const (
	ValidationStatusPending ValidationStatus = "pending"
	ValidationStatusRunning ValidationStatus = "running"
	ValidationStatusPassed  ValidationStatus = "passed"
	ValidationStatusFailed  ValidationStatus = "failed"
	ValidationStatusSkipped ValidationStatus = "skipped"
)

// NewRollbackSystem creates a new rollback system
func NewRollbackSystem(config RollbackConfig) *RollbackSystem {
	if config.MaxConcurrentRollbacks == 0 {
		config.MaxConcurrentRollbacks = 2
	}
	if config.RollbackTimeout == 0 {
		config.RollbackTimeout = 15 * time.Minute
	}
	if config.HealthCheckThreshold == 0 {
		config.HealthCheckThreshold = 0.8
	}
	if config.ErrorRateThreshold == 0 {
		config.ErrorRateThreshold = 0.05
	}

	return &RollbackSystem{
		versioningSystem: NewVersioningSystem(VersioningConfig{}),
		deploymentSystem: NewDeploymentSystem(DeploymentConfig{}),
		activeRollbacks:  make(map[string]*RollbackOperation),
		rollbackHistory:  []*RollbackOperation{},
		config:           config,
	}
}

// InitiateRollback initiates a rollback operation
func (rs *RollbackSystem) InitiateRollback(ctx context.Context, addonID, fromVersion, toVersion, initiatedBy string, reason RollbackReason, rollbackType RollbackType) (*RollbackOperation, error) {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	// Check concurrent rollback limit
	if len(rs.activeRollbacks) >= rs.config.MaxConcurrentRollbacks {
		return nil, fmt.Errorf("maximum concurrent rollbacks reached")
	}

	// Validate versions exist
	if _, err := rs.versioningSystem.GetVersion(addonID, fromVersion); err != nil {
		return nil, fmt.Errorf("source version not found: %w", err)
	}

	if _, err := rs.versioningSystem.GetVersion(addonID, toVersion); err != nil {
		return nil, fmt.Errorf("target version not found: %w", err)
	}

	// Create rollback operation
	operation := &RollbackOperation{
		ID:          generateRollbackID(),
		AddonID:     addonID,
		FromVersion: fromVersion,
		ToVersion:   toVersion,
		Reason:      reason,
		Type:        rollbackType,
		Status:      RollbackStatusPending,
		StartedAt:   time.Now(),
		InitiatedBy: initiatedBy,
		Steps:       rs.generateRollbackSteps(rollbackType),
		Validation:  rs.generateValidationChecks(addonID, fromVersion, toVersion),
		Context:     make(map[string]interface{}),
	}

	// Store operation
	rs.activeRollbacks[operation.ID] = operation

	// Start rollback in background
	go rs.executeRollback(ctx, operation)

	return operation, nil
}

// executeRollback executes the rollback operation
func (rs *RollbackSystem) executeRollback(ctx context.Context, operation *RollbackOperation) {
	defer func() {
		if r := recover(); r != nil {
			rs.handleRollbackPanic(operation, r)
		}
	}()

	rs.updateRollbackStatus(operation, RollbackStatusInProgress)

	// Create rollback context with timeout
	rollbackCtx, cancel := context.WithTimeout(ctx, rs.config.RollbackTimeout)
	defer cancel()

	// Run pre-rollback validation
	if !rs.runValidationChecks(rollbackCtx, operation, operation.Validation.PreRollbackChecks) {
		rs.failRollback(operation, "Pre-rollback validation failed")
		return
	}

	// Execute rollback steps
	for i := range operation.Steps {
		step := &operation.Steps[i]

		select {
		case <-rollbackCtx.Done():
			rs.failRollback(operation, "Rollback timed out")
			return
		default:
		}

		if err := rs.executeRollbackStep(rollbackCtx, operation, step); err != nil {
			rs.failRollback(operation, fmt.Sprintf("Step %s failed: %s", step.Name, err.Error()))
			return
		}
	}

	// Run post-rollback validation
	if !rs.runValidationChecks(rollbackCtx, operation, operation.Validation.PostRollbackChecks) {
		rs.failRollback(operation, "Post-rollback validation failed")
		return
	}

	rs.completeRollback(operation)
}

// generateRollbackSteps generates the steps for a rollback operation
func (rs *RollbackSystem) generateRollbackSteps(rollbackType RollbackType) []RollbackStep {
	baseSteps := []RollbackStep{
		{ID: "step_1", Name: "stop_current_version", Description: "Stop current version", Status: RollbackStepStatusPending, MaxRetries: 2},
		{ID: "step_2", Name: "backup_current_state", Description: "Backup current state", Status: RollbackStepStatusPending, MaxRetries: 3},
		{ID: "step_3", Name: "restore_previous_version", Description: "Restore previous version", Status: RollbackStepStatusPending, MaxRetries: 3},
		{ID: "step_4", Name: "update_configuration", Description: "Update configuration", Status: RollbackStepStatusPending, MaxRetries: 2},
		{ID: "step_5", Name: "restart_services", Description: "Restart services", Status: RollbackStepStatusPending, MaxRetries: 1},
		{ID: "step_6", Name: "verify_rollback", Description: "Verify rollback", Status: RollbackStepStatusPending, MaxRetries: 2},
	}

	return baseSteps
}

// generateValidationChecks generates validation checks for rollback
func (rs *RollbackSystem) generateValidationChecks(addonID, fromVersion, toVersion string) RollbackValidation {
	preChecks := []ValidationCheck{
		{ID: "pre_1", Name: "version_compatibility", Status: ValidationStatusPending},
		{ID: "pre_2", Name: "dependency_check", Status: ValidationStatusPending},
		{ID: "pre_3", Name: "backup_verification", Status: ValidationStatusPending},
	}

	postChecks := []ValidationCheck{
		{ID: "post_1", Name: "service_health", Status: ValidationStatusPending},
		{ID: "post_2", Name: "functionality_test", Status: ValidationStatusPending},
		{ID: "post_3", Name: "performance_check", Status: ValidationStatusPending},
	}

	return RollbackValidation{
		PreRollbackChecks:  preChecks,
		PostRollbackChecks: postChecks,
		CompatibilityCheck: true,
		DataMigrationCheck: true,
		DependencyCheck:    true,
	}
}

// executeRollbackStep executes a single rollback step
func (rs *RollbackSystem) executeRollbackStep(ctx context.Context, operation *RollbackOperation, step *RollbackStep) error {
	step.Status = RollbackStepStatusRunning
	step.StartedAt = time.Now()

	// Simulate step execution based on step name
	var err error
	switch step.Name {
	case "stop_current_version":
		time.Sleep(time.Millisecond * 200)
	case "backup_current_state":
		time.Sleep(time.Millisecond * 500)
	case "restore_previous_version":
		time.Sleep(time.Millisecond * 800)
	case "update_configuration":
		time.Sleep(time.Millisecond * 300)
	case "restart_services":
		time.Sleep(time.Millisecond * 600)
	case "verify_rollback":
		time.Sleep(time.Millisecond * 400)
	}

	now := time.Now()
	step.CompletedAt = &now
	step.Duration = time.Since(step.StartedAt)

	if err != nil {
		step.Status = RollbackStepStatusFailed
		step.Error = err.Error()
		return err
	}

	step.Status = RollbackStepStatusCompleted
	return nil
}

// runValidationChecks runs validation checks
func (rs *RollbackSystem) runValidationChecks(ctx context.Context, operation *RollbackOperation, checks []ValidationCheck) bool {
	for i := range checks {
		check := &checks[i]
		check.Status = ValidationStatusRunning
		check.CheckedAt = time.Now()

		// Simulate validation check
		time.Sleep(time.Millisecond * 100)
		check.Result = true
		check.Status = ValidationStatusPassed
		check.Message = "Check passed"
	}
	return true
}

// updateRollbackStatus updates the rollback status
func (rs *RollbackSystem) updateRollbackStatus(operation *RollbackOperation, status RollbackStatus) {
	rs.mutex.Lock()
	operation.Status = status
	rs.mutex.Unlock()
}

// completeRollback completes the rollback operation
func (rs *RollbackSystem) completeRollback(operation *RollbackOperation) {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	now := time.Now()
	operation.CompletedAt = &now
	operation.Status = RollbackStatusCompleted

	// Move to history and remove from active
	rs.rollbackHistory = append(rs.rollbackHistory, operation)
	delete(rs.activeRollbacks, operation.ID)
}

// failRollback fails the rollback operation
func (rs *RollbackSystem) failRollback(operation *RollbackOperation, reason string) {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	now := time.Now()
	operation.CompletedAt = &now
	operation.Status = RollbackStatusFailed
	operation.Context["failureReason"] = reason

	// Move to history and remove from active
	rs.rollbackHistory = append(rs.rollbackHistory, operation)
	delete(rs.activeRollbacks, operation.ID)
}

// handleRollbackPanic handles rollback panics
func (rs *RollbackSystem) handleRollbackPanic(operation *RollbackOperation, r interface{}) {
	rs.failRollback(operation, fmt.Sprintf("Rollback panicked: %v", r))
}

func generateRollbackID() string {
	return fmt.Sprintf("rollback_%d", time.Now().UnixNano())
}
