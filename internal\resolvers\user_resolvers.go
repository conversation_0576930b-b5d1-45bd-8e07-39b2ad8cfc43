package resolvers

import (
	"context"
	"fmt"
	"time"
	"goVwPlatformAPI/graph/model"
	"goVwPlatformAPI/internal/auth"
	"goVwPlatformAPI/internal/database"
)

// UserResolver handles user-related GraphQL operations
type UserResolver struct {
	db *database.DB
}

// NewUserResolver creates a new user resolver
func NewUserResolver(db *database.DB) *UserResolver {
	return &UserResolver{db: db}
}

// Me returns the current authenticated user
func (r *UserResolver) Me(ctx context.Context) (*model.User, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	return r.getUserByID(ctx, userID)
}

// UserProfile returns the user's profile
func (r *UserResolver) UserProfile(ctx context.Context) (*model.UserProfile, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	return r.getUserProfile(ctx, userID)
}

// UserSettings returns the user's settings
func (r *UserResolver) UserSettings(ctx context.Context) (*model.UserSettings, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	return r.getUserSettings(ctx, userID)
}

// UserSubscription returns the user's subscription
func (r *UserResolver) UserSubscription(ctx context.Context) (*model.UserSubscription, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	return r.getUserSubscription(ctx, userID)
}

// Notifications returns the user's notifications
func (r *UserResolver) Notifications(ctx context.Context, limit *int, unreadOnly *bool) ([]*model.Notification, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	limitVal := 20
	if limit != nil && *limit > 0 && *limit <= 100 {
		limitVal = *limit
	}

	unreadOnlyVal := false
	if unreadOnly != nil {
		unreadOnlyVal = *unreadOnly
	}

	return r.getUserNotifications(ctx, userID, limitVal, unreadOnlyVal)
}

// UpdateProfile updates the user's profile
func (r *UserResolver) UpdateProfile(ctx context.Context, input model.UpdateProfileInput) (*model.UserProfile, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Update user basic info
	if input.FirstName != nil || input.LastName != nil || input.Company != nil {
		query := "UPDATE users SET updated_at = NOW()"
		args := []interface{}{}
		argCount := 0

		if input.FirstName != nil {
			argCount++
			query += fmt.Sprintf(", first_name = $%d", argCount)
			args = append(args, *input.FirstName)
		}

		if input.LastName != nil {
			argCount++
			query += fmt.Sprintf(", last_name = $%d", argCount)
			args = append(args, *input.LastName)
		}

		if input.Company != nil {
			argCount++
			query += fmt.Sprintf(", company = $%d", argCount)
			args = append(args, *input.Company)
		}

		argCount++
		query += fmt.Sprintf(" WHERE id = $%d", argCount)
		args = append(args, userID)

		_, err := r.db.Pool.Exec(ctx, query, args...)
		if err != nil {
			return nil, fmt.Errorf("failed to update user: %w", err)
		}
	}

	// Note: UserProfile update functionality is not available due to missing fields in the current model
	// These would need to be added to the GraphQL schema and regenerated models
	return &model.UserProfile{
		UserID: userID,
	}, nil
}

// UpdateSettings updates the user's settings
func (r *UserResolver) UpdateSettings(ctx context.Context, input model.UpdateSettingsInput) (*model.UserSettings, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Note: UserSettings update functionality is not available due to missing fields in the current model
	// These would need to be added to the GraphQL schema and regenerated models
	return &model.UserSettings{
		UserID: userID,
	}, nil
}

// ChangePassword changes the user's password
func (r *UserResolver) ChangePassword(ctx context.Context, currentPassword, newPassword string) (bool, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return false, fmt.Errorf("user not authenticated")
	}

	// Verify current password
	var storedPassword string
	err = r.db.Pool.QueryRow(ctx, "SELECT password FROM users WHERE id = $1", userID).Scan(&storedPassword)
	if err != nil {
		return false, fmt.Errorf("failed to get user password: %w", err)
	}

	// Verify current password (simplified - in real implementation would use proper password hashing)
	if storedPassword != currentPassword {
		return false, fmt.Errorf("current password is incorrect")
	}

	// Update password
	_, err = r.db.Pool.Exec(ctx, "UPDATE users SET password = $1, updated_at = NOW() WHERE id = $2", newPassword, userID)
	if err != nil {
		return false, fmt.Errorf("failed to update password: %w", err)
	}

	return true, nil
}

// Helper methods
func (r *UserResolver) getUserByID(ctx context.Context, userID string) (*model.User, error) {
	query := `
		SELECT id, email, COALESCE(role, 'client'),
		       COALESCE(first_name, ''), COALESCE(last_name, ''),
		       COALESCE(email_verified, false), last_login_at, created_at, updated_at
		FROM users WHERE id = $1
	`

	var user model.User
	var lastLoginAt *time.Time

	err := r.db.Pool.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Email, &user.Role,
		&user.FirstName, &user.LastName,
		&user.EmailVerified, &lastLoginAt, &user.CreatedAt, &user.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Note: LastLoginAt field is not available in the User model

	return &user, nil
}

func (r *UserResolver) getUserProfile(ctx context.Context, userID string) (*model.UserProfile, error) {
	// Mock implementation - in real app would query user_profiles table
	return &model.UserProfile{
		UserID: userID,
		// Note: ID, Bio, Website, Location, Timezone, Language, SocialLinks, CreatedAt, UpdatedAt fields are not available in the current model
		// These would need to be added to the GraphQL schema and regenerated models
	}, nil
}

func (r *UserResolver) getUserSettings(ctx context.Context, userID string) (*model.UserSettings, error) {
	// Mock implementation - in real app would query user_settings table
	return &model.UserSettings{
		UserID: userID,
		// Note: ID, Notifications, Privacy, Appearance, CreatedAt, UpdatedAt fields are not available in the current model
		// These would need to be added to the GraphQL schema and regenerated models
	}, nil
}

func (r *UserResolver) getUserSubscription(ctx context.Context, userID string) (*model.UserSubscription, error) {
	// Mock implementation - in real app would query user_subscriptions table
	return &model.UserSubscription{
		ID:     generateID(),
		UserID: userID,
		Tier: &model.SubscriptionTier{
			ID:       generateID(),
			Name:     "pro",
			IsActive: true,
			// Note: DisplayName, Description, Price, Currency, BillingPeriod, Features, SortOrder fields are not available in the current model
			CreatedAt: model.Time(time.Now().Add(-30 * 24 * time.Hour)),
			UpdatedAt: model.Time(time.Now()),
		},
		Status: "active",
		// Note: StartedAt, ExpiresAt, Features fields are not available in the current model
		CreatedAt: model.Time(time.Now().Add(-15 * 24 * time.Hour)),
		UpdatedAt: model.Time(time.Now()),
	}, nil
}

func (r *UserResolver) getUserNotifications(ctx context.Context, userID string, limit int, unreadOnly bool) ([]*model.Notification, error) {
	// Mock implementation - in real app would query notifications table
	notifications := []*model.Notification{
		{
			ID:        generateID(),
			Type:      "system",
			Title:     "Welcome to VelocityWave!",
			Message:   "Thank you for joining our platform. Get started by creating your first site.",
			IsRead:    false,
			CreatedAt: time.Now().Add(-2 * time.Hour),
		},
		{
			ID:        generateID(),
			Type:      "billing",
			Title:     "Payment Successful",
			Message:   "Your Pro subscription has been renewed for another month.",
			IsRead:    true,
			// Note: ReadAt field is not available in the Notification model
			CreatedAt: time.Now().Add(-24 * time.Hour),
		},
	}

	if unreadOnly {
		var unread []*model.Notification
		for _, n := range notifications {
			if !n.IsRead {
				unread = append(unread, n)
			}
		}
		notifications = unread
	}

	if len(notifications) > limit {
		notifications = notifications[:limit]
	}

	return notifications, nil
}

// Helper functions


