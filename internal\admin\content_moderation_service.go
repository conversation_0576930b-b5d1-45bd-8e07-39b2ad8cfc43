package admin

import (
	"context"
	"fmt"
	"goVwPlatformAPI/internal/database"
	"strings"
	"time"
)

// ContentModerationService handles admin content moderation operations
type ContentModerationService struct {
	db *database.DB
}

// NewContentModerationService creates a new content moderation service
func NewContentModerationService(db *database.DB) *ContentModerationService {
	return &ContentModerationService{db: db}
}

// ModerationItem represents any content item that can be moderated
type ModerationItem struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"` // page, addon, comment, profile, etc.
	Title         string                 `json:"title"`
	Content       string                 `json:"content"`
	AuthorID      string                 `json:"author_id"`
	AuthorEmail   string                 `json:"author_email"`
	Status        string                 `json:"status"`
	Flags         []ModerationFlag       `json:"flags"`
	Reports       []ModerationReport     `json:"reports"`
	ReviewNotes   string                 `json:"review_notes"`
	ReviewedBy    string                 `json:"reviewed_by"`
	ReviewedAt    *time.Time             `json:"reviewed_at"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
	Metadata      map[string]interface{} `json:"metadata"`
	RiskScore     float64                `json:"risk_score"`
	AutoModerated bool                   `json:"auto_moderated"`
	URL           string                 `json:"url"`
	ThumbnailURL  string                 `json:"thumbnail_url"`
}

// ModerationFlag represents a flag raised on content
type ModerationFlag struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`     // spam, inappropriate, copyright, etc.
	Severity    string                 `json:"severity"` // low, medium, high, critical
	Description string                 `json:"description"`
	ReportedBy  string                 `json:"reported_by"`
	ReportedAt  time.Time              `json:"reported_at"`
	Status      string                 `json:"status"` // pending, reviewed, resolved, dismissed
	Metadata    map[string]interface{} `json:"metadata"`
}

// ModerationReport represents a user report on content
type ModerationReport struct {
	ID          string                 `json:"id"`
	ReporterID  string                 `json:"reporter_id"`
	Reason      string                 `json:"reason"`
	Description string                 `json:"description"`
	Evidence    []string               `json:"evidence"` // URLs to screenshots, etc.
	Status      string                 `json:"status"`
	CreatedAt   time.Time              `json:"created_at"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ModerationAction represents an action taken on content
type ModerationAction struct {
	ID          string                 `json:"id"`
	ItemID      string                 `json:"item_id"`
	ItemType    string                 `json:"item_type"`
	Action      string                 `json:"action"` // approve, reject, hide, delete, warn
	Reason      string                 `json:"reason"`
	ModeratorID string                 `json:"moderator_id"`
	CreatedAt   time.Time              `json:"created_at"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ModerationFilters represents filters for content moderation
type ModerationFilters struct {
	Type           string     `json:"type"`
	Status         string     `json:"status"`
	FlagType       string     `json:"flag_type"`
	MinRiskScore   *float64   `json:"min_risk_score"`
	MaxRiskScore   *float64   `json:"max_risk_score"`
	AuthorID       string     `json:"author_id"`
	ReportedAfter  *time.Time `json:"reported_after"`
	ReportedBefore *time.Time `json:"reported_before"`
	AutoModerated  *bool      `json:"auto_moderated"`
	HasReports     *bool      `json:"has_reports"`
}

// ModerationQueue represents the moderation queue with pagination
type ModerationQueue struct {
	Items      []ModerationItem `json:"items"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	TotalPages int              `json:"total_pages"`
}

// GetModerationQueue retrieves items in the moderation queue
func (s *ContentModerationService) GetModerationQueue(ctx context.Context, filters *ModerationFilters, page, pageSize int) (*ModerationQueue, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 25
	}

	offset := (page - 1) * pageSize
	var items []ModerationItem

	// Get pages that need moderation
	pageItems, err := s.getModerationPages(ctx, filters, pageSize/4, offset/4)
	if err == nil {
		items = append(items, pageItems...)
	}

	// Get addons that need moderation
	addonItems, err := s.getModerationAddons(ctx, filters, pageSize/4, offset/4)
	if err == nil {
		items = append(items, addonItems...)
	}

	// Get expert profiles that need moderation
	profileItems, err := s.getModerationProfiles(ctx, filters, pageSize/4, offset/4)
	if err == nil {
		items = append(items, profileItems...)
	}

	// Get reported content
	reportedItems, err := s.getReportedContent(ctx, filters, pageSize/4, offset/4)
	if err == nil {
		items = append(items, reportedItems...)
	}

	// Sort by risk score and creation date
	// In a real implementation, you'd sort this properly

	// Calculate total (simplified)
	total := int64(len(items) * 4) // Rough estimate
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &ModerationQueue{
		Items:      items,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// getModerationPages gets pages that need moderation
func (s *ContentModerationService) getModerationPages(ctx context.Context, filters *ModerationFilters, limit, offset int) ([]ModerationItem, error) {
	query := `
		SELECT 
			p.id, p.title, p.content, p.created_by, u.email,
			COALESCE(p.status, 'draft') as status, p.created_at, p.updated_at
		FROM pages p
		JOIN users u ON p.created_by = u.id
		WHERE p.status IN ('draft', 'pending_review')
		ORDER BY p.created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := s.db.Pool.Query(ctx, query, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query pages for moderation: %w", err)
	}
	defer rows.Close()

	var items []ModerationItem
	for rows.Next() {
		var item ModerationItem
		var content *string

		err := rows.Scan(
			&item.ID, &item.Title, &content, &item.AuthorID, &item.AuthorEmail,
			&item.Status, &item.CreatedAt, &item.UpdatedAt,
		)
		if err != nil {
			continue
		}

		item.Type = "page"
		if content != nil {
			item.Content = *content
		}
		item.RiskScore = s.calculateContentRiskScore(item.Content, item.Title)
		item.Flags = []ModerationFlag{}
		item.Reports = []ModerationReport{}
		item.Metadata = make(map[string]interface{})
		item.URL = fmt.Sprintf("/admin/moderation/page/%s", item.ID)

		// Check for automatic flags
		item.Flags = s.getAutomaticFlags(item.Content, item.Title)

		items = append(items, item)
	}

	return items, nil
}

// getModerationAddons gets addons that need moderation
func (s *ContentModerationService) getModerationAddons(ctx context.Context, filters *ModerationFilters, limit, offset int) ([]ModerationItem, error) {
	query := `
		SELECT 
			ac.id, ac.name, ac.config, ac.created_by, u.email,
			ac.state as status, ac.created_at, ac.updated_at
		FROM addon_configs ac
		JOIN users u ON ac.created_by = u.id
		WHERE ac.state IN ('REVIEW', 'DEVELOPMENT')
		ORDER BY ac.created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := s.db.Pool.Query(ctx, query, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query addons for moderation: %w", err)
	}
	defer rows.Close()

	var items []ModerationItem
	for rows.Next() {
		var item ModerationItem
		var config *string

		err := rows.Scan(
			&item.ID, &item.Title, &config, &item.AuthorID, &item.AuthorEmail,
			&item.Status, &item.CreatedAt, &item.UpdatedAt,
		)
		if err != nil {
			continue
		}

		item.Type = "addon"
		if config != nil {
			item.Content = *config
		}
		item.RiskScore = s.calculateAddonRiskScore(item.Content, item.Title)
		item.Flags = []ModerationFlag{}
		item.Reports = []ModerationReport{}
		item.Metadata = make(map[string]interface{})
		item.URL = fmt.Sprintf("/admin/moderation/addon/%s", item.ID)

		// Check for automatic flags
		item.Flags = s.getAutomaticFlags(item.Content, item.Title)

		items = append(items, item)
	}

	return items, nil
}

// getModerationProfiles gets expert profiles that need moderation
func (s *ContentModerationService) getModerationProfiles(ctx context.Context, filters *ModerationFilters, limit, offset int) ([]ModerationItem, error) {
	query := `
		SELECT 
			ep.id, CONCAT(u.first_name, ' ', u.last_name) as title, 
			ep.bio, ep.user_id, u.email,
			ep.status, ep.created_at, ep.updated_at
		FROM expert_profiles ep
		JOIN users u ON ep.user_id = u.id
		WHERE ep.verification_status = 'PENDING'
		ORDER BY ep.created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := s.db.Pool.Query(ctx, query, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query profiles for moderation: %w", err)
	}
	defer rows.Close()

	var items []ModerationItem
	for rows.Next() {
		var item ModerationItem
		var bio *string

		err := rows.Scan(
			&item.ID, &item.Title, &bio, &item.AuthorID, &item.AuthorEmail,
			&item.Status, &item.CreatedAt, &item.UpdatedAt,
		)
		if err != nil {
			continue
		}

		item.Type = "expert_profile"
		if bio != nil {
			item.Content = *bio
		}
		item.RiskScore = s.calculateContentRiskScore(item.Content, item.Title)
		item.Flags = []ModerationFlag{}
		item.Reports = []ModerationReport{}
		item.Metadata = make(map[string]interface{})
		item.URL = fmt.Sprintf("/admin/moderation/profile/%s", item.ID)

		items = append(items, item)
	}

	return items, nil
}

// getReportedContent gets content that has been reported by users
func (s *ContentModerationService) getReportedContent(ctx context.Context, filters *ModerationFilters, limit, offset int) ([]ModerationItem, error) {
	// This would query a reports table that doesn't exist yet
	// For now, return empty slice
	return []ModerationItem{}, nil
}

// calculateContentRiskScore calculates a risk score for content
func (s *ContentModerationService) calculateContentRiskScore(content, title string) float64 {
	score := 0.0

	// Check for suspicious patterns
	suspiciousWords := []string{
		"spam", "scam", "fake", "illegal", "hack", "crack",
		"free money", "get rich quick", "guaranteed", "no risk",
	}

	contentLower := strings.ToLower(content + " " + title)

	for _, word := range suspiciousWords {
		if strings.Contains(contentLower, word) {
			score += 20.0
		}
	}

	// Check for excessive capitalization
	if strings.Count(content, strings.ToUpper(content)) > len(content)/4 {
		score += 15.0
	}

	// Check for excessive punctuation
	punctCount := strings.Count(content, "!") + strings.Count(content, "?")
	if punctCount > 10 {
		score += 10.0
	}

	// Check for URLs (potential spam)
	if strings.Contains(contentLower, "http") {
		score += 5.0
	}

	// Check for email addresses
	if strings.Contains(contentLower, "@") && strings.Contains(contentLower, ".") {
		score += 10.0
	}

	// Normalize to 0-100 scale
	if score > 100 {
		score = 100
	}

	return score
}

// calculateAddonRiskScore calculates a risk score for addon content
func (s *ContentModerationService) calculateAddonRiskScore(config, title string) float64 {
	score := s.calculateContentRiskScore(config, title)

	// Additional checks for addon-specific risks
	configLower := strings.ToLower(config)

	// Check for potentially dangerous JavaScript
	dangerousPatterns := []string{
		"eval(", "document.write", "innerHTML", "outerhtml",
		"script", "iframe", "object", "embed",
	}

	for _, pattern := range dangerousPatterns {
		if strings.Contains(configLower, pattern) {
			score += 25.0
		}
	}

	// Check for external requests
	if strings.Contains(configLower, "fetch") || strings.Contains(configLower, "xmlhttprequest") {
		score += 15.0
	}

	// Normalize to 0-100 scale
	if score > 100 {
		score = 100
	}

	return score
}

// getAutomaticFlags generates automatic flags based on content analysis
func (s *ContentModerationService) getAutomaticFlags(content, title string) []ModerationFlag {
	var flags []ModerationFlag

	contentLower := strings.ToLower(content + " " + title)

	// Spam detection
	spamWords := []string{"free money", "get rich quick", "guaranteed", "no risk"}
	for _, word := range spamWords {
		if strings.Contains(contentLower, word) {
			flags = append(flags, ModerationFlag{
				ID:          fmt.Sprintf("auto_%d", time.Now().UnixNano()),
				Type:        "spam",
				Severity:    "medium",
				Description: fmt.Sprintf("Contains potential spam keyword: %s", word),
				ReportedBy:  "system",
				ReportedAt:  time.Now(),
				Status:      "pending",
				Metadata:    make(map[string]interface{}),
			})
		}
	}

	// Inappropriate content detection
	inappropriateWords := []string{"hate", "violence", "illegal"}
	for _, word := range inappropriateWords {
		if strings.Contains(contentLower, word) {
			flags = append(flags, ModerationFlag{
				ID:          fmt.Sprintf("auto_%d", time.Now().UnixNano()),
				Type:        "inappropriate",
				Severity:    "high",
				Description: fmt.Sprintf("Contains potentially inappropriate content: %s", word),
				ReportedBy:  "system",
				ReportedAt:  time.Now(),
				Status:      "pending",
				Metadata:    make(map[string]interface{}),
			})
		}
	}

	// Security risk detection
	securityRisks := []string{"eval(", "document.write", "script>"}
	for _, risk := range securityRisks {
		if strings.Contains(contentLower, risk) {
			flags = append(flags, ModerationFlag{
				ID:          fmt.Sprintf("auto_%d", time.Now().UnixNano()),
				Type:        "security_risk",
				Severity:    "critical",
				Description: fmt.Sprintf("Contains potential security risk: %s", risk),
				ReportedBy:  "system",
				ReportedAt:  time.Now(),
				Status:      "pending",
				Metadata:    make(map[string]interface{}),
			})
		}
	}

	return flags
}

// ApproveContent approves content after moderation
func (s *ContentModerationService) ApproveContent(ctx context.Context, itemID, itemType, moderatorID, notes string) error {
	var query string
	var status string

	switch itemType {
	case "page":
		query = "UPDATE pages SET status = $1, updated_at = NOW() WHERE id = $2"
		status = "published"
	case "addon":
		query = "UPDATE addon_configs SET state = $1, updated_at = NOW() WHERE id = $2"
		status = "PRODUCTION"
	case "expert_profile":
		query = "UPDATE expert_profiles SET verification_status = $1, updated_at = NOW() WHERE id = $2"
		status = "VERIFIED"
	default:
		return fmt.Errorf("unsupported item type: %s", itemType)
	}

	_, err := s.db.Pool.Exec(ctx, query, status, itemID)
	if err != nil {
		return fmt.Errorf("failed to approve content: %w", err)
	}

	// Log the moderation action
	return s.logModerationAction(ctx, itemID, itemType, "approve", notes, moderatorID)
}

// RejectContent rejects content after moderation
func (s *ContentModerationService) RejectContent(ctx context.Context, itemID, itemType, moderatorID, reason string) error {
	var query string
	var status string

	switch itemType {
	case "page":
		query = "UPDATE pages SET status = $1, updated_at = NOW() WHERE id = $2"
		status = "rejected"
	case "addon":
		query = "UPDATE addon_configs SET state = $1, updated_at = NOW() WHERE id = $2"
		status = "REJECTED"
	case "expert_profile":
		query = "UPDATE expert_profiles SET verification_status = $1, updated_at = NOW() WHERE id = $2"
		status = "REJECTED"
	default:
		return fmt.Errorf("unsupported item type: %s", itemType)
	}

	_, err := s.db.Pool.Exec(ctx, query, status, itemID)
	if err != nil {
		return fmt.Errorf("failed to reject content: %w", err)
	}

	// Log the moderation action
	return s.logModerationAction(ctx, itemID, itemType, "reject", reason, moderatorID)
}

// HideContent hides content without deleting it
func (s *ContentModerationService) HideContent(ctx context.Context, itemID, itemType, moderatorID, reason string) error {
	var query string
	var status string

	switch itemType {
	case "page":
		query = "UPDATE pages SET is_hidden = true, updated_at = NOW() WHERE id = $1"
	case "addon":
		query = "UPDATE addon_configs SET state = $1, updated_at = NOW() WHERE id = $2"
		status = "DISABLED"
	case "expert_profile":
		query = "UPDATE expert_profiles SET status = $1, updated_at = NOW() WHERE id = $2"
		status = "SUSPENDED"
	default:
		return fmt.Errorf("unsupported item type: %s", itemType)
	}

	if status != "" {
		_, err := s.db.Pool.Exec(ctx, query, status, itemID)
		if err != nil {
			return fmt.Errorf("failed to hide content: %w", err)
		}
	} else {
		_, err := s.db.Pool.Exec(ctx, query, itemID)
		if err != nil {
			return fmt.Errorf("failed to hide content: %w", err)
		}
	}

	// Log the moderation action
	return s.logModerationAction(ctx, itemID, itemType, "hide", reason, moderatorID)
}

// DeleteContent permanently deletes content
func (s *ContentModerationService) DeleteContent(ctx context.Context, itemID, itemType, moderatorID, reason string) error {
	var query string

	switch itemType {
	case "page":
		query = "DELETE FROM pages WHERE id = $1"
	case "addon":
		query = "DELETE FROM addon_configs WHERE id = $1"
	case "expert_profile":
		query = "DELETE FROM expert_profiles WHERE id = $1"
	default:
		return fmt.Errorf("unsupported item type: %s", itemType)
	}

	_, err := s.db.Pool.Exec(ctx, query, itemID)
	if err != nil {
		return fmt.Errorf("failed to delete content: %w", err)
	}

	// Log the moderation action
	return s.logModerationAction(ctx, itemID, itemType, "delete", reason, moderatorID)
}

// logModerationAction logs a moderation action
func (s *ContentModerationService) logModerationAction(ctx context.Context, itemID, itemType, action, reason, moderatorID string) error {
	// For now, just log to stdout (in production, would store in database)
	fmt.Printf("MODERATION_ACTION: moderator=%s item=%s type=%s action=%s reason=%s\n",
		moderatorID, itemID, itemType, action, reason)
	return nil
}

// GetModerationStats returns moderation statistics
func (s *ContentModerationService) GetModerationStats(ctx context.Context) (*ModerationStats, error) {
	stats := &ModerationStats{
		ByType:   make(map[string]int64),
		ByStatus: make(map[string]int64),
	}

	// Count items by type (simplified)
	stats.ByType["pages"] = 0
	stats.ByType["addons"] = 0
	stats.ByType["profiles"] = 0

	// Count pages needing moderation
	var pageCount int64
	s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM pages WHERE status IN ('draft', 'pending_review')
	`).Scan(&pageCount)
	stats.ByType["pages"] = pageCount

	// Count addons needing moderation
	var addonCount int64
	s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM addon_configs WHERE state = 'REVIEW'
	`).Scan(&addonCount)
	stats.ByType["addons"] = addonCount

	// Count profiles needing moderation
	var profileCount int64
	s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM expert_profiles WHERE verification_status = 'PENDING'
	`).Scan(&profileCount)
	stats.ByType["profiles"] = profileCount

	// Calculate totals
	stats.TotalPending = stats.ByType["pages"] + stats.ByType["addons"] + stats.ByType["profiles"]
	stats.HighRiskItems = stats.TotalPending / 4 // Rough estimate
	stats.AutoModeratedToday = 0                 // Would calculate from logs

	return stats, nil
}

// ModerationStats represents moderation statistics
type ModerationStats struct {
	TotalPending       int64            `json:"total_pending"`
	HighRiskItems      int64            `json:"high_risk_items"`
	AutoModeratedToday int64            `json:"auto_moderated_today"`
	ByType             map[string]int64 `json:"by_type"`
	ByStatus           map[string]int64 `json:"by_status"`
	AverageReviewTime  float64          `json:"average_review_time_hours"`
}
