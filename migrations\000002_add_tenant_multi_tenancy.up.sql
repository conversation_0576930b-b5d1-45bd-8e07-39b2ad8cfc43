CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Insert default tenant with fixed UUID and proper conflict handling
-- Use INSERT with WHERE NOT EXISTS to avoid conflicts
INSERT INTO tenants (id, name)
SELECT '11111111-1111-1111-1111-111111111111', 'Default Tenant'
WHERE NOT EXISTS (
    SELECT 1 FROM tenants WHERE name = 'Default Tenant'
);

-- Users table changes (only if column doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'tenant_id') THEN
        ALTER TABLE users ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
    END IF;
END $$;

-- Sites table changes (only if column doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'sites' AND column_name = 'tenant_id') THEN
        ALTER TABLE sites ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
    END IF;
END $$;

-- Snippets table changes (only if table exists and column doesn't exist)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'snippets') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'snippets' AND column_name = 'tenant_id') THEN
        ALTER TABLE snippets ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
    END IF;
END $$;