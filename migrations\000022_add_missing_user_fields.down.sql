-- Remove indexes
DROP INDEX IF EXISTS idx_users_email_verified;
DROP INDEX IF EXISTS idx_users_status;
DROP INDEX IF EXISTS idx_users_role;
DROP INDEX IF EXISTS idx_users_last_login;
DROP INDEX IF EXISTS idx_pages_author_id;
DROP INDEX IF EXISTS idx_pages_published_at;
DROP INDEX IF EXISTS idx_pages_page_type;

-- Remove added columns from users table
ALTER TABLE users DROP COLUMN IF EXISTS profile_picture;
ALTER TABLE users DROP COLUMN IF EXISTS first_name;
ALTER TABLE users DROP COLUMN IF EXISTS last_name;
ALTER TABLE users DROP COLUMN IF EXISTS phone;
ALTER TABLE users DROP COLUMN IF EXISTS email_verified;
ALTER TABLE users DROP COLUMN IF EXISTS email_verified_at;
ALTER TABLE users DROP COLUMN IF EXISTS email_verification_token;
ALTER TABLE users DROP COLUMN IF EXISTS password_reset_token;
ALTER TABLE users DROP COLUMN IF EXISTS password_reset_expires;
ALTER TABLE users DROP COLUMN IF EXISTS last_login_at;
ALTER TABLE users DROP COLUMN IF EXISTS last_login_ip;
ALTER TABLE users DROP COLUMN IF EXISTS status;
ALTER TABLE users DROP COLUMN IF EXISTS user_role;

-- Remove added columns from sites table
ALTER TABLE sites DROP COLUMN IF EXISTS favicon_url;
ALTER TABLE sites DROP COLUMN IF EXISTS logo_url;
ALTER TABLE sites DROP COLUMN IF EXISTS meta_image;
ALTER TABLE sites DROP COLUMN IF EXISTS analytics_code;
ALTER TABLE sites DROP COLUMN IF EXISTS custom_css;
ALTER TABLE sites DROP COLUMN IF EXISTS custom_js;
ALTER TABLE sites DROP COLUMN IF EXISTS robots_txt;
ALTER TABLE sites DROP COLUMN IF EXISTS sitemap_url;

-- Remove added columns from pages table
ALTER TABLE pages DROP COLUMN IF EXISTS featured_image;
ALTER TABLE pages DROP COLUMN IF EXISTS excerpt;
ALTER TABLE pages DROP COLUMN IF EXISTS author_id;
ALTER TABLE pages DROP COLUMN IF EXISTS published_at;
ALTER TABLE pages DROP COLUMN IF EXISTS page_type;
ALTER TABLE pages DROP COLUMN IF EXISTS template_name;
ALTER TABLE pages DROP COLUMN IF EXISTS custom_css;
ALTER TABLE pages DROP COLUMN IF EXISTS custom_js;

-- Remove added columns from tenants table
ALTER TABLE tenants DROP COLUMN IF EXISTS company_logo;
ALTER TABLE tenants DROP COLUMN IF EXISTS company_website;
ALTER TABLE tenants DROP COLUMN IF EXISTS company_phone;
ALTER TABLE tenants DROP COLUMN IF EXISTS company_address;
ALTER TABLE tenants DROP COLUMN IF EXISTS billing_email;
ALTER TABLE tenants DROP COLUMN IF EXISTS tax_id;
ALTER TABLE tenants DROP COLUMN IF EXISTS currency;
ALTER TABLE tenants DROP COLUMN IF EXISTS timezone;
ALTER TABLE tenants DROP COLUMN IF EXISTS settings;