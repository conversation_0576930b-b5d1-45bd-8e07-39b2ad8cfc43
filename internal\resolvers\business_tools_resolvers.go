package resolvers

import (
	"context"
	"fmt"
	"goVwPlatformAPI/graph/model"
	"goVwPlatformAPI/internal/auth"
	"goVwPlatformAPI/internal/database"
	"time"
)

// BusinessToolsResolver handles business tools GraphQL operations
type BusinessToolsResolver struct {
	db *database.DB
}

// NewBusinessToolsResolver creates a new business tools resolver
func NewBusinessToolsResolver(db *database.DB) *BusinessToolsResolver {
	return &BusinessToolsResolver{db: db}
}

// BusinessPlan returns the user's business plan
func (r *BusinessToolsResolver) BusinessPlan(ctx context.Context) (*model.BusinessPlan, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Mock implementation - in real app would query business_plans table
	return &model.BusinessPlan{
		ID:          generateID(),
		UserID:      userID,
		Title:       "My Startup Business Plan",
		Description: func() *string { s := "Comprehensive business plan for my tech startup"; return &s }(),
		Content:     map[string]interface{}{"sections": []string{"executive_summary", "market_analysis", "financial_projections"}},
		Status:      "draft",
		CreatedAt:   time.Now().Add(-7 * 24 * time.Hour),
		UpdatedAt:   time.Now().Add(-1 * time.Hour),
	}, nil
}

// CreateBusinessPlan creates a new business plan
func (r *BusinessToolsResolver) CreateBusinessPlan(ctx context.Context, input model.CreateBusinessPlanInput) (*model.BusinessPlan, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	planID := generateID()

	// Create business plan with default sections
	plan := &model.BusinessPlan{
		ID:          planID,
		UserID:      userID,
		Title:       input.Title,
		Description: input.Description,
		Content:     input.Content,
		Status:      "draft",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Add default sections
	defaultSections := []struct {
		Type  string
		Title string
		Order int
	}{
		{"executive_summary", "Executive Summary", 1},
		{"company_description", "Company Description", 2},
		{"market_analysis", "Market Analysis", 3},
		{"organization_management", "Organization & Management", 4},
		{"products_services", "Products & Services", 5},
		{"marketing_sales", "Marketing & Sales", 6},
		{"funding_request", "Funding Request", 7},
		{"financial_projections", "Financial Projections", 8},
		{"appendix", "Appendix", 9},
	}

	// Default sections are now part of the Content JSON field
	// No need to append to Sections field as it doesn't exist
	_ = defaultSections // Avoid unused variable error

	return plan, nil
}

// UpdateBusinessPlan updates a business plan
func (r *BusinessToolsResolver) UpdateBusinessPlan(ctx context.Context, id string, input model.UpdateBusinessPlanInput) (*model.BusinessPlan, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Get existing plan
	plan, err := r.BusinessPlan(ctx)
	if err != nil {
		return nil, err
	}

	// Update fields
	// Note: Year, Revenue, Expenses, Profit fields are not available in the current model
	// These would need to be added to the GraphQL schema and regenerated models

	plan.UpdatedAt = model.Time(time.Now())

	return plan, nil
}

// FinancialPlan returns the user's financial plan
func (r *BusinessToolsResolver) FinancialPlan(ctx context.Context) (*model.FinancialPlan, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Mock implementation
	return &model.FinancialPlan{
		ID:        generateID(),
		UserID:    userID,
		Year:      2024,
		Revenue:   100000.0,
		Expenses:  80000.0,
		Profit:    20000.0,
		CreatedAt: time.Now().Add(-5 * 24 * time.Hour),
		UpdatedAt: time.Now().Add(-1 * time.Hour),
	}, nil
}

// CreateFinancialPlan creates a new financial plan
func (r *BusinessToolsResolver) CreateFinancialPlan(ctx context.Context, input model.CreateFinancialPlanInput) (*model.FinancialPlan, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	plan := &model.FinancialPlan{
		ID:       generateID(),
		UserID:   userID,
		// Note: Year, Revenue, Expenses, Profit fields are not available in the current model
		CreatedAt: model.Time(time.Now()),
		UpdatedAt: model.Time(time.Now()),
	}

	return plan, nil
}

// CompetitorResearch returns competitor research data
func (r *BusinessToolsResolver) CompetitorResearch(ctx context.Context, industry *string, location *string) (*model.CompetitorResearch, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Mock implementation
	return &model.CompetitorResearch{
		ID:       generateID(),
		UserID:   userID,
		Industry: func() *string { s := "Technology"; return &s }(),
		// Note: Location, Competitors, and MarketAnalysis fields are not available in the current model
		// These would need to be added to the GraphQL schema and regenerated models
		CreatedAt: time.Now().Add(-3 * 24 * time.Hour),
		UpdatedAt: time.Now().Add(-1 * time.Hour),
	}, nil
}

// ComplianceCheck returns compliance information for a specific regulation
func (r *BusinessToolsResolver) ComplianceCheck(ctx context.Context, regulation string, industry *string, location *string) (*model.ComplianceCheck, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Mock implementation
	return &model.ComplianceCheck{
		ID:         generateID(),
		UserID:     userID,
		// Note: All fields except basic ones are not available in the current model
		// These would need to be added to the GraphQL schema and regenerated models
		CreatedAt: time.Now().Add(-1 * 24 * time.Hour),
		UpdatedAt: time.Now().Add(-1 * time.Hour),
	}, nil
}

// CreateComplianceCheck creates a new compliance check
func (r *BusinessToolsResolver) CreateComplianceCheck(ctx context.Context, input model.CreateComplianceCheckInput) (*model.ComplianceCheck, error) {
	userID, err := auth.GetUserIDFromContext(ctx)
	if err != nil || userID == "" {
		return nil, fmt.Errorf("user not authenticated")
	}

	// Note: CreateComplianceCheckInput fields are not available in the current model
	// Return a mock compliance check for now
	return &model.ComplianceCheck{
		ID:        generateID(),
		UserID:    userID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}, nil
}

// Helper functions for business tools
func (r *BusinessToolsResolver) generateBusinessPlanTemplate(planType string) []*model.BusinessPlanSection {
	templates := map[string][]struct {
		Type  string
		Title string
		Order int
	}{
		"startup": {
			{"executive_summary", "Executive Summary", 1},
			{"company_description", "Company Description", 2},
			{"market_analysis", "Market Analysis", 3},
			{"organization_management", "Organization & Management", 4},
			{"products_services", "Products & Services", 5},
			{"marketing_sales", "Marketing & Sales", 6},
			{"funding_request", "Funding Request", 7},
			{"financial_projections", "Financial Projections", 8},
		},
		"traditional": {
			{"executive_summary", "Executive Summary", 1},
			{"company_overview", "Company Overview", 2},
			{"market_research", "Market Research", 3},
			{"competitive_analysis", "Competitive Analysis", 4},
			{"marketing_plan", "Marketing Plan", 5},
			{"operations_plan", "Operations Plan", 6},
			{"management_team", "Management Team", 7},
			{"financial_plan", "Financial Plan", 8},
		},
	}

	template, exists := templates[planType]
	if !exists {
		template = templates["startup"] // Default to startup template
	}

	var sections []*model.BusinessPlanSection
	for _, section := range template {
		sections = append(sections, &model.BusinessPlanSection{
			ID:         generateID(),
			// Note: Type and IsComplete fields are not available in the current model
			Title:      section.Title,
			Content:    "",
			Order:      section.Order,
		})
	}

	return sections
}






