package addon

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// ReviewSystem handles addon review and approval workflow
type ReviewSystem struct {
	metadataManager *MetadataManager
	testingEnv      *TestingEnvironment
	activeReviews   map[string]*ReviewSession
	reviewQueue     []*ReviewRequest
	mutex           sync.RWMutex
	config          ReviewConfig
}

// ReviewConfig contains configuration for the review system
type ReviewConfig struct {
	AutoApproveThreshold float64       `json:"autoApproveThreshold"`
	ReviewTimeout        time.Duration `json:"reviewTimeout"`
	RequiredReviewers    int           `json:"requiredReviewers"`
	EnableAutoTesting    bool          `json:"enableAutoTesting"`
	SecurityScanEnabled  bool          `json:"securityScanEnabled"`
}

// ReviewRequest represents a request for addon review
type ReviewRequest struct {
	ID             string                 `json:"id"`
	AddonID        string                 `json:"addonId"`
	SubmitterID    string                 `json:"submitterId"`
	AddonConfig    *AddonConfiguration    `json:"addonConfig"`
	SubmissionType SubmissionType         `json:"submissionType"`
	Priority       ReviewPriority         `json:"priority"`
	Status         ReviewStatus           `json:"status"`
	SubmittedAt    time.Time              `json:"submittedAt"`
	ReviewDeadline time.Time              `json:"reviewDeadline"`
	Notes          string                 `json:"notes"`
	TestResults    *TestResults           `json:"testResults,omitempty"`
	SecurityScan   *SecurityScanResults   `json:"securityScan,omitempty"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// ReviewSession represents an active review session
type ReviewSession struct {
	ID          string                 `json:"id"`
	RequestID   string                 `json:"requestId"`
	ReviewerID  string                 `json:"reviewerId"`
	Status      ReviewSessionStatus    `json:"status"`
	StartedAt   time.Time              `json:"startedAt"`
	CompletedAt *time.Time             `json:"completedAt,omitempty"`
	Decision    ReviewDecision         `json:"decision"`
	Score       float64                `json:"score"`
	Comments    []ReviewComment        `json:"comments"`
	Checklist   ReviewChecklist        `json:"checklist"`
	TimeSpent   time.Duration          `json:"timeSpent"`
	Context     map[string]interface{} `json:"context"`
}

// SubmissionType represents the type of submission
type SubmissionType string

const (
	SubmissionTypeNew    SubmissionType = "new"
	SubmissionTypeUpdate SubmissionType = "update"
	SubmissionTypeFix    SubmissionType = "fix"
)

// ReviewPriority represents the priority of a review
type ReviewPriority string

const (
	ReviewPriorityLow      ReviewPriority = "low"
	ReviewPriorityNormal   ReviewPriority = "normal"
	ReviewPriorityHigh     ReviewPriority = "high"
	ReviewPriorityCritical ReviewPriority = "critical"
)

// ReviewStatus represents the status of a review request
type ReviewStatus string

const (
	ReviewStatusPending   ReviewStatus = "pending"
	ReviewStatusInReview  ReviewStatus = "in_review"
	ReviewStatusApproved  ReviewStatus = "approved"
	ReviewStatusRejected  ReviewStatus = "rejected"
	ReviewStatusNeedsWork ReviewStatus = "needs_work"
	ReviewStatusCancelled ReviewStatus = "cancelled"
)

// ReviewSessionStatus represents the status of a review session
type ReviewSessionStatus string

const (
	ReviewSessionStatusActive    ReviewSessionStatus = "active"
	ReviewSessionStatusCompleted ReviewSessionStatus = "completed"
	ReviewSessionStatusAbandoned ReviewSessionStatus = "abandoned"
)

// ReviewDecision represents a reviewer's decision
type ReviewDecision string

const (
	ReviewDecisionApprove   ReviewDecision = "approve"
	ReviewDecisionReject    ReviewDecision = "reject"
	ReviewDecisionNeedsWork ReviewDecision = "needs_work"
	ReviewDecisionDefer     ReviewDecision = "defer"
)

// ReviewComment represents a comment made during review
type ReviewComment struct {
	ID          string                 `json:"id"`
	ReviewerID  string                 `json:"reviewerId"`
	Timestamp   time.Time              `json:"timestamp"`
	Type        CommentType            `json:"type"`
	Severity    CommentSeverity        `json:"severity"`
	Category    string                 `json:"category"`
	Message     string                 `json:"message"`
	CodeRef     *CodeReference         `json:"codeRef,omitempty"`
	Suggestions []string               `json:"suggestions,omitempty"`
	Context     map[string]interface{} `json:"context,omitempty"`
}

// CommentType represents the type of review comment
type CommentType string

const (
	CommentTypeGeneral     CommentType = "general"
	CommentTypeSecurity    CommentType = "security"
	CommentTypePerformance CommentType = "performance"
	CommentTypeUsability   CommentType = "usability"
	CommentTypeCode        CommentType = "code"
	CommentTypeMetadata    CommentType = "metadata"
)

// CommentSeverity represents the severity of a review comment
type CommentSeverity string

const (
	CommentSeverityInfo     CommentSeverity = "info"
	CommentSeverityWarning  CommentSeverity = "warning"
	CommentSeverityError    CommentSeverity = "error"
	CommentSeverityCritical CommentSeverity = "critical"
)

// CodeReference represents a reference to specific code
type CodeReference struct {
	NodeID     string `json:"nodeId,omitempty"`
	LineNumber int    `json:"lineNumber,omitempty"`
	Column     int    `json:"column,omitempty"`
	Length     int    `json:"length,omitempty"`
	Context    string `json:"context,omitempty"`
}

// ReviewChecklist represents a checklist for review
type ReviewChecklist struct {
	SecurityChecks    []ChecklistItem `json:"securityChecks"`
	PerformanceChecks []ChecklistItem `json:"performanceChecks"`
	UsabilityChecks   []ChecklistItem `json:"usabilityChecks"`
	CodeQualityChecks []ChecklistItem `json:"codeQualityChecks"`
	MetadataChecks    []ChecklistItem `json:"metadataChecks"`
}

// ChecklistItem represents a single checklist item
type ChecklistItem struct {
	ID          string    `json:"id"`
	Description string    `json:"description"`
	Required    bool      `json:"required"`
	Checked     bool      `json:"checked"`
	CheckedAt   time.Time `json:"checkedAt,omitempty"`
	Notes       string    `json:"notes,omitempty"`
}

// SecurityScanResults contains security scan results
type SecurityScanResults struct {
	ScanID          string                  `json:"scanId"`
	ScannedAt       time.Time               `json:"scannedAt"`
	Passed          bool                    `json:"passed"`
	Score           float64                 `json:"score"`
	Vulnerabilities []SecurityVulnerability `json:"vulnerabilities"`
	Warnings        []SecurityWarning       `json:"warnings"`
	Recommendations []string                `json:"recommendations"`
}

// SecurityVulnerability represents a security vulnerability
type SecurityVulnerability struct {
	ID          string         `json:"id"`
	Type        string         `json:"type"`
	Severity    string         `json:"severity"`
	Description string         `json:"description"`
	Location    *CodeReference `json:"location,omitempty"`
	CWE         string         `json:"cwe,omitempty"`
	CVSS        float64        `json:"cvss,omitempty"`
	Remediation string         `json:"remediation,omitempty"`
}

// SecurityWarning represents a security warning
type SecurityWarning struct {
	Type        string         `json:"type"`
	Description string         `json:"description"`
	Location    *CodeReference `json:"location,omitempty"`
	Suggestion  string         `json:"suggestion,omitempty"`
}

// NewReviewSystem creates a new review system
func NewReviewSystem(config ReviewConfig) *ReviewSystem {
	if config.AutoApproveThreshold == 0 {
		config.AutoApproveThreshold = 0.95
	}
	if config.ReviewTimeout == 0 {
		config.ReviewTimeout = 72 * time.Hour
	}
	if config.RequiredReviewers == 0 {
		config.RequiredReviewers = 2
	}

	return &ReviewSystem{
		metadataManager: NewMetadataManager(),
		testingEnv:      NewTestingEnvironment(),
		activeReviews:   make(map[string]*ReviewSession),
		reviewQueue:     []*ReviewRequest{},
		config:          config,
	}
}

// SubmitForReview submits an addon for review
func (rs *ReviewSystem) SubmitForReview(ctx context.Context, addonID, submitterID string, config *AddonConfiguration, submissionType SubmissionType) (*ReviewRequest, error) {
	// Validate addon configuration
	if err := rs.metadataManager.ValidateMetadata(&config.Metadata); err != nil {
		return nil, fmt.Errorf("invalid addon metadata: %w", err)
	}

	// Create review request
	request := &ReviewRequest{
		ID:             generateReviewRequestID(),
		AddonID:        addonID,
		SubmitterID:    submitterID,
		AddonConfig:    config,
		SubmissionType: submissionType,
		Priority:       rs.determinePriority(config, submissionType),
		Status:         ReviewStatusPending,
		SubmittedAt:    time.Now(),
		ReviewDeadline: time.Now().Add(rs.config.ReviewTimeout),
		Metadata:       make(map[string]interface{}),
	}

	// Run automated tests if enabled
	if rs.config.EnableAutoTesting {
		testResults, err := rs.runAutomatedTests(ctx, config)
		if err != nil {
			return nil, fmt.Errorf("automated testing failed: %w", err)
		}
		request.TestResults = testResults
	}

	// Run security scan if enabled
	if rs.config.SecurityScanEnabled {
		scanResults, err := rs.runSecurityScan(ctx, config)
		if err != nil {
			return nil, fmt.Errorf("security scan failed: %w", err)
		}
		request.SecurityScan = scanResults
	}

	// Check for auto-approval
	if rs.canAutoApprove(request) {
		request.Status = ReviewStatusApproved
		return request, nil
	}

	// Add to review queue
	rs.mutex.Lock()
	rs.reviewQueue = append(rs.reviewQueue, request)
	rs.sortReviewQueue()
	rs.mutex.Unlock()

	return request, nil
}

// AssignReviewer assigns a reviewer to a review request
func (rs *ReviewSystem) AssignReviewer(requestID, reviewerID string) (*ReviewSession, error) {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	// Find the review request
	var request *ReviewRequest
	for _, req := range rs.reviewQueue {
		if req.ID == requestID {
			request = req
			break
		}
	}

	if request == nil {
		return nil, fmt.Errorf("review request not found: %s", requestID)
	}

	if request.Status != ReviewStatusPending {
		return nil, fmt.Errorf("review request is not pending: %s", request.Status)
	}

	// Create review session
	session := &ReviewSession{
		ID:         generateReviewSessionID(),
		RequestID:  requestID,
		ReviewerID: reviewerID,
		Status:     ReviewSessionStatusActive,
		StartedAt:  time.Now(),
		Comments:   []ReviewComment{},
		Checklist:  rs.generateChecklist(request.AddonConfig),
		Context:    make(map[string]interface{}),
	}

	// Update request status
	request.Status = ReviewStatusInReview

	// Store session
	rs.activeReviews[session.ID] = session

	return session, nil
}

// SubmitReview submits a completed review
func (rs *ReviewSystem) SubmitReview(sessionID string, decision ReviewDecision, score float64, finalComments string) error {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	session, exists := rs.activeReviews[sessionID]
	if !exists {
		return fmt.Errorf("review session not found: %s", sessionID)
	}

	if session.Status != ReviewSessionStatusActive {
		return fmt.Errorf("review session is not active: %s", session.Status)
	}

	// Complete the session
	now := time.Now()
	session.CompletedAt = &now
	session.Decision = decision
	session.Score = score
	session.Status = ReviewSessionStatusCompleted
	session.TimeSpent = time.Since(session.StartedAt)

	// Add final comments if provided
	if finalComments != "" {
		comment := ReviewComment{
			ID:         generateCommentID(),
			ReviewerID: session.ReviewerID,
			Timestamp:  now,
			Type:       CommentTypeGeneral,
			Severity:   CommentSeverityInfo,
			Category:   "final_review",
			Message:    finalComments,
		}
		session.Comments = append(session.Comments, comment)
	}

	// Update review request status based on decision
	request := rs.findRequestByID(session.RequestID)
	if request != nil {
		switch decision {
		case ReviewDecisionApprove:
			request.Status = ReviewStatusApproved
		case ReviewDecisionReject:
			request.Status = ReviewStatusRejected
		case ReviewDecisionNeedsWork:
			request.Status = ReviewStatusNeedsWork
		}
	}

	return nil
}

// AddComment adds a comment to a review session
func (rs *ReviewSystem) AddComment(sessionID, reviewerID string, commentType CommentType, severity CommentSeverity, category, message string, codeRef *CodeReference) error {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	session, exists := rs.activeReviews[sessionID]
	if !exists {
		return fmt.Errorf("review session not found: %s", sessionID)
	}

	if session.ReviewerID != reviewerID {
		return fmt.Errorf("reviewer ID mismatch")
	}

	comment := ReviewComment{
		ID:         generateCommentID(),
		ReviewerID: reviewerID,
		Timestamp:  time.Now(),
		Type:       commentType,
		Severity:   severity,
		Category:   category,
		Message:    message,
		CodeRef:    codeRef,
	}

	session.Comments = append(session.Comments, comment)

	return nil
}

// GetReviewQueue returns the current review queue
func (rs *ReviewSystem) GetReviewQueue() []*ReviewRequest {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	// Return a copy to prevent external modification
	queue := make([]*ReviewRequest, len(rs.reviewQueue))
	copy(queue, rs.reviewQueue)
	return queue
}

// GetReviewSession returns a review session by ID
func (rs *ReviewSystem) GetReviewSession(sessionID string) (*ReviewSession, error) {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	session, exists := rs.activeReviews[sessionID]
	if !exists {
		return nil, fmt.Errorf("review session not found: %s", sessionID)
	}

	return session, nil
}

// Helper methods

func (rs *ReviewSystem) determinePriority(config *AddonConfiguration, submissionType SubmissionType) ReviewPriority {
	// Determine priority based on various factors
	if submissionType == SubmissionTypeFix {
		return ReviewPriorityHigh
	}

	// Check for security-related addons
	for _, tag := range config.Metadata.Tags {
		if tag == "security" || tag == "authentication" {
			return ReviewPriorityHigh
		}
	}

	// Check for public addons
	if config.Metadata.IsPublic {
		return ReviewPriorityNormal
	}

	return ReviewPriorityLow
}

func (rs *ReviewSystem) canAutoApprove(request *ReviewRequest) bool {
	// Auto-approve only if all conditions are met
	if !rs.config.EnableAutoTesting || !rs.config.SecurityScanEnabled {
		return false
	}

	if request.TestResults == nil || !request.TestResults.Success {
		return false
	}

	if request.SecurityScan == nil || !request.SecurityScan.Passed {
		return false
	}

	// Check if scores meet threshold
	testScore := 1.0
	if len(request.TestResults.Errors) > 0 {
		testScore = 0.5
	} else if len(request.TestResults.Warnings) > 0 {
		testScore = 0.8
	}

	combinedScore := (testScore + request.SecurityScan.Score) / 2
	return combinedScore >= rs.config.AutoApproveThreshold
}

func (rs *ReviewSystem) runAutomatedTests(ctx context.Context, config *AddonConfiguration) (*TestResults, error) {
	// Use the testing environment to run automated tests
	options := TestOptions{
		Timeout:             time.Minute * 5,
		EnablePerformance:   true,
		EnableSecurity:      true,
		EnableCompatibility: true,
		Environment:         "review",
	}

	session, err := rs.testingEnv.StartTest(ctx, config, options)
	if err != nil {
		return nil, err
	}

	// Wait for test completion (simplified for this example)
	time.Sleep(time.Second * 10)

	// Get test results
	finalSession, err := rs.testingEnv.GetTestSession(session.ID)
	if err != nil {
		return nil, err
	}

	return finalSession.Results, nil
}

func (rs *ReviewSystem) runSecurityScan(ctx context.Context, config *AddonConfiguration) (*SecurityScanResults, error) {
	// Simulate security scanning
	results := &SecurityScanResults{
		ScanID:          generateScanID(),
		ScannedAt:       time.Now(),
		Passed:          true,
		Score:           0.95,
		Vulnerabilities: []SecurityVulnerability{},
		Warnings:        []SecurityWarning{},
		Recommendations: []string{},
	}

	// Simulate some basic security checks
	if !config.Settings.Security.Sandbox {
		results.Warnings = append(results.Warnings, SecurityWarning{
			Type:        "configuration",
			Description: "Sandbox mode is not enabled",
			Suggestion:  "Enable sandbox mode for better security",
		})
		results.Score -= 0.1
	}

	if len(config.Settings.Permissions) > 10 {
		results.Warnings = append(results.Warnings, SecurityWarning{
			Type:        "permissions",
			Description: "Too many permissions requested",
			Suggestion:  "Review and minimize required permissions",
		})
		results.Score -= 0.05
	}

	results.Passed = results.Score >= 0.7

	return results, nil
}

func (rs *ReviewSystem) generateChecklist(config *AddonConfiguration) ReviewChecklist {
	return ReviewChecklist{
		SecurityChecks: []ChecklistItem{
			{ID: "sec_1", Description: "Addon uses secure coding practices", Required: true},
			{ID: "sec_2", Description: "No hardcoded credentials or secrets", Required: true},
			{ID: "sec_3", Description: "Input validation is properly implemented", Required: true},
			{ID: "sec_4", Description: "Sandbox restrictions are respected", Required: true},
		},
		PerformanceChecks: []ChecklistItem{
			{ID: "perf_1", Description: "Addon has reasonable resource usage", Required: true},
			{ID: "perf_2", Description: "No memory leaks detected", Required: false},
			{ID: "perf_3", Description: "Execution time is within limits", Required: true},
		},
		UsabilityChecks: []ChecklistItem{
			{ID: "ui_1", Description: "Addon has clear and helpful documentation", Required: true},
			{ID: "ui_2", Description: "User interface is intuitive", Required: false},
			{ID: "ui_3", Description: "Error messages are user-friendly", Required: true},
		},
		CodeQualityChecks: []ChecklistItem{
			{ID: "code_1", Description: "Code follows platform conventions", Required: true},
			{ID: "code_2", Description: "Workflow is well-structured", Required: true},
			{ID: "code_3", Description: "No deprecated APIs are used", Required: true},
		},
		MetadataChecks: []ChecklistItem{
			{ID: "meta_1", Description: "Addon metadata is complete and accurate", Required: true},
			{ID: "meta_2", Description: "Category and tags are appropriate", Required: true},
			{ID: "meta_3", Description: "Version follows semantic versioning", Required: true},
		},
	}
}

func (rs *ReviewSystem) sortReviewQueue() {
	// Sort by priority and submission time
	// Implementation would sort the queue based on priority and age
}

func (rs *ReviewSystem) findRequestByID(requestID string) *ReviewRequest {
	for _, request := range rs.reviewQueue {
		if request.ID == requestID {
			return request
		}
	}
	return nil
}

// ID generation functions
func generateReviewRequestID() string {
	return fmt.Sprintf("review_req_%d", time.Now().UnixNano())
}

func generateReviewSessionID() string {
	return fmt.Sprintf("review_sess_%d", time.Now().UnixNano())
}

func generateCommentID() string {
	return fmt.Sprintf("comment_%d", time.Now().UnixNano())
}

func generateScanID() string {
	return fmt.Sprintf("scan_%d", time.Now().UnixNano())
}
