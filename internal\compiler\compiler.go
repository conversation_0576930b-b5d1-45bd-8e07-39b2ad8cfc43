package compiler

import (
	"archive/zip"
	"fmt"
	"html/template"
	"io"
	"os"
	"path/filepath"

	"goVwPlatformAPI/internal/database"
)

type Compiler struct {
	db        *database.DB
	outputDir string
}

func NewCompiler(db *database.DB, outputDir string) *Compiler {
	return &Compiler{
		db:        db,
		outputDir: outputDir,
	}
}

func (c *Compiler) CompileSite() error {
	// Load site data from database
	// TODO: Implement actual page loading from database
	pages := []*Page{{
		Slug:  "index",
		Title: "Home Page",
	}}

	// Create output directory structure
	if err := os.MkdirAll(filepath.Join(c.outputDir, "assets"), 0755); err != nil {
		return fmt.Errorf("failed to create assets directory: %w", err)
	}

	// Generate HTML pages
	for _, page := range pages {
		if err := c.generateHTMLPage(page); err != nil {
			return fmt.Errorf("failed to generate page %s: %w", page.Slug, err)
		}
	}

	// Bundle HTMX scripts
	if err := c.bundleHTMX(); err != nil {
		return fmt.Errorf("failed to bundle HTMX: %w", err)
	}

	// Compile CSS themes
	if err := c.compileCSSThemes(); err != nil {
		return fmt.Errorf("failed to compile CSS themes: %w", err)
	}

	// Create zip archive
	if err := c.createZipArchive(); err != nil {
		return fmt.Errorf("failed to create zip archive: %w", err)
	}

	return nil
}

type Page struct {
	Slug  string
	Title string
}

func (c *Compiler) generateHTMLPage(page *Page) error {
	pageDir := filepath.Join(c.outputDir, page.Slug)
	if err := os.MkdirAll(pageDir, 0755); err != nil {
		return fmt.Errorf("failed to create page directory: %w", err)
	}

	outputPath := filepath.Join(pageDir, "index.html")
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer file.Close()

	tmpl, err := template.New("page").Parse(`
	<!DOCTYPE html>
	<html>
	<head>
		<title>{{.Title}}</title>
		<link rel="stylesheet" href="/assets/theme.css">
	</head>
	<body>
		<h1>{{.Title}}</h1>
		<nav>
			<!-- TODO: Add navigation -->
		</nav>
		<main>
			{{/* TODO: Add page content */}}
		</main>
		<script src="/assets/htmx.min.js"></script>
	</body>
	</html>
	`)
	if err != nil {
		return fmt.Errorf("failed to parse template: %w", err)
	}

	if err := tmpl.Execute(file, page); err != nil {
		return fmt.Errorf("failed to execute template: %w", err)
	}

	return nil
}

func (c *Compiler) bundleHTMX() error {
	srcPath := filepath.Join("node_modules", "htmx.org", "dist", "htmx.min.js")
	destPath := filepath.Join(c.outputDir, "assets", "htmx.min.js")

	input, err := os.ReadFile(srcPath)
	if err != nil {
		return fmt.Errorf("failed to read HTMX source: %w", err)
	}

	if err := os.WriteFile(destPath, input, 0644); err != nil {
		return fmt.Errorf("failed to write HTMX bundle: %w", err)
	}

	return nil
}

func (c *Compiler) compileCSSThemes() error {
	// TODO: Load actual theme variables from database
	variables := map[string]string{
		"--primary-color":   "#007bff",
		"--secondary-color": "#6c757d",
	}

	css := ":root {\n"
	for name, value := range variables {
		css += fmt.Sprintf("  %s: %s;\n", name, value)
	}
	css += "}\n"

	themePath := filepath.Join(c.outputDir, "assets", "theme.css")
	if err := os.WriteFile(themePath, []byte(css), 0644); err != nil {
		return fmt.Errorf("failed to write theme CSS: %w", err)
	}

	return nil
}

func (c *Compiler) createZipArchive() error {
	zipPath := filepath.Join(c.outputDir, "site.zip")
	zipFile, err := os.Create(zipPath)
	if err != nil {
		return fmt.Errorf("failed to create zip file: %w", err)
	}
	defer zipFile.Close()

	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	err = filepath.Walk(c.outputDir, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories and the zip file itself
		if info.IsDir() || filePath == zipPath {
			return nil
		}

		relPath, err := filepath.Rel(c.outputDir, filePath)
		if err != nil {
			return err
		}

		zipEntry, err := zipWriter.Create(relPath)
		if err != nil {
			return err
		}

		file, err := os.Open(filePath)
		if err != nil {
			return err
		}
		defer file.Close()

		_, err = io.Copy(zipEntry, file)
		return err
	})

	return err
}
