/* Velocity Platform Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.addon-builder {
    min-height: 600px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.snippet-preview {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    background-color: white;
}

.theme-variable {
    margin-bottom: 1rem;
}

.theme-variable label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.navigation-tree {
    list-style: none;
    padding-left: 0;
}

.navigation-tree li {
    margin: 0.5rem 0;
}

.navigation-tree .nav-item {
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: white;
    cursor: move;
}

.navigation-tree .nav-item:hover {
    background-color: #f8f9fa;
}

.addon-status {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.addon-status.development {
    background-color: #fff3cd;
    color: #856404;
}

.addon-status.review {
    background-color: #cff4fc;
    color: #055160;
}

.addon-status.production {
    background-color: #d1e7dd;
    color: #0f5132;
}

.addon-status.disabled {
    background-color: #f8d7da;
    color: #721c24;
}

.rete-editor {
    width: 100%;
    height: 500px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 1.25rem 1rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: .65;
    transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
}

.btn-group-sm > .btn, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
}

.table-responsive {
    border-radius: 0.375rem;
}

.badge {
    font-size: 0.75em;
}

.text-muted {
    color: #6c757d !important;
}

.border-start {
    border-left: 1px solid #dee2e6 !important;
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}

.border-top {
    border-top: 1px solid #dee2e6 !important;
}

.border-bottom {
    border-bottom: 1px solid #dee2e6 !important;
}