-- Drop triggers
DROP TRIGGER IF EXISTS update_external_api_rate_limits_updated_at ON external_api_rate_limits;
DROP TRIGGER IF EXISTS update_external_api_connections_updated_at ON external_api_connections;
DROP TRIGGER IF EXISTS update_external_api_credentials_updated_at ON external_api_credentials;
DROP TRIGGER IF EXISTS update_external_api_configurations_updated_at ON external_api_configurations;

-- Drop indexes
DROP INDEX IF EXISTS idx_external_api_rate_limits_window_start;
DROP INDEX IF EXISTS idx_external_api_rate_limits_credentials_id;
DROP INDEX IF EXISTS idx_external_api_rate_limits_api_config_id;

DROP INDEX IF EXISTS idx_external_api_call_logs_status_code;
DROP INDEX IF EXISTS idx_external_api_call_logs_created_by;
DROP INDEX IF EXISTS idx_external_api_call_logs_created_at;
DROP INDEX IF EXISTS idx_external_api_call_logs_credentials_id;
DROP INDEX IF EXISTS idx_external_api_call_logs_api_config_id;

DROP INDEX IF EXISTS idx_external_api_connections_created_by;
DROP INDEX IF EXISTS idx_external_api_connections_status;
DROP INDEX IF EXISTS idx_external_api_connections_credentials_id;
DROP INDEX IF EXISTS idx_external_api_connections_api_config_id;

DROP INDEX IF EXISTS idx_external_api_credentials_expires_at;
DROP INDEX IF EXISTS idx_external_api_credentials_is_active;
DROP INDEX IF EXISTS idx_external_api_credentials_tenant_id;
DROP INDEX IF EXISTS idx_external_api_credentials_created_by;
DROP INDEX IF EXISTS idx_external_api_credentials_api_config_id;

DROP INDEX IF EXISTS idx_external_api_configurations_auth_type;
DROP INDEX IF EXISTS idx_external_api_configurations_status;
DROP INDEX IF EXISTS idx_external_api_configurations_tenant_id;
DROP INDEX IF EXISTS idx_external_api_configurations_created_by;

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS external_api_rate_limits;
DROP TABLE IF EXISTS external_api_call_logs;
DROP TABLE IF EXISTS external_api_connections;
DROP TABLE IF EXISTS external_api_credentials;
DROP TABLE IF EXISTS external_api_configurations;