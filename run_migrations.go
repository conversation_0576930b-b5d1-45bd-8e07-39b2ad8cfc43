package main

import (
	"log"
	"os"

	"goVwPlatformAPI/internal/database"
)

func main() {
	// Set environment variables for database connection
	os.Setenv("DB_HOST", "**************")
	os.Setenv("DB_PORT", "5432")
	os.Setenv("DB_NAME", "VW_Platform_DB")
	os.Setenv("DB_USER", "PLATFORMDB")
	os.Setenv("DB_PASSWORD", "$Jf6sSkfyPb&v7r1")

	// Initialize database connection
	db, err := database.NewConnection()
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer db.Close()

	// Run migrations
	err = db.RunMigrations()
	if err != nil {
		log.Fatal("Failed to run migrations:", err)
	}

	log.Println("Database migrations completed successfully")

	// Seed database
	err = db.SeedDatabase()
	if err != nil {
		log.Fatal("Failed to seed database:", err)
	}

	log.Println("Database seeding completed successfully")
}
