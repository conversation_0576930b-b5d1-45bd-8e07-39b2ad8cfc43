{{define "content"}}
<div class="container py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2>Compliance Tracking</h2>
            <p class="text-muted mb-0">Monitor and manage your business compliance requirements</p>
        </div>
        <div>
            <button class="btn btn-outline-primary me-2" onclick="generateComplianceReport()">
                <i class="bi bi-file-earmark-text me-1"></i>Generate Report
            </button>
            <button class="btn btn-primary" onclick="addComplianceFramework()">
                <i class="bi bi-plus me-1"></i>Add Framework
            </button>
        </div>
    </div>
    
    <!-- Compliance Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success" id="compliantCount">0</h4>
                    <small class="text-muted">Compliant</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-warning" id="inProgressCount">0</h4>
                    <small class="text-muted">In Progress</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-danger" id="nonCompliantCount">0</h4>
                    <small class="text-muted">Non-Compliant</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info" id="alertsCount">0</h4>
                    <small class="text-muted">Active Alerts</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Alerts Section -->
    <div class="card mb-4" id="alertsSection">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="bi bi-exclamation-triangle me-2"></i>Compliance Alerts
            </h6>
        </div>
        <div class="card-body">
            <div id="alertsList">
                <p class="text-muted text-center py-3">No active alerts</p>
            </div>
        </div>
    </div>
    
    <!-- Compliance Frameworks -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Compliance Frameworks</h6>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="filterType" id="allFrameworks" checked>
                    <label class="btn btn-outline-secondary" for="allFrameworks">All</label>
                    <input type="radio" class="btn-check" name="filterType" id="mandatoryOnly">
                    <label class="btn btn-outline-secondary" for="mandatoryOnly">Mandatory</label>
                    <input type="radio" class="btn-check" name="filterType" id="industrySpecific">
                    <label class="btn btn-outline-secondary" for="industrySpecific">Industry</label>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="frameworksList">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading compliance frameworks...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Framework Modal -->
<div class="modal fade" id="addFrameworkModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Compliance Framework</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-4">
                    <div class="col-12">
                        <h6>Available Frameworks</h6>
                        <div id="availableFrameworks" class="row">
                            <!-- Available frameworks will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <form id="frameworkForm">
                    <input type="hidden" id="selectedFrameworkId">
                    <div class="mb-3">
                        <label for="assignedTo" class="form-label">Assign To</label>
                        <select class="form-select" id="assignedTo">
                            <option value="">Select team member</option>
                            <!-- Team members will be loaded here -->
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="priorityLevel" class="form-label">Priority Level</label>
                            <select class="form-select" id="priorityLevel">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="critical">Critical</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="targetDate" class="form-label">Target Completion Date</label>
                            <input type="date" class="form-control" id="targetDate">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="3" placeholder="Add any relevant notes..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveFramework()">Add Framework</button>
            </div>
        </div>
    </div>
</div>

<!-- Compliance Details Modal -->
<div class="modal fade" id="complianceDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="complianceDetailsTitle">Compliance Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="complianceDetailsContent">
                    <!-- Compliance details will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let complianceData = [];
let availableFrameworks = [];
let alerts = [];

document.addEventListener('DOMContentLoaded', function() {
    loadComplianceData();
    loadAvailableFrameworks();
    loadAlerts();
    
    // Setup filter handlers
    document.querySelectorAll('input[name="filterType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            filterFrameworks();
        });
    });
});

async function loadComplianceData() {
    try {
        // Mock data for demonstration
        complianceData = [
            {
                id: '1',
                framework: {
                    id: 'gdpr',
                    frameworkName: 'General Data Protection Regulation',
                    frameworkCode: 'GDPR',
                    isMandatory: true,
                    industrySector: 'All'
                },
                complianceStatus: 'compliant',
                priorityLevel: 'high',
                nextReviewDate: '2024-05-25',
                completionPercentage: 100
            },
            {
                id: '2',
                framework: {
                    id: 'hswa',
                    frameworkName: 'Health and Safety at Work',
                    frameworkCode: 'HSWA',
                    isMandatory: true,
                    industrySector: 'All'
                },
                complianceStatus: 'in_progress',
                priorityLevel: 'medium',
                nextReviewDate: '2024-03-15',
                completionPercentage: 75
            },
            {
                id: '3',
                framework: {
                    id: 'iso27001',
                    frameworkName: 'ISO 27001',
                    frameworkCode: 'ISO27001',
                    isMandatory: false,
                    industrySector: 'Technology'
                },
                complianceStatus: 'not_started',
                priorityLevel: 'low',
                nextReviewDate: null,
                completionPercentage: 0
            }
        ];
        
        updateOverviewStats();
        renderFrameworks();
    } catch (error) {
        console.error('Failed to load compliance data:', error);
    }
}

async function loadAvailableFrameworks() {
    try {
        // Mock available frameworks
        availableFrameworks = [
            { id: 'gdpr', name: 'General Data Protection Regulation', mandatory: true, sector: 'All' },
            { id: 'hswa', name: 'Health and Safety at Work', mandatory: true, sector: 'All' },
            { id: 'employment', name: 'Employment Law Compliance', mandatory: true, sector: 'All' },
            { id: 'aml', name: 'Anti-Money Laundering', mandatory: true, sector: 'Financial Services' },
            { id: 'iso27001', name: 'ISO 27001', mandatory: false, sector: 'Technology' },
            { id: 'pci_dss', name: 'PCI DSS', mandatory: false, sector: 'Retail/E-commerce' }
        ];
    } catch (error) {
        console.error('Failed to load available frameworks:', error);
    }
}

async function loadAlerts() {
    try {
        // Mock alerts
        alerts = [
            {
                id: '1',
                alertType: 'deadline_approaching',
                alertTitle: 'GDPR Review Due Soon',
                alertMessage: 'Annual GDPR compliance review is due in 30 days',
                severity: 'medium',
                triggerDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                id: '2',
                alertType: 'overdue',
                alertTitle: 'Health & Safety Training Overdue',
                alertMessage: 'Staff health and safety training is 15 days overdue',
                severity: 'high',
                triggerDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
            }
        ];
        
        renderAlerts();
    } catch (error) {
        console.error('Failed to load alerts:', error);
    }
}

function updateOverviewStats() {
    const compliant = complianceData.filter(c => c.complianceStatus === 'compliant').length;
    const inProgress = complianceData.filter(c => c.complianceStatus === 'in_progress').length;
    const nonCompliant = complianceData.filter(c => c.complianceStatus === 'non_compliant' || c.complianceStatus === 'not_started').length;
    
    document.getElementById('compliantCount').textContent = compliant;
    document.getElementById('inProgressCount').textContent = inProgress;
    document.getElementById('nonCompliantCount').textContent = nonCompliant;
    document.getElementById('alertsCount').textContent = alerts.length;
}

function renderAlerts() {
    const alertsList = document.getElementById('alertsList');
    
    if (alerts.length === 0) {
        alertsList.innerHTML = '<p class="text-muted text-center py-3">No active alerts</p>';
        return;
    }
    
    alertsList.innerHTML = alerts.map(alert => `
        <div class="alert alert-${getSeverityClass(alert.severity)} d-flex justify-content-between align-items-start" role="alert">
            <div>
                <h6 class="alert-heading">${alert.alertTitle}</h6>
                <p class="mb-1">${alert.alertMessage}</p>
                <small class="text-muted">
                    ${alert.alertType === 'overdue' ? 'Overdue since' : 'Due'}: ${formatDate(alert.triggerDate)}
                </small>
            </div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="acknowledgeAlert('${alert.id}')">
                Acknowledge
            </button>
        </div>
    `).join('');
}

function renderFrameworks() {
    const container = document.getElementById('frameworksList');
    
    if (complianceData.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-shield-check display-4 text-muted"></i>
                <h5 class="mt-3">No compliance frameworks added</h5>
                <p class="text-muted">Add your first compliance framework to start tracking</p>
                <button class="btn btn-primary" onclick="addComplianceFramework()">
                    <i class="bi bi-plus me-1"></i>Add Framework
                </button>
            </div>
        `;
        return;
    }
    
    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Framework</th>
                        <th>Status</th>
                        <th>Priority</th>
                        <th>Progress</th>
                        <th>Next Review</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${complianceData.map(compliance => `
                        <tr>
                            <td>
                                <div>
                                    <h6 class="mb-1">${compliance.framework.frameworkName}</h6>
                                    <small class="text-muted">
                                        ${compliance.framework.frameworkCode}
                                        ${compliance.framework.isMandatory ? '<span class="badge bg-danger ms-1">Mandatory</span>' : ''}
                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-${getStatusColor(compliance.complianceStatus)}">
                                    ${compliance.complianceStatus.replace('_', ' ')}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-${getPriorityColor(compliance.priorityLevel)}">
                                    ${compliance.priorityLevel}
                                </span>
                            </td>
                            <td>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-${getStatusColor(compliance.complianceStatus)}" 
                                         style="width: ${compliance.completionPercentage}%"></div>
                                </div>
                                <small class="text-muted">${compliance.completionPercentage}%</small>
                            </td>
                            <td>
                                ${compliance.nextReviewDate ? formatDate(compliance.nextReviewDate) : 'Not set'}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewComplianceDetails('${compliance.id}')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="updateComplianceStatus('${compliance.id}')">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function getStatusColor(status) {
    switch (status) {
        case 'compliant': return 'success';
        case 'in_progress': return 'warning';
        case 'non_compliant': return 'danger';
        case 'not_started': return 'secondary';
        default: return 'secondary';
    }
}

function getPriorityColor(priority) {
    switch (priority) {
        case 'critical': return 'danger';
        case 'high': return 'warning';
        case 'medium': return 'info';
        case 'low': return 'secondary';
        default: return 'secondary';
    }
}

function getSeverityClass(severity) {
    switch (severity) {
        case 'critical': return 'danger';
        case 'high': return 'warning';
        case 'medium': return 'info';
        case 'low': return 'light';
        default: return 'info';
    }
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

function addComplianceFramework() {
    // Populate available frameworks
    const container = document.getElementById('availableFrameworks');
    container.innerHTML = availableFrameworks.map(framework => `
        <div class="col-md-6 mb-3">
            <div class="card framework-option" onclick="selectFramework('${framework.id}')">
                <div class="card-body">
                    <h6 class="card-title">${framework.name}</h6>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">${framework.sector}</small>
                        ${framework.mandatory ? '<span class="badge bg-danger">Mandatory</span>' : '<span class="badge bg-secondary">Optional</span>'}
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    const modal = new bootstrap.Modal(document.getElementById('addFrameworkModal'));
    modal.show();
}

function selectFramework(frameworkId) {
    document.getElementById('selectedFrameworkId').value = frameworkId;
    
    // Highlight selected framework
    document.querySelectorAll('.framework-option').forEach(option => {
        option.classList.remove('border-primary');
    });
    event.currentTarget.classList.add('border-primary');
}

function saveFramework() {
    const frameworkId = document.getElementById('selectedFrameworkId').value;
    if (!frameworkId) {
        showAlert('Please select a framework', 'warning');
        return;
    }
    
    // Mock save - in real implementation, this would call GraphQL
    showAlert('Compliance framework added successfully!', 'success');
    bootstrap.Modal.getInstance(document.getElementById('addFrameworkModal')).hide();
    
    // Reload data
    loadComplianceData();
}

function viewComplianceDetails(complianceId) {
    const compliance = complianceData.find(c => c.id === complianceId);
    if (!compliance) return;
    
    document.getElementById('complianceDetailsTitle').textContent = compliance.framework.frameworkName;
    document.getElementById('complianceDetailsContent').innerHTML = `
        <div class="row">
            <div class="col-md-8">
                <h6>Requirements Checklist</h6>
                <div class="list-group">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Data Processing Records</h6>
                            <p class="mb-1">Maintain records of all data processing activities</p>
                        </div>
                        <span class="badge bg-success">Complete</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Privacy Policy</h6>
                            <p class="mb-1">Update privacy policy to comply with GDPR</p>
                        </div>
                        <span class="badge bg-warning">In Progress</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Staff Training</h6>
                            <p class="mb-1">Conduct GDPR awareness training for all staff</p>
                        </div>
                        <span class="badge bg-secondary">Pending</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <h6>Compliance Summary</h6>
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Overall Progress:</span>
                            <strong>${compliance.completionPercentage}%</strong>
                        </div>
                        <div class="progress mb-3">
                            <div class="progress-bar" style="width: ${compliance.completionPercentage}%"></div>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Status:</span>
                            <span class="badge bg-${getStatusColor(compliance.complianceStatus)}">
                                ${compliance.complianceStatus.replace('_', ' ')}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Priority:</span>
                            <span class="badge bg-${getPriorityColor(compliance.priorityLevel)}">
                                ${compliance.priorityLevel}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Next Review:</span>
                            <span>${compliance.nextReviewDate ? formatDate(compliance.nextReviewDate) : 'Not set'}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('complianceDetailsModal'));
    modal.show();
}

function acknowledgeAlert(alertId) {
    alerts = alerts.filter(alert => alert.id !== alertId);
    renderAlerts();
    updateOverviewStats();
    showAlert('Alert acknowledged', 'success');
}

function generateComplianceReport() {
    showAlert('Compliance report generation coming soon!', 'info');
}

function filterFrameworks() {
    // TODO: Implement framework filtering
    renderFrameworks();
}

function showAlert(message, type) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    document.body.insertAdjacentHTML('afterbegin', alertHTML);
    
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 3000);
}
</script>

<style>
.framework-option {
    cursor: pointer;
    transition: all 0.2s ease;
}

.framework-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.framework-option.border-primary {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}
</style>
{{end}}

{{define "title"}}Compliance Tracking - VelocityWave{{end}}