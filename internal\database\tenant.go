package database

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type Tenant struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	Slug      string    `json:"slug"`
	Domain    string    `json:"domain"`
	Status    string    `json:"status"`
	PlanType  string    `json:"plan_type"`
	MaxUsers  int       `json:"max_users"`
	MaxSites  int       `json:"max_sites"`
	Settings  string    `json:"settings"` // JSON blob for tenant-specific settings
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type TenantService struct {
	DB *DB
}

// NewTenantService creates a new tenant service
func NewTenantService(db *DB) *TenantService {
	return &TenantService{DB: db}
}

// GetTenantByID retrieves a tenant by ID
func (s *TenantService) GetTenantByID(tenantID string) (*Tenant, error) {
	var tenant Tenant
	query := `
		SELECT id, name, slug, domain, status, plan_type, max_users, max_sites, 
		       settings, created_at, updated_at
		FROM tenants 
		WHERE id = $1
	`

	err := s.DB.Pool.QueryRow(context.Background(), query, tenantID).Scan(
		&tenant.ID, &tenant.Name, &tenant.Slug, &tenant.Domain, &tenant.Status,
		&tenant.PlanType, &tenant.MaxUsers, &tenant.MaxSites, &tenant.Settings,
		&tenant.CreatedAt, &tenant.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return &tenant, nil
}

// GetTenantBySlug retrieves a tenant by slug
func (s *TenantService) GetTenantBySlug(slug string) (*Tenant, error) {
	var tenant Tenant
	query := `
		SELECT id, name, slug, domain, status, plan_type, max_users, max_sites, 
		       settings, created_at, updated_at
		FROM tenants 
		WHERE slug = $1
	`

	err := s.DB.Pool.QueryRow(context.Background(), query, slug).Scan(
		&tenant.ID, &tenant.Name, &tenant.Slug, &tenant.Domain, &tenant.Status,
		&tenant.PlanType, &tenant.MaxUsers, &tenant.MaxSites, &tenant.Settings,
		&tenant.CreatedAt, &tenant.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return &tenant, nil
}

// CreateTenant creates a new tenant
func (s *TenantService) CreateTenant(name, slug, domain, planType string) (*Tenant, error) {
	tenantID := uuid.New().String()
	now := time.Now()

	// Set default limits based on plan type
	maxUsers, maxSites := s.getPlanLimits(planType)

	tenant := &Tenant{
		ID:        tenantID,
		Name:      name,
		Slug:      slug,
		Domain:    domain,
		Status:    "active",
		PlanType:  planType,
		MaxUsers:  maxUsers,
		MaxSites:  maxSites,
		Settings:  "{}",
		CreatedAt: now,
		UpdatedAt: now,
	}

	query := `
		INSERT INTO tenants (id, name, slug, domain, status, plan_type, max_users, max_sites, 
		                     settings, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
	`

	_, err := s.DB.Pool.Exec(context.Background(), query,
		tenant.ID, tenant.Name, tenant.Slug, tenant.Domain, tenant.Status,
		tenant.PlanType, tenant.MaxUsers, tenant.MaxSites, tenant.Settings,
		tenant.CreatedAt, tenant.UpdatedAt)

	if err != nil {
		return nil, err
	}

	return tenant, nil
}

// UpdateTenant updates tenant information
func (s *TenantService) UpdateTenant(tenantID string, updates map[string]interface{}) error {
	// Build dynamic update query
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	for field, value := range updates {
		setParts = append(setParts, fmt.Sprintf("%s = $%d", field, argIndex))
		args = append(args, value)
		argIndex++
	}

	// Always update the updated_at field
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add tenant ID for WHERE clause
	args = append(args, tenantID)

	query := fmt.Sprintf("UPDATE tenants SET %s WHERE id = $%d",
		fmt.Sprintf("%s", setParts), argIndex)

	_, err := s.DB.Pool.Exec(context.Background(), query, args...)
	return err
}

// GetTenantUsers retrieves all users for a tenant
func (s *TenantService) GetTenantUsers(tenantID string, limit, offset int) ([]interface{}, error) {
	query := `
		SELECT id, email, first_name, last_name, role, email_verified, created_at
		FROM users 
		WHERE tenant_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := s.DB.Pool.Query(context.Background(), query, tenantID, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []interface{}
	for rows.Next() {
		var user struct {
			ID            string    `json:"id"`
			Email         string    `json:"email"`
			FirstName     string    `json:"first_name"`
			LastName      string    `json:"last_name"`
			Role          string    `json:"role"`
			EmailVerified bool      `json:"email_verified"`
			CreatedAt     time.Time `json:"created_at"`
		}

		err := rows.Scan(&user.ID, &user.Email, &user.FirstName, &user.LastName,
			&user.Role, &user.EmailVerified, &user.CreatedAt)
		if err != nil {
			return nil, err
		}

		users = append(users, user)
	}

	return users, nil
}

// GetTenantStats retrieves statistics for a tenant
func (s *TenantService) GetTenantStats(tenantID string) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Count users
	var userCount int
	err := s.DB.Pool.QueryRow(context.Background(),
		"SELECT COUNT(*) FROM users WHERE tenant_id = $1", tenantID).Scan(&userCount)
	if err != nil {
		return nil, err
	}
	stats["user_count"] = userCount

	// Count sites
	var siteCount int
	err = s.DB.Pool.QueryRow(context.Background(),
		"SELECT COUNT(*) FROM sites WHERE tenant_id = $1", tenantID).Scan(&siteCount)
	if err != nil {
		return nil, err
	}
	stats["site_count"] = siteCount

	// Count pages
	var pageCount int
	err = s.DB.Pool.QueryRow(context.Background(),
		"SELECT COUNT(*) FROM pages WHERE tenant_id = $1", tenantID).Scan(&pageCount)
	if err != nil {
		return nil, err
	}
	stats["page_count"] = pageCount

	// Count addons
	var addonCount int
	err = s.DB.Pool.QueryRow(context.Background(),
		"SELECT COUNT(*) FROM addons WHERE tenant_id = $1", tenantID).Scan(&addonCount)
	if err != nil {
		return nil, err
	}
	stats["addon_count"] = addonCount

	return stats, nil
}

// ValidateTenantAccess checks if a user has access to a tenant
func (s *TenantService) ValidateTenantAccess(userID, tenantID string) (bool, error) {
	var count int
	err := s.DB.Pool.QueryRow(context.Background(),
		"SELECT COUNT(*) FROM users WHERE id = $1 AND tenant_id = $2",
		userID, tenantID).Scan(&count)

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// CheckTenantLimits verifies if tenant is within usage limits
func (s *TenantService) CheckTenantLimits(tenantID string) (map[string]bool, error) {
	tenant, err := s.GetTenantByID(tenantID)
	if err != nil {
		return nil, err
	}

	stats, err := s.GetTenantStats(tenantID)
	if err != nil {
		return nil, err
	}

	limits := make(map[string]bool)

	// Check user limit
	userCount := stats["user_count"].(int)
	limits["users_within_limit"] = userCount < tenant.MaxUsers

	// Check site limit
	siteCount := stats["site_count"].(int)
	limits["sites_within_limit"] = siteCount < tenant.MaxSites

	return limits, nil
}

// DeleteTenant soft deletes a tenant by setting status to 'deleted'
func (s *TenantService) DeleteTenant(tenantID string) error {
	query := `UPDATE tenants SET status = 'deleted', updated_at = $1 WHERE id = $2`
	_, err := s.DB.Pool.Exec(context.Background(), query, time.Now(), tenantID)
	return err
}

// ListTenants retrieves all tenants with pagination
func (s *TenantService) ListTenants(limit, offset int, status string) ([]Tenant, error) {
	var query string
	var args []interface{}

	if status != "" {
		query = `
			SELECT id, name, slug, domain, status, plan_type, max_users, max_sites, 
			       settings, created_at, updated_at
			FROM tenants 
			WHERE status = $1
			ORDER BY created_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{status, limit, offset}
	} else {
		query = `
			SELECT id, name, slug, domain, status, plan_type, max_users, max_sites, 
			       settings, created_at, updated_at
			FROM tenants 
			ORDER BY created_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{limit, offset}
	}

	rows, err := s.DB.Pool.Query(context.Background(), query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tenants []Tenant
	for rows.Next() {
		var tenant Tenant
		err := rows.Scan(
			&tenant.ID, &tenant.Name, &tenant.Slug, &tenant.Domain, &tenant.Status,
			&tenant.PlanType, &tenant.MaxUsers, &tenant.MaxSites, &tenant.Settings,
			&tenant.CreatedAt, &tenant.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		tenants = append(tenants, tenant)
	}

	return tenants, nil
}

// getPlanLimits returns the limits for a given plan type
func (s *TenantService) getPlanLimits(planType string) (maxUsers, maxSites int) {
	switch planType {
	case "free":
		return 1, 1
	case "business":
		return 5, 3
	case "sole_tradies":
		return 3, 5
	case "growth":
		return 10, 10
	case "pro":
		return 50, 25
	case "enterprise":
		return -1, -1 // Unlimited
	default:
		return 1, 1 // Default to free plan limits
	}
}
