<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Addon Metadata Editor - Velocity Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-lightning-charge"></i>
                VelocityWave
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/builder/addon-builder">
                    <i class="bi bi-arrow-left"></i> Back to Builder
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle"></i>
                            Metadata Editor
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="nav flex-column nav-pills">
                            <a class="nav-link active" href="#basic-info" data-bs-toggle="pill">
                                <i class="bi bi-file-text"></i> Basic Information
                            </a>
                            <a class="nav-link" href="#categorization" data-bs-toggle="pill">
                                <i class="bi bi-tags"></i> Categorization
                            </a>
                            <a class="nav-link" href="#publishing" data-bs-toggle="pill">
                                <i class="bi bi-globe"></i> Publishing
                            </a>
                            <a class="nav-link" href="#properties" data-bs-toggle="pill">
                                <i class="bi bi-gear"></i> Properties
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Preview Card -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-eye"></i>
                            Live Preview
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="metadata-preview" class="small">
                            <div class="addon-preview-card">
                                <div class="d-flex align-items-center mb-2">
                                    <img id="preview-icon" src="/static/images/default-addon-icon.svg" 
                                         alt="Addon Icon" class="me-2" style="width: 32px; height: 32px;">
                                    <div>
                                        <div id="preview-name" class="fw-bold">Untitled Addon</div>
                                        <div id="preview-version" class="text-muted small">v1.0.0</div>
                                    </div>
                                </div>
                                <p id="preview-description" class="small text-muted">
                                    No description provided
                                </p>
                                <div id="preview-tags" class="mb-2"></div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span id="preview-category" class="badge bg-secondary">utility</span>
                                    <span id="preview-visibility" class="badge bg-warning">Private</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">
                                <i class="bi bi-pencil-square"></i>
                                Edit Addon Metadata
                            </h4>
                            <div>
                                <button class="btn btn-outline-secondary" onclick="resetForm()">
                                    <i class="bi bi-arrow-clockwise"></i> Reset
                                </button>
                                <button class="btn btn-success" onclick="saveMetadata()">
                                    <i class="bi bi-check-lg"></i> Save Changes
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="metadata-form">
                            <div class="tab-content">
                                <!-- Basic Information Tab -->
                                <div class="tab-pane fade show active" id="basic-info">
                                    <h5 class="mb-3">Basic Information</h5>
                                    
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="mb-3">
                                                <label for="addon-name" class="form-label">
                                                    Addon Name <span class="text-danger">*</span>
                                                </label>
                                                <input type="text" class="form-control" id="addon-name" 
                                                       placeholder="Enter addon name" required
                                                       onchange="updatePreview()">
                                                <div class="form-text">
                                                    Choose a descriptive name for your addon (3-50 characters)
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="addon-version" class="form-label">
                                                    Version <span class="text-danger">*</span>
                                                </label>
                                                <input type="text" class="form-control" id="addon-version" 
                                                       value="1.0.0" placeholder="1.0.0" required
                                                       onchange="updatePreview()">
                                                <div class="form-text">
                                                    Semantic versioning (x.y.z)
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="addon-description" class="form-label">
                                            Description <span class="text-danger">*</span>
                                        </label>
                                        <textarea class="form-control" id="addon-description" rows="4" 
                                                  placeholder="Describe what your addon does..." required
                                                  onchange="updatePreview()"></textarea>
                                        <div class="form-text">
                                            Provide a clear description of your addon's functionality (10-500 characters)
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="addon-author" class="form-label">
                                            Author <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="addon-author" 
                                               placeholder="Your name or organization" required
                                               onchange="updatePreview()">
                                        <div class="form-text">
                                            Your name or organization name
                                        </div>
                                    </div>
                                </div>

                                <!-- Categorization Tab -->
                                <div class="tab-pane fade" id="categorization">
                                    <h5 class="mb-3">Categorization</h5>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="addon-category" class="form-label">
                                                    Category <span class="text-danger">*</span>
                                                </label>
                                                <select class="form-select" id="addon-category" required
                                                        onchange="updatePreview()">
                                                    <option value="utility">Utility</option>
                                                    <option value="productivity">Productivity</option>
                                                    <option value="communication">Communication</option>
                                                    <option value="analytics">Analytics</option>
                                                    <option value="marketing">Marketing</option>
                                                    <option value="ecommerce">E-commerce</option>
                                                    <option value="social">Social</option>
                                                    <option value="content">Content</option>
                                                    <option value="design">Design</option>
                                                    <option value="development">Development</option>
                                                    <option value="integration">Integration</option>
                                                    <option value="automation">Automation</option>
                                                    <option value="security">Security</option>
                                                    <option value="finance">Finance</option>
                                                    <option value="education">Education</option>
                                                    <option value="entertainment">Entertainment</option>
                                                    <option value="other">Other</option>
                                                </select>
                                                <div class="form-text">
                                                    Select the most appropriate category
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="addon-icon" class="form-label">
                                                    Icon URL
                                                </label>
                                                <input type="url" class="form-control" id="addon-icon" 
                                                       placeholder="https://example.com/icon.svg"
                                                       onchange="updatePreview()">
                                                <div class="form-text">
                                                    URL to an icon image (SVG preferred)
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="addon-tags" class="form-label">
                                            Tags
                                        </label>
                                        <input type="text" class="form-control" id="addon-tags" 
                                               placeholder="tag1, tag2, tag3"
                                               onchange="updatePreview()">
                                        <div class="form-text">
                                            Comma-separated tags to help users find your addon (max 10 tags)
                                        </div>
                                        <div id="tags-preview" class="mt-2"></div>
                                    </div>
                                </div>

                                <!-- Publishing Tab -->
                                <div class="tab-pane fade" id="publishing">
                                    <h5 class="mb-3">Publishing Settings</h5>
                                    
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="addon-public"
                                                   onchange="updatePreview()">
                                            <label class="form-check-label" for="addon-public">
                                                Make this addon public
                                            </label>
                                        </div>
                                        <div class="form-text">
                                            Public addons can be discovered and installed by other users
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i>
                                        <strong>Publishing Guidelines:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li>Ensure your addon follows our community guidelines</li>
                                            <li>Test thoroughly before making public</li>
                                            <li>Provide clear documentation and examples</li>
                                            <li>Use appropriate tags and categories</li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Properties Tab -->
                                <div class="tab-pane fade" id="properties">
                                    <h5 class="mb-3">Additional Properties</h5>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="addon-license" class="form-label">
                                                    License
                                                </label>
                                                <select class="form-select" id="addon-license">
                                                    <option value="MIT">MIT</option>
                                                    <option value="Apache-2.0">Apache 2.0</option>
                                                    <option value="GPL-3.0">GPL 3.0</option>
                                                    <option value="BSD-3-Clause">BSD 3-Clause</option>
                                                    <option value="ISC">ISC</option>
                                                    <option value="Proprietary">Proprietary</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="addon-homepage" class="form-label">
                                                    Homepage URL
                                                </label>
                                                <input type="url" class="form-control" id="addon-homepage" 
                                                       placeholder="https://example.com">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="addon-repository" class="form-label">
                                                    Repository URL
                                                </label>
                                                <input type="url" class="form-control" id="addon-repository" 
                                                       placeholder="https://github.com/user/repo">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="addon-bugtracker" class="form-label">
                                                    Bug Tracker URL
                                                </label>
                                                <input type="url" class="form-control" id="addon-bugtracker" 
                                                       placeholder="https://github.com/user/repo/issues">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="addon-keywords" class="form-label">
                                            Keywords
                                        </label>
                                        <input type="text" class="form-control" id="addon-keywords" 
                                               placeholder="keyword1, keyword2, keyword3">
                                        <div class="form-text">
                                            Additional keywords for search optimization
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Metadata management functionality
        let currentMetadata = {
            name: '',
            description: '',
            category: 'utility',
            author: '',
            version: '1.0.0',
            tags: [],
            icon: '',
            isPublic: false,
            properties: {
                license: 'MIT',
                homepage: '',
                repository: '',
                bugTracker: '',
                keywords: ''
            }
        };

        function updatePreview() {
            // Get form values
            const name = document.getElementById('addon-name').value || 'Untitled Addon';
            const description = document.getElementById('addon-description').value || 'No description provided';
            const category = document.getElementById('addon-category').value;
            const version = document.getElementById('addon-version').value || '1.0.0';
            const icon = document.getElementById('addon-icon').value;
            const isPublic = document.getElementById('addon-public').checked;
            const tagsInput = document.getElementById('addon-tags').value;
            
            // Parse tags
            const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag) : [];
            
            // Update preview
            document.getElementById('preview-name').textContent = name;
            document.getElementById('preview-description').textContent = description;
            document.getElementById('preview-version').textContent = `v${version}`;
            document.getElementById('preview-category').textContent = category;
            document.getElementById('preview-visibility').textContent = isPublic ? 'Public' : 'Private';
            document.getElementById('preview-visibility').className = `badge ${isPublic ? 'bg-success' : 'bg-warning'}`;
            
            // Update icon
            const iconElement = document.getElementById('preview-icon');
            if (icon) {
                iconElement.src = icon;
            } else {
                iconElement.src = '/static/images/default-addon-icon.svg';
            }
            
            // Update tags preview
            const tagsPreview = document.getElementById('tags-preview');
            if (tags.length > 0) {
                tagsPreview.innerHTML = tags.map(tag => 
                    `<span class="badge bg-primary me-1">${tag}</span>`
                ).join('');
                
                const previewTags = document.getElementById('preview-tags');
                previewTags.innerHTML = tags.slice(0, 3).map(tag => 
                    `<span class="badge bg-light text-dark me-1 small">${tag}</span>`
                ).join('');
                if (tags.length > 3) {
                    previewTags.innerHTML += `<span class="badge bg-light text-dark small">+${tags.length - 3}</span>`;
                }
            } else {
                tagsPreview.innerHTML = '';
                document.getElementById('preview-tags').innerHTML = '';
            }
            
            // Update current metadata
            currentMetadata = {
                name,
                description,
                category,
                author: document.getElementById('addon-author').value,
                version,
                tags,
                icon,
                isPublic,
                properties: {
                    license: document.getElementById('addon-license').value,
                    homepage: document.getElementById('addon-homepage').value,
                    repository: document.getElementById('addon-repository').value,
                    bugTracker: document.getElementById('addon-bugtracker').value,
                    keywords: document.getElementById('addon-keywords').value
                }
            };
        }

        function saveMetadata() {
            // Validate required fields
            if (!currentMetadata.name || !currentMetadata.description || !currentMetadata.author) {
                alert('Please fill in all required fields (Name, Description, Author)');
                return;
            }
            
            // Save metadata via GraphQL
            fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    query: `
                        mutation UpdateAddonMetadata($id: ID!, $metadata: AddonMetadataInput!) {
                            updateAddonMetadata(id: $id, metadata: $metadata) {
                                id
                                name
                                metadata {
                                    name
                                    description
                                    category
                                    version
                                    tags
                                }
                            }
                        }
                    `,
                    variables: {
                        id: getAddonId(), // Get from URL or storage
                        metadata: currentMetadata
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.errors) {
                    console.error('GraphQL errors:', data.errors);
                    alert('Error saving metadata: ' + data.errors[0].message);
                } else {
                    alert('Metadata saved successfully!');
                    // Optionally redirect back to builder
                    // window.location.href = '/builder/addon-builder';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error saving metadata');
            });
        }

        function resetForm() {
            if (confirm('Are you sure you want to reset all changes?')) {
                document.getElementById('metadata-form').reset();
                updatePreview();
            }
        }

        function getAddonId() {
            // Get addon ID from URL parameters or local storage
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('id') || localStorage.getItem('currentAddonId') || 'new';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updatePreview();
            
            // Load existing metadata if editing
            const addonId = getAddonId();
            if (addonId && addonId !== 'new') {
                loadExistingMetadata(addonId);
            }
        });

        function loadExistingMetadata(addonId) {
            // Load existing metadata from server
            fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    query: `
                        query LoadAddonConfig($id: ID!) {
                            loadAddonConfig(id: $id) {
                                id
                                name
                                metadata {
                                    name
                                    description
                                    category
                                    author
                                    version
                                    tags
                                    icon
                                    isPublic
                                    properties
                                }
                            }
                        }
                    `,
                    variables: { id: addonId }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.data?.loadAddonConfig?.metadata) {
                    const metadata = data.data.loadAddonConfig.metadata;
                    
                    // Populate form fields
                    document.getElementById('addon-name').value = metadata.name || '';
                    document.getElementById('addon-description').value = metadata.description || '';
                    document.getElementById('addon-category').value = metadata.category || 'utility';
                    document.getElementById('addon-author').value = metadata.author || '';
                    document.getElementById('addon-version').value = metadata.version || '1.0.0';
                    document.getElementById('addon-tags').value = metadata.tags ? metadata.tags.join(', ') : '';
                    document.getElementById('addon-icon').value = metadata.icon || '';
                    document.getElementById('addon-public').checked = metadata.isPublic || false;
                    
                    if (metadata.properties) {
                        document.getElementById('addon-license').value = metadata.properties.license || 'MIT';
                        document.getElementById('addon-homepage').value = metadata.properties.homepage || '';
                        document.getElementById('addon-repository').value = metadata.properties.repository || '';
                        document.getElementById('addon-bugtracker').value = metadata.properties.bugTracker || '';
                        document.getElementById('addon-keywords').value = metadata.properties.keywords || '';
                    }
                    
                    updatePreview();
                }
            })
            .catch(error => {
                console.error('Error loading metadata:', error);
            });
        }
    </script>
</body>
</html>