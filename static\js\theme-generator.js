(function() {
  'use strict';

  const themeVariables = {
    '--bs-primary': '#0d6efd',
    '--bs-secondary': '#6c757d',
    '--bs-success': '#198754',
    '--bs-danger': '#dc3545',
    '--bs-warning': '#ffc107',
    '--bs-info': '#0dcaf0'
  };

  function initializeColorPickers() {
    document.querySelectorAll('.spectrum').forEach(input => {
      const cssVar = input.dataset.var;
      $(input).spectrum({
        color: themeVariables[cssVar],
        change: function(color) {
          updateCssVariable(cssVar, color.toHexString());
        }
      });
    });
  }

  function updateCssVariable(name, value) {
    document.documentElement.style.setProperty(name, value);
    themeVariables[name] = value;
  }

  function exportTheme() {
    const dataStr = JSON.stringify(themeVariables);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
    
    const link = document.createElement('a');
    link.download = 'theme.json';
    link.href = dataUri;
    link.click();
  }

  function importTheme() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';
    
    input.onchange = function(e) {
      const file = e.target.files[0];
      const reader = new FileReader();
      
      reader.onload = function() {
        const importedTheme = JSON.parse(reader.result);
        Object.entries(importedTheme).forEach(([key, value]) => {
          updateCssVariable(key, value);
          $(`[data-var="${key}"]`).spectrum('set', value);
        });
      };
      
      reader.readAsText(file);
    };
    
    input.click();
  }

  window.exportTheme = exportTheme;
  window.importTheme = importTheme;
  
  document.addEventListener('DOMContentLoaded', initializeColorPickers);
})();