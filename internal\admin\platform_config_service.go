package admin

import (
	"context"
	"fmt"
	"goVwPlatformAPI/internal/database"
	"time"
)

// PlatformConfigService handles platform configuration management
type PlatformConfigService struct {
	db *database.DB
}

// NewPlatformConfigService creates a new platform configuration service
func NewPlatformConfigService(db *database.DB) *PlatformConfigService {
	return &PlatformConfigService{db: db}
}

// PlatformConfig represents platform-wide configuration
type PlatformConfig struct {
	ID          string                 `json:"id"`
	Category    string                 `json:"category"`
	Key         string                 `json:"key"`
	Value       interface{}            `json:"value"`
	Type        string                 `json:"type"` // string, number, boolean, json
	Description string                 `json:"description"`
	IsPublic    bool                   `json:"is_public"`
	IsEditable  bool                   `json:"is_editable"`
	Validation  *ConfigValidation      `json:"validation"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	UpdatedBy   string                 `json:"updated_by"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ConfigValidation represents validation rules for configuration values
type ConfigValidation struct {
	Required      bool          `json:"required"`
	MinValue      *float64      `json:"min_value,omitempty"`
	MaxValue      *float64      `json:"max_value,omitempty"`
	MinLength     *int          `json:"min_length,omitempty"`
	MaxLength     *int          `json:"max_length,omitempty"`
	Pattern       string        `json:"pattern,omitempty"`
	AllowedValues []interface{} `json:"allowed_values,omitempty"`
}

// ConfigCategory represents different configuration categories
type ConfigCategory struct {
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Description string `json:"description"`
	Icon        string `json:"icon"`
	Order       int    `json:"order"`
}

// GetConfigCategories returns all configuration categories
func (s *PlatformConfigService) GetConfigCategories() []ConfigCategory {
	return []ConfigCategory{
		{
			Name:        "general",
			DisplayName: "General Settings",
			Description: "Basic platform configuration",
			Icon:        "settings",
			Order:       1,
		},
		{
			Name:        "security",
			DisplayName: "Security Settings",
			Description: "Security and authentication configuration",
			Icon:        "shield",
			Order:       2,
		},
		{
			Name:        "email",
			DisplayName: "Email Settings",
			Description: "Email service configuration",
			Icon:        "mail",
			Order:       3,
		},
		{
			Name:        "storage",
			DisplayName: "Storage Settings",
			Description: "File storage and CDN configuration",
			Icon:        "database",
			Order:       4,
		},
		{
			Name:        "api",
			DisplayName: "API Settings",
			Description: "API rate limits and configuration",
			Icon:        "code",
			Order:       5,
		},
		{
			Name:        "features",
			DisplayName: "Feature Flags",
			Description: "Enable/disable platform features",
			Icon:        "flag",
			Order:       6,
		},
	}
}

// GetPlatformConfigs retrieves platform configurations by category
func (s *PlatformConfigService) GetPlatformConfigs(ctx context.Context, category string) ([]PlatformConfig, error) {
	// In a real implementation, this would query a configuration table
	// For now, return default configurations

	configs := s.getDefaultConfigs()

	if category != "" {
		var filtered []PlatformConfig
		for _, config := range configs {
			if config.Category == category {
				filtered = append(filtered, config)
			}
		}
		return filtered, nil
	}

	return configs, nil
}

// getDefaultConfigs returns default platform configurations
func (s *PlatformConfigService) getDefaultConfigs() []PlatformConfig {
	now := time.Now()

	return []PlatformConfig{
		{
			ID:          "general_site_name",
			Category:    "general",
			Key:         "site_name",
			Value:       "VelocityWave Platform",
			Type:        "string",
			Description: "The name of the platform displayed to users",
			IsPublic:    true,
			IsEditable:  true,
			Validation: &ConfigValidation{
				Required:  true,
				MinLength: &[]int{3}[0],
				MaxLength: &[]int{100}[0],
			},
			CreatedAt: now,
			UpdatedAt: now,
			Metadata:  make(map[string]interface{}),
		},
		{
			ID:          "general_maintenance_mode",
			Category:    "general",
			Key:         "maintenance_mode",
			Value:       false,
			Type:        "boolean",
			Description: "Enable maintenance mode to restrict access",
			IsPublic:    false,
			IsEditable:  true,
			Validation: &ConfigValidation{
				Required: true,
			},
			CreatedAt: now,
			UpdatedAt: now,
			Metadata:  make(map[string]interface{}),
		},
		{
			ID:          "security_session_timeout",
			Category:    "security",
			Key:         "session_timeout",
			Value:       3600,
			Type:        "number",
			Description: "Session timeout in seconds",
			IsPublic:    false,
			IsEditable:  true,
			Validation: &ConfigValidation{
				Required: true,
				MinValue: &[]float64{300}[0],   // 5 minutes
				MaxValue: &[]float64{86400}[0], // 24 hours
			},
			CreatedAt: now,
			UpdatedAt: now,
			Metadata:  make(map[string]interface{}),
		},
		{
			ID:          "api_rate_limit",
			Category:    "api",
			Key:         "rate_limit_per_minute",
			Value:       1000,
			Type:        "number",
			Description: "API requests per minute per user",
			IsPublic:    false,
			IsEditable:  true,
			Validation: &ConfigValidation{
				Required: true,
				MinValue: &[]float64{10}[0],
				MaxValue: &[]float64{10000}[0],
			},
			CreatedAt: now,
			UpdatedAt: now,
			Metadata:  make(map[string]interface{}),
		},
		{
			ID:          "features_addon_marketplace",
			Category:    "features",
			Key:         "addon_marketplace_enabled",
			Value:       true,
			Type:        "boolean",
			Description: "Enable the addon marketplace feature",
			IsPublic:    true,
			IsEditable:  true,
			Validation: &ConfigValidation{
				Required: true,
			},
			CreatedAt: now,
			UpdatedAt: now,
			Metadata:  make(map[string]interface{}),
		},
	}
}

// UpdatePlatformConfig updates a platform configuration
func (s *PlatformConfigService) UpdatePlatformConfig(ctx context.Context, configID string, value interface{}, adminID string) error {
	// In a real implementation, this would update the database
	// For now, just validate and log the change

	configs := s.getDefaultConfigs()
	var config *PlatformConfig

	for _, c := range configs {
		if c.ID == configID {
			config = &c
			break
		}
	}

	if config == nil {
		return fmt.Errorf("configuration not found: %s", configID)
	}

	if !config.IsEditable {
		return fmt.Errorf("configuration is not editable: %s", configID)
	}

	// Validate the new value
	if err := s.validateConfigValue(config, value); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Log the configuration change
	fmt.Printf("CONFIG_UPDATE: admin=%s config=%s old_value=%v new_value=%v\n",
		adminID, configID, config.Value, value)

	return nil
}

// validateConfigValue validates a configuration value against its validation rules
func (s *PlatformConfigService) validateConfigValue(config *PlatformConfig, value interface{}) error {
	if config.Validation == nil {
		return nil
	}

	validation := config.Validation

	// Check required
	if validation.Required && value == nil {
		return fmt.Errorf("value is required")
	}

	if value == nil {
		return nil
	}

	// Type-specific validation
	switch config.Type {
	case "string":
		str, ok := value.(string)
		if !ok {
			return fmt.Errorf("expected string value")
		}

		if validation.MinLength != nil && len(str) < *validation.MinLength {
			return fmt.Errorf("string too short (min: %d)", *validation.MinLength)
		}

		if validation.MaxLength != nil && len(str) > *validation.MaxLength {
			return fmt.Errorf("string too long (max: %d)", *validation.MaxLength)
		}

	case "number":
		var num float64
		switch v := value.(type) {
		case int:
			num = float64(v)
		case float64:
			num = v
		default:
			return fmt.Errorf("expected numeric value")
		}

		if validation.MinValue != nil && num < *validation.MinValue {
			return fmt.Errorf("value too small (min: %f)", *validation.MinValue)
		}

		if validation.MaxValue != nil && num > *validation.MaxValue {
			return fmt.Errorf("value too large (max: %f)", *validation.MaxValue)
		}

	case "boolean":
		if _, ok := value.(bool); !ok {
			return fmt.Errorf("expected boolean value")
		}
	}

	// Check allowed values
	if len(validation.AllowedValues) > 0 {
		found := false
		for _, allowed := range validation.AllowedValues {
			if value == allowed {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("value not in allowed list")
		}
	}

	return nil
}
