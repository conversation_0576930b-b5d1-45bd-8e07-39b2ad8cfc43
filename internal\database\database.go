package database

import (
	"context"
	"fmt"
	"log"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"goVwPlatformAPI/graph/model"
)

// APICredentials represents stored credentials for external APIs
type APICredentials struct {
	Name       string
	Endpoint   string
	AuthHeader string
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

type DB struct {
	Pool *pgxpool.Pool
}

func NewConnection() (*DB, error) {
	host := os.Getenv("PGHOST")
	port := os.Getenv("PGPORT")
	dbname := os.Getenv("PGDATABASE")
	user := os.Getenv("PGUSER")
	password := os.Getenv("PGPASSWORD")

	// Debug logging for connection parameters
	log.Printf("DB Connection Parameters:")
	log.Printf("Host: %s", host)
	log.Printf("Port: %s", port)
	log.Printf("Database: %s", dbname)
	log.Printf("User: %s", user)
	log.Printf("Password set: %t", len(password) > 0)

	if host == "" || port == "" || dbname == "" || user == "" || password == "" {
		return nil, fmt.Errorf("missing required database environment variables")
	}

	// Create connection URI with properly encoded credentials
	encodedUser := url.QueryEscape(user)
	encodedPassword := url.QueryEscape(password)
	connURI := fmt.Sprintf("postgresql://%s:%s@%s:%s/%s?sslmode=disable",
		encodedUser,
		encodedPassword,
		host,
		port,
		dbname)

	fmt.Printf("Using connection URI: %s\n", strings.Replace(connURI, encodedPassword, "***", 1))

	config, err := pgxpool.ParseConfig(connURI)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	pool, err := pgxpool.NewWithConfig(context.Background(), config)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	return &DB{Pool: pool}, nil
}

func (db *DB) Close() {
	db.Pool.Close()
}

func (db *DB) StoreAPICredentials(ctx context.Context, name string, credentials *APICredentials, userID string) error {
	_, err := db.Pool.Exec(ctx,
		`INSERT INTO api_credentials (name, endpoint, auth_header, created_by)
		 VALUES ($1, $2, $3, $4)
		 ON CONFLICT (name) DO UPDATE
		 SET endpoint = $2, auth_header = $3, updated_at = NOW()`,
		name, credentials.Endpoint, credentials.AuthHeader, userID)
	return err
}

func (db *DB) GetAPICredentials(ctx context.Context, name string, userID string) (*APICredentials, error) {
	var creds APICredentials
	err := db.Pool.QueryRow(ctx,
		`SELECT name, endpoint, auth_header, created_at, updated_at
		 FROM api_credentials
		 WHERE name = $1 AND created_by = $2`,
		name, userID).Scan(
		&creds.Name, &creds.Endpoint, &creds.AuthHeader, &creds.CreatedAt, &creds.UpdatedAt)
	if err != nil {
		return nil, err
	}
	return &creds, nil
}

func (db *DB) CreateSnippet(ctx context.Context, input model.CreatePredefinedSnippetInput, userID string) (*model.PredefinedSnippet, error) {
	var snippet model.PredefinedSnippet
	err := db.Pool.QueryRow(ctx,
		`INSERT INTO predefined_snippets (name, category, html_content, css_content, js_content, version)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id, name, category, html_content, css_content, js_content, version, created_at, updated_at`,
		input.Name, input.Category, input.HTMLContent, input.CSSContent, input.JsContent, "1.0",
	).Scan(
		&snippet.ID, &snippet.Name, &snippet.Category,
		&snippet.HTMLContent, &snippet.CSSContent, &snippet.JsContent, &snippet.Version, &snippet.CreatedAt, &snippet.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create snippet: %w", err)
	}
	return &snippet, nil
}

func (db *DB) UpdateSnippet(ctx context.Context, input model.UpdatePredefinedSnippetInput, userID string) (*model.PredefinedSnippet, error) {
	var snippet model.PredefinedSnippet
	err := db.Pool.QueryRow(ctx,
		`UPDATE predefined_snippets
		SET name = COALESCE($1, name),
			category = COALESCE($2, category),
			html_content = COALESCE($3, html_content),
			css_content = COALESCE($4, css_content),
			js_content = COALESCE($5, js_content),
			version = COALESCE($6, version),
			updated_at = NOW()
		WHERE id = $7
		RETURNING id, name, category, html_content, css_content, js_content, version, created_at, updated_at`,
		input.Name, input.Category, input.HTMLContent,
		input.CSSContent, input.JsContent, "1.0", input.ID,
	).Scan(
		&snippet.ID, &snippet.Name, &snippet.Category,
		&snippet.HTMLContent, &snippet.CSSContent, &snippet.JsContent, &snippet.Version, &snippet.CreatedAt, &snippet.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to update snippet: %w", err)
	}
	return &snippet, nil
}

func (db *DB) DeleteSnippet(ctx context.Context, id string, userID string) error {
	_, err := db.Pool.Exec(ctx,
		`DELETE FROM predefined_snippets WHERE id = $1`,
		id,
	)
	return err
}

func (db *DB) SaveAddonConfig(ctx context.Context, input model.AddonConfigInput, userID string) (*model.AddonConfig, error) {
	var config model.AddonConfig
	err := db.Pool.QueryRow(ctx,
		`INSERT INTO addon_configs (name, config, created_by)
		VALUES ($1, $2, $3)
		ON CONFLICT (name, created_by) DO UPDATE
		SET config = EXCLUDED.config,
		    updated_at = NOW()
		RETURNING id, name, config, created_at, updated_at`,
		input.Name, input.Config, userID,
	).Scan(
		&config.ID, &config.Name, &config.Config, &config.CreatedAt, &config.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to save addon config: %w", err)
	}
	return &config, nil
}

func (db *DB) GetAddonsInReview(ctx context.Context) ([]*model.AddonConfig, error) {
	rows, err := db.Pool.Query(ctx, `
		SELECT id, name, config, state, created_at, updated_at
		FROM addon_configs
		WHERE state = 'REVIEW'
		ORDER BY created_at DESC`)
	if err != nil {
		return nil, fmt.Errorf("failed to query addons in review: %w", err)
	}
	defer rows.Close()

	var configs []*model.AddonConfig
	for rows.Next() {
		var config model.AddonConfig
		err := rows.Scan(
			&config.ID, &config.Name, &config.Config,
			&config.State, &config.CreatedAt, &config.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan addon config: %w", err)
		}
		configs = append(configs, &config)
	}

	return configs, nil
}

func (db *DB) UpdateAddonState(ctx context.Context, id string, state model.AddonState, userID string) (*model.AddonConfig, error) {
	var config model.AddonConfig
	err := db.Pool.QueryRow(ctx, `
		UPDATE addon_configs
		SET state = $1
		WHERE id = $2
		RETURNING id, name, config, state, created_at, updated_at`,
		state, id,
	).Scan(
		&config.ID, &config.Name, &config.Config,
		&config.State, &config.CreatedAt, &config.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to update addon state: %w", err)
	}
	return &config, nil
}

func (db *DB) LoadAddonConfig(ctx context.Context, id string, userID string) (*model.AddonConfig, error) {
	var config model.AddonConfig
	err := db.Pool.QueryRow(ctx,
		`SELECT id, name, config, created_at, updated_at
		FROM addon_configs
		WHERE id = $1 AND created_by = $2`,
		id, userID,
	).Scan(
		&config.ID, &config.Name, &config.Config, &config.CreatedAt, &config.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon config: %w", err)
	}
	return &config, nil
}

func (db *DB) GetPredefinedSnippets(ctx context.Context, category *string) ([]*model.PredefinedSnippet, error) {
	var snippets []*model.PredefinedSnippet
	var rows pgx.Rows
	var err error

	if category != nil {
		rows, err = db.Pool.Query(ctx,
			`SELECT id, name, category, description, html_content, css_content, js_content, version, created_at
			FROM predefined_snippets WHERE category = $1`, *category)
	} else {
		rows, err = db.Pool.Query(ctx,
			`SELECT id, name, category, description, html_content, css_content, js_content, version, created_at
			FROM predefined_snippets`)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to query snippets: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var snippet model.PredefinedSnippet
		err := rows.Scan(
			&snippet.ID, &snippet.Name, &snippet.Category,
			&snippet.HTMLContent, &snippet.CSSContent, &snippet.JsContent, &snippet.Version, &snippet.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan snippet: %w", err)
		}
		snippets = append(snippets, &snippet)
	}

	return snippets, nil
}

func (db *DB) MovePage(ctx context.Context, id string, parentID *string, position int, userID string) (*model.Page, error) {
	tx, err := db.Pool.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// First update the moving page's position and parent
	var page model.Page
	var scannedParentID *string
	err = tx.QueryRow(ctx,
		`UPDATE pages
		SET parent_id = $1, position = $2
		WHERE id = $3 AND created_by = $4
		RETURNING id, title, slug, parent_id, position, is_hidden, created_at, updated_at`,
		parentID, position, id, userID,
	).Scan(
		&page.ID, &page.Title, &page.Slug, &scannedParentID, &page.Position, &page.IsHidden, &page.CreatedAt, &page.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to move page: %w", err)
	}

	// Then update positions of siblings
	_, err = tx.Exec(ctx,
		`UPDATE pages
		SET position = position + 1
		WHERE (parent_id IS NULL AND $1 IS NULL OR parent_id = $1)
			AND position >= $2
			AND id != $3`,
		parentID, position, id,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to update sibling positions: %w", err)
	}

	err = tx.Commit(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return &page, nil
}

func (db *DB) GetNavigation(ctx context.Context, userID string) ([]*model.Page, error) {
	rows, err := db.Pool.Query(ctx,
		`WITH RECURSIVE page_tree AS (
			SELECT id, title, slug, parent_id, position, is_hidden, created_at, updated_at
			FROM pages
			WHERE parent_id IS NULL AND created_by = $1
			UNION ALL
			SELECT p.id, p.title, p.slug, p.parent_id, p.position, p.is_hidden, p.created_at, p.updated_at
			FROM pages p
			INNER JOIN page_tree pt ON pt.id = p.parent_id
		)
		SELECT id, title, slug, parent_id, position, is_hidden, created_at, updated_at
		FROM page_tree
		ORDER BY position`,
		userID,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to query navigation: %w", err)
	}
	defer rows.Close()

	var pages []*model.Page
	pageMap := make(map[string]*model.Page)

	for rows.Next() {
		var page model.Page
		var parentID *string
		err := rows.Scan(
			&page.ID, &page.Title, &page.Slug, &parentID, &page.Position,
			&page.IsHidden, &page.CreatedAt, &page.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan page: %w", err)
		}

		pageMap[page.ID] = &page
		if parentID == nil {
			pages = append(pages, &page)
		} else {
			if parent, ok := pageMap[*parentID]; ok {
				parent.Children = append(parent.Children, &page)
			}
		}
	}

	return pages, nil
}

// User management functions
func (db *DB) CreateUser(ctx context.Context, email, passwordHash string) (*model.User, error) {
	var user model.User
	err := db.Pool.QueryRow(ctx,
		`INSERT INTO users (email, password_hash, tenant_id)
		VALUES ($1, $2, (SELECT id FROM tenants WHERE name = 'Default Tenant'))
		RETURNING id, email, created_at`,
		email, passwordHash,
	).Scan(&user.ID, &user.Email, &user.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}
	return &user, nil
}

func (db *DB) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	var passwordHash string
	var tenantID string
	err := db.Pool.QueryRow(ctx,
		`SELECT id, email, password_hash, tenant_id, created_at FROM users WHERE email = $1`,
		email,
	).Scan(&user.ID, &user.Email, &passwordHash, &tenantID, &user.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	// Note: Password hash and tenant ID are retrieved but not stored in model
	// They are used internally for authentication and tenant operations
	_ = passwordHash // Used for authentication
	_ = tenantID     // Used for tenant operations
	return &user, nil
}

func (db *DB) GetUserByID(ctx context.Context, id string) (*model.User, error) {
	var user model.User
	var tenantID string
	err := db.Pool.QueryRow(ctx,
		`SELECT id, email, tenant_id, created_at FROM users WHERE id = $1`,
		id,
	).Scan(&user.ID, &user.Email, &tenantID, &user.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	// Note: TenantID is retrieved for internal operations
	_ = tenantID
	return &user, nil
}

// Vehicle management functions
func (db *DB) CreateVehicle(ctx context.Context, input model.VehicleInput, userID string) (*model.Vehicle, error) {
	var vehicle model.Vehicle
	var owner model.User

	err := db.Pool.QueryRow(ctx,
		`INSERT INTO vehicles (vin, make, model, year, owner_id, tenant_id)
		VALUES ($1, $2, $3, $4, $5, (SELECT tenant_id FROM users WHERE id = $5))
		RETURNING vin, make, model, year, created_at, updated_at`,
		input.Vin, input.Make, input.Model, input.Year, userID,
	).Scan(&vehicle.Vin, &vehicle.Make, &vehicle.Model, &vehicle.Year, &vehicle.CreatedAt, &vehicle.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create vehicle: %w", err)
	}

	// Get owner info
	owner.ID = userID
	vehicle.Owner = &owner

	return &vehicle, nil
}

func (db *DB) GetVehicles(ctx context.Context, userID string) ([]*model.Vehicle, error) {
	rows, err := db.Pool.Query(ctx,
		`SELECT v.vin, v.make, v.model, v.year, v.created_at, v.updated_at,
		        u.id, u.email, u.created_at
		FROM vehicles v
		JOIN users u ON v.owner_id = u.id
		WHERE v.owner_id = $1`,
		userID,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to query vehicles: %w", err)
	}
	defer rows.Close()

	var vehicles []*model.Vehicle
	for rows.Next() {
		var vehicle model.Vehicle
		var owner model.User
		err := rows.Scan(
			&vehicle.Vin, &vehicle.Make, &vehicle.Model, &vehicle.Year, &vehicle.CreatedAt, &vehicle.UpdatedAt,
			&owner.ID, &owner.Email, &owner.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan vehicle: %w", err)
		}
		vehicle.Owner = &owner
		vehicles = append(vehicles, &vehicle)
	}

	return vehicles, nil
}

func (db *DB) GetVehicleByVIN(ctx context.Context, vin string, userID string) (*model.Vehicle, error) {
	var vehicle model.Vehicle
	var owner model.User
	err := db.Pool.QueryRow(ctx,
		`SELECT v.vin, v.make, v.model, v.year, v.created_at, v.updated_at,
		        u.id, u.email, u.created_at
		FROM vehicles v
		JOIN users u ON v.owner_id = u.id
		WHERE v.vin = $1 AND v.owner_id = $2`,
		vin, userID,
	).Scan(
		&vehicle.Vin, &vehicle.Make, &vehicle.Model, &vehicle.Year, &vehicle.CreatedAt, &vehicle.UpdatedAt,
		&owner.ID, &owner.Email, &owner.CreatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get vehicle: %w", err)
	}
	vehicle.Owner = &owner
	return &vehicle, nil
}

// Web Addon management functions
func (db *DB) CreateWebAddon(ctx context.Context, input model.WebAddonInput, userID string) (*model.WebAddon, error) {
	var addon model.WebAddon
	err := db.Pool.QueryRow(ctx,
		`INSERT INTO web_addons (name, description, rete_config, version, dependencies)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id, name, description, rete_config, version, dependencies, status, created_at, updated_at`,
		input.Name, input.Description, input.ReteConfig, input.Version, input.Dependencies,
	).Scan(
		&addon.ID, &addon.Name, &addon.Description, &addon.ReteConfig, &addon.Version, &addon.Dependencies, &addon.Status, &addon.CreatedAt, &addon.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create web addon: %w", err)
	}
	return &addon, nil
}

func (db *DB) UpdateWebAddon(ctx context.Context, id string, input model.WebAddonInput, userID string) (*model.WebAddon, error) {
	var addon model.WebAddon
	err := db.Pool.QueryRow(ctx,
		`UPDATE web_addons
		SET name = $1, description = $2, rete_config = $3, version = $4, dependencies = $5, updated_at = NOW()
		WHERE id = $6
		RETURNING id, name, description, rete_config, version, dependencies, status, created_at, updated_at`,
		input.Name, input.Description, input.ReteConfig, input.Version, input.Dependencies, id,
	).Scan(
		&addon.ID, &addon.Name, &addon.Description, &addon.ReteConfig, &addon.Version, &addon.Dependencies, &addon.Status, &addon.CreatedAt, &addon.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to update web addon: %w", err)
	}
	return &addon, nil
}

func (db *DB) GetWebAddons(ctx context.Context, status *string) ([]*model.WebAddon, error) {
	var addons []*model.WebAddon
	var rows pgx.Rows
	var err error

	if status != nil {
		rows, err = db.Pool.Query(ctx,
			`SELECT id, name, description, rete_config, version, dependencies, status, created_at, updated_at
			FROM web_addons WHERE status = $1`, *status)
	} else {
		rows, err = db.Pool.Query(ctx,
			`SELECT id, name, description, rete_config, version, dependencies, status, created_at, updated_at
			FROM web_addons`)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to query web addons: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var addon model.WebAddon
		err := rows.Scan(
			&addon.ID, &addon.Name, &addon.Description, &addon.ReteConfig, &addon.Version, &addon.Dependencies, &addon.Status, &addon.CreatedAt, &addon.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan web addon: %w", err)
		}
		addons = append(addons, &addon)
	}

	return addons, nil
}

// API Key management functions
func (db *DB) GenerateSiteApiKey(ctx context.Context, siteID string, permissions int, userID string) (*model.APIKey, error) {
	var apiKey model.APIKey
	err := db.Pool.QueryRow(ctx,
		`INSERT INTO api_keys (site_id, permissions, expires_at)
		VALUES ($1, $2, NOW() + INTERVAL '1 year')
		RETURNING key_id, site_id, permissions, expires_at, revoked, created_at, updated_at`,
		siteID, permissions,
	).Scan(
		&apiKey.ID, &apiKey.SiteID, &apiKey.Permissions, &apiKey.ExpiresAt, &apiKey.Revoked, &apiKey.CreatedAt, &apiKey.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate API key: %w", err)
	}

	// Generate the actual key string
	apiKey.Key = fmt.Sprintf("vw_%s_%s", siteID, apiKey.ID)

	return &apiKey, nil
}

func (db *DB) RevokeApiKey(ctx context.Context, keyID string, userID string) error {
	_, err := db.Pool.Exec(ctx,
		`UPDATE api_keys SET revoked = true, updated_at = NOW() WHERE key_id = $1`,
		keyID,
	)
	return err
}

func (db *DB) RotateApiKey(ctx context.Context, keyID string, userID string) (*model.APIKey, error) {
	var apiKey model.APIKey
	err := db.Pool.QueryRow(ctx,
		`UPDATE api_keys 
		SET expires_at = NOW() + INTERVAL '1 year', updated_at = NOW()
		WHERE key_id = $1
		RETURNING key_id, site_id, permissions, expires_at, revoked, created_at, updated_at`,
		keyID,
	).Scan(
		&apiKey.ID, &apiKey.SiteID, &apiKey.Permissions, &apiKey.ExpiresAt, &apiKey.Revoked, &apiKey.CreatedAt, &apiKey.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to rotate API key: %w", err)
	}

	// Generate the actual key string
	apiKey.Key = fmt.Sprintf("vw_%s_%s", apiKey.SiteID, apiKey.ID)

	return &apiKey, nil
}

// Subscription management functions - using temporary structs until GraphQL models are generated
type SubscriptionTier struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	PriceMonthly float64                `json:"price_monthly"`
	Features     map[string]interface{} `json:"features"`
	Limits       map[string]interface{} `json:"limits"`
}

type UserSubscription struct {
	ID                 string            `json:"id"`
	UserID             string            `json:"user_id"`
	TierID             string            `json:"tier_id"`
	Status             string            `json:"status"`
	CurrentPeriodStart time.Time         `json:"current_period_start"`
	CurrentPeriodEnd   time.Time         `json:"current_period_end"`
	Tier               *SubscriptionTier `json:"tier,omitempty"`
}

func (db *DB) GetSubscriptionTiers(ctx context.Context) ([]*SubscriptionTier, error) {
	rows, err := db.Pool.Query(ctx, `
		SELECT id, name, price_monthly, features, limits
		FROM subscription_tiers
		WHERE is_active = true
		ORDER BY price_monthly ASC
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to query subscription tiers: %w", err)
	}
	defer rows.Close()

	var tiers []*SubscriptionTier
	for rows.Next() {
		var tier SubscriptionTier
		err := rows.Scan(&tier.ID, &tier.Name, &tier.PriceMonthly, &tier.Features, &tier.Limits)
		if err != nil {
			return nil, fmt.Errorf("failed to scan subscription tier: %w", err)
		}
		tiers = append(tiers, &tier)
	}

	return tiers, nil
}

func (db *DB) GetUserSubscription(ctx context.Context, userID string) (*UserSubscription, error) {
	var sub UserSubscription
	var tier SubscriptionTier

	err := db.Pool.QueryRow(ctx, `
		SELECT 
			us.id, us.user_id, us.tier_id, us.status, 
			us.current_period_start, us.current_period_end,
			st.id, st.name, st.price_monthly, st.features, st.limits
		FROM user_subscriptions us
		JOIN subscription_tiers st ON us.tier_id = st.id
		WHERE us.user_id = $1 AND us.status = 'active'
		ORDER BY us.created_at DESC
		LIMIT 1
	`, userID).Scan(
		&sub.ID, &sub.UserID, &sub.TierID, &sub.Status,
		&sub.CurrentPeriodStart, &sub.CurrentPeriodEnd,
		&tier.ID, &tier.Name, &tier.PriceMonthly, &tier.Features, &tier.Limits,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user subscription: %w", err)
	}

	sub.Tier = &tier
	return &sub, nil
}
