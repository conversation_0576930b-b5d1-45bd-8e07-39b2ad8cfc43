// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
	"time"

	"github.com/99designs/gqlgen/graphql"
)

type ComponentResult interface {
	IsComponentResult()
}

type APIConnection struct {
	ID            string           `json:"id"`
	APIConfigID   string           `json:"apiConfigId"`
	CredentialsID string           `json:"credentialsId"`
	Status        ConnectionStatus `json:"status"`
	LastPing      time.Time        `json:"lastPing"`
	ResponseTime  int              `json:"responseTime"`
	ErrorCount    int              `json:"errorCount"`
	SuccessCount  int              `json:"successCount"`
	TotalRequests int              `json:"totalRequests"`
	CreatedAt     time.Time        `json:"createdAt"`
}

type APICredentials struct {
	ID          string     `json:"id"`
	Name        string     `json:"name"`
	APIConfigID string     `json:"apiConfigId"`
	AuthType    AuthType   `json:"authType"`
	IsActive    bool       `json:"isActive"`
	ExpiresAt   *time.Time `json:"expiresAt,omitempty"`
	LastUsed    *time.Time `json:"lastUsed,omitempty"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   time.Time  `json:"updatedAt"`
}

type APIEndpoint struct {
	ID          string           `json:"id"`
	Path        string           `json:"path"`
	Method      HTTPMethod       `json:"method"`
	Description *string          `json:"description,omitempty"`
	Parameters  []*APIParameter  `json:"parameters"`
	Headers     any              `json:"headers,omitempty"`
	RateLimit   *RateLimitConfig `json:"rateLimit,omitempty"`
	Timeout     *int             `json:"timeout,omitempty"`
	Retries     *int             `json:"retries,omitempty"`
}

type APIKey struct {
	ID          string `json:"id"`
	SiteID      string `json:"siteId"`
	Key         string `json:"key"`
	Permissions int    `json:"permissions"`
	ExpiresAt   string `json:"expiresAt"`
	Revoked     bool   `json:"revoked"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
}

type APIParameter struct {
	Name        string  `json:"name"`
	Type        string  `json:"type"`
	Required    bool    `json:"required"`
	Default     *string `json:"default,omitempty"`
	Description *string `json:"description,omitempty"`
	Validation  *string `json:"validation,omitempty"`
}

type APIParameterInput struct {
	Name        string  `json:"name"`
	Type        string  `json:"type"`
	Required    bool    `json:"required"`
	Default     *string `json:"default,omitempty"`
	Description *string `json:"description,omitempty"`
	Validation  *string `json:"validation,omitempty"`
}

type APISecurityConfig struct {
	RequireHTTPS   bool         `json:"requireHttps"`
	AllowedDomains []string     `json:"allowedDomains,omitempty"`
	BlockedDomains []string     `json:"blockedDomains,omitempty"`
	ValidateSsl    bool         `json:"validateSsl"`
	MaxRequestSize int          `json:"maxRequestSize"`
	AllowedMethods []HTTPMethod `json:"allowedMethods"`
}

type APISecurityConfigInput struct {
	RequireHTTPS   bool         `json:"requireHttps"`
	AllowedDomains []string     `json:"allowedDomains,omitempty"`
	BlockedDomains []string     `json:"blockedDomains,omitempty"`
	ValidateSsl    bool         `json:"validateSsl"`
	MaxRequestSize int          `json:"maxRequestSize"`
	AllowedMethods []HTTPMethod `json:"allowedMethods"`
}

type ActivityItem struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Timestamp   time.Time `json:"timestamp"`
	Metadata    any       `json:"metadata,omitempty"`
}

type Addon struct {
	ID          string     `json:"id"`
	Name        string     `json:"name"`
	Description *string    `json:"description,omitempty"`
	Version     string     `json:"version"`
	Status      AddonState `json:"status"`
	Author      string     `json:"author"`
	Category    string     `json:"category"`
	Tags        []string   `json:"tags"`
	IsPublic    bool       `json:"isPublic"`
	Downloads   int        `json:"downloads"`
	Rating      float64    `json:"rating"`
	ReviewCount int        `json:"reviewCount"`
	Config      any        `json:"config,omitempty"`
	State       AddonState `json:"state"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   time.Time  `json:"updatedAt"`
}

type AddonConfig struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Config    string     `json:"config"`
	State     AddonState `json:"state"`
	CreatedAt time.Time  `json:"createdAt"`
	UpdatedAt time.Time  `json:"updatedAt"`
}

type AddonConfigInput struct {
	Name   string `json:"name"`
	Config string `json:"config"`
}

type AddonInstallation struct {
	ID          string    `json:"id"`
	AddonID     string    `json:"addonID"`
	UserID      string    `json:"userID"`
	SiteID      string    `json:"siteID"`
	Status      string    `json:"status"`
	InstalledAt time.Time `json:"installedAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

type AddonMetadata struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Category    string    `json:"category"`
	Author      string    `json:"author"`
	Version     string    `json:"version"`
	Tags        []string  `json:"tags"`
	Icon        string    `json:"icon"`
	IsPublic    bool      `json:"isPublic"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
	Properties  any       `json:"properties,omitempty"`
}

type AddonMetadataInput struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Category    string   `json:"category"`
	Version     string   `json:"version"`
	Tags        []string `json:"tags"`
	Icon        *string  `json:"icon,omitempty"`
	IsPublic    *bool    `json:"isPublic,omitempty"`
	Properties  any      `json:"properties,omitempty"`
}

type AddonPerformance struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Author      string  `json:"author"`
	Downloads   int     `json:"downloads"`
	Rating      float64 `json:"rating"`
	Revenue     float64 `json:"revenue"`
	ActiveUsers int     `json:"activeUsers"`
}

type AddonPerformanceMetrics struct {
	CPUUsage        float64 `json:"cpuUsage"`
	MemoryUsage     int     `json:"memoryUsage"`
	NetworkCalls    int     `json:"networkCalls"`
	DatabaseQueries int     `json:"databaseQueries"`
	ResponseTime    string  `json:"responseTime"`
	Throughput      float64 `json:"throughput"`
}

type AddonPreviewData struct {
	GeneratedAt time.Time `json:"generatedAt"`
	PreviewHTML string    `json:"previewHTML"`
	PreviewCSS  string    `json:"previewCSS"`
	PreviewJs   string    `json:"previewJS"`
	Screenshots []string  `json:"screenshots"`
	DemoData    any       `json:"demoData,omitempty"`
}

type AddonReview struct {
	ID        string    `json:"id"`
	AddonID   string    `json:"addonID"`
	UserID    string    `json:"userID"`
	Rating    float64   `json:"rating"`
	Comment   string    `json:"comment"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type AddonReviewFiltersInput struct {
	State            *string    `json:"state,omitempty"`
	Category         *string    `json:"category,omitempty"`
	AuthorID         *string    `json:"authorID,omitempty"`
	MinSecurityScore *float64   `json:"minSecurityScore,omitempty"`
	MaxSecurityScore *float64   `json:"maxSecurityScore,omitempty"`
	MinQualityScore  *float64   `json:"minQualityScore,omitempty"`
	MaxQualityScore  *float64   `json:"maxQualityScore,omitempty"`
	HasSecurityFlags *bool      `json:"hasSecurityFlags,omitempty"`
	HasQualityFlags  *bool      `json:"hasQualityFlags,omitempty"`
	SubmittedAfter   *time.Time `json:"submittedAfter,omitempty"`
	SubmittedBefore  *time.Time `json:"submittedBefore,omitempty"`
	ReviewedBy       *string    `json:"reviewedBy,omitempty"`
	Priority         *string    `json:"priority,omitempty"`
}

type AddonReviewListResponse struct {
	Addons     []*AdminAddonReview `json:"addons"`
	Total      int                 `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"pageSize"`
	TotalPages int                 `json:"totalPages"`
}

type AddonReviewStats struct {
	PendingReview         int     `json:"pendingReview"`
	InDevelopment         int     `json:"inDevelopment"`
	Approved              int     `json:"approved"`
	Rejected              int     `json:"rejected"`
	ByState               any     `json:"byState"`
	AverageReviewTimeDays float64 `json:"averageReviewTimeDays"`
	HighSecurityRiskCount int     `json:"highSecurityRiskCount"`
	LowQualityCount       int     `json:"lowQualityCount"`
}

type AddonSecurityCheckResults struct {
	Passed            bool     `json:"passed"`
	SandboxViolations []string `json:"sandboxViolations"`
	PermissionIssues  []string `json:"permissionIssues"`
	DataLeaks         []string `json:"dataLeaks"`
	MaliciousPatterns []string `json:"maliciousPatterns"`
}

type AddonTestError struct {
	Code      string    `json:"code"`
	Message   string    `json:"message"`
	NodeID    *string   `json:"nodeId,omitempty"`
	Severity  string    `json:"severity"`
	Timestamp time.Time `json:"timestamp"`
}

type AddonTestLog struct {
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	NodeID    *string   `json:"nodeId,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

type AddonTestOptionsInput struct {
	Timeout             int     `json:"timeout"`
	EnablePerformance   bool    `json:"enablePerformance"`
	EnableSecurity      bool    `json:"enableSecurity"`
	EnableCompatibility bool    `json:"enableCompatibility"`
	TestData            any     `json:"testData,omitempty"`
	MockServices        any     `json:"mockServices,omitempty"`
	Environment         *string `json:"environment,omitempty"`
}

type AddonTestResults struct {
	Success        bool                       `json:"success"`
	ExecutionTime  string                     `json:"executionTime"`
	NodesExecuted  int                        `json:"nodesExecuted"`
	NodesTotal     int                        `json:"nodesTotal"`
	Errors         []*AddonTestError          `json:"errors"`
	Warnings       []*AddonTestWarning        `json:"warnings"`
	Performance    *AddonPerformanceMetrics   `json:"performance,omitempty"`
	SecurityChecks *AddonSecurityCheckResults `json:"securityChecks,omitempty"`
}

type AddonTestSession struct {
	ID          string            `json:"id"`
	AddonID     string            `json:"addonId"`
	Status      AddonTestStatus   `json:"status"`
	StartedAt   time.Time         `json:"startedAt"`
	CompletedAt *time.Time        `json:"completedAt,omitempty"`
	Results     *AddonTestResults `json:"results,omitempty"`
	Logs        []*AddonTestLog   `json:"logs"`
}

type AddonTestWarning struct {
	Code      string    `json:"code"`
	Message   string    `json:"message"`
	NodeID    *string   `json:"nodeId,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

type AdminActivityItem struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	UserEmail   string    `json:"userEmail"`
	Timestamp   time.Time `json:"timestamp"`
	Severity    string    `json:"severity"`
}

type AdminAddonReview struct {
	ID                  string              `json:"id"`
	Name                string              `json:"name"`
	Description         string              `json:"description"`
	Version             string              `json:"version"`
	AuthorID            string              `json:"authorID"`
	AuthorEmail         string              `json:"authorEmail"`
	AuthorName          string              `json:"authorName"`
	State               string              `json:"state"`
	Category            string              `json:"category"`
	Tags                []string            `json:"tags"`
	Config              string              `json:"config"`
	Dependencies        []string            `json:"dependencies"`
	Permissions         []string            `json:"permissions"`
	SecurityScore       float64             `json:"securityScore"`
	QualityScore        float64             `json:"qualityScore"`
	OverallScore        float64             `json:"overallScore"`
	ReviewNotes         string              `json:"reviewNotes"`
	ReviewedBy          string              `json:"reviewedBy"`
	ReviewedAt          *time.Time          `json:"reviewedAt,omitempty"`
	ApprovedBy          string              `json:"approvedBy"`
	ApprovedAt          *time.Time          `json:"approvedAt,omitempty"`
	RejectionReason     string              `json:"rejectionReason"`
	SecurityFlags       []*SecurityFlag     `json:"securityFlags"`
	QualityFlags        []*QualityFlag      `json:"qualityFlags"`
	TestResults         []*TestResult       `json:"testResults"`
	PerformanceMetrics  *PerformanceMetrics `json:"performanceMetrics"`
	CreatedAt           time.Time           `json:"createdAt"`
	UpdatedAt           time.Time           `json:"updatedAt"`
	SubmittedAt         *time.Time          `json:"submittedAt,omitempty"`
	PreviewURL          string              `json:"previewURL"`
	DownloadCount       int                 `json:"downloadCount"`
	Rating              float64             `json:"rating"`
	ReviewCount         int                 `json:"reviewCount"`
	RevenueGenerated    float64             `json:"revenueGenerated"`
	InstallationCount   int                 `json:"installationCount"`
	UninstallationCount int                 `json:"uninstallationCount"`
	ErrorReports        int                 `json:"errorReports"`
	SupportTickets      int                 `json:"supportTickets"`
}

type AdminDashboardKPIs struct {
	TotalUsers            int                  `json:"totalUsers"`
	ActiveUsers           int                  `json:"activeUsers"`
	NewUsersToday         int                  `json:"newUsersToday"`
	NewUsersThisWeek      int                  `json:"newUsersThisWeek"`
	NewUsersThisMonth     int                  `json:"newUsersThisMonth"`
	TotalExperts          int                  `json:"totalExperts"`
	ActiveExperts         int                  `json:"activeExperts"`
	PendingExpertApps     int                  `json:"pendingExpertApps"`
	ExpertApprovalRate    float64              `json:"expertApprovalRate"`
	TotalSites            int                  `json:"totalSites"`
	PublishedSites        int                  `json:"publishedSites"`
	TotalPages            int                  `json:"totalPages"`
	TotalAddons           int                  `json:"totalAddons"`
	AddonsInReview        int                  `json:"addonsInReview"`
	TotalRevenue          float64              `json:"totalRevenue"`
	MonthlyRevenue        float64              `json:"monthlyRevenue"`
	AverageRevenuePerUser float64              `json:"averageRevenuePerUser"`
	SystemUptime          string               `json:"systemUptime"`
	DatabaseSize          string               `json:"databaseSize"`
	StorageUsed           string               `json:"storageUsed"`
	APICallsToday         int                  `json:"apiCallsToday"`
	ErrorRate             float64              `json:"errorRate"`
	RecentActivity        []*AdminActivityItem `json:"recentActivity"`
	TopPerformingAddons   []*AddonPerformance  `json:"topPerformingAddons"`
	TopActiveUsers        []*UserActivity      `json:"topActiveUsers"`
	LastUpdated           time.Time            `json:"lastUpdated"`
}

type AdminExpertApplication struct {
	ID                string     `json:"id"`
	UserID            string     `json:"userID"`
	UserEmail         string     `json:"userEmail"`
	FirstName         string     `json:"firstName"`
	LastName          string     `json:"lastName"`
	Company           string     `json:"company"`
	Status            string     `json:"status"`
	ExpertiseAreas    []string   `json:"expertiseAreas"`
	YearsOfExperience int        `json:"yearsOfExperience"`
	HourlyRate        float64    `json:"hourlyRate"`
	Currency          string     `json:"currency"`
	Bio               string     `json:"bio"`
	CoverLetter       string     `json:"coverLetter"`
	ReviewNotes       string     `json:"reviewNotes"`
	ReviewedBy        string     `json:"reviewedBy"`
	ReviewedAt        *time.Time `json:"reviewedAt,omitempty"`
	ApprovedBy        string     `json:"approvedBy"`
	ApprovedAt        *time.Time `json:"approvedAt,omitempty"`
	RejectionReason   string     `json:"rejectionReason"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         time.Time  `json:"updatedAt"`
	Score             float64    `json:"score"`
	Flags             []string   `json:"flags"`
}

type AdminExpertProfile struct {
	ID                 string    `json:"id"`
	UserID             string    `json:"userID"`
	UserEmail          string    `json:"userEmail"`
	FirstName          string    `json:"firstName"`
	LastName           string    `json:"lastName"`
	Company            string    `json:"company"`
	Status             string    `json:"status"`
	Tier               string    `json:"tier"`
	ExpertiseAreas     []string  `json:"expertiseAreas"`
	YearsOfExperience  int       `json:"yearsOfExperience"`
	HourlyRate         float64   `json:"hourlyRate"`
	Currency           string    `json:"currency"`
	Rating             float64   `json:"rating"`
	ReviewCount        int       `json:"reviewCount"`
	CompletedProjects  int       `json:"completedProjects"`
	ActiveProjects     int       `json:"activeProjects"`
	TotalEarnings      float64   `json:"totalEarnings"`
	VerificationStatus string    `json:"verificationStatus"`
	CreatedAt          time.Time `json:"createdAt"`
	UpdatedAt          time.Time `json:"updatedAt"`
	Flags              []string  `json:"flags"`
	Notes              string    `json:"notes"`
}

type AdminUser struct {
	ID               string     `json:"id"`
	Email            string     `json:"email"`
	Role             string     `json:"role"`
	Status           string     `json:"status"`
	FirstName        string     `json:"firstName"`
	LastName         string     `json:"lastName"`
	Company          string     `json:"company"`
	TenantID         string     `json:"tenantID"`
	TenantName       string     `json:"tenantName"`
	EmailVerified    bool       `json:"emailVerified"`
	LastLoginAt      *time.Time `json:"lastLoginAt,omitempty"`
	CreatedAt        time.Time  `json:"createdAt"`
	UpdatedAt        time.Time  `json:"updatedAt"`
	SubscriptionTier string     `json:"subscriptionTier"`
	SitesCount       int        `json:"sitesCount"`
	PagesCount       int        `json:"pagesCount"`
	AddonsCount      int        `json:"addonsCount"`
	StorageUsed      int        `json:"storageUsed"`
	APICallsUsed     int        `json:"apiCallsUsed"`
	TotalRevenue     float64    `json:"totalRevenue"`
	Flags            []string   `json:"flags"`
	Notes            string     `json:"notes"`
}

type AnalyticsSettings struct {
	GoogleAnalytics  *string  `json:"googleAnalytics,omitempty"`
	GoogleTagManager *string  `json:"googleTagManager,omitempty"`
	FacebookPixel    *string  `json:"facebookPixel,omitempty"`
	Hotjar           *string  `json:"hotjar,omitempty"`
	CustomScripts    []string `json:"customScripts,omitempty"`
}

type AnalyticsSettingsInput struct {
	GoogleAnalytics  *string  `json:"googleAnalytics,omitempty"`
	GoogleTagManager *string  `json:"googleTagManager,omitempty"`
	FacebookPixel    *string  `json:"facebookPixel,omitempty"`
	Hotjar           *string  `json:"hotjar,omitempty"`
	CustomScripts    []string `json:"customScripts,omitempty"`
}

type AppearanceSettings struct {
	Theme      string `json:"theme"`
	Language   string `json:"language"`
	Timezone   string `json:"timezone"`
	DateFormat string `json:"dateFormat"`
	Currency   string `json:"currency"`
}

type AppearanceSettingsInput struct {
	Theme      *string `json:"theme,omitempty"`
	Language   *string `json:"language,omitempty"`
	Timezone   *string `json:"timezone,omitempty"`
	DateFormat *string `json:"dateFormat,omitempty"`
	Currency   *string `json:"currency,omitempty"`
}

type AuthPayload struct {
	Token string `json:"token"`
	User  *User  `json:"user"`
}

type BrowserStat struct {
	Browser    string  `json:"browser"`
	Version    string  `json:"version"`
	Count      int     `json:"count"`
	Percentage float64 `json:"percentage"`
}

type BusinessCompliance struct {
	ID                     string                           `json:"id"`
	TenantID               string                           `json:"tenantId"`
	UserID                 string                           `json:"userId"`
	FrameworkID            string                           `json:"frameworkId"`
	Framework              *ComplianceFramework             `json:"framework"`
	ComplianceStatus       string                           `json:"complianceStatus"`
	AssignedTo             *User                            `json:"assignedTo,omitempty"`
	PriorityLevel          string                           `json:"priorityLevel"`
	TargetCompletionDate   *time.Time                       `json:"targetCompletionDate,omitempty"`
	LastReviewDate         *time.Time                       `json:"lastReviewDate,omitempty"`
	NextReviewDate         *time.Time                       `json:"nextReviewDate,omitempty"`
	ComplianceAchievedDate *time.Time                       `json:"complianceAchievedDate,omitempty"`
	Notes                  *string                          `json:"notes,omitempty"`
	EvidenceDocuments      any                              `json:"evidenceDocuments"`
	ActionItems            any                              `json:"actionItems"`
	RequirementTracking    []*ComplianceRequirementTracking `json:"requirementTracking"`
	Alerts                 []*ComplianceAlert               `json:"alerts"`
	CreatedAt              time.Time                        `json:"createdAt"`
	UpdatedAt              time.Time                        `json:"updatedAt"`
}

type BusinessPlan struct {
	ID          string                 `json:"id"`
	UserID      string                 `json:"userId"`
	Name        string                 `json:"name"`
	Description *string                `json:"description,omitempty"`
	Status      string                 `json:"status"`
	Sections    []*BusinessPlanSection `json:"sections"`
	CreatedAt   time.Time              `json:"createdAt"`
	UpdatedAt   time.Time              `json:"updatedAt"`
}

type BusinessPlanSection struct {
	ID         string `json:"id"`
	Type       string `json:"type"`
	Title      string `json:"title"`
	Content    string `json:"content"`
	Order      int    `json:"order"`
	IsComplete bool   `json:"isComplete"`
}

type Certification struct {
	ID           string     `json:"id"`
	Name         string     `json:"name"`
	Issuer       string     `json:"issuer"`
	IssueDate    time.Time  `json:"issueDate"`
	ExpiryDate   *time.Time `json:"expiryDate,omitempty"`
	CredentialID *string    `json:"credentialId,omitempty"`
	URL          *string    `json:"url,omitempty"`
	Verified     bool       `json:"verified"`
}

type CertificationInput struct {
	Name         string     `json:"name"`
	Issuer       string     `json:"issuer"`
	IssueDate    time.Time  `json:"issueDate"`
	ExpiryDate   *time.Time `json:"expiryDate,omitempty"`
	CredentialID *string    `json:"credentialId,omitempty"`
	URL          *string    `json:"url,omitempty"`
}

type ClientRequirement struct {
	ID                 string               `json:"id"`
	UserID             string               `json:"userId"`
	TenantID           string               `json:"tenantId"`
	ProjectTitle       string               `json:"projectTitle"`
	ProjectDescription string               `json:"projectDescription"`
	ProjectType        string               `json:"projectType"`
	RequiredExpertise  []string             `json:"requiredExpertise"`
	ProjectScope       string               `json:"projectScope"`
	BudgetMin          *float64             `json:"budgetMin,omitempty"`
	BudgetMax          *float64             `json:"budgetMax,omitempty"`
	Currency           string               `json:"currency"`
	TimelineWeeks      *int                 `json:"timelineWeeks,omitempty"`
	StartDate          *time.Time           `json:"startDate,omitempty"`
	Urgency            string               `json:"urgency"`
	Status             string               `json:"status"`
	Matches            []*ExpertClientMatch `json:"matches"`
	CreatedAt          time.Time            `json:"createdAt"`
	UpdatedAt          time.Time            `json:"updatedAt"`
}

type Competitor struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Website     *string  `json:"website,omitempty"`
	Description *string  `json:"description,omitempty"`
	Strengths   []string `json:"strengths"`
	Weaknesses  []string `json:"weaknesses"`
	MarketShare *float64 `json:"marketShare,omitempty"`
	Revenue     *float64 `json:"revenue,omitempty"`
	Employees   *int     `json:"employees,omitempty"`
	Founded     *int     `json:"founded,omitempty"`
}

type CompetitorResearch struct {
	ID             string          `json:"id"`
	UserID         string          `json:"userId"`
	Industry       *string         `json:"industry,omitempty"`
	Location       *string         `json:"location,omitempty"`
	Competitors    []*Competitor   `json:"competitors"`
	MarketAnalysis *MarketAnalysis `json:"marketAnalysis"`
	CreatedAt      time.Time       `json:"createdAt"`
	UpdatedAt      time.Time       `json:"updatedAt"`
}

type CompilationMetadata struct {
	CompiledAt     time.Time `json:"compiledAt"`
	Version        string    `json:"version"`
	PageCount      int       `json:"pageCount"`
	ComponentCount int       `json:"componentCount"`
	ThemeConfig    any       `json:"themeConfig"`
}

type CompilationResult struct {
	HTML     string               `json:"html"`
	CSS      string               `json:"css"`
	Js       string               `json:"js"`
	Assets   any                  `json:"assets"`
	Metadata *CompilationMetadata `json:"metadata"`
	Success  bool                 `json:"success"`
	Message  *string              `json:"message,omitempty"`
}

type CompleteOnboardingStepInput struct {
	StepName string `json:"stepName"`
	Data     any    `json:"data,omitempty"`
}

type ComplianceAlert struct {
	ID                    string     `json:"id"`
	TenantID              string     `json:"tenantId"`
	BusinessComplianceID  *string    `json:"businessComplianceId,omitempty"`
	RequirementTrackingID *string    `json:"requirementTrackingId,omitempty"`
	AlertType             string     `json:"alertType"`
	AlertTitle            string     `json:"alertTitle"`
	AlertMessage          string     `json:"alertMessage"`
	Severity              string     `json:"severity"`
	AlertStatus           string     `json:"alertStatus"`
	AcknowledgedBy        *User      `json:"acknowledgedBy,omitempty"`
	AcknowledgedAt        *time.Time `json:"acknowledgedAt,omitempty"`
	ResolvedAt            *time.Time `json:"resolvedAt,omitempty"`
	TriggerDate           time.Time  `json:"triggerDate"`
	ExpiryDate            *time.Time `json:"expiryDate,omitempty"`
	CreatedAt             time.Time  `json:"createdAt"`
}

type ComplianceCheck struct {
	ID              string                   `json:"id"`
	UserID          string                   `json:"userId"`
	Regulation      string                   `json:"regulation"`
	Industry        *string                  `json:"industry,omitempty"`
	Location        *string                  `json:"location,omitempty"`
	Requirements    []*ComplianceRequirement `json:"requirements"`
	ComplianceScore float64                  `json:"complianceScore"`
	Recommendations []string                 `json:"recommendations"`
	CreatedAt       time.Time                `json:"createdAt"`
	UpdatedAt       time.Time                `json:"updatedAt"`
}

type ComplianceFramework struct {
	ID                    string                   `json:"id"`
	FrameworkName         string                   `json:"frameworkName"`
	FrameworkCode         string                   `json:"frameworkCode"`
	Description           *string                  `json:"description,omitempty"`
	RegulatoryBody        *string                  `json:"regulatoryBody,omitempty"`
	IndustrySector        *string                  `json:"industrySector,omitempty"`
	FrameworkType         string                   `json:"frameworkType"`
	IsMandatory           bool                     `json:"isMandatory"`
	EffectiveDate         *time.Time               `json:"effectiveDate,omitempty"`
	ReviewFrequencyMonths int                      `json:"reviewFrequencyMonths"`
	Requirements          []*ComplianceRequirement `json:"requirements"`
	CreatedAt             time.Time                `json:"createdAt"`
	UpdatedAt             time.Time                `json:"updatedAt"`
}

type ComplianceRequirement struct {
	ID                     string    `json:"id"`
	FrameworkID            string    `json:"frameworkId"`
	RequirementCode        string    `json:"requirementCode"`
	RequirementTitle       string    `json:"requirementTitle"`
	RequirementDescription string    `json:"requirementDescription"`
	ComplianceLevel        string    `json:"complianceLevel"`
	Category               *string   `json:"category,omitempty"`
	Subcategory            *string   `json:"subcategory,omitempty"`
	EvidenceRequired       []string  `json:"evidenceRequired"`
	ReviewFrequencyMonths  int       `json:"reviewFrequencyMonths"`
	PenaltyDescription     *string   `json:"penaltyDescription,omitempty"`
	CreatedAt              time.Time `json:"createdAt"`
	UpdatedAt              time.Time `json:"updatedAt"`
}

type ComplianceRequirementTracking struct {
	ID                   string                 `json:"id"`
	BusinessComplianceID string                 `json:"businessComplianceId"`
	RequirementID        string                 `json:"requirementId"`
	Requirement          *ComplianceRequirement `json:"requirement"`
	RequirementStatus    string                 `json:"requirementStatus"`
	CompletionPercentage int                    `json:"completionPercentage"`
	StartedDate          *time.Time             `json:"startedDate,omitempty"`
	CompletedDate        *time.Time             `json:"completedDate,omitempty"`
	DueDate              *time.Time             `json:"dueDate,omitempty"`
	EvidenceProvided     any                    `json:"evidenceProvided"`
	ImplementationNotes  *string                `json:"implementationNotes,omitempty"`
	AssessorNotes        *string                `json:"assessorNotes,omitempty"`
	RiskLevel            string                 `json:"riskLevel"`
	ImpactDescription    *string                `json:"impactDescription,omitempty"`
	CreatedAt            time.Time              `json:"createdAt"`
	UpdatedAt            time.Time              `json:"updatedAt"`
}

type Conversation struct {
	ID           string    `json:"id"`
	Participants []*User   `json:"participants"`
	LastMessage  *Message  `json:"lastMessage,omitempty"`
	UnreadCount  int       `json:"unreadCount"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

type CreateAPICredentialsInput struct {
	Name        string     `json:"name"`
	APIConfigID string     `json:"apiConfigId"`
	AuthType    AuthType   `json:"authType"`
	Credentials any        `json:"credentials"`
	ExpiresAt   *time.Time `json:"expiresAt,omitempty"`
}

type CreateAPIEndpointInput struct {
	Path        string                `json:"path"`
	Method      HTTPMethod            `json:"method"`
	Description *string               `json:"description,omitempty"`
	Parameters  []*APIParameterInput  `json:"parameters"`
	Headers     any                   `json:"headers,omitempty"`
	RateLimit   *RateLimitConfigInput `json:"rateLimit,omitempty"`
	Timeout     *int                  `json:"timeout,omitempty"`
	Retries     *int                  `json:"retries,omitempty"`
}

type CreateAddonInput struct {
	Name        string   `json:"name"`
	Description *string  `json:"description,omitempty"`
	Version     string   `json:"version"`
	Category    string   `json:"category"`
	Tags        []string `json:"tags"`
	IsPublic    *bool    `json:"isPublic,omitempty"`
	Config      any      `json:"config,omitempty"`
}

type CreateAddonReviewInput struct {
	AddonID string  `json:"addonID"`
	Rating  float64 `json:"rating"`
	Comment string  `json:"comment"`
}

type CreateBusinessComplianceInput struct {
	FrameworkID          string     `json:"frameworkId"`
	AssignedTo           *string    `json:"assignedTo,omitempty"`
	PriorityLevel        *string    `json:"priorityLevel,omitempty"`
	TargetCompletionDate *time.Time `json:"targetCompletionDate,omitempty"`
	Notes                *string    `json:"notes,omitempty"`
}

type CreateBusinessPlanInput struct {
	Name        string  `json:"name"`
	Description *string `json:"description,omitempty"`
	Template    *string `json:"template,omitempty"`
}

type CreateClientRequirementInput struct {
	ProjectTitle       string     `json:"projectTitle"`
	ProjectDescription string     `json:"projectDescription"`
	ProjectType        string     `json:"projectType"`
	RequiredExpertise  []string   `json:"requiredExpertise"`
	ProjectScope       string     `json:"projectScope"`
	BudgetMin          *float64   `json:"budgetMin,omitempty"`
	BudgetMax          *float64   `json:"budgetMax,omitempty"`
	Currency           *string    `json:"currency,omitempty"`
	TimelineWeeks      *int       `json:"timelineWeeks,omitempty"`
	StartDate          *time.Time `json:"startDate,omitempty"`
	Urgency            *string    `json:"urgency,omitempty"`
}

type CreateComplianceCheckInput struct {
	Regulation string  `json:"regulation"`
	Industry   *string `json:"industry,omitempty"`
	Location   *string `json:"location,omitempty"`
}

type CreateExpertApplicationInput struct {
	ProfessionalTitle     string   `json:"professionalTitle"`
	YearsExperience       int      `json:"yearsExperience"`
	ExpertiseAreas        []string `json:"expertiseAreas"`
	HourlyRate            *float64 `json:"hourlyRate,omitempty"`
	Currency              *string  `json:"currency,omitempty"`
	Qualifications        any      `json:"qualifications,omitempty"`
	MotivationStatement   *string  `json:"motivationStatement,omitempty"`
	PortfolioURL          *string  `json:"portfolioUrl,omitempty"`
	LinkedinURL           *string  `json:"linkedinUrl,omitempty"`
	WebsiteURL            *string  `json:"websiteUrl,omitempty"`
	AvailabilityHours     *int     `json:"availabilityHours,omitempty"`
	PreferredProjectTypes []string `json:"preferredProjectTypes,omitempty"`
}

type CreateExpertReviewInput struct {
	ExpertID       string   `json:"expertId"`
	ProjectID      *string  `json:"projectId,omitempty"`
	Rating         float64  `json:"rating"`
	Title          string   `json:"title"`
	Comment        string   `json:"comment"`
	Pros           []string `json:"pros,omitempty"`
	Cons           []string `json:"cons,omitempty"`
	WouldRecommend bool     `json:"wouldRecommend"`
}

type CreateExternalAPIInput struct {
	Name               string                    `json:"name"`
	Description        *string                   `json:"description,omitempty"`
	BaseURL            string                    `json:"baseUrl"`
	Version            *string                   `json:"version,omitempty"`
	AuthenticationType AuthType                  `json:"authenticationType"`
	Endpoints          []*CreateAPIEndpointInput `json:"endpoints"`
	Headers            any                       `json:"headers,omitempty"`
	RateLimit          *RateLimitConfigInput     `json:"rateLimit,omitempty"`
	Security           *APISecurityConfigInput   `json:"security,omitempty"`
	Documentation      *string                   `json:"documentation,omitempty"`
	CacheTTL           *int                      `json:"cacheTTL,omitempty"`
	Metadata           any                       `json:"metadata,omitempty"`
}

type CreateFinancialPlanInput struct {
	Name        string  `json:"name"`
	Description *string `json:"description,omitempty"`
}

type CreatePageInput struct {
	Title           string  `json:"title"`
	Slug            *string `json:"slug,omitempty"`
	Content         *string `json:"content,omitempty"`
	ParentID        *string `json:"parentID,omitempty"`
	IsHidden        *bool   `json:"isHidden,omitempty"`
	MetaTitle       *string `json:"metaTitle,omitempty"`
	MetaDescription *string `json:"metaDescription,omitempty"`
	MetaKeywords    *string `json:"metaKeywords,omitempty"`
	CustomCSS       *string `json:"customCSS,omitempty"`
	CustomJs        *string `json:"customJS,omitempty"`
	FeaturedImage   *string `json:"featuredImage,omitempty"`
	Template        *string `json:"template,omitempty"`
	Settings        any     `json:"settings,omitempty"`
}

type CreatePredefinedSnippetInput struct {
	Name        string  `json:"name"`
	Category    string  `json:"category"`
	Description *string `json:"description,omitempty"`
	HTMLContent string  `json:"htmlContent"`
	CSSContent  *string `json:"cssContent,omitempty"`
	JsContent   *string `json:"jsContent,omitempty"`
	Version     string  `json:"version"`
}

type CreateProjectEngagementInput struct {
	ClientRequirementID    string     `json:"clientRequirementId"`
	ExpertProfileID        string     `json:"expertProfileId"`
	ProjectTitle           string     `json:"projectTitle"`
	ProjectDescription     string     `json:"projectDescription"`
	AgreedScope            *string    `json:"agreedScope,omitempty"`
	AgreedRate             float64    `json:"agreedRate"`
	RateType               *string    `json:"rateType,omitempty"`
	EstimatedHours         *int       `json:"estimatedHours,omitempty"`
	TotalBudget            *float64   `json:"totalBudget,omitempty"`
	StartDate              time.Time  `json:"startDate"`
	EndDate                *time.Time `json:"endDate,omitempty"`
	EstimatedDurationWeeks *int       `json:"estimatedDurationWeeks,omitempty"`
}

type CreateProjectInput struct {
	Title          string     `json:"title"`
	Description    string     `json:"description"`
	Category       string     `json:"category"`
	Budget         *float64   `json:"budget,omitempty"`
	HourlyRate     *float64   `json:"hourlyRate,omitempty"`
	EstimatedHours *int       `json:"estimatedHours,omitempty"`
	ExpertID       *string    `json:"expertId,omitempty"`
	StartDate      *time.Time `json:"startDate,omitempty"`
	EndDate        *time.Time `json:"endDate,omitempty"`
}

type CreateReviewInput struct {
	ExpertProfileID     string   `json:"expertProfileId"`
	ProjectEngagementID *string  `json:"projectEngagementId,omitempty"`
	OverallRating       float64  `json:"overallRating"`
	CommunicationRating *float64 `json:"communicationRating,omitempty"`
	ExpertiseRating     *float64 `json:"expertiseRating,omitempty"`
	TimelinessRating    *float64 `json:"timelinessRating,omitempty"`
	ValueRating         *float64 `json:"valueRating,omitempty"`
	ReviewTitle         *string  `json:"reviewTitle,omitempty"`
	ReviewText          string   `json:"reviewText"`
	Pros                *string  `json:"pros,omitempty"`
	Cons                *string  `json:"cons,omitempty"`
	WouldRecommend      *bool    `json:"wouldRecommend,omitempty"`
	WouldHireAgain      *bool    `json:"wouldHireAgain,omitempty"`
}

type CreateSiteInput struct {
	Name         string  `json:"name"`
	Description  *string `json:"description,omitempty"`
	CustomDomain *string `json:"customDomain,omitempty"`
	ThemeConfig  any     `json:"themeConfig,omitempty"`
	SeoConfig    any     `json:"seoConfig,omitempty"`
}

type CreateSubscriptionTierInput struct {
	Name         string  `json:"name"`
	PriceMonthly float64 `json:"priceMonthly"`
	Features     any     `json:"features"`
	Limits       any     `json:"limits"`
	IsActive     *bool   `json:"isActive,omitempty"`
}

type CreateSupportTicketInput struct {
	Subject     string            `json:"subject"`
	Description string            `json:"description"`
	Priority    string            `json:"priority"`
	Category    string            `json:"category"`
	Attachments []*graphql.Upload `json:"attachments,omitempty"`
}

type CreateTenantInput struct {
	Name     string  `json:"name"`
	Slug     string  `json:"slug"`
	Domain   *string `json:"domain,omitempty"`
	PlanType string  `json:"planType"`
}

type DashboardStats struct {
	TotalSites       int             `json:"totalSites"`
	TotalPages       int             `json:"totalPages"`
	TotalAddons      int             `json:"totalAddons"`
	CurrentUsage     any             `json:"currentUsage"`
	SubscriptionTier string          `json:"subscriptionTier"`
	StorageUsed      float64         `json:"storageUsed"`
	BandwidthUsed    float64         `json:"bandwidthUsed"`
	APICallsUsed     int             `json:"apiCallsUsed"`
	RecentActivity   []*ActivityItem `json:"recentActivity"`
}

type Deliverable struct {
	ID          string     `json:"id"`
	ProjectID   string     `json:"projectId"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Status      string     `json:"status"`
	DueDate     *time.Time `json:"dueDate,omitempty"`
	CompletedAt *time.Time `json:"completedAt,omitempty"`
	Attachments []string   `json:"attachments,omitempty"`
	Feedback    *string    `json:"feedback,omitempty"`
}

type DeviceStats struct {
	Desktop          int            `json:"desktop"`
	Mobile           int            `json:"mobile"`
	Tablet           int            `json:"tablet"`
	Browsers         []*BrowserStat `json:"browsers"`
	OperatingSystems []*OSStat      `json:"operatingSystems"`
}

type Education struct {
	ID           string  `json:"id"`
	Institution  string  `json:"institution"`
	Degree       string  `json:"degree"`
	FieldOfStudy string  `json:"fieldOfStudy"`
	StartYear    int     `json:"startYear"`
	EndYear      *int    `json:"endYear,omitempty"`
	Gpa          *string `json:"gpa,omitempty"`
	Description  *string `json:"description,omitempty"`
}

type EducationInput struct {
	Institution  string  `json:"institution"`
	Degree       string  `json:"degree"`
	FieldOfStudy string  `json:"fieldOfStudy"`
	StartYear    int     `json:"startYear"`
	EndYear      *int    `json:"endYear,omitempty"`
	Gpa          *string `json:"gpa,omitempty"`
	Description  *string `json:"description,omitempty"`
}

type ExpenseProjection struct {
	ID       string  `json:"id"`
	Year     int     `json:"year"`
	Month    int     `json:"month"`
	Amount   float64 `json:"amount"`
	Category string  `json:"category"`
}

type ExpertApplication struct {
	ID                  string     `json:"id"`
	UserID              string     `json:"userId"`
	TenantID            string     `json:"tenantId"`
	ApplicationStatus   string     `json:"applicationStatus"`
	SubmittedAt         time.Time  `json:"submittedAt"`
	ReviewedAt          *time.Time `json:"reviewedAt,omitempty"`
	ProfessionalTitle   string     `json:"professionalTitle"`
	YearsExperience     int        `json:"yearsExperience"`
	ExpertiseAreas      []string   `json:"expertiseAreas"`
	HourlyRate          *float64   `json:"hourlyRate,omitempty"`
	Currency            string     `json:"currency"`
	Qualifications      any        `json:"qualifications"`
	MotivationStatement *string    `json:"motivationStatement,omitempty"`
	PortfolioURL        *string    `json:"portfolioUrl,omitempty"`
	LinkedinURL         *string    `json:"linkedinUrl,omitempty"`
	WebsiteURL          *string    `json:"websiteUrl,omitempty"`
	AdminNotes          *string    `json:"adminNotes,omitempty"`
	RejectionReason     *string    `json:"rejectionReason,omitempty"`
	CreatedAt           time.Time  `json:"createdAt"`
	UpdatedAt           time.Time  `json:"updatedAt"`
}

type ExpertApplicationFiltersInput struct {
	Status          *string    `json:"status,omitempty"`
	ExpertiseArea   *string    `json:"expertiseArea,omitempty"`
	MinExperience   *int       `json:"minExperience,omitempty"`
	MaxExperience   *int       `json:"maxExperience,omitempty"`
	MinHourlyRate   *float64   `json:"minHourlyRate,omitempty"`
	MaxHourlyRate   *float64   `json:"maxHourlyRate,omitempty"`
	SubmittedAfter  *time.Time `json:"submittedAfter,omitempty"`
	SubmittedBefore *time.Time `json:"submittedBefore,omitempty"`
	ReviewedBy      *string    `json:"reviewedBy,omitempty"`
	HasFlags        []string   `json:"hasFlags,omitempty"`
	MinScore        *float64   `json:"minScore,omitempty"`
}

type ExpertApplicationInput struct {
	ExpertiseAreas        []string              `json:"expertiseAreas"`
	YearsOfExperience     int                   `json:"yearsOfExperience"`
	HourlyRate            float64               `json:"hourlyRate"`
	Currency              string                `json:"currency"`
	Bio                   string                `json:"bio"`
	Portfolio             []*PortfolioItemInput `json:"portfolio,omitempty"`
	Certifications        []*CertificationInput `json:"certifications,omitempty"`
	Education             []*EducationInput     `json:"education,omitempty"`
	Languages             []string              `json:"languages"`
	Availability          *string               `json:"availability,omitempty"`
	PreferredProjectTypes []string              `json:"preferredProjectTypes,omitempty"`
	LinkedinURL           *string               `json:"linkedinUrl,omitempty"`
	WebsiteURL            *string               `json:"websiteUrl,omitempty"`
	ResumeURL             *string               `json:"resumeUrl,omitempty"`
	CoverLetter           string                `json:"coverLetter"`
}

type ExpertApplicationListResponse struct {
	Applications []*AdminExpertApplication `json:"applications"`
	Total        int                       `json:"total"`
	Page         int                       `json:"page"`
	PageSize     int                       `json:"pageSize"`
	TotalPages   int                       `json:"totalPages"`
}

type ExpertClientMatch struct {
	ID                     string         `json:"id"`
	ClientRequirementID    string         `json:"clientRequirementId"`
	ExpertProfileID        string         `json:"expertProfileId"`
	ExpertProfile          *ExpertProfile `json:"expertProfile"`
	CompatibilityScore     float64        `json:"compatibilityScore"`
	ExpertiseMatchScore    float64        `json:"expertiseMatchScore"`
	BudgetMatchScore       float64        `json:"budgetMatchScore"`
	AvailabilityMatchScore float64        `json:"availabilityMatchScore"`
	ExperienceMatchScore   float64        `json:"experienceMatchScore"`
	MatchStatus            string         `json:"matchStatus"`
	SuggestedAt            time.Time      `json:"suggestedAt"`
	ContactedAt            *time.Time     `json:"contactedAt,omitempty"`
	RespondedAt            *time.Time     `json:"respondedAt,omitempty"`
	ClientMessage          *string        `json:"clientMessage,omitempty"`
	ExpertResponse         *string        `json:"expertResponse,omitempty"`
	CreatedAt              time.Time      `json:"createdAt"`
	UpdatedAt              time.Time      `json:"updatedAt"`
}

type ExpertFiltersInput struct {
	ExpertiseAreas []string `json:"expertiseAreas,omitempty"`
	MinHourlyRate  *float64 `json:"minHourlyRate,omitempty"`
	MaxHourlyRate  *float64 `json:"maxHourlyRate,omitempty"`
	MinRating      *float64 `json:"minRating,omitempty"`
	Availability   *string  `json:"availability,omitempty"`
	Languages      []string `json:"languages,omitempty"`
	Tier           *string  `json:"tier,omitempty"`
	Location       *string  `json:"location,omitempty"`
}

type ExpertProfile struct {
	ID                 string                  `json:"id"`
	UserID             string                  `json:"userId"`
	User               *User                   `json:"user,omitempty"`
	TenantID           string                  `json:"tenantId"`
	ProfileStatus      string                  `json:"profileStatus"`
	ProfessionalTitle  string                  `json:"professionalTitle"`
	Bio                *string                 `json:"bio,omitempty"`
	ExpertiseAreas     []string                `json:"expertiseAreas"`
	YearsExperience    int                     `json:"yearsExperience"`
	HourlyRate         float64                 `json:"hourlyRate"`
	Currency           string                  `json:"currency"`
	AvailabilityStatus string                  `json:"availabilityStatus"`
	PortfolioURL       *string                 `json:"portfolioUrl,omitempty"`
	LinkedinURL        *string                 `json:"linkedinUrl,omitempty"`
	WebsiteURL         *string                 `json:"websiteUrl,omitempty"`
	TotalProjects      int                     `json:"totalProjects"`
	CompletedProjects  int                     `json:"completedProjects"`
	AverageRating      float64                 `json:"averageRating"`
	TotalReviews       int                     `json:"totalReviews"`
	AcceptsNewProjects bool                    `json:"acceptsNewProjects"`
	ResponseTimeHours  int                     `json:"responseTimeHours"`
	Specializations    []*ExpertSpecialization `json:"specializations"`
	CreatedAt          time.Time               `json:"createdAt"`
	UpdatedAt          time.Time               `json:"updatedAt"`
}

type ExpertProfileListResponse struct {
	Profiles   []*AdminExpertProfile `json:"profiles"`
	Total      int                   `json:"total"`
	Page       int                   `json:"page"`
	PageSize   int                   `json:"pageSize"`
	TotalPages int                   `json:"totalPages"`
}

type ExpertReview struct {
	ID                  string         `json:"id"`
	ExpertProfileID     string         `json:"expertProfileId"`
	ExpertProfile       *ExpertProfile `json:"expertProfile"`
	ClientUserID        string         `json:"clientUserId"`
	ClientUser          *User          `json:"clientUser"`
	ProjectEngagementID *string        `json:"projectEngagementId,omitempty"`
	OverallRating       float64        `json:"overallRating"`
	CommunicationRating *float64       `json:"communicationRating,omitempty"`
	ExpertiseRating     *float64       `json:"expertiseRating,omitempty"`
	TimelinessRating    *float64       `json:"timelinessRating,omitempty"`
	ValueRating         *float64       `json:"valueRating,omitempty"`
	ReviewTitle         *string        `json:"reviewTitle,omitempty"`
	ReviewText          string         `json:"reviewText"`
	Pros                *string        `json:"pros,omitempty"`
	Cons                *string        `json:"cons,omitempty"`
	WouldRecommend      bool           `json:"wouldRecommend"`
	WouldHireAgain      bool           `json:"wouldHireAgain"`
	ReviewStatus        string         `json:"reviewStatus"`
	IsVerified          bool           `json:"isVerified"`
	ExpertResponse      *string        `json:"expertResponse,omitempty"`
	ExpertRespondedAt   *time.Time     `json:"expertRespondedAt,omitempty"`
	HelpfulVotes        int            `json:"helpfulVotes"`
	TotalVotes          int            `json:"totalVotes"`
	CreatedAt           time.Time      `json:"createdAt"`
	UpdatedAt           time.Time      `json:"updatedAt"`
}

type ExpertSearchFilters struct {
	ExpertiseAreas     []string `json:"expertiseAreas,omitempty"`
	MinRating          *float64 `json:"minRating,omitempty"`
	MaxHourlyRate      *float64 `json:"maxHourlyRate,omitempty"`
	AvailabilityStatus *string  `json:"availabilityStatus,omitempty"`
	YearsExperience    *int     `json:"yearsExperience,omitempty"`
	SearchTerm         *string  `json:"searchTerm,omitempty"`
}

type ExpertSpecialization struct {
	ID                 string    `json:"id"`
	ExpertProfileID    string    `json:"expertProfileId"`
	SpecializationName string    `json:"specializationName"`
	ProficiencyLevel   string    `json:"proficiencyLevel"`
	YearsExperience    int       `json:"yearsExperience"`
	CreatedAt          time.Time `json:"createdAt"`
}

type ExternalAPI struct {
	ID                 string             `json:"id"`
	Name               string             `json:"name"`
	Description        *string            `json:"description,omitempty"`
	BaseURL            string             `json:"baseUrl"`
	Version            *string            `json:"version,omitempty"`
	AuthenticationType AuthType           `json:"authenticationType"`
	Endpoints          []*APIEndpoint     `json:"endpoints"`
	Headers            any                `json:"headers,omitempty"`
	RateLimit          *RateLimitConfig   `json:"rateLimit,omitempty"`
	Security           *APISecurityConfig `json:"security,omitempty"`
	Documentation      *string            `json:"documentation,omitempty"`
	Status             APIStatus          `json:"status"`
	CacheTTL           *int               `json:"cacheTTL,omitempty"`
	CreatedAt          time.Time          `json:"createdAt"`
	UpdatedAt          time.Time          `json:"updatedAt"`
	Metadata           any                `json:"metadata,omitempty"`
}

type FinancialPlan struct {
	ID                  string                `json:"id"`
	UserID              string                `json:"userId"`
	Name                string                `json:"name"`
	RevenueProjections  []*RevenueProjection  `json:"revenueProjections"`
	ExpenseProjections  []*ExpenseProjection  `json:"expenseProjections"`
	FundingRequirements []*FundingRequirement `json:"fundingRequirements"`
	CreatedAt           time.Time             `json:"createdAt"`
	UpdatedAt           time.Time             `json:"updatedAt"`
}

type FundingRequirement struct {
	ID          string  `json:"id"`
	Amount      float64 `json:"amount"`
	Purpose     string  `json:"purpose"`
	Timeline    string  `json:"timeline"`
	Type        string  `json:"type"`
	Description *string `json:"description,omitempty"`
}

type GeneralSettings struct {
	Title       string  `json:"title"`
	Tagline     *string `json:"tagline,omitempty"`
	Description *string `json:"description,omitempty"`
	Language    string  `json:"language"`
	Timezone    string  `json:"timezone"`
	DateFormat  string  `json:"dateFormat"`
	TimeFormat  string  `json:"timeFormat"`
}

type GeneralSettingsInput struct {
	Title       *string `json:"title,omitempty"`
	Tagline     *string `json:"tagline,omitempty"`
	Description *string `json:"description,omitempty"`
	Language    *string `json:"language,omitempty"`
	Timezone    *string `json:"timezone,omitempty"`
	DateFormat  *string `json:"dateFormat,omitempty"`
	TimeFormat  *string `json:"timeFormat,omitempty"`
}

type HeaderInput struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type Invoice struct {
	ID              string     `json:"id"`
	UserID          string     `json:"userID"`
	SubscriptionID  string     `json:"subscriptionID"`
	Amount          float64    `json:"amount"`
	Currency        string     `json:"currency"`
	Status          string     `json:"status"`
	DueDate         time.Time  `json:"dueDate"`
	PaidAt          *time.Time `json:"paidAt,omitempty"`
	StripeInvoiceID *string    `json:"stripeInvoiceId,omitempty"`
	DownloadURL     *string    `json:"downloadUrl,omitempty"`
	CreatedAt       time.Time  `json:"createdAt"`
}

type LocationStat struct {
	Country     string  `json:"country"`
	CountryCode string  `json:"countryCode"`
	City        *string `json:"city,omitempty"`
	Visitors    int     `json:"visitors"`
	Percentage  float64 `json:"percentage"`
}

type MarketAnalysis struct {
	TotalMarketSize float64  `json:"totalMarketSize"`
	GrowthRate      float64  `json:"growthRate"`
	KeyTrends       []string `json:"keyTrends"`
	Opportunities   []string `json:"opportunities"`
	Threats         []string `json:"threats"`
	BarriersToEntry []string `json:"barriersToEntry"`
}

type MediaAsset struct {
	ID               string    `json:"id"`
	Filename         string    `json:"filename"`
	OriginalFilename string    `json:"originalFilename"`
	FilePath         string    `json:"filePath"`
	FileSize         int       `json:"fileSize"`
	MimeType         string    `json:"mimeType"`
	AltText          *string   `json:"altText,omitempty"`
	IsPublic         bool      `json:"isPublic"`
	CreatedAt        time.Time `json:"createdAt"`
}

type Message struct {
	ID             string     `json:"id"`
	ConversationID string     `json:"conversationId"`
	SenderID       string     `json:"senderId"`
	Sender         *User      `json:"sender"`
	Content        string     `json:"content"`
	MessageType    string     `json:"messageType"`
	Attachments    any        `json:"attachments"`
	IsRead         bool       `json:"isRead"`
	ReadAt         *time.Time `json:"readAt,omitempty"`
	SentAt         time.Time  `json:"sentAt"`
	EditedAt       *time.Time `json:"editedAt,omitempty"`
}

type Milestone struct {
	ID          string     `json:"id"`
	ProjectID   string     `json:"projectId"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Amount      float64    `json:"amount"`
	Status      string     `json:"status"`
	DueDate     *time.Time `json:"dueDate,omitempty"`
	CompletedAt *time.Time `json:"completedAt,omitempty"`
	PaidAt      *time.Time `json:"paidAt,omitempty"`
}

type ModerationFiltersInput struct {
	Type           *string    `json:"type,omitempty"`
	Status         *string    `json:"status,omitempty"`
	FlagType       *string    `json:"flagType,omitempty"`
	MinRiskScore   *float64   `json:"minRiskScore,omitempty"`
	MaxRiskScore   *float64   `json:"maxRiskScore,omitempty"`
	AuthorID       *string    `json:"authorID,omitempty"`
	ReportedAfter  *time.Time `json:"reportedAfter,omitempty"`
	ReportedBefore *time.Time `json:"reportedBefore,omitempty"`
	AutoModerated  *bool      `json:"autoModerated,omitempty"`
	HasReports     *bool      `json:"hasReports,omitempty"`
}

type ModerationFlag struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Severity    string    `json:"severity"`
	Description string    `json:"description"`
	ReportedBy  string    `json:"reportedBy"`
	ReportedAt  time.Time `json:"reportedAt"`
	Status      string    `json:"status"`
}

type ModerationItem struct {
	ID            string              `json:"id"`
	Type          string              `json:"type"`
	Title         string              `json:"title"`
	Content       string              `json:"content"`
	AuthorID      string              `json:"authorID"`
	AuthorEmail   string              `json:"authorEmail"`
	Status        string              `json:"status"`
	Flags         []*ModerationFlag   `json:"flags"`
	Reports       []*ModerationReport `json:"reports"`
	ReviewNotes   string              `json:"reviewNotes"`
	ReviewedBy    string              `json:"reviewedBy"`
	ReviewedAt    *time.Time          `json:"reviewedAt,omitempty"`
	CreatedAt     time.Time           `json:"createdAt"`
	UpdatedAt     time.Time           `json:"updatedAt"`
	RiskScore     float64             `json:"riskScore"`
	AutoModerated bool                `json:"autoModerated"`
	URL           string              `json:"url"`
}

type ModerationQueue struct {
	Items      []*ModerationItem `json:"items"`
	Total      int               `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"pageSize"`
	TotalPages int               `json:"totalPages"`
}

type ModerationReport struct {
	ID          string    `json:"id"`
	ReporterID  string    `json:"reporterID"`
	Reason      string    `json:"reason"`
	Description string    `json:"description"`
	Evidence    []string  `json:"evidence"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"createdAt"`
}

type ModerationStats struct {
	TotalPending       int     `json:"totalPending"`
	HighRiskItems      int     `json:"highRiskItems"`
	AutoModeratedToday int     `json:"autoModeratedToday"`
	ByType             any     `json:"byType"`
	ByStatus           any     `json:"byStatus"`
	AverageReviewTime  float64 `json:"averageReviewTime"`
}

type MovePageInput struct {
	PageID      string  `json:"pageID"`
	NewParentID *string `json:"newParentID,omitempty"`
	NewPosition int     `json:"newPosition"`
}

type Mutation struct {
}

type Notification struct {
	ID        string     `json:"id"`
	User      *User      `json:"user"`
	Type      string     `json:"type"`
	Title     string     `json:"title"`
	Message   string     `json:"message"`
	Data      any        `json:"data,omitempty"`
	IsRead    bool       `json:"isRead"`
	ReadAt    *time.Time `json:"readAt,omitempty"`
	ActionURL *string    `json:"actionUrl,omitempty"`
	CreatedAt time.Time  `json:"createdAt"`
	ExpiresAt *time.Time `json:"expiresAt,omitempty"`
}

type NotificationSettings struct {
	Email     bool `json:"email"`
	Push      bool `json:"push"`
	Sms       bool `json:"sms"`
	Marketing bool `json:"marketing"`
	Updates   bool `json:"updates"`
	Security  bool `json:"security"`
}

type NotificationSettingsInput struct {
	Email     *bool `json:"email,omitempty"`
	Push      *bool `json:"push,omitempty"`
	Sms       *bool `json:"sms,omitempty"`
	Marketing *bool `json:"marketing,omitempty"`
	Updates   *bool `json:"updates,omitempty"`
	Security  *bool `json:"security,omitempty"`
}

type OSStat struct {
	Os         string  `json:"os"`
	Version    string  `json:"version"`
	Count      int     `json:"count"`
	Percentage float64 `json:"percentage"`
}

type OnboardingProgress struct {
	UserID         string            `json:"userID"`
	CurrentStep    int               `json:"currentStep"`
	TotalSteps     int               `json:"totalSteps"`
	CompletedSteps int               `json:"completedSteps"`
	IsCompleted    bool              `json:"isCompleted"`
	CompletionRate float64           `json:"completionRate"`
	Steps          []*OnboardingStep `json:"steps"`
}

type OnboardingStep struct {
	ID          string     `json:"id"`
	Name        string     `json:"name"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Required    bool       `json:"required"`
	Order       int        `json:"order"`
	Completed   bool       `json:"completed"`
	CompletedAt *time.Time `json:"completedAt,omitempty"`
	Data        any        `json:"data,omitempty"`
}

type Page struct {
	ID              string     `json:"id"`
	Title           string     `json:"title"`
	Slug            string     `json:"slug"`
	Content         string     `json:"content"`
	ParentID        *string    `json:"parentId,omitempty"`
	Parent          *Page      `json:"parent,omitempty"`
	Children        []*Page    `json:"children"`
	Position        int        `json:"position"`
	IsHidden        bool       `json:"isHidden"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       time.Time  `json:"updatedAt"`
	MetaTitle       string     `json:"metaTitle"`
	MetaDescription string     `json:"metaDescription"`
	MetaKeywords    string     `json:"metaKeywords"`
	CustomCSS       string     `json:"customCSS"`
	CustomJs        string     `json:"customJS"`
	FeaturedImage   string     `json:"featuredImage"`
	Status          string     `json:"status"`
	PublishedAt     *time.Time `json:"publishedAt,omitempty"`
	Template        string     `json:"template"`
	Settings        any        `json:"settings"`
	ContentVersion  int        `json:"contentVersion"`
	IsPublished     bool       `json:"isPublished"`
}

type PageAnalytics struct {
	PageID      string  `json:"pageId"`
	Page        *Page   `json:"page"`
	Views       int     `json:"views"`
	UniqueViews int     `json:"uniqueViews"`
	BounceRate  float64 `json:"bounceRate"`
	AverageTime int     `json:"averageTime"`
}

type PageComponent struct {
	ID        string    `json:"id"`
	PageID    string    `json:"pageId"`
	Type      string    `json:"type"`
	Name      string    `json:"name"`
	Config    any       `json:"config"`
	Position  int       `json:"position"`
	IsVisible bool      `json:"isVisible"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type PageContent struct {
	ID          string    `json:"id"`
	PageID      string    `json:"pageID"`
	Content     any       `json:"content"`
	Version     int       `json:"version"`
	IsPublished bool      `json:"isPublished"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

type PageContentInput struct {
	PageID      string `json:"pageID"`
	Content     any    `json:"content"`
	IsPublished *bool  `json:"isPublished,omitempty"`
}

type PerformanceMetrics struct {
	LoadTime        int     `json:"loadTime"`
	MemoryUsage     int     `json:"memoryUsage"`
	CPUUsage        float64 `json:"cpuUsage"`
	NetworkRequests int     `json:"networkRequests"`
	DomNodes        int     `json:"domNodes"`
	BundleSize      int     `json:"bundleSize"`
	Dependencies    int     `json:"dependencies"`
	Complexity      float64 `json:"complexity"`
}

type PerformanceSettings struct {
	Caching      bool `json:"caching"`
	Compression  bool `json:"compression"`
	Minification bool `json:"minification"`
	LazyLoading  bool `json:"lazyLoading"`
	CdnEnabled   bool `json:"cdnEnabled"`
}

type PerformanceSettingsInput struct {
	Caching      *bool `json:"caching,omitempty"`
	Compression  *bool `json:"compression,omitempty"`
	Minification *bool `json:"minification,omitempty"`
	LazyLoading  *bool `json:"lazyLoading,omitempty"`
	CdnEnabled   *bool `json:"cdnEnabled,omitempty"`
}

type PortfolioItem struct {
	ID           string   `json:"id"`
	Title        string   `json:"title"`
	Description  string   `json:"description"`
	URL          *string  `json:"url,omitempty"`
	ImageURL     *string  `json:"imageUrl,omitempty"`
	Technologies []string `json:"technologies"`
	Year         int      `json:"year"`
	Category     *string  `json:"category,omitempty"`
	Featured     bool     `json:"featured"`
}

type PortfolioItemInput struct {
	Title        string   `json:"title"`
	Description  string   `json:"description"`
	URL          *string  `json:"url,omitempty"`
	ImageURL     *string  `json:"imageUrl,omitempty"`
	Technologies []string `json:"technologies"`
	Year         int      `json:"year"`
	Category     *string  `json:"category,omitempty"`
	Featured     *bool    `json:"featured,omitempty"`
}

type PredefinedSnippet struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Category    string  `json:"category"`
	HTMLContent string  `json:"htmlContent"`
	CSSContent  *string `json:"cssContent,omitempty"`
	JsContent   *string `json:"jsContent,omitempty"`
	Version     string  `json:"version"`
	CreatedAt   string  `json:"createdAt"`
	UpdatedAt   string  `json:"updatedAt"`
}

func (PredefinedSnippet) IsComponentResult() {}

type PredefinedSnippetInput struct {
	Name        string  `json:"name"`
	Category    string  `json:"category"`
	HTMLContent string  `json:"htmlContent"`
	CSSContent  *string `json:"cssContent,omitempty"`
	JsContent   *string `json:"jsContent,omitempty"`
	Version     string  `json:"version"`
}

type PrivacySettings struct {
	ProfileVisibility string `json:"profileVisibility"`
	ShowEmail         bool   `json:"showEmail"`
	ShowLocation      bool   `json:"showLocation"`
	AllowMessages     bool   `json:"allowMessages"`
	AllowConnections  bool   `json:"allowConnections"`
}

type PrivacySettingsInput struct {
	ProfileVisibility *string `json:"profileVisibility,omitempty"`
	ShowEmail         *bool   `json:"showEmail,omitempty"`
	ShowLocation      *bool   `json:"showLocation,omitempty"`
	AllowMessages     *bool   `json:"allowMessages,omitempty"`
	AllowConnections  *bool   `json:"allowConnections,omitempty"`
}

type Project struct {
	ID             string               `json:"id"`
	Title          string               `json:"title"`
	Description    string               `json:"description"`
	Status         string               `json:"status"`
	Client         *User                `json:"client"`
	Expert         *ExpertProfile       `json:"expert,omitempty"`
	Category       string               `json:"category"`
	Budget         *float64             `json:"budget,omitempty"`
	HourlyRate     *float64             `json:"hourlyRate,omitempty"`
	EstimatedHours *int                 `json:"estimatedHours,omitempty"`
	ActualHours    *int                 `json:"actualHours,omitempty"`
	StartDate      *time.Time           `json:"startDate,omitempty"`
	EndDate        *time.Time           `json:"endDate,omitempty"`
	CompletedAt    *time.Time           `json:"completedAt,omitempty"`
	Deliverables   []*Deliverable       `json:"deliverables"`
	Milestones     []*Milestone         `json:"milestones"`
	Messages       []*ProjectMessage    `json:"messages"`
	Attachments    []*ProjectAttachment `json:"attachments"`
	CreatedAt      time.Time            `json:"createdAt"`
	UpdatedAt      time.Time            `json:"updatedAt"`
}

type ProjectAttachment struct {
	ID         string    `json:"id"`
	Filename   string    `json:"filename"`
	FileSize   int       `json:"fileSize"`
	MimeType   string    `json:"mimeType"`
	URL        string    `json:"url"`
	UploadedBy *User     `json:"uploadedBy"`
	UploadedAt time.Time `json:"uploadedAt"`
}

type ProjectCommunication struct {
	ID             string     `json:"id"`
	EngagementID   string     `json:"engagementId"`
	SenderID       string     `json:"senderId"`
	Sender         *User      `json:"sender"`
	MessageType    string     `json:"messageType"`
	Subject        *string    `json:"subject,omitempty"`
	MessageContent string     `json:"messageContent"`
	Attachments    any        `json:"attachments"`
	IsRead         bool       `json:"isRead"`
	ReadAt         *time.Time `json:"readAt,omitempty"`
	Priority       string     `json:"priority"`
	CreatedAt      time.Time  `json:"createdAt"`
}

type ProjectEngagement struct {
	ID                     string                  `json:"id"`
	ClientRequirementID    string                  `json:"clientRequirementId"`
	ExpertProfileID        string                  `json:"expertProfileId"`
	ExpertProfile          *ExpertProfile          `json:"expertProfile"`
	ProjectTitle           string                  `json:"projectTitle"`
	ProjectDescription     string                  `json:"projectDescription"`
	AgreedScope            *string                 `json:"agreedScope,omitempty"`
	AgreedRate             float64                 `json:"agreedRate"`
	Currency               string                  `json:"currency"`
	RateType               string                  `json:"rateType"`
	EstimatedHours         *int                    `json:"estimatedHours,omitempty"`
	TotalBudget            *float64                `json:"totalBudget,omitempty"`
	StartDate              time.Time               `json:"startDate"`
	EndDate                *time.Time              `json:"endDate,omitempty"`
	EstimatedDurationWeeks *int                    `json:"estimatedDurationWeeks,omitempty"`
	EngagementStatus       string                  `json:"engagementStatus"`
	ProgressPercentage     int                     `json:"progressPercentage"`
	Milestones             []*ProjectMilestone     `json:"milestones"`
	Communications         []*ProjectCommunication `json:"communications"`
	CreatedAt              time.Time               `json:"createdAt"`
	UpdatedAt              time.Time               `json:"updatedAt"`
}

type ProjectMessage struct {
	ID          string               `json:"id"`
	ProjectID   string               `json:"projectId"`
	Sender      *User                `json:"sender"`
	Content     string               `json:"content"`
	Attachments []*ProjectAttachment `json:"attachments,omitempty"`
	IsRead      bool                 `json:"isRead"`
	CreatedAt   time.Time            `json:"createdAt"`
}

type ProjectMilestone struct {
	ID                   string     `json:"id"`
	EngagementID         string     `json:"engagementId"`
	MilestoneTitle       string     `json:"milestoneTitle"`
	MilestoneDescription *string    `json:"milestoneDescription,omitempty"`
	MilestoneOrder       int        `json:"milestoneOrder"`
	DueDate              time.Time  `json:"dueDate"`
	CompletedDate        *time.Time `json:"completedDate,omitempty"`
	MilestoneValue       *float64   `json:"milestoneValue,omitempty"`
	PaymentStatus        string     `json:"paymentStatus"`
	MilestoneStatus      string     `json:"milestoneStatus"`
	CompletionNotes      *string    `json:"completionNotes,omitempty"`
	CreatedAt            time.Time  `json:"createdAt"`
	UpdatedAt            time.Time  `json:"updatedAt"`
}

type PublishPageInput struct {
	PageID      string     `json:"pageID"`
	PublishNow  *bool      `json:"publishNow,omitempty"`
	ScheduledAt *time.Time `json:"scheduledAt,omitempty"`
}

type PublishResult struct {
	Success      bool       `json:"success"`
	Message      string     `json:"message"`
	PublishedURL *string    `json:"publishedUrl,omitempty"`
	DeploymentID *string    `json:"deploymentId,omitempty"`
	PublishedAt  *time.Time `json:"publishedAt,omitempty"`
}

type PublishSiteInput struct {
	Domain         *string `json:"domain,omitempty"`
	GenerateAPIKey *bool   `json:"generateApiKey,omitempty"`
	VersionMessage *string `json:"versionMessage,omitempty"`
}

type PublishingHistory struct {
	ID              string    `json:"id"`
	SiteID          string    `json:"siteId"`
	Version         string    `json:"version"`
	VersionMessage  *string   `json:"versionMessage,omitempty"`
	PublishedURL    string    `json:"publishedUrl"`
	DeploymentID    string    `json:"deploymentId"`
	Status          string    `json:"status"`
	PublishedAt     time.Time `json:"publishedAt"`
	PublishedBy     string    `json:"publishedBy"`
	CompilationData any       `json:"compilationData,omitempty"`
}

type PublishingVersion struct {
	Version     string    `json:"version"`
	PublishedAt time.Time `json:"publishedAt"`
	Message     *string   `json:"message,omitempty"`
	Status      string    `json:"status"`
	URL         string    `json:"url"`
}

type QualityFlag struct {
	ID           string    `json:"id"`
	Type         string    `json:"type"`
	Severity     string    `json:"severity"`
	Description  string    `json:"description"`
	Location     string    `json:"location"`
	Suggestion   string    `json:"suggestion"`
	AutoDetected bool      `json:"autoDetected"`
	Status       string    `json:"status"`
	CreatedAt    time.Time `json:"createdAt"`
}

type Query struct {
}

type RateLimitConfig struct {
	RequestsPerMinute int `json:"requestsPerMinute"`
	Burst             int `json:"burst"`
}

type RateLimitConfigInput struct {
	RequestsPerMinute int `json:"requestsPerMinute"`
	Burst             int `json:"burst"`
}

type RevenueProjection struct {
	ID     string  `json:"id"`
	Year   int     `json:"year"`
	Month  int     `json:"month"`
	Amount float64 `json:"amount"`
	Source string  `json:"source"`
}

type SEOSettings struct {
	MetaTitle       *string  `json:"metaTitle,omitempty"`
	MetaDescription *string  `json:"metaDescription,omitempty"`
	Keywords        []string `json:"keywords,omitempty"`
	OgImage         *string  `json:"ogImage,omitempty"`
	TwitterCard     *string  `json:"twitterCard,omitempty"`
	RobotsTxt       *string  `json:"robotsTxt,omitempty"`
	Sitemap         bool     `json:"sitemap"`
}

type SEOSettingsInput struct {
	MetaTitle       *string  `json:"metaTitle,omitempty"`
	MetaDescription *string  `json:"metaDescription,omitempty"`
	Keywords        []string `json:"keywords,omitempty"`
	OgImage         *string  `json:"ogImage,omitempty"`
	TwitterCard     *string  `json:"twitterCard,omitempty"`
	RobotsTxt       *string  `json:"robotsTxt,omitempty"`
	Sitemap         *bool    `json:"sitemap,omitempty"`
}

type SLAInfo struct {
	ResponseTime   int     `json:"responseTime"`
	ResolutionTime int     `json:"resolutionTime"`
	IsBreached     bool    `json:"isBreached"`
	TimeRemaining  int     `json:"timeRemaining"`
	BreachReason   *string `json:"breachReason,omitempty"`
}

type SecurityFlag struct {
	ID           string    `json:"id"`
	Type         string    `json:"type"`
	Severity     string    `json:"severity"`
	Description  string    `json:"description"`
	Location     string    `json:"location"`
	Suggestion   string    `json:"suggestion"`
	AutoDetected bool      `json:"autoDetected"`
	Status       string    `json:"status"`
	CreatedAt    time.Time `json:"createdAt"`
}

type SecuritySettings struct {
	ForceHTTPS        bool     `json:"forceHttps"`
	EnableCaptcha     bool     `json:"enableCaptcha"`
	AllowedDomains    []string `json:"allowedDomains,omitempty"`
	BlockedIps        []string `json:"blockedIps,omitempty"`
	PasswordProtected bool     `json:"passwordProtected"`
}

type SecuritySettingsInput struct {
	ForceHTTPS        *bool    `json:"forceHttps,omitempty"`
	EnableCaptcha     *bool    `json:"enableCaptcha,omitempty"`
	AllowedDomains    []string `json:"allowedDomains,omitempty"`
	BlockedIps        []string `json:"blockedIps,omitempty"`
	PasswordProtected *bool    `json:"passwordProtected,omitempty"`
}

type SendMessageInput struct {
	ConversationID string  `json:"conversationId"`
	Content        string  `json:"content"`
	MessageType    *string `json:"messageType,omitempty"`
	Attachments    any     `json:"attachments,omitempty"`
}

type Site struct {
	ID           string      `json:"id"`
	Name         string      `json:"name"`
	Description  *string     `json:"description,omitempty"`
	UserID       string      `json:"userID"`
	TenantID     string      `json:"tenantID"`
	Status       string      `json:"status"`
	CustomDomain *string     `json:"customDomain,omitempty"`
	SslEnabled   bool        `json:"sslEnabled"`
	ThemeConfig  any         `json:"themeConfig"`
	SeoConfig    any         `json:"seoConfig"`
	CreatedAt    time.Time   `json:"createdAt"`
	UpdatedAt    time.Time   `json:"updatedAt"`
	PageCount    int         `json:"pageCount"`
	IsPublished  bool        `json:"isPublished"`
	Pages        []*SitePage `json:"pages"`
}

type SiteAnalytics struct {
	ID                     string           `json:"id"`
	SiteID                 string           `json:"siteId"`
	PageViews              int              `json:"pageViews"`
	UniqueVisitors         int              `json:"uniqueVisitors"`
	BounceRate             float64          `json:"bounceRate"`
	AverageSessionDuration int              `json:"averageSessionDuration"`
	TopPages               []*PageAnalytics `json:"topPages"`
	TrafficSources         []*TrafficSource `json:"trafficSources"`
	DeviceStats            *DeviceStats     `json:"deviceStats"`
	LocationStats          []*LocationStat  `json:"locationStats"`
	Period                 string           `json:"period"`
	StartDate              time.Time        `json:"startDate"`
	EndDate                time.Time        `json:"endDate"`
}

type SiteCollaborator struct {
	ID          string     `json:"id"`
	SiteID      string     `json:"siteId"`
	User        *User      `json:"user"`
	Role        string     `json:"role"`
	Permissions []string   `json:"permissions"`
	InvitedBy   *User      `json:"invitedBy"`
	InvitedAt   time.Time  `json:"invitedAt"`
	AcceptedAt  *time.Time `json:"acceptedAt,omitempty"`
	Status      string     `json:"status"`
}

type SiteNavigation struct {
	ID         string    `json:"id"`
	SiteID     string    `json:"siteID"`
	PageID     *string   `json:"pageID,omitempty"`
	ParentID   *string   `json:"parentID,omitempty"`
	Title      string    `json:"title"`
	URL        string    `json:"url"`
	Position   int       `json:"position"`
	IsExternal bool      `json:"isExternal"`
	IsVisible  bool      `json:"isVisible"`
	TenantID   string    `json:"tenantID"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

type SitePage struct {
	ID         string    `json:"id"`
	SiteID     string    `json:"siteID"`
	PageID     string    `json:"pageID"`
	IsHomepage bool      `json:"isHomepage"`
	Position   int       `json:"position"`
	TenantID   string    `json:"tenantID"`
	CreatedAt  time.Time `json:"createdAt"`
	Page       *Page     `json:"page,omitempty"`
}

type SiteSettings struct {
	ID              string  `json:"id"`
	SiteName        string  `json:"siteName"`
	SiteDescription *string `json:"siteDescription,omitempty"`
	CustomDomain    *string `json:"customDomain,omitempty"`
	SslEnabled      bool    `json:"sslEnabled"`
	IsPublished     bool    `json:"isPublished"`
	ThemeConfig     any     `json:"themeConfig"`
	SeoConfig       any     `json:"seoConfig"`
}

type SiteSettingsInput struct {
	SiteName        string  `json:"siteName"`
	SiteDescription *string `json:"siteDescription,omitempty"`
	CustomDomain    *string `json:"customDomain,omitempty"`
	ThemeConfig     any     `json:"themeConfig,omitempty"`
	SeoConfig       any     `json:"seoConfig,omitempty"`
}

type Skill struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	Category          string `json:"category"`
	Level             string `json:"level"`
	YearsOfExperience int    `json:"yearsOfExperience"`
	Endorsed          bool   `json:"endorsed"`
	Endorsements      int    `json:"endorsements"`
}

type SocialLink struct {
	Platform string `json:"platform"`
	URL      string `json:"url"`
	Verified bool   `json:"verified"`
}

type SocialLinkInput struct {
	Platform string `json:"platform"`
	URL      string `json:"url"`
}

type SocialSettings struct {
	Facebook  *string `json:"facebook,omitempty"`
	Twitter   *string `json:"twitter,omitempty"`
	Instagram *string `json:"instagram,omitempty"`
	Linkedin  *string `json:"linkedin,omitempty"`
	Youtube   *string `json:"youtube,omitempty"`
	Github    *string `json:"github,omitempty"`
}

type SocialSettingsInput struct {
	Facebook  *string `json:"facebook,omitempty"`
	Twitter   *string `json:"twitter,omitempty"`
	Instagram *string `json:"instagram,omitempty"`
	Linkedin  *string `json:"linkedin,omitempty"`
	Youtube   *string `json:"youtube,omitempty"`
	Github    *string `json:"github,omitempty"`
}

type SubscriptionTier struct {
	ID           string    `json:"id"`
	Name         string    `json:"name"`
	PriceMonthly float64   `json:"priceMonthly"`
	Features     any       `json:"features"`
	Limits       any       `json:"limits"`
	IsActive     bool      `json:"isActive"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

type SupportAttachment struct {
	ID         string    `json:"id"`
	Filename   string    `json:"filename"`
	FileSize   int       `json:"fileSize"`
	MimeType   string    `json:"mimeType"`
	URL        string    `json:"url"`
	UploadedBy *User     `json:"uploadedBy"`
	UploadedAt time.Time `json:"uploadedAt"`
}

type SupportMessage struct {
	ID          string               `json:"id"`
	TicketID    string               `json:"ticketId"`
	Author      *User                `json:"author"`
	AuthorType  string               `json:"authorType"`
	Content     string               `json:"content"`
	IsInternal  bool                 `json:"isInternal"`
	Attachments []*SupportAttachment `json:"attachments,omitempty"`
	CreatedAt   time.Time            `json:"createdAt"`
}

type SupportTicket struct {
	ID            string               `json:"id"`
	Subject       string               `json:"subject"`
	Description   string               `json:"description"`
	Status        string               `json:"status"`
	Priority      string               `json:"priority"`
	Category      string               `json:"category"`
	User          *User                `json:"user"`
	AssignedTo    *User                `json:"assignedTo,omitempty"`
	AssignedBy    *User                `json:"assignedBy,omitempty"`
	AssignedAt    *time.Time           `json:"assignedAt,omitempty"`
	FirstResponse *time.Time           `json:"firstResponse,omitempty"`
	ResolvedAt    *time.Time           `json:"resolvedAt,omitempty"`
	ClosedAt      *time.Time           `json:"closedAt,omitempty"`
	Tags          []string             `json:"tags"`
	Messages      []*SupportMessage    `json:"messages"`
	Attachments   []*SupportAttachment `json:"attachments"`
	SLA           *SLAInfo             `json:"sla,omitempty"`
	CreatedAt     time.Time            `json:"createdAt"`
	UpdatedAt     time.Time            `json:"updatedAt"`
}

type SystemHealth struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Services  any       `json:"services"`
}

type Tenant struct {
	ID        string       `json:"id"`
	Name      string       `json:"name"`
	Slug      string       `json:"slug"`
	Domain    *string      `json:"domain,omitempty"`
	Status    string       `json:"status"`
	PlanType  string       `json:"planType"`
	MaxUsers  int          `json:"maxUsers"`
	MaxSites  int          `json:"maxSites"`
	Settings  any          `json:"settings"`
	CreatedAt time.Time    `json:"createdAt"`
	UpdatedAt time.Time    `json:"updatedAt"`
	Stats     *TenantStats `json:"stats,omitempty"`
}

type TenantLimits struct {
	UsersWithinLimit bool `json:"usersWithinLimit"`
	SitesWithinLimit bool `json:"sitesWithinLimit"`
}

type TenantStats struct {
	UserCount  int `json:"userCount"`
	SiteCount  int `json:"siteCount"`
	PageCount  int `json:"pageCount"`
	AddonCount int `json:"addonCount"`
}

type TestAPIConnectionInput struct {
	APIConfigID   string  `json:"apiConfigId"`
	CredentialsID string  `json:"credentialsId"`
	Endpoint      *string `json:"endpoint,omitempty"`
	TestData      any     `json:"testData,omitempty"`
}

type TestResult struct {
	ID       string    `json:"id"`
	TestType string    `json:"testType"`
	Status   string    `json:"status"`
	Score    float64   `json:"score"`
	Details  string    `json:"details"`
	Duration int       `json:"duration"`
	RunAt    time.Time `json:"runAt"`
}

type TrafficSource struct {
	Source     string  `json:"source"`
	Medium     string  `json:"medium"`
	Campaign   *string `json:"campaign,omitempty"`
	Visitors   int     `json:"visitors"`
	Percentage float64 `json:"percentage"`
}

type UpdateAPICredentialsInput struct {
	Name        *string    `json:"name,omitempty"`
	Credentials any        `json:"credentials,omitempty"`
	IsActive    *bool      `json:"isActive,omitempty"`
	ExpiresAt   *time.Time `json:"expiresAt,omitempty"`
}

type UpdateAddonInput struct {
	Name        *string  `json:"name,omitempty"`
	Description *string  `json:"description,omitempty"`
	Version     *string  `json:"version,omitempty"`
	Category    *string  `json:"category,omitempty"`
	Tags        []string `json:"tags,omitempty"`
	IsPublic    *bool    `json:"isPublic,omitempty"`
	Config      any      `json:"config,omitempty"`
}

type UpdateBusinessPlanInput struct {
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
	Status      *string `json:"status,omitempty"`
}

type UpdateBusinessPlanSectionInput struct {
	Title      *string `json:"title,omitempty"`
	Content    *string `json:"content,omitempty"`
	IsComplete *bool   `json:"isComplete,omitempty"`
}

type UpdateComplianceStatusInput struct {
	ComplianceStatus  string  `json:"complianceStatus"`
	Notes             *string `json:"notes,omitempty"`
	EvidenceDocuments any     `json:"evidenceDocuments,omitempty"`
	ActionItems       any     `json:"actionItems,omitempty"`
}

type UpdateExpertProfileInput struct {
	ProfessionalTitle  *string  `json:"professionalTitle,omitempty"`
	Bio                *string  `json:"bio,omitempty"`
	ExpertiseAreas     []string `json:"expertiseAreas,omitempty"`
	HourlyRate         *float64 `json:"hourlyRate,omitempty"`
	AvailabilityStatus *string  `json:"availabilityStatus,omitempty"`
	PortfolioURL       *string  `json:"portfolioUrl,omitempty"`
	LinkedinURL        *string  `json:"linkedinUrl,omitempty"`
	WebsiteURL         *string  `json:"websiteUrl,omitempty"`
	AcceptsNewProjects *bool    `json:"acceptsNewProjects,omitempty"`
	ResponseTimeHours  *int     `json:"responseTimeHours,omitempty"`
}

type UpdateExpertReviewInput struct {
	Rating         *float64 `json:"rating,omitempty"`
	Title          *string  `json:"title,omitempty"`
	Comment        *string  `json:"comment,omitempty"`
	Pros           []string `json:"pros,omitempty"`
	Cons           []string `json:"cons,omitempty"`
	WouldRecommend *bool    `json:"wouldRecommend,omitempty"`
}

type UpdateExternalAPIInput struct {
	Name               *string                   `json:"name,omitempty"`
	Description        *string                   `json:"description,omitempty"`
	BaseURL            *string                   `json:"baseUrl,omitempty"`
	Version            *string                   `json:"version,omitempty"`
	AuthenticationType *AuthType                 `json:"authenticationType,omitempty"`
	Endpoints          []*CreateAPIEndpointInput `json:"endpoints,omitempty"`
	Headers            any                       `json:"headers,omitempty"`
	RateLimit          *RateLimitConfigInput     `json:"rateLimit,omitempty"`
	Security           *APISecurityConfigInput   `json:"security,omitempty"`
	Documentation      *string                   `json:"documentation,omitempty"`
	Status             *APIStatus                `json:"status,omitempty"`
	CacheTTL           *int                      `json:"cacheTTL,omitempty"`
	Metadata           any                       `json:"metadata,omitempty"`
}

type UpdateFinancialPlanInput struct {
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
}

type UpdatePageInput struct {
	Title           *string `json:"title,omitempty"`
	Slug            *string `json:"slug,omitempty"`
	Content         *string `json:"content,omitempty"`
	ParentID        *string `json:"parentID,omitempty"`
	Position        *int    `json:"position,omitempty"`
	IsHidden        *bool   `json:"isHidden,omitempty"`
	MetaTitle       *string `json:"metaTitle,omitempty"`
	MetaDescription *string `json:"metaDescription,omitempty"`
	MetaKeywords    *string `json:"metaKeywords,omitempty"`
	CustomCSS       *string `json:"customCSS,omitempty"`
	CustomJs        *string `json:"customJS,omitempty"`
	FeaturedImage   *string `json:"featuredImage,omitempty"`
	Status          *string `json:"status,omitempty"`
	Template        *string `json:"template,omitempty"`
	Settings        any     `json:"settings,omitempty"`
}

type UpdatePredefinedSnippetInput struct {
	ID          string  `json:"id"`
	Name        *string `json:"name,omitempty"`
	Category    *string `json:"category,omitempty"`
	Description *string `json:"description,omitempty"`
	HTMLContent *string `json:"htmlContent,omitempty"`
	CSSContent  *string `json:"cssContent,omitempty"`
	JsContent   *string `json:"jsContent,omitempty"`
	Version     *string `json:"version,omitempty"`
}

type UpdateProfileInput struct {
	FirstName   *string            `json:"firstName,omitempty"`
	LastName    *string            `json:"lastName,omitempty"`
	Company     *string            `json:"company,omitempty"`
	Bio         *string            `json:"bio,omitempty"`
	Website     *string            `json:"website,omitempty"`
	Location    *string            `json:"location,omitempty"`
	Timezone    *string            `json:"timezone,omitempty"`
	Language    *string            `json:"language,omitempty"`
	SocialLinks []*SocialLinkInput `json:"socialLinks,omitempty"`
}

type UpdateProjectInput struct {
	Title          *string    `json:"title,omitempty"`
	Description    *string    `json:"description,omitempty"`
	Status         *string    `json:"status,omitempty"`
	Budget         *float64   `json:"budget,omitempty"`
	HourlyRate     *float64   `json:"hourlyRate,omitempty"`
	EstimatedHours *int       `json:"estimatedHours,omitempty"`
	ActualHours    *int       `json:"actualHours,omitempty"`
	EndDate        *time.Time `json:"endDate,omitempty"`
}

type UpdateSettingsInput struct {
	Notifications *NotificationSettingsInput `json:"notifications,omitempty"`
	Privacy       *PrivacySettingsInput      `json:"privacy,omitempty"`
	Appearance    *AppearanceSettingsInput   `json:"appearance,omitempty"`
}

type UpdateSiteInput struct {
	Name         *string `json:"name,omitempty"`
	Description  *string `json:"description,omitempty"`
	Status       *string `json:"status,omitempty"`
	CustomDomain *string `json:"customDomain,omitempty"`
	SslEnabled   *bool   `json:"sslEnabled,omitempty"`
	ThemeConfig  any     `json:"themeConfig,omitempty"`
	SeoConfig    any     `json:"seoConfig,omitempty"`
}

type UpdateSiteSettingsInput struct {
	General     *GeneralSettingsInput     `json:"general,omitempty"`
	Seo         *SEOSettingsInput         `json:"seo,omitempty"`
	Social      *SocialSettingsInput      `json:"social,omitempty"`
	Analytics   *AnalyticsSettingsInput   `json:"analytics,omitempty"`
	Security    *SecuritySettingsInput    `json:"security,omitempty"`
	Performance *PerformanceSettingsInput `json:"performance,omitempty"`
}

type UpdateSubscriptionTierInput struct {
	Name         *string  `json:"name,omitempty"`
	PriceMonthly *float64 `json:"priceMonthly,omitempty"`
	Features     any      `json:"features,omitempty"`
	Limits       any      `json:"limits,omitempty"`
	IsActive     *bool    `json:"isActive,omitempty"`
}

type UpdateSupportTicketInput struct {
	Subject     *string `json:"subject,omitempty"`
	Description *string `json:"description,omitempty"`
	Priority    *string `json:"priority,omitempty"`
	Category    *string `json:"category,omitempty"`
	Status      *string `json:"status,omitempty"`
}

type UpdateTenantInput struct {
	Name     *string `json:"name,omitempty"`
	Slug     *string `json:"slug,omitempty"`
	Domain   *string `json:"domain,omitempty"`
	Status   *string `json:"status,omitempty"`
	PlanType *string `json:"planType,omitempty"`
	MaxUsers *int    `json:"maxUsers,omitempty"`
	MaxSites *int    `json:"maxSites,omitempty"`
	Settings any     `json:"settings,omitempty"`
}

type UpdateUserProfileInput struct {
	FirstName    *string `json:"firstName,omitempty"`
	LastName     *string `json:"lastName,omitempty"`
	Company      *string `json:"company,omitempty"`
	BusinessType *string `json:"businessType,omitempty"`
	Industry     *string `json:"industry,omitempty"`
	TeamSize     *string `json:"teamSize,omitempty"`
	PrimaryGoal  *string `json:"primaryGoal,omitempty"`
	AvatarURL    *string `json:"avatarUrl,omitempty"`
	Timezone     *string `json:"timezone,omitempty"`
	Language     *string `json:"language,omitempty"`
}

type UpdateUserSettingsInput struct {
	EmailNotifications   *bool   `json:"emailNotifications,omitempty"`
	BrowserNotifications *bool   `json:"browserNotifications,omitempty"`
	MarketingEmails      *bool   `json:"marketingEmails,omitempty"`
	WeeklyDigest         *bool   `json:"weeklyDigest,omitempty"`
	SecurityAlerts       *bool   `json:"securityAlerts,omitempty"`
	Theme                *string `json:"theme,omitempty"`
	SidebarCollapsed     *bool   `json:"sidebarCollapsed,omitempty"`
	DefaultDashboardView *string `json:"defaultDashboardView,omitempty"`
	AutoSave             *bool   `json:"autoSave,omitempty"`
}

type UpgradeSubscriptionInput struct {
	TierID          string  `json:"tierID"`
	PaymentMethodID *string `json:"paymentMethodId,omitempty"`
}

type UsageTracking struct {
	ID           string    `json:"id"`
	UserID       string    `json:"userID"`
	ResourceType string    `json:"resourceType"`
	UsageAmount  int       `json:"usageAmount"`
	PeriodStart  time.Time `json:"periodStart"`
	PeriodEnd    time.Time `json:"periodEnd"`
	CreatedAt    time.Time `json:"createdAt"`
}

type User struct {
	ID            string            `json:"id"`
	Email         string            `json:"email"`
	Role          string            `json:"role"`
	Status        string            `json:"status"`
	FirstName     *string           `json:"firstName,omitempty"`
	LastName      *string           `json:"lastName,omitempty"`
	Company       *string           `json:"company,omitempty"`
	EmailVerified bool              `json:"emailVerified"`
	LastLoginAt   *time.Time        `json:"lastLoginAt,omitempty"`
	CreatedAt     time.Time         `json:"createdAt"`
	UpdatedAt     time.Time         `json:"updatedAt"`
	Profile       *UserProfile      `json:"profile,omitempty"`
	Settings      *UserSettings     `json:"settings,omitempty"`
	Subscription  *UserSubscription `json:"subscription,omitempty"`
	Notes         *string           `json:"notes,omitempty"`
}

type UserActivity struct {
	UserID       string    `json:"userID"`
	Email        string    `json:"email"`
	Role         string    `json:"role"`
	LastActive   time.Time `json:"lastActive"`
	SitesCreated int       `json:"sitesCreated"`
	PagesCreated int       `json:"pagesCreated"`
	APICallsUsed int       `json:"apiCallsUsed"`
}

type UserListResponse struct {
	Users      []*AdminUser `json:"users"`
	Total      int          `json:"total"`
	Page       int          `json:"page"`
	PageSize   int          `json:"pageSize"`
	TotalPages int          `json:"totalPages"`
}

type UserProfile struct {
	UserID       string  `json:"userID"`
	FirstName    *string `json:"firstName,omitempty"`
	LastName     *string `json:"lastName,omitempty"`
	Company      *string `json:"company,omitempty"`
	BusinessType *string `json:"businessType,omitempty"`
	Industry     *string `json:"industry,omitempty"`
	TeamSize     *string `json:"teamSize,omitempty"`
	PrimaryGoal  *string `json:"primaryGoal,omitempty"`
	AvatarURL    *string `json:"avatarUrl,omitempty"`
	Timezone     string  `json:"timezone"`
	Language     string  `json:"language"`
}

type UserSearchFiltersInput struct {
	Email            *string    `json:"email,omitempty"`
	Role             *string    `json:"role,omitempty"`
	Status           *string    `json:"status,omitempty"`
	TenantID         *string    `json:"tenantID,omitempty"`
	SubscriptionTier *string    `json:"subscriptionTier,omitempty"`
	CreatedAfter     *time.Time `json:"createdAfter,omitempty"`
	CreatedBefore    *time.Time `json:"createdBefore,omitempty"`
	LastActiveAfter  *time.Time `json:"lastActiveAfter,omitempty"`
	HasFlags         []string   `json:"hasFlags,omitempty"`
	MinRevenue       *float64   `json:"minRevenue,omitempty"`
	MaxRevenue       *float64   `json:"maxRevenue,omitempty"`
}

type UserSettings struct {
	ID                   string    `json:"id"`
	UserID               string    `json:"userID"`
	EmailNotifications   bool      `json:"emailNotifications"`
	BrowserNotifications bool      `json:"browserNotifications"`
	MarketingEmails      bool      `json:"marketingEmails"`
	WeeklyDigest         bool      `json:"weeklyDigest"`
	SecurityAlerts       bool      `json:"securityAlerts"`
	Theme                string    `json:"theme"`
	SidebarCollapsed     bool      `json:"sidebarCollapsed"`
	DefaultDashboardView string    `json:"defaultDashboardView"`
	AutoSave             bool      `json:"autoSave"`
	CreatedAt            time.Time `json:"createdAt"`
	UpdatedAt            time.Time `json:"updatedAt"`
}

type UserStats struct {
	ByRole                any `json:"byRole"`
	ByStatus              any `json:"byStatus"`
	NewUsersLast30Days    int `json:"newUsersLast30Days"`
	ActiveUsersLast30Days int `json:"activeUsersLast30Days"`
}

type UserSubscription struct {
	ID                   string            `json:"id"`
	UserID               string            `json:"userID"`
	Tier                 *SubscriptionTier `json:"tier"`
	Status               string            `json:"status"`
	CurrentPeriodStart   time.Time         `json:"currentPeriodStart"`
	CurrentPeriodEnd     time.Time         `json:"currentPeriodEnd"`
	StripeSubscriptionID *string           `json:"stripeSubscriptionId,omitempty"`
	CreatedAt            time.Time         `json:"createdAt"`
	UpdatedAt            time.Time         `json:"updatedAt"`
}

type Vehicle struct {
	Vin       string `json:"vin"`
	Make      string `json:"make"`
	Model     string `json:"model"`
	Year      int    `json:"year"`
	Owner     *User  `json:"owner"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}

type VehicleInput struct {
	Vin   string `json:"vin"`
	Make  string `json:"make"`
	Model string `json:"model"`
	Year  int    `json:"year"`
}

type WebAddon struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	Description  *string `json:"description,omitempty"`
	ReteConfig   string  `json:"reteConfig"`
	Version      string  `json:"version"`
	Dependencies *string `json:"dependencies,omitempty"`
	Status       string  `json:"status"`
	CreatedAt    string  `json:"createdAt"`
	UpdatedAt    string  `json:"updatedAt"`
}

func (WebAddon) IsComponentResult() {}

type WebAddonInput struct {
	Name         string  `json:"name"`
	Description  *string `json:"description,omitempty"`
	ReteConfig   string  `json:"reteConfig"`
	Version      string  `json:"version"`
	Dependencies *string `json:"dependencies,omitempty"`
}

type APIStatus string

const (
	APIStatusActive      APIStatus = "ACTIVE"
	APIStatusInactive    APIStatus = "INACTIVE"
	APIStatusDeprecated  APIStatus = "DEPRECATED"
	APIStatusMaintenance APIStatus = "MAINTENANCE"
)

var AllAPIStatus = []APIStatus{
	APIStatusActive,
	APIStatusInactive,
	APIStatusDeprecated,
	APIStatusMaintenance,
}

func (e APIStatus) IsValid() bool {
	switch e {
	case APIStatusActive, APIStatusInactive, APIStatusDeprecated, APIStatusMaintenance:
		return true
	}
	return false
}

func (e APIStatus) String() string {
	return string(e)
}

func (e *APIStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = APIStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid APIStatus", str)
	}
	return nil
}

func (e APIStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *APIStatus) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e APIStatus) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type AddonState string

const (
	AddonStateDevelopment AddonState = "DEVELOPMENT"
	AddonStateReview      AddonState = "REVIEW"
	AddonStateProduction  AddonState = "PRODUCTION"
	AddonStateDisabled    AddonState = "DISABLED"
)

var AllAddonState = []AddonState{
	AddonStateDevelopment,
	AddonStateReview,
	AddonStateProduction,
	AddonStateDisabled,
}

func (e AddonState) IsValid() bool {
	switch e {
	case AddonStateDevelopment, AddonStateReview, AddonStateProduction, AddonStateDisabled:
		return true
	}
	return false
}

func (e AddonState) String() string {
	return string(e)
}

func (e *AddonState) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = AddonState(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid AddonState", str)
	}
	return nil
}

func (e AddonState) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *AddonState) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e AddonState) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type AddonTestStatus string

const (
	AddonTestStatusPending   AddonTestStatus = "PENDING"
	AddonTestStatusRunning   AddonTestStatus = "RUNNING"
	AddonTestStatusCompleted AddonTestStatus = "COMPLETED"
	AddonTestStatusFailed    AddonTestStatus = "FAILED"
	AddonTestStatusTimedout  AddonTestStatus = "TIMEDOUT"
	AddonTestStatusCancelled AddonTestStatus = "CANCELLED"
)

var AllAddonTestStatus = []AddonTestStatus{
	AddonTestStatusPending,
	AddonTestStatusRunning,
	AddonTestStatusCompleted,
	AddonTestStatusFailed,
	AddonTestStatusTimedout,
	AddonTestStatusCancelled,
}

func (e AddonTestStatus) IsValid() bool {
	switch e {
	case AddonTestStatusPending, AddonTestStatusRunning, AddonTestStatusCompleted, AddonTestStatusFailed, AddonTestStatusTimedout, AddonTestStatusCancelled:
		return true
	}
	return false
}

func (e AddonTestStatus) String() string {
	return string(e)
}

func (e *AddonTestStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = AddonTestStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid AddonTestStatus", str)
	}
	return nil
}

func (e AddonTestStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *AddonTestStatus) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e AddonTestStatus) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type AuthType string

const (
	AuthTypeNone   AuthType = "NONE"
	AuthTypeAPIKey AuthType = "API_KEY"
	AuthTypeOauth2 AuthType = "OAUTH2"
	AuthTypeBearer AuthType = "BEARER"
)

var AllAuthType = []AuthType{
	AuthTypeNone,
	AuthTypeAPIKey,
	AuthTypeOauth2,
	AuthTypeBearer,
}

func (e AuthType) IsValid() bool {
	switch e {
	case AuthTypeNone, AuthTypeAPIKey, AuthTypeOauth2, AuthTypeBearer:
		return true
	}
	return false
}

func (e AuthType) String() string {
	return string(e)
}

func (e *AuthType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = AuthType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid AuthType", str)
	}
	return nil
}

func (e AuthType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *AuthType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e AuthType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type ComponentType string

const (
	ComponentTypeAddon   ComponentType = "ADDON"
	ComponentTypeSnippet ComponentType = "SNIPPET"
)

var AllComponentType = []ComponentType{
	ComponentTypeAddon,
	ComponentTypeSnippet,
}

func (e ComponentType) IsValid() bool {
	switch e {
	case ComponentTypeAddon, ComponentTypeSnippet:
		return true
	}
	return false
}

func (e ComponentType) String() string {
	return string(e)
}

func (e *ComponentType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ComponentType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ComponentType", str)
	}
	return nil
}

func (e ComponentType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *ComponentType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e ComponentType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type ConnectionStatus string

const (
	ConnectionStatusConnected    ConnectionStatus = "CONNECTED"
	ConnectionStatusDisconnected ConnectionStatus = "DISCONNECTED"
	ConnectionStatusError        ConnectionStatus = "ERROR"
	ConnectionStatusTesting      ConnectionStatus = "TESTING"
)

var AllConnectionStatus = []ConnectionStatus{
	ConnectionStatusConnected,
	ConnectionStatusDisconnected,
	ConnectionStatusError,
	ConnectionStatusTesting,
}

func (e ConnectionStatus) IsValid() bool {
	switch e {
	case ConnectionStatusConnected, ConnectionStatusDisconnected, ConnectionStatusError, ConnectionStatusTesting:
		return true
	}
	return false
}

func (e ConnectionStatus) String() string {
	return string(e)
}

func (e *ConnectionStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ConnectionStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ConnectionStatus", str)
	}
	return nil
}

func (e ConnectionStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *ConnectionStatus) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e ConnectionStatus) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type HTTPMethod string

const (
	HTTPMethodGet     HTTPMethod = "GET"
	HTTPMethodPost    HTTPMethod = "POST"
	HTTPMethodPut     HTTPMethod = "PUT"
	HTTPMethodDelete  HTTPMethod = "DELETE"
	HTTPMethodPatch   HTTPMethod = "PATCH"
	HTTPMethodHead    HTTPMethod = "HEAD"
	HTTPMethodOptions HTTPMethod = "OPTIONS"
)

var AllHTTPMethod = []HTTPMethod{
	HTTPMethodGet,
	HTTPMethodPost,
	HTTPMethodPut,
	HTTPMethodDelete,
	HTTPMethodPatch,
	HTTPMethodHead,
	HTTPMethodOptions,
}

func (e HTTPMethod) IsValid() bool {
	switch e {
	case HTTPMethodGet, HTTPMethodPost, HTTPMethodPut, HTTPMethodDelete, HTTPMethodPatch, HTTPMethodHead, HTTPMethodOptions:
		return true
	}
	return false
}

func (e HTTPMethod) String() string {
	return string(e)
}

func (e *HTTPMethod) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = HTTPMethod(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid HTTPMethod", str)
	}
	return nil
}

func (e HTTPMethod) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *HTTPMethod) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e HTTPMethod) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
