// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"goVwPlatformAPI/graph/model"
)

// NewExecutableSchema creates an ExecutableSchema from the ResolverRoot interface.
func NewExecutableSchema(cfg Config) graphql.ExecutableSchema {
	return &executableSchema{
		resolvers:  cfg.Resolvers,
		directives: cfg.Directives,
		complexity: cfg.Complexity,
	}
}

type Config struct {
	Resolvers  ResolverRoot
	Directives DirectiveRoot
	Complexity ComplexityRoot
}

type ResolverRoot interface {
	Mutation() MutationResolver
	Query() QueryResolver
}

type DirectiveRoot struct{}

type ComplexityRoot struct{}

type executableSchema struct {
	resolvers  ResolverRoot
	directives DirectiveRoot
	complexity ComplexityRoot
}

type executionContext struct {
	*graphql.OperationContext
	*executableSchema
	mu     sync.Mutex
	result graphql.Marshaler
	vars   map[string]interface{}
}

// Complexity method with correct signature for older GraphQL library
func (e *executableSchema) Complexity(typeName, field string, childComplexity int, rawArgs map[string]interface{}) (int, bool) {
	return childComplexity, true
}

func (e *executableSchema) Schema() *ast.Schema {
	return parsedSchema
}

func (e *executableSchema) Exec(ctx context.Context) graphql.ResponseHandler {
	return func(ctx context.Context) *graphql.Response {
		return &graphql.Response{Data: []byte(`{}`)}
	}
}

var parsedSchema = &ast.Schema{}

// Time scalar methods with correct signatures
func (ec *executionContext) unmarshalInputTime(v interface{}) (time.Time, error) {
	if str, ok := v.(string); ok {
		return time.Parse(time.RFC3339, str)
	}
	return time.Time{}, fmt.Errorf("invalid time format")
}

func (ec *executionContext) _Time(ctx context.Context, sel ast.SelectionSet, v *time.Time) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return graphql.MarshalTime(*v)
}

// Resolver interfaces - these will be implemented by the actual resolvers
type MutationResolver interface {
	// Add all mutation methods here
	GenerateSiteAPIKey(ctx context.Context, siteID string, permissions int) (*model.APIKey, error)
}

type QueryResolver interface {
	// Add all query methods here
}