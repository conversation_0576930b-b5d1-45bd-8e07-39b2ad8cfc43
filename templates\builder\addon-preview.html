<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Addon Preview - Velocity Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-lightning-charge"></i>
                VelocityWave
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/builder/addon-builder">
                    <i class="bi bi-arrow-left"></i> Back to Builder
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-eye"></i>
                            Preview Options
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Preview Mode</label>
                            <select class="form-select" id="preview-mode" onchange="updatePreviewMode()">
                                <option value="interactive">Interactive Demo</option>
                                <option value="static">Static Preview</option>
                                <option value="code">Generated Code</option>
                                <option value="documentation">Documentation</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Device Preview</label>
                            <div class="btn-group-vertical w-100" role="group">
                                <input type="radio" class="btn-check" name="device" id="desktop" checked onchange="updateDevice('desktop')">
                                <label class="btn btn-outline-primary" for="desktop">
                                    <i class="bi bi-display"></i> Desktop
                                </label>
                                
                                <input type="radio" class="btn-check" name="device" id="tablet" onchange="updateDevice('tablet')">
                                <label class="btn btn-outline-primary" for="tablet">
                                    <i class="bi bi-tablet"></i> Tablet
                                </label>
                                
                                <input type="radio" class="btn-check" name="device" id="mobile" onchange="updateDevice('mobile')">
                                <label class="btn btn-outline-primary" for="mobile">
                                    <i class="bi bi-phone"></i> Mobile
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <button class="btn btn-success w-100" onclick="generatePreview()">
                                <i class="bi bi-arrow-clockwise"></i> Refresh Preview
                            </button>
                        </div>

                        <div class="mb-3">
                            <button class="btn btn-outline-secondary w-100" onclick="exportPreview()">
                                <i class="bi bi-download"></i> Export Preview
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Addon Info Card -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-info-circle"></i>
                            Addon Information
                        </h6>
                    </div>
                    <div class="card-body" id="addon-info">
                        <div class="text-center">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="small mt-2">Loading addon info...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Preview Area -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">
                                <i class="bi bi-eye"></i>
                                Addon Preview
                            </h4>
                            <div>
                                <button class="btn btn-outline-primary" onclick="openFullscreen()">
                                    <i class="bi bi-arrows-fullscreen"></i> Fullscreen
                                </button>
                                <button class="btn btn-primary" onclick="testAddon()">
                                    <i class="bi bi-play-circle"></i> Test Addon
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Preview Container -->
                        <div id="preview-container" class="position-relative">
                            <div id="preview-frame" class="preview-desktop">
                                <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading preview...</span>
                                        </div>
                                        <p class="mt-3">Generating addon preview...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preview Tabs -->
                <div class="card mt-3">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="preview-tabs">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#demo-tab">
                                    <i class="bi bi-play-circle"></i> Interactive Demo
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#workflow-tab">
                                    <i class="bi bi-diagram-3"></i> Workflow
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#code-tab">
                                    <i class="bi bi-code-slash"></i> Generated Code
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#logs-tab">
                                    <i class="bi bi-terminal"></i> Execution Logs
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <!-- Interactive Demo Tab -->
                            <div class="tab-pane fade show active" id="demo-tab">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h5>Demo Controls</h5>
                                        <div class="mb-3">
                                            <label class="form-label">Test Input Data</label>
                                            <textarea class="form-control" id="demo-input" rows="4" 
                                                      placeholder='{"message": "Hello World", "value": 42}'></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <button class="btn btn-primary" onclick="runDemo()">
                                                <i class="bi bi-play"></i> Run Demo
                                            </button>
                                            <button class="btn btn-outline-secondary" onclick="resetDemo()">
                                                <i class="bi bi-arrow-clockwise"></i> Reset
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h5>Demo Output</h5>
                                        <div id="demo-output" class="border rounded p-3 bg-light" style="min-height: 150px;">
                                            <em class="text-muted">Click "Run Demo" to see output...</em>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Workflow Tab -->
                            <div class="tab-pane fade" id="workflow-tab">
                                <div id="workflow-visualization">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading workflow...</span>
                                        </div>
                                        <p class="mt-2">Loading workflow visualization...</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Generated Code Tab -->
                            <div class="tab-pane fade" id="code-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>HTML</h5>
                                        <pre><code id="generated-html" class="language-html">Loading...</code></pre>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>JavaScript</h5>
                                        <pre><code id="generated-js" class="language-javascript">Loading...</code></pre>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <h5>CSS</h5>
                                        <pre><code id="generated-css" class="language-css">Loading...</code></pre>
                                    </div>
                                </div>
                            </div>

                            <!-- Execution Logs Tab -->
                            <div class="tab-pane fade" id="logs-tab">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="mb-0">Execution Logs</h5>
                                    <div>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">
                                            <i class="bi bi-trash"></i> Clear
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary" onclick="downloadLogs()">
                                            <i class="bi bi-download"></i> Download
                                        </button>
                                    </div>
                                </div>
                                <div id="execution-logs" class="border rounded p-3 bg-dark text-light" style="height: 300px; overflow-y: auto; font-family: monospace;">
                                    <div class="text-muted">No logs available. Run the demo to see execution logs.</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentAddonId = null;
        let currentPreviewData = null;
        let executionLogs = [];

        // Initialize preview
        document.addEventListener('DOMContentLoaded', function() {
            currentAddonId = getAddonId();
            if (currentAddonId) {
                loadAddonInfo();
                generatePreview();
            }
        });

        function getAddonId() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('id') || localStorage.getItem('currentAddonId');
        }

        function loadAddonInfo() {
            fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    query: `
                        query LoadAddonConfig($id: ID!) {
                            loadAddonConfig(id: $id) {
                                id
                                name
                                metadata {
                                    name
                                    description
                                    category
                                    author
                                    version
                                    tags
                                    icon
                                }
                            }
                        }
                    `,
                    variables: { id: currentAddonId }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.data?.loadAddonConfig) {
                    displayAddonInfo(data.data.loadAddonConfig);
                }
            })
            .catch(error => {
                console.error('Error loading addon info:', error);
                document.getElementById('addon-info').innerHTML = 
                    '<div class="text-danger">Error loading addon information</div>';
            });
        }

        function displayAddonInfo(addon) {
            const metadata = addon.metadata || {};
            const infoHtml = `
                <div class="d-flex align-items-center mb-2">
                    <img src="${metadata.icon || '/static/images/default-addon-icon.svg'}" 
                         alt="Addon Icon" class="me-2" style="width: 32px; height: 32px;">
                    <div>
                        <div class="fw-bold">${metadata.name || addon.name}</div>
                        <div class="text-muted small">v${metadata.version || '1.0.0'}</div>
                    </div>
                </div>
                <p class="small text-muted mb-2">${metadata.description || 'No description'}</p>
                <div class="mb-2">
                    <span class="badge bg-secondary">${metadata.category || 'utility'}</span>
                </div>
                <div class="small">
                    <strong>Author:</strong> ${metadata.author || 'Unknown'}<br>
                    ${metadata.tags && metadata.tags.length > 0 ? 
                        `<strong>Tags:</strong> ${metadata.tags.join(', ')}` : ''}
                </div>
            `;
            document.getElementById('addon-info').innerHTML = infoHtml;
        }

        function generatePreview() {
            if (!currentAddonId) return;

            addLog('info', 'Generating addon preview...');

            fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    query: `
                        mutation GenerateAddonPreview($id: ID!) {
                            generateAddonPreview(id: $id) {
                                generatedAt
                                previewHTML
                                previewCSS
                                previewJS
                                demoData
                            }
                        }
                    `,
                    variables: { id: currentAddonId }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.data?.generateAddonPreview) {
                    currentPreviewData = data.data.generateAddonPreview;
                    displayPreview(currentPreviewData);
                    addLog('success', 'Preview generated successfully');
                } else if (data.errors) {
                    addLog('error', 'Error generating preview: ' + data.errors[0].message);
                }
            })
            .catch(error => {
                console.error('Error generating preview:', error);
                addLog('error', 'Failed to generate preview');
            });
        }

        function displayPreview(previewData) {
            // Update preview frame
            const previewFrame = document.getElementById('preview-frame');
            previewFrame.innerHTML = `
                <style>${previewData.previewCSS}</style>
                ${previewData.previewHTML}
                <script>${previewData.previewJS}</script>
            `;

            // Update code tabs
            document.getElementById('generated-html').textContent = previewData.previewHTML;
            document.getElementById('generated-css').textContent = previewData.previewCSS;
            document.getElementById('generated-js').textContent = previewData.previewJS;

            // Set demo input data
            if (previewData.demoData) {
                document.getElementById('demo-input').value = JSON.stringify(previewData.demoData, null, 2);
            }
        }

        function updatePreviewMode() {
            const mode = document.getElementById('preview-mode').value;
            addLog('info', `Switched to ${mode} preview mode`);
            // Implement different preview modes
        }

        function updateDevice(device) {
            const previewFrame = document.getElementById('preview-frame');
            previewFrame.className = `preview-${device}`;
            addLog('info', `Switched to ${device} preview`);
        }

        function runDemo() {
            const input = document.getElementById('demo-input').value;
            const output = document.getElementById('demo-output');
            
            try {
                const inputData = JSON.parse(input);
                addLog('info', 'Running demo with input data');
                
                // Simulate addon execution
                output.innerHTML = '<div class="text-primary">Running demo...</div>';
                
                setTimeout(() => {
                    const result = simulateAddonExecution(inputData);
                    output.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                    addLog('success', 'Demo completed successfully');
                }, 1000);
                
            } catch (error) {
                output.innerHTML = `<div class="text-danger">Error: ${error.message}</div>`;
                addLog('error', 'Demo failed: ' + error.message);
            }
        }

        function resetDemo() {
            document.getElementById('demo-output').innerHTML = 
                '<em class="text-muted">Click "Run Demo" to see output...</em>';
            addLog('info', 'Demo reset');
        }

        function simulateAddonExecution(inputData) {
            // Simple simulation of addon execution
            return {
                success: true,
                timestamp: new Date().toISOString(),
                input: inputData,
                output: {
                    message: `Processed: ${inputData.message || 'No message'}`,
                    value: (inputData.value || 0) * 2,
                    processed: true
                },
                executionTime: Math.floor(Math.random() * 500 + 100) + 'ms'
            };
        }

        function testAddon() {
            window.location.href = `/builder/addon-testing?id=${currentAddonId}`;
        }

        function openFullscreen() {
            const previewFrame = document.getElementById('preview-frame');
            if (previewFrame.requestFullscreen) {
                previewFrame.requestFullscreen();
            }
        }

        function exportPreview() {
            if (!currentPreviewData) {
                alert('No preview data to export');
                return;
            }

            const exportData = {
                html: currentPreviewData.previewHTML,
                css: currentPreviewData.previewCSS,
                js: currentPreviewData.previewJS,
                generatedAt: currentPreviewData.generatedAt
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], 
                                 { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `addon-preview-${currentAddonId}.json`;
            a.click();
            URL.revokeObjectURL(url);

            addLog('info', 'Preview exported successfully');
        }

        function addLog(level, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = { timestamp, level, message };
            executionLogs.push(logEntry);

            const logsContainer = document.getElementById('execution-logs');
            const logElement = document.createElement('div');
            logElement.className = `log-entry log-${level}`;
            
            const levelColor = {
                'info': 'text-info',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[level] || 'text-light';

            logElement.innerHTML = `
                <span class="text-muted">[${timestamp}]</span> 
                <span class="${levelColor}">[${level.toUpperCase()}]</span> 
                ${message}
            `;

            logsContainer.appendChild(logElement);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function clearLogs() {
            executionLogs = [];
            document.getElementById('execution-logs').innerHTML = 
                '<div class="text-muted">Logs cleared.</div>';
        }

        function downloadLogs() {
            const blob = new Blob([JSON.stringify(executionLogs, null, 2)], 
                                 { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `addon-logs-${currentAddonId}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>

    <style>
        .preview-desktop {
            width: 100%;
            min-height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .preview-tablet {
            width: 768px;
            min-height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            margin: 0 auto;
        }

        .preview-mobile {
            width: 375px;
            min-height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            margin: 0 auto;
        }

        .log-entry {
            margin-bottom: 4px;
            font-size: 12px;
            line-height: 1.4;
        }

        pre code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            display: block;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</body>
</html>