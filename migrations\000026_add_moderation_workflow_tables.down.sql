-- Drop indexes first
DROP INDEX IF EXISTS idx_user_sessions_started_at;
DROP INDEX IF EXISTS idx_user_sessions_session_id;
DROP INDEX IF EXISTS idx_user_sessions_user_id;

DROP INDEX IF EXISTS idx_analytics_events_created_at;
DROP INDEX IF EXISTS idx_analytics_events_event_type;
DROP INDEX IF EXISTS idx_analytics_events_user_id;

DROP INDEX IF EXISTS idx_workflow_executions_created_at;
DROP INDEX IF EXISTS idx_workflow_executions_status;
DROP INDEX IF EXISTS idx_workflow_executions_content_id;
DROP INDEX IF EXISTS idx_workflow_executions_workflow_id;

DROP INDEX IF EXISTS idx_expert_profiles_verification_status;
DROP INDEX IF EXISTS idx_expert_profiles_status;
DROP INDEX IF EXISTS idx_expert_profiles_user_id;

DROP INDEX IF EXISTS idx_expert_applications_created_at;
DROP INDEX IF EXISTS idx_expert_applications_status;
DROP INDEX IF EXISTS idx_expert_applications_user_id;

DROP INDEX IF EXISTS idx_support_tickets_created_at;
DROP INDEX IF EXISTS idx_support_tickets_priority;
DROP INDEX IF EXISTS idx_support_tickets_status;
DROP INDEX IF EXISTS idx_support_tickets_assigned_to;
DROP INDEX IF EXISTS idx_support_tickets_user_id;

DROP INDEX IF EXISTS idx_user_actions_created_at;
DROP INDEX IF EXISTS idx_user_actions_admin_id;
DROP INDEX IF EXISTS idx_user_actions_user_id;

-- Drop tables in reverse order of creation
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS analytics_events;
DROP TABLE IF EXISTS system_alerts;
DROP TABLE IF EXISTS system_health_checks;
DROP TABLE IF EXISTS moderation_actions;
DROP TABLE IF EXISTS content_reports;
DROP TABLE IF EXISTS workflow_executions;
DROP TABLE IF EXISTS moderation_workflows;