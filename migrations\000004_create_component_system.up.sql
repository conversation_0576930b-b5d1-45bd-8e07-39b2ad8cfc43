CREATE TABLE IF NOT EXISTS web_addons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    rete_config JSONB NOT NULL,
    version TEXT NOT NULL,
    dependencies JSONB,
    status TEXT NOT NULL DEFAULT 'development' CHECK (status IN ('development', 'review', 'production', 'disabled')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS predefined_snippets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    html_content TEXT NOT NULL,
    css_content TEXT,
    js_content TEXT,
    version TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS component_registry (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    component_id UUID NOT NULL,
    component_type TEXT NOT NULL CHECK (component_type IN ('addon', 'snippet')),
    version TEXT NOT NULL,
    active BOOLEAN NOT NULL DEFAULT true,
    registered_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_web_addons_status ON web_addons(status);
CREATE INDEX IF NOT EXISTS idx_snippets_category ON predefined_snippets(category);
CREATE INDEX IF NOT EXISTS idx_registry_active ON component_registry(active);