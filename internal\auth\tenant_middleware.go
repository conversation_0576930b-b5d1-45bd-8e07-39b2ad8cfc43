package auth

import (
	"context"
	"net/http"
	"strings"
)

type tenantContextKey string

const TenantContextKey tenantContextKey = "tenant"

// TenantInfo holds tenant information for the request context
type TenantInfo struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Slug     string `json:"slug"`
	PlanType string `json:"plan_type"`
}

// ExtractTenantFromSubdomain middleware extracts tenant from subdomain
func ExtractTenantFromSubdomain() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			host := r.Host

			// Remove port if present
			if colonIndex := strings.Index(host, ":"); colonIndex != -1 {
				host = host[:colonIndex]
			}

			// Extract subdomain
			parts := strings.Split(host, ".")
			if len(parts) >= 3 {
				subdomain := parts[0]

				// Skip common subdomains
				if subdomain != "www" && subdomain != "api" && subdomain != "admin" {
					// TODO: Look up tenant by subdomain
					tenantInfo := &TenantInfo{
						Slug: subdomain,
					}

					ctx := context.WithValue(r.Context(), TenantContextKey, tenantInfo)
					r = r.WithContext(ctx)
				}
			}

			next.ServeHTTP(w, r)
		})
	}
}

// RequireTenant middleware ensures a tenant is present in the context
func RequireTenant() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			tenantInfo, ok := r.Context().Value(TenantContextKey).(*TenantInfo)
			if !ok || tenantInfo == nil {
				http.Error(w, "Tenant not found", http.StatusNotFound)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// ValidateTenantAccess middleware ensures user has access to the tenant
func ValidateTenantAccess() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Get user claims
			userClaims, ok := r.Context().Value(UserContextKey).(*Claims)
			if !ok {
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}

			// Get tenant info
			tenantInfo, ok := r.Context().Value(TenantContextKey).(*TenantInfo)
			if !ok {
				http.Error(w, "Tenant not found", http.StatusNotFound)
				return
			}

			// Validate user belongs to this tenant
			if userClaims.TenantID != tenantInfo.ID {
				http.Error(w, "Access denied to this tenant", http.StatusForbidden)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// GetTenantFromContext extracts tenant info from request context
func GetTenantFromContext(r *http.Request) (*TenantInfo, bool) {
	tenantInfo, ok := r.Context().Value(TenantContextKey).(*TenantInfo)
	return tenantInfo, ok
}

// TenantScopedQuery adds tenant filtering to database queries
func TenantScopedQuery(baseQuery string, tenantID string) string {
	// Add tenant_id filter to WHERE clause
	if strings.Contains(strings.ToUpper(baseQuery), "WHERE") {
		return baseQuery + " AND tenant_id = '" + tenantID + "'"
	} else {
		return baseQuery + " WHERE tenant_id = '" + tenantID + "'"
	}
}

// ValidateResourceAccess checks if a resource belongs to the user's tenant
func ValidateResourceAccess(userTenantID, resourceTenantID string) bool {
	return userTenantID == resourceTenantID
}

// GetDefaultTenantID returns the default tenant ID for new users
func GetDefaultTenantID() string {
	return "11111111-1111-1111-1111-111111111111"
}

// IsSuperAdmin checks if user is a super admin (can access all tenants)
func IsSuperAdmin(claims *Claims) bool {
	return claims != nil && claims.Role == "admin" && claims.TenantID == GetDefaultTenantID()
}
