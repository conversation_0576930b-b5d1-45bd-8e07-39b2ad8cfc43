package addon

import (
	"encoding/json"
	"fmt"
	"time"
)

// AddonConfiguration represents the complete addon configuration
type AddonConfiguration struct {
	Metadata AddonMetadata `json:"metadata"`
	Workflow WorkflowGraph `json:"workflow"`
	Settings AddonSettings `json:"settings"`
	Version  string        `json:"version"`
}

// AddonMetadata contains basic information about the addon
type AddonMetadata struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Category    string            `json:"category"`
	Author      string            `json:"author"`
	Version     string            `json:"version"`
	Tags        []string          `json:"tags"`
	Icon        string            `json:"icon"`
	IsPublic    bool              `json:"isPublic"`
	CreatedAt   time.Time         `json:"createdAt"`
	UpdatedAt   time.Time         `json:"updatedAt"`
	Properties  map[string]string `json:"properties"`
}

// WorkflowGraph represents the visual node-based workflow
type WorkflowGraph struct {
	Nodes       map[string]WorkflowNode     `json:"nodes"`
	Connections []WorkflowConnection        `json:"connections"`
	Variables   map[string]WorkflowVariable `json:"variables"`
	Triggers    []WorkflowTrigger           `json:"triggers"`
	Layout      WorkflowLayout              `json:"layout"`
}

// WorkflowNode represents a single node in the workflow
type WorkflowNode struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"`
	Label      string                 `json:"label"`
	Position   NodePosition           `json:"position"`
	Properties map[string]interface{} `json:"properties"`
	Inputs     []NodeSocket           `json:"inputs"`
	Outputs    []NodeSocket           `json:"outputs"`
	Config     NodeConfig             `json:"config"`
	Validation NodeValidation         `json:"validation"`
}

// NodePosition represents the visual position of a node
type NodePosition struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
	Z int     `json:"z"`
}

// NodeSocket represents an input or output connection point
type NodeSocket struct {
	ID       string      `json:"id"`
	Name     string      `json:"name"`
	Type     string      `json:"type"`
	Required bool        `json:"required"`
	Default  interface{} `json:"default"`
}

// NodeConfig contains node-specific configuration
type NodeConfig struct {
	Timeout     int                    `json:"timeout"`
	Retries     int                    `json:"retries"`
	Async       bool                   `json:"async"`
	Cache       bool                   `json:"cache"`
	CacheTTL    int                    `json:"cacheTTL"`
	Conditions  []string               `json:"conditions"`
	Environment map[string]interface{} `json:"environment"`
}

// NodeValidation contains validation rules for the node
type NodeValidation struct {
	Required []string               `json:"required"`
	Rules    map[string]interface{} `json:"rules"`
	Messages map[string]string      `json:"messages"`
}

// WorkflowConnection represents a connection between nodes
type WorkflowConnection struct {
	ID           string                 `json:"id"`
	FromNode     string                 `json:"fromNode"`
	FromOutput   string                 `json:"fromOutput"`
	ToNode       string                 `json:"toNode"`
	ToInput      string                 `json:"toInput"`
	Properties   map[string]interface{} `json:"properties"`
	Conditions   []string               `json:"conditions"`
	Transformers []DataTransformer      `json:"transformers"`
}

// DataTransformer represents data transformation rules
type DataTransformer struct {
	Type       string                 `json:"type"`
	Expression string                 `json:"expression"`
	Parameters map[string]interface{} `json:"parameters"`
}

// WorkflowVariable represents a workflow-level variable
type WorkflowVariable struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"`
	Default     interface{} `json:"default"`
	Description string      `json:"description"`
	Required    bool        `json:"required"`
	Scope       string      `json:"scope"` // global, node, connection
}

// WorkflowTrigger represents an event that can start the workflow
type WorkflowTrigger struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"` // webhook, schedule, event, manual
	Config     map[string]interface{} `json:"config"`
	Conditions []string               `json:"conditions"`
	Active     bool                   `json:"active"`
}

// WorkflowLayout contains visual layout information
type WorkflowLayout struct {
	Zoom       float64                `json:"zoom"`
	Pan        NodePosition           `json:"pan"`
	Grid       bool                   `json:"grid"`
	Snap       bool                   `json:"snap"`
	Theme      string                 `json:"theme"`
	Minimap    bool                   `json:"minimap"`
	Properties map[string]interface{} `json:"properties"`
}

// AddonSettings contains runtime settings for the addon
type AddonSettings struct {
	Permissions   []string               `json:"permissions"`
	Dependencies  []AddonDependency      `json:"dependencies"`
	Resources     ResourceRequirements   `json:"resources"`
	Security      SecuritySettings       `json:"security"`
	Compatibility CompatibilitySettings  `json:"compatibility"`
	Deployment    DeploymentSettings     `json:"deployment"`
	Monitoring    MonitoringSettings     `json:"monitoring"`
	Configuration map[string]interface{} `json:"configuration"`
}

// AddonDependency represents a dependency on another addon or service
type AddonDependency struct {
	Name     string `json:"name"`
	Version  string `json:"version"`
	Type     string `json:"type"` // addon, service, library
	Required bool   `json:"required"`
	Source   string `json:"source"`
}

// ResourceRequirements specifies resource limits
type ResourceRequirements struct {
	Memory    string `json:"memory"`
	CPU       string `json:"cpu"`
	Storage   string `json:"storage"`
	Bandwidth string `json:"bandwidth"`
	Timeout   int    `json:"timeout"`
}

// SecuritySettings contains security configuration
type SecuritySettings struct {
	Sandbox        bool     `json:"sandbox"`
	AllowedDomains []string `json:"allowedDomains"`
	RequiredScopes []string `json:"requiredScopes"`
	EncryptData    bool     `json:"encryptData"`
	AuditLog       bool     `json:"auditLog"`
	RateLimiting   bool     `json:"rateLimiting"`
	MaxRequests    int      `json:"maxRequests"`
	TimeWindow     int      `json:"timeWindow"`
}

// CompatibilitySettings defines compatibility requirements
type CompatibilitySettings struct {
	MinPlatformVersion string   `json:"minPlatformVersion"`
	SupportedBrowsers  []string `json:"supportedBrowsers"`
	RequiredFeatures   []string `json:"requiredFeatures"`
	ConflictsWith      []string `json:"conflictsWith"`
}

// DeploymentSettings contains deployment configuration
type DeploymentSettings struct {
	Environment    string                 `json:"environment"`
	AutoDeploy     bool                   `json:"autoDeploy"`
	Rollback       bool                   `json:"rollback"`
	HealthCheck    string                 `json:"healthCheck"`
	StartupProbe   string                 `json:"startupProbe"`
	ReadinessProbe string                 `json:"readinessProbe"`
	Scaling        ScalingSettings        `json:"scaling"`
	Variables      map[string]interface{} `json:"variables"`
}

// ScalingSettings defines auto-scaling parameters
type ScalingSettings struct {
	MinInstances int `json:"minInstances"`
	MaxInstances int `json:"maxInstances"`
	TargetCPU    int `json:"targetCPU"`
	TargetMemory int `json:"targetMemory"`
}

// MonitoringSettings contains monitoring configuration
type MonitoringSettings struct {
	Enabled   bool     `json:"enabled"`
	Metrics   []string `json:"metrics"`
	Alerts    []string `json:"alerts"`
	LogLevel  string   `json:"logLevel"`
	Tracing   bool     `json:"tracing"`
	Profiling bool     `json:"profiling"`
	Dashboard bool     `json:"dashboard"`
}

// AddonSerializer handles serialization and deserialization of addon configurations
type AddonSerializer struct {
	version string
}

// NewAddonSerializer creates a new addon serializer
func NewAddonSerializer() *AddonSerializer {
	return &AddonSerializer{
		version: "1.0.0",
	}
}

// Serialize converts an addon configuration to JSON
func (s *AddonSerializer) Serialize(config *AddonConfiguration) ([]byte, error) {
	if config == nil {
		return nil, fmt.Errorf("addon configuration is nil")
	}

	// Validate configuration before serialization
	if err := s.ValidateConfiguration(config); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	// Set serialization version
	config.Version = s.version

	// Serialize to JSON with proper formatting
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to serialize configuration: %w", err)
	}

	return data, nil
}

// Deserialize converts JSON data to an addon configuration
func (s *AddonSerializer) Deserialize(data []byte) (*AddonConfiguration, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("empty configuration data")
	}

	var config AddonConfiguration
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to deserialize configuration: %w", err)
	}

	// Validate deserialized configuration
	if err := s.ValidateConfiguration(&config); err != nil {
		return nil, fmt.Errorf("deserialized configuration validation failed: %w", err)
	}

	// Check version compatibility
	if err := s.CheckVersionCompatibility(config.Version); err != nil {
		return nil, fmt.Errorf("version compatibility check failed: %w", err)
	}

	return &config, nil
}

// ValidateConfiguration validates an addon configuration
func (s *AddonSerializer) ValidateConfiguration(config *AddonConfiguration) error {
	if config == nil {
		return fmt.Errorf("configuration is nil")
	}

	// Validate metadata
	if err := s.validateMetadata(&config.Metadata); err != nil {
		return fmt.Errorf("metadata validation failed: %w", err)
	}

	// Validate workflow
	if err := s.validateWorkflow(&config.Workflow); err != nil {
		return fmt.Errorf("workflow validation failed: %w", err)
	}

	// Validate settings
	if err := s.validateSettings(&config.Settings); err != nil {
		return fmt.Errorf("settings validation failed: %w", err)
	}

	return nil
}

// validateMetadata validates addon metadata
func (s *AddonSerializer) validateMetadata(metadata *AddonMetadata) error {
	if metadata.Name == "" {
		return fmt.Errorf("addon name is required")
	}

	if metadata.Version == "" {
		return fmt.Errorf("addon version is required")
	}

	if metadata.Category == "" {
		return fmt.Errorf("addon category is required")
	}

	// Validate version format (semantic versioning)
	if !isValidSemanticVersion(metadata.Version) {
		return fmt.Errorf("invalid version format: %s", metadata.Version)
	}

	return nil
}

// validateWorkflow validates the workflow graph
func (s *AddonSerializer) validateWorkflow(workflow *WorkflowGraph) error {
	if len(workflow.Nodes) == 0 {
		return fmt.Errorf("workflow must contain at least one node")
	}

	// Validate nodes
	for nodeID, node := range workflow.Nodes {
		if err := s.validateNode(nodeID, &node); err != nil {
			return fmt.Errorf("node %s validation failed: %w", nodeID, err)
		}
	}

	// Validate connections
	for i, conn := range workflow.Connections {
		if err := s.validateConnection(i, &conn, workflow.Nodes); err != nil {
			return fmt.Errorf("connection %d validation failed: %w", i, err)
		}
	}

	// Check for cycles in the workflow
	if s.hasCycles(workflow) {
		return fmt.Errorf("workflow contains cycles")
	}

	return nil
}

// validateNode validates a workflow node
func (s *AddonSerializer) validateNode(nodeID string, node *WorkflowNode) error {
	if node.ID != nodeID {
		return fmt.Errorf("node ID mismatch: expected %s, got %s", nodeID, node.ID)
	}

	if node.Type == "" {
		return fmt.Errorf("node type is required")
	}

	if node.Label == "" {
		return fmt.Errorf("node label is required")
	}

	return nil
}

// validateConnection validates a workflow connection
func (s *AddonSerializer) validateConnection(index int, conn *WorkflowConnection, nodes map[string]WorkflowNode) error {
	if conn.FromNode == "" || conn.ToNode == "" {
		return fmt.Errorf("connection must specify both from and to nodes")
	}

	// Check if referenced nodes exist
	if _, exists := nodes[conn.FromNode]; !exists {
		return fmt.Errorf("from node %s does not exist", conn.FromNode)
	}

	if _, exists := nodes[conn.ToNode]; !exists {
		return fmt.Errorf("to node %s does not exist", conn.ToNode)
	}

	return nil
}

// validateSettings validates addon settings
func (s *AddonSerializer) validateSettings(settings *AddonSettings) error {
	// Validate resource requirements
	if settings.Resources.Memory == "" {
		settings.Resources.Memory = "128Mi" // Default
	}

	if settings.Resources.CPU == "" {
		settings.Resources.CPU = "100m" // Default
	}

	return nil
}

// CheckVersionCompatibility checks if the configuration version is compatible
func (s *AddonSerializer) CheckVersionCompatibility(version string) error {
	if version == "" {
		return fmt.Errorf("version is required")
	}

	// For now, we only support version 1.0.0
	if version != "1.0.0" {
		return fmt.Errorf("unsupported version: %s", version)
	}

	return nil
}

// hasCycles checks if the workflow graph contains cycles
func (s *AddonSerializer) hasCycles(workflow *WorkflowGraph) bool {
	// Build adjacency list
	graph := make(map[string][]string)
	for _, conn := range workflow.Connections {
		graph[conn.FromNode] = append(graph[conn.FromNode], conn.ToNode)
	}

	// Use DFS to detect cycles
	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	for nodeID := range workflow.Nodes {
		if !visited[nodeID] {
			if s.dfsHasCycle(nodeID, graph, visited, recStack) {
				return true
			}
		}
	}

	return false
}

// dfsHasCycle performs DFS to detect cycles
func (s *AddonSerializer) dfsHasCycle(nodeID string, graph map[string][]string, visited, recStack map[string]bool) bool {
	visited[nodeID] = true
	recStack[nodeID] = true

	for _, neighbor := range graph[nodeID] {
		if !visited[neighbor] {
			if s.dfsHasCycle(neighbor, graph, visited, recStack) {
				return true
			}
		} else if recStack[neighbor] {
			return true
		}
	}

	recStack[nodeID] = false
	return false
}

// isValidSemanticVersion checks if a version string follows semantic versioning
func isValidSemanticVersion(version string) bool {
	// Simple regex for semantic versioning (major.minor.patch)
	// In a real implementation, you might want to use a proper semver library
	if len(version) == 0 {
		return false
	}

	// Basic check for x.y.z format
	parts := len(version)
	return parts > 4 && version[0] >= '0' && version[0] <= '9'
}

// SerializeToFile saves an addon configuration to a file
func (s *AddonSerializer) SerializeToFile(config *AddonConfiguration, filePath string) error {
	data, err := s.Serialize(config)
	if err != nil {
		return err
	}

	// In a real implementation, you would write to file
	// For now, we'll just return success
	_ = filePath
	_ = data

	return nil
}

// DeserializeFromFile loads an addon configuration from a file
func (s *AddonSerializer) DeserializeFromFile(filePath string) (*AddonConfiguration, error) {
	// In a real implementation, you would read from file
	// For now, we'll return an error
	_ = filePath
	return nil, fmt.Errorf("file operations not implemented in this context")
}
