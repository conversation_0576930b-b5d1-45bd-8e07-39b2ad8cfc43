-- Add invoices table for billing management
CREATE TABLE IF NOT EXISTS invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subscription_id UUID NOT NULL REFERENCES user_subscriptions(id),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'failed', 'canceled')),
    due_date TIMESTAMPTZ NOT NULL,
    paid_at TIMESTAMPTZ,
    stripe_invoice_id VARCHAR(255),
    download_url TEXT,
    tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_invoices_subscription_id ON invoices(subscription_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON invoices(due_date);
CREATE INDEX IF NOT EXISTS idx_invoices_tenant_id ON invoices(tenant_id);

-- Update subscription_tiers table to add missing columns
ALTER TABLE subscription_tiers ADD COLUMN IF NOT EXISTS is_active BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE subscription_tiers ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';

-- Update user_subscriptions table to add missing columns
ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';

-- Update usage_tracking table to add missing columns
ALTER TABLE usage_tracking ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';

-- Add indexes for tenant_id columns
CREATE INDEX IF NOT EXISTS idx_subscription_tiers_tenant_id ON subscription_tiers(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_tenant_id ON user_subscriptions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_tenant_id ON usage_tracking(tenant_id);