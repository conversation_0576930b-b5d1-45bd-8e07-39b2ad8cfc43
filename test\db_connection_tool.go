package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"github.com/jackc/pgx/v5"
	"github.com/joho/godotenv"
)

func main() {
	// Get absolute path to .env file in root directory
	envPath := filepath.Join("..", ".env")
	absPath, err := filepath.Abs(envPath)
	if err != nil {
		log.Fatalf("Error getting absolute path: %v", err)
	}

	// Load environment variables from .env file
	err = godotenv.Load(absPath)
	if err != nil {
		log.Fatalf("Error loading .env file: %v", err)
	}

	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	dbname := os.Getenv("DB_NAME")
	user := os.Getenv("DB_USER")
	password := os.Getenv("DB_PASSWORD")

	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	fmt.Printf("Testing connection with:\n%s\n", connStr)

	conn, err := pgx.Connect(context.Background(), connStr)
	if err != nil {
		fmt.Printf("Connection failed: %v\n", err)
		os.Exit(1)
	}
	defer conn.Close(context.Background())

	fmt.Println("Successfully connected to database!")
}
