package addon

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// InstallationSystem handles addon installation and management
type InstallationSystem struct {
	marketplace       *Marketplace
	versioningSystem  *VersioningSystem
	deploymentSystem  *DeploymentSystem
	userInstallations map[string][]*Installation
	installationQueue []*InstallationRequest
	mutex             sync.RWMutex
	config            InstallationConfig
}

// InstallationConfig contains installation system configuration
type InstallationConfig struct {
	MaxConcurrentInstalls int           `json:"maxConcurrentInstalls"`
	InstallTimeout        time.Duration `json:"installTimeout"`
	AutoUpdateEnabled     bool          `json:"autoUpdateEnabled"`
	SandboxInstalls       bool          `json:"sandboxInstalls"`
	RequireApproval       bool          `json:"requireApproval"`
}

// Installation represents an installed addon
type Installation struct {
	ID            string                 `json:"id"`
	AddonID       string                 `json:"addonId"`
	UserID        string                 `json:"userId"`
	Version       string                 `json:"version"`
	Status        InstallationStatus     `json:"status"`
	InstallType   InstallationType       `json:"installType"`
	InstalledAt   time.Time              `json:"installedAt"`
	LastUsed      *time.Time             `json:"lastUsed,omitempty"`
	UpdatedAt     time.Time              `json:"updatedAt"`
	Configuration map[string]interface{} `json:"configuration"`
	Permissions   []string               `json:"permissions"`
	Settings      InstallationSettings   `json:"settings"`
	Usage         InstallationUsage      `json:"usage"`
	Health        InstallationHealth     `json:"health"`
	Context       map[string]interface{} `json:"context"`
}

// InstallationStatus represents the status of an installation
type InstallationStatus string

const (
	InstallationStatusPending      InstallationStatus = "pending"
	InstallationStatusInstalling   InstallationStatus = "installing"
	InstallationStatusActive       InstallationStatus = "active"
	InstallationStatusInactive     InstallationStatus = "inactive"
	InstallationStatusUpdating     InstallationStatus = "updating"
	InstallationStatusUninstalling InstallationStatus = "uninstalling"
	InstallationStatusFailed       InstallationStatus = "failed"
	InstallationStatusSuspended    InstallationStatus = "suspended"
)

// InstallationType represents the type of installation
type InstallationType string

const (
	InstallationTypeUser      InstallationType = "user"
	InstallationTypeWorkspace InstallationType = "workspace"
	InstallationTypeGlobal    InstallationType = "global"
	InstallationTypeTrial     InstallationType = "trial"
)

// InstallationRequest represents a request to install an addon
type InstallationRequest struct {
	ID          string                 `json:"id"`
	AddonID     string                 `json:"addonId"`
	UserID      string                 `json:"userId"`
	Version     string                 `json:"version"`
	InstallType InstallationType       `json:"installType"`
	RequestedAt time.Time              `json:"requestedAt"`
	Priority    RequestPriority        `json:"priority"`
	Options     InstallationOptions    `json:"options"`
	Context     map[string]interface{} `json:"context"`
}

// RequestPriority represents the priority of an installation request
type RequestPriority string

const (
	RequestPriorityLow    RequestPriority = "low"
	RequestPriorityNormal RequestPriority = "normal"
	RequestPriorityHigh   RequestPriority = "high"
	RequestPriorityUrgent RequestPriority = "urgent"
)

// InstallationOptions contains options for installation
type InstallationOptions struct {
	AutoStart        bool                   `json:"autoStart"`
	Configuration    map[string]interface{} `json:"configuration"`
	Permissions      []string               `json:"permissions"`
	Environment      string                 `json:"environment"`
	SkipDependencies bool                   `json:"skipDependencies"`
	ForceInstall     bool                   `json:"forceInstall"`
	TrialMode        bool                   `json:"trialMode"`
	TrialDuration    time.Duration          `json:"trialDuration"`
}

// InstallationSettings contains installation-specific settings
type InstallationSettings struct {
	AutoUpdate     bool                   `json:"autoUpdate"`
	Notifications  bool                   `json:"notifications"`
	DataCollection bool                   `json:"dataCollection"`
	ErrorReporting bool                   `json:"errorReporting"`
	CustomSettings map[string]interface{} `json:"customSettings"`
}

// InstallationUsage tracks usage statistics
type InstallationUsage struct {
	TotalSessions    int64            `json:"totalSessions"`
	TotalDuration    time.Duration    `json:"totalDuration"`
	LastSession      time.Time        `json:"lastSession"`
	FeatureUsage     map[string]int64 `json:"featureUsage"`
	ErrorCount       int64            `json:"errorCount"`
	CrashCount       int64            `json:"crashCount"`
	PerformanceScore float64          `json:"performanceScore"`
}

// InstallationHealth represents the health status of an installation
type InstallationHealth struct {
	Status          HealthStatus       `json:"status"`
	LastCheck       time.Time          `json:"lastCheck"`
	Issues          []HealthIssue      `json:"issues"`
	Metrics         map[string]float64 `json:"metrics"`
	Recommendations []string           `json:"recommendations"`
}

// HealthStatus represents the health status
type HealthStatus string

const (
	HealthStatusHealthy  HealthStatus = "healthy"
	HealthStatusWarning  HealthStatus = "warning"
	HealthStatusCritical HealthStatus = "critical"
	HealthStatusUnknown  HealthStatus = "unknown"
)

// HealthIssue represents a health issue
type HealthIssue struct {
	Type        string    `json:"type"`
	Severity    string    `json:"severity"`
	Description string    `json:"description"`
	Solution    string    `json:"solution"`
	DetectedAt  time.Time `json:"detectedAt"`
}

// InstallationResult contains the result of an installation
type InstallationResult struct {
	Success      bool                   `json:"success"`
	Installation *Installation          `json:"installation,omitempty"`
	Error        string                 `json:"error,omitempty"`
	Warnings     []string               `json:"warnings"`
	Duration     time.Duration          `json:"duration"`
	Steps        []InstallationStep     `json:"steps"`
	Context      map[string]interface{} `json:"context"`
}

// InstallationStep represents a step in the installation process
type InstallationStep struct {
	Name        string        `json:"name"`
	Status      string        `json:"status"`
	StartedAt   time.Time     `json:"startedAt"`
	CompletedAt *time.Time    `json:"completedAt,omitempty"`
	Duration    time.Duration `json:"duration"`
	Output      string        `json:"output"`
	Error       string        `json:"error,omitempty"`
}

// NewInstallationSystem creates a new installation system
func NewInstallationSystem(config InstallationConfig) *InstallationSystem {
	if config.MaxConcurrentInstalls == 0 {
		config.MaxConcurrentInstalls = 5
	}
	if config.InstallTimeout == 0 {
		config.InstallTimeout = 10 * time.Minute
	}

	return &InstallationSystem{
		marketplace:       NewMarketplace(MarketplaceConfig{}),
		versioningSystem:  NewVersioningSystem(VersioningConfig{}),
		deploymentSystem:  NewDeploymentSystem(DeploymentConfig{}),
		userInstallations: make(map[string][]*Installation),
		installationQueue: []*InstallationRequest{},
		config:            config,
	}
}

// RequestInstallation requests installation of an addon
func (is *InstallationSystem) RequestInstallation(ctx context.Context, addonID, userID, version string, installType InstallationType, options InstallationOptions) (*InstallationRequest, error) {
	is.mutex.Lock()
	defer is.mutex.Unlock()

	// Validate addon exists
	addon, exists := is.marketplace.addons[addonID]
	if !exists {
		return nil, fmt.Errorf("addon not found: %s", addonID)
	}

	// Check if already installed
	if is.isAddonInstalled(userID, addonID) && !options.ForceInstall {
		return nil, fmt.Errorf("addon already installed")
	}

	// Validate version
	if version == "" {
		version = addon.Version // Use latest version
	}

	// Create installation request
	request := &InstallationRequest{
		ID:          generateInstallationRequestID(),
		AddonID:     addonID,
		UserID:      userID,
		Version:     version,
		InstallType: installType,
		RequestedAt: time.Now(),
		Priority:    RequestPriorityNormal,
		Options:     options,
		Context:     make(map[string]interface{}),
	}

	// Add to queue
	is.installationQueue = append(is.installationQueue, request)

	// Process installation if not at capacity
	if len(is.getActiveInstallations()) < is.config.MaxConcurrentInstalls {
		go is.processInstallation(ctx, request)
	}

	return request, nil
}

// InstallAddon installs an addon directly
func (is *InstallationSystem) InstallAddon(ctx context.Context, addonID, userID, version string, options InstallationOptions) (*InstallationResult, error) {
	request, err := is.RequestInstallation(ctx, addonID, userID, version, InstallationTypeUser, options)
	if err != nil {
		return nil, err
	}

	return is.executeInstallation(ctx, request)
}

// executeInstallation executes the installation process
func (is *InstallationSystem) executeInstallation(ctx context.Context, request *InstallationRequest) (*InstallationResult, error) {
	startTime := time.Now()

	result := &InstallationResult{
		Success:  true,
		Warnings: []string{},
		Steps:    []InstallationStep{},
		Context:  make(map[string]interface{}),
	}

	// Create installation record
	installation := &Installation{
		ID:            generateInstallationID(),
		AddonID:       request.AddonID,
		UserID:        request.UserID,
		Version:       request.Version,
		Status:        InstallationStatusInstalling,
		InstallType:   request.InstallType,
		InstalledAt:   time.Now(),
		UpdatedAt:     time.Now(),
		Configuration: request.Options.Configuration,
		Permissions:   request.Options.Permissions,
		Settings: InstallationSettings{
			AutoUpdate:     true,
			Notifications:  true,
			DataCollection: false,
			ErrorReporting: true,
			CustomSettings: make(map[string]interface{}),
		},
		Usage: InstallationUsage{
			FeatureUsage: make(map[string]int64),
		},
		Health: InstallationHealth{
			Status:    HealthStatusUnknown,
			LastCheck: time.Now(),
			Issues:    []HealthIssue{},
			Metrics:   make(map[string]float64),
		},
		Context: make(map[string]interface{}),
	}

	// Execute installation steps
	steps := []string{
		"validate_addon",
		"check_dependencies",
		"download_addon",
		"verify_integrity",
		"install_files",
		"configure_addon",
		"register_addon",
		"start_addon",
	}

	for _, stepName := range steps {
		step := InstallationStep{
			Name:      stepName,
			Status:    "running",
			StartedAt: time.Now(),
		}

		err := is.executeInstallationStep(ctx, installation, stepName)

		now := time.Now()
		step.CompletedAt = &now
		step.Duration = time.Since(step.StartedAt)

		if err != nil {
			step.Status = "failed"
			step.Error = err.Error()
			result.Success = false
			result.Error = fmt.Sprintf("Step %s failed: %s", stepName, err.Error())
		} else {
			step.Status = "completed"
			step.Output = fmt.Sprintf("Step %s completed successfully", stepName)
		}

		result.Steps = append(result.Steps, step)

		if !result.Success {
			break
		}
	}

	result.Duration = time.Since(startTime)

	if result.Success {
		// Complete installation
		installation.Status = InstallationStatusActive
		installation.Health.Status = HealthStatusHealthy

		// Add to user installations
		is.mutex.Lock()
		is.userInstallations[request.UserID] = append(is.userInstallations[request.UserID], installation)
		is.mutex.Unlock()

		result.Installation = installation
	} else {
		// Mark as failed
		installation.Status = InstallationStatusFailed
		installation.Health.Status = HealthStatusCritical
	}

	return result, nil
}

// executeInstallationStep executes a single installation step
func (is *InstallationSystem) executeInstallationStep(ctx context.Context, installation *Installation, stepName string) error {
	switch stepName {
	case "validate_addon":
		return is.validateAddon(installation.AddonID)
	case "check_dependencies":
		return is.checkDependencies(installation.AddonID)
	case "download_addon":
		return is.downloadAddon(installation.AddonID, installation.Version)
	case "verify_integrity":
		return is.verifyIntegrity(installation.AddonID, installation.Version)
	case "install_files":
		return is.installFiles(installation)
	case "configure_addon":
		return is.configureAddon(installation)
	case "register_addon":
		return is.registerAddon(installation)
	case "start_addon":
		return is.startAddon(installation)
	default:
		return fmt.Errorf("unknown installation step: %s", stepName)
	}
}

// UninstallAddon uninstalls an addon
func (is *InstallationSystem) UninstallAddon(ctx context.Context, userID, addonID string) error {
	is.mutex.Lock()
	defer is.mutex.Unlock()

	installations := is.userInstallations[userID]
	for i, installation := range installations {
		if installation.AddonID == addonID {
			// Mark as uninstalling
			installation.Status = InstallationStatusUninstalling

			// Execute uninstallation steps
			err := is.executeUninstallation(ctx, installation)
			if err != nil {
				installation.Status = InstallationStatusFailed
				return err
			}

			// Remove from installations
			is.userInstallations[userID] = append(installations[:i], installations[i+1:]...)
			return nil
		}
	}

	return fmt.Errorf("addon not installed: %s", addonID)
}

// GetUserInstallations returns all installations for a user
func (is *InstallationSystem) GetUserInstallations(userID string) ([]*Installation, error) {
	is.mutex.RLock()
	defer is.mutex.RUnlock()

	installations, exists := is.userInstallations[userID]
	if !exists {
		return []*Installation{}, nil
	}

	return installations, nil
}

// UpdateAddon updates an installed addon to a new version
func (is *InstallationSystem) UpdateAddon(ctx context.Context, userID, addonID, newVersion string) (*InstallationResult, error) {
	is.mutex.Lock()
	defer is.mutex.Unlock()

	installations := is.userInstallations[userID]
	for _, installation := range installations {
		if installation.AddonID == addonID {
			if installation.Version == newVersion {
				return nil, fmt.Errorf("addon already at version %s", newVersion)
			}

			// Mark as updating
			installation.Status = InstallationStatusUpdating

			// Execute update
			result := is.executeUpdate(ctx, installation, newVersion)

			if result.Success {
				installation.Version = newVersion
				installation.Status = InstallationStatusActive
				installation.UpdatedAt = time.Now()
			} else {
				installation.Status = InstallationStatusFailed
			}

			return result, nil
		}
	}

	return nil, fmt.Errorf("addon not installed: %s", addonID)
}

// Helper methods

func (is *InstallationSystem) processInstallation(ctx context.Context, request *InstallationRequest) {
	_, err := is.executeInstallation(ctx, request)
	if err != nil {
		// Handle installation failure
		fmt.Printf("Installation failed for request %s: %v\n", request.ID, err)
	}

	// Remove from queue
	is.mutex.Lock()
	for i, req := range is.installationQueue {
		if req.ID == request.ID {
			is.installationQueue = append(is.installationQueue[:i], is.installationQueue[i+1:]...)
			break
		}
	}
	is.mutex.Unlock()
}

func (is *InstallationSystem) isAddonInstalled(userID, addonID string) bool {
	installations, exists := is.userInstallations[userID]
	if !exists {
		return false
	}

	for _, installation := range installations {
		if installation.AddonID == addonID && installation.Status == InstallationStatusActive {
			return true
		}
	}

	return false
}

func (is *InstallationSystem) getActiveInstallations() []*Installation {
	var active []*Installation
	for _, installations := range is.userInstallations {
		for _, installation := range installations {
			if installation.Status == InstallationStatusInstalling {
				active = append(active, installation)
			}
		}
	}
	return active
}

// Installation step implementations

func (is *InstallationSystem) validateAddon(addonID string) error {
	addon, exists := is.marketplace.addons[addonID]
	if !exists {
		return fmt.Errorf("addon not found")
	}

	if addon.Status != AddonStatusPublished {
		return fmt.Errorf("addon not published")
	}

	return nil
}

func (is *InstallationSystem) checkDependencies(addonID string) error {
	addon := is.marketplace.addons[addonID]

	for _, dep := range addon.Dependencies {
		// Check if dependency is available
		if _, exists := is.marketplace.addons[dep]; !exists {
			return fmt.Errorf("dependency not found: %s", dep)
		}
	}

	return nil
}

func (is *InstallationSystem) downloadAddon(addonID, version string) error {
	// Simulate download
	time.Sleep(time.Millisecond * 500)
	return nil
}

func (is *InstallationSystem) verifyIntegrity(addonID, version string) error {
	// Simulate integrity check
	time.Sleep(time.Millisecond * 200)
	return nil
}

func (is *InstallationSystem) installFiles(installation *Installation) error {
	// Simulate file installation
	time.Sleep(time.Millisecond * 800)
	return nil
}

func (is *InstallationSystem) configureAddon(installation *Installation) error {
	// Apply configuration
	time.Sleep(time.Millisecond * 300)
	return nil
}

func (is *InstallationSystem) registerAddon(installation *Installation) error {
	// Register addon in system
	time.Sleep(time.Millisecond * 100)
	return nil
}

func (is *InstallationSystem) startAddon(installation *Installation) error {
	// Start addon if auto-start is enabled
	time.Sleep(time.Millisecond * 200)
	return nil
}

func (is *InstallationSystem) executeUninstallation(ctx context.Context, installation *Installation) error {
	// Execute uninstallation steps
	steps := []string{"stop_addon", "remove_files", "cleanup_config", "unregister_addon"}

	for range steps {
		time.Sleep(time.Millisecond * 100) // Simulate step execution
	}

	return nil
}

func (is *InstallationSystem) executeUpdate(ctx context.Context, installation *Installation, newVersion string) *InstallationResult {
	// Simulate update process
	result := &InstallationResult{
		Success:  true,
		Duration: time.Second * 2,
		Steps:    []InstallationStep{},
		Context:  make(map[string]interface{}),
	}

	return result
}

func generateInstallationRequestID() string {
	return fmt.Sprintf("install_req_%d", time.Now().UnixNano())
}

func generateInstallationID() string {
	return fmt.Sprintf("install_%d", time.Now().UnixNano())
}
