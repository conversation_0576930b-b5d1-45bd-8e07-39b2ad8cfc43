-- Drop triggers
DROP TRIGGER IF EXISTS update_conversations_updated_at ON conversations;
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
DROP TRIGGER IF EXISTS update_user_settings_updated_at ON user_settings;

-- Drop indexes
DROP INDEX IF EXISTS idx_conversations_tenant_id;
DROP INDEX IF EXISTS idx_conversations_created_by;
DROP INDEX IF EXISTS idx_conversations_last_message_at;

DROP INDEX IF EXISTS idx_conversation_participants_conversation_id;
DROP INDEX IF EXISTS idx_conversation_participants_user_id;

DROP INDEX IF EXISTS idx_messages_conversation_id;
DROP INDEX IF EXISTS idx_messages_sender_id;
DROP INDEX IF EXISTS idx_messages_created_at;
DROP INDEX IF EXISTS idx_messages_reply_to_id;

DROP INDEX IF EXISTS idx_message_read_status_message_id;
DROP INDEX IF EXISTS idx_message_read_status_user_id;

DROP INDEX IF EXISTS idx_publishing_history_site_id;
DROP INDEX IF EXISTS idx_publishing_history_tenant_id;
DROP INDEX IF EXISTS idx_publishing_history_published_by;
DROP INDEX IF EXISTS idx_publishing_history_published_at;

DROP INDEX IF EXISTS idx_user_profiles_user_id;
DROP INDEX IF EXISTS idx_user_settings_user_id;

DROP INDEX IF EXISTS idx_activity_items_user_id;
DROP INDEX IF EXISTS idx_activity_items_tenant_id;
DROP INDEX IF EXISTS idx_activity_items_created_at;
DROP INDEX IF EXISTS idx_activity_items_type;

-- Drop tables
DROP TABLE IF EXISTS message_read_status;
DROP TABLE IF EXISTS messages;
DROP TABLE IF EXISTS conversation_participants;
DROP TABLE IF EXISTS conversations;
DROP TABLE IF EXISTS publishing_history;
DROP TABLE IF EXISTS user_profiles;
DROP TABLE IF EXISTS user_settings;
DROP TABLE IF EXISTS activity_items;