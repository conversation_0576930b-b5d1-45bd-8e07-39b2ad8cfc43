<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Addon.Name}} - Addon Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-lightning-charge"></i>
                VelocityWave
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/marketplace">
                    <i class="bi bi-arrow-left"></i> Back to Marketplace
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Addon Header -->
        <div class="row">
            <div class="col-lg-8">
                <div class="d-flex align-items-start mb-4">
                    <img src="{{.Addon.Icon}}" alt="{{.Addon.Name}}" class="me-4" style="width: 80px; height: 80px;">
                    <div class="flex-grow-1">
                        <h1 class="h2 mb-2">{{.Addon.Name}}</h1>
                        <p class="text-muted mb-2">{{.Addon.ShortDescription}}</p>
                        <div class="d-flex align-items-center mb-2">
                            <div class="rating me-3">
                                <div id="addon-rating" data-rating="{{.Addon.Metrics.Rating}}"></div>
                                <small class="text-muted">({{.Addon.Metrics.ReviewCount}} reviews)</small>
                            </div>
                            <span class="badge bg-primary me-2">{{.Addon.Category}}</span>
                            <span class="text-muted">by {{.Addon.Author}}</span>
                        </div>
                        <div class="d-flex align-items-center text-muted small">
                            <i class="bi bi-download me-1"></i>
                            <span class="me-3">{{.Stats.TotalDownloads}} downloads</span>
                            <i class="bi bi-people me-1"></i>
                            <span class="me-3">{{.Stats.ActiveInstalls}} active installs</span>
                            <i class="bi bi-clock me-1"></i>
                            <span>Updated {{.Addon.UpdatedAt}}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <!-- Install Card -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                {{if eq .Addon.Pricing.Model "free"}}
                                    <h4 class="text-success mb-0">Free</h4>
                                {{else}}
                                    <h4 class="mb-0">${{.Addon.Pricing.Price}}</h4>
                                    <small class="text-muted">{{.Addon.Pricing.Period}}</small>
                                {{end}}
                            </div>
                            <div class="text-end">
                                <small class="text-muted">Version {{.Addon.Version}}</small>
                            </div>
                        </div>
                        
                        <button class="btn btn-primary w-100 mb-2" onclick="installAddon('{{.Addon.ID}}')">
                            <i class="bi bi-download"></i> Install Addon
                        </button>
                        
                        {{if .Addon.DemoURL}}
                        <button class="btn btn-outline-secondary w-100 mb-2" onclick="openDemo('{{.Addon.DemoURL}}')">
                            <i class="bi bi-play-circle"></i> Try Demo
                        </button>
                        {{end}}
                        
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-secondary flex-fill" onclick="addToWishlist('{{.Addon.ID}}')">
                                <i class="bi bi-heart"></i>
                            </button>
                            <button class="btn btn-outline-secondary flex-fill" onclick="shareAddon('{{.Addon.ID}}')">
                                <i class="bi bi-share"></i>
                            </button>
                            <button class="btn btn-outline-secondary flex-fill" onclick="reportAddon('{{.Addon.ID}}')">
                                <i class="bi bi-flag"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">Quick Stats</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 border-end">
                                <div class="h5 mb-0">{{.Stats.WeeklyDownloads}}</div>
                                <small class="text-muted">This Week</small>
                            </div>
                            <div class="col-6">
                                <div class="h5 mb-0">{{.Stats.MonthlyDownloads}}</div>
                                <small class="text-muted">This Month</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Screenshots -->
        {{if .Screenshots}}
        <div class="mb-4">
            <h5 class="mb-3">Screenshots</h5>
            <div class="row">
                {{range .Screenshots}}
                <div class="col-md-4 mb-3">
                    <img src="{{.ThumbnailURL}}" alt="{{.Caption}}" class="img-fluid rounded screenshot-thumb" 
                         onclick="openScreenshot('{{.URL}}')" style="cursor: pointer;">
                    {{if .Caption}}
                    <p class="small text-muted mt-1">{{.Caption}}</p>
                    {{end}}
                </div>
                {{end}}
            </div>
        </div>
        {{end}}

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="addonTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button">
                    <i class="bi bi-info-circle"></i> Overview
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button">
                    <i class="bi bi-star"></i> Reviews ({{.Addon.Metrics.ReviewCount}})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="changelog-tab" data-bs-toggle="tab" data-bs-target="#changelog" type="button">
                    <i class="bi bi-clock-history"></i> Changelog
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="support-tab" data-bs-toggle="tab" data-bs-target="#support" type="button">
                    <i class="bi bi-question-circle"></i> Support
                </button>
            </li>
        </ul>

        <div class="tab-content mt-4" id="addonTabsContent">
            <!-- Overview Tab -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Description -->
                        <div class="mb-4">
                            <h5>Description</h5>
                            <div class="addon-description">
                                {{.Addon.Description}}
                            </div>
                        </div>

                        <!-- Documentation -->
                        {{if .Documentation.README}}
                        <div class="mb-4">
                            <h5>Documentation</h5>
                            <div class="border rounded p-3 bg-light">
                                <div id="readme-content">{{.Documentation.README}}</div>
                            </div>
                        </div>
                        {{end}}

                        <!-- Features -->
                        <div class="mb-4">
                            <h5>Key Features</h5>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success me-2"></i>Easy to install and configure</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Seamless integration</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Regular updates and support</li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Addon Info -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">Addon Information</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <td>Version:</td>
                                        <td>{{.Addon.Version}}</td>
                                    </tr>
                                    <tr>
                                        <td>License:</td>
                                        <td>{{.Addon.License}}</td>
                                    </tr>
                                    <tr>
                                        <td>Category:</td>
                                        <td>{{.Addon.Category}}</td>
                                    </tr>
                                    <tr>
                                        <td>Published:</td>
                                        <td>{{.Addon.PublishedAt}}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Tags -->
                        {{if .Addon.Tags}}
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">Tags</h6>
                            </div>
                            <div class="card-body">
                                {{range .Addon.Tags}}
                                <span class="badge bg-light text-dark me-1 mb-1">{{.}}</span>
                                {{end}}
                            </div>
                        </div>
                        {{end}}

                        <!-- Dependencies -->
                        {{if .Dependencies}}
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">Dependencies</h6>
                            </div>
                            <div class="card-body">
                                {{range .Dependencies}}
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>{{.Name}}</span>
                                    <span class="badge bg-secondary">{{.Version}}</span>
                                </div>
                                {{end}}
                            </div>
                        </div>
                        {{end}}

                        <!-- Compatibility -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Compatibility</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>Platform:</strong> {{.Compatibility.PlatformVersions}}
                                </div>
                                <div class="mb-2">
                                    <strong>Browsers:</strong>
                                    {{range .Compatibility.BrowserSupport}}
                                    <span class="badge bg-light text-dark me-1">{{.Browser}}</span>
                                    {{end}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reviews Tab -->
            <div class="tab-pane fade" id="reviews" role="tabpanel">
                <div class="row">
                    <div class="col-lg-4">
                        <!-- Rating Summary -->
                        <div class="card mb-4">
                            <div class="card-body text-center">
                                <div class="display-4 fw-bold">{{.Addon.Metrics.Rating}}</div>
                                <div id="summary-rating" data-rating="{{.Addon.Metrics.Rating}}"></div>
                                <p class="text-muted">{{.Addon.Metrics.ReviewCount}} reviews</p>
                            </div>
                        </div>

                        <!-- Rating Breakdown -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Rating Breakdown</h6>
                            </div>
                            <div class="card-body">
                                {{range $rating, $count := .Stats.RatingBreakdown}}
                                <div class="d-flex align-items-center mb-2">
                                    <span class="me-2">{{$rating}} stars</span>
                                    <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                        <div class="progress-bar" style="width: {{$count}}%"></div>
                                    </div>
                                    <span class="small text-muted">{{$count}}</span>
                                </div>
                                {{end}}
                            </div>
                        </div>

                        <!-- Write Review -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Write a Review</h6>
                            </div>
                            <div class="card-body">
                                <form id="review-form">
                                    <div class="mb-3">
                                        <label class="form-label">Rating</label>
                                        <div id="review-rating-input"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Title</label>
                                        <input type="text" class="form-control" id="review-title" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Review</label>
                                        <textarea class="form-control" id="review-content" rows="4" required></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">Submit Review</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-8">
                        <!-- Reviews List -->
                        <div id="reviews-list">
                            {{range .Reviews}}
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        <img src="{{.UserAvatar}}" alt="{{.Username}}" class="rounded-circle me-3" style="width: 40px; height: 40px;">
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div>
                                                    <h6 class="mb-0">{{.Username}}</h6>
                                                    <div class="review-rating" data-rating="{{.Rating}}"></div>
                                                </div>
                                                <small class="text-muted">{{.CreatedAt}}</small>
                                            </div>
                                            {{if .Title}}
                                            <h6>{{.Title}}</h6>
                                            {{end}}
                                            <p class="mb-2">{{.Content}}</p>
                                            {{if .Pros}}
                                            <div class="mb-2">
                                                <strong class="text-success">Pros:</strong>
                                                <ul class="mb-0">
                                                    {{range .Pros}}
                                                    <li>{{.}}</li>
                                                    {{end}}
                                                </ul>
                                            </div>
                                            {{end}}
                                            {{if .Cons}}
                                            <div class="mb-2">
                                                <strong class="text-danger">Cons:</strong>
                                                <ul class="mb-0">
                                                    {{range .Cons}}
                                                    <li>{{.}}</li>
                                                    {{end}}
                                                </ul>
                                            </div>
                                            {{end}}
                                            <div class="d-flex align-items-center">
                                                <button class="btn btn-sm btn-outline-secondary me-2" onclick="markHelpful('{{.ID}}')">
                                                    <i class="bi bi-hand-thumbs-up"></i> Helpful ({{.Helpful}})
                                                </button>
                                                {{if .Verified}}
                                                <span class="badge bg-success">Verified Purchase</span>
                                                {{end}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{end}}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Changelog Tab -->
            <div class="tab-pane fade" id="changelog" role="tabpanel">
                {{range .Changelog}}
                <div class="card mb-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Version {{.Version}}</h6>
                            <div>
                                <span class="badge bg-{{if .Breaking}}danger{{else if .Security}}warning{{else}}primary{{end}}">{{.Type}}</span>
                                <small class="text-muted ms-2">{{.Date}}</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <p>{{.Description}}</p>
                        {{if .Changes}}
                        <ul class="list-unstyled">
                            {{range .Changes}}
                            <li class="mb-1">
                                <span class="badge bg-light text-dark me-2">{{.Type}}</span>
                                {{.Description}}
                            </li>
                            {{end}}
                        </ul>
                        {{end}}
                    </div>
                </div>
                {{end}}
            </div>

            <!-- Support Tab -->
            <div class="tab-pane fade" id="support" role="tabpanel">
                <div class="row">
                    <div class="col-lg-6">
                        <!-- Support Channels -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Get Support</h6>
                            </div>
                            <div class="card-body">
                                {{range .SupportInfo.SupportChannels}}
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-{{if eq .Type "email"}}envelope{{else if eq .Type "chat"}}chat{{else}}question-circle{{end}} me-3"></i>
                                    <div>
                                        <div class="fw-bold">{{.Type | title}}</div>
                                        <div class="text-muted small">{{.Contact}}</div>
                                        <div class="text-muted small">{{.Hours}}</div>
                                    </div>
                                </div>
                                {{end}}
                            </div>
                        </div>

                        <!-- FAQ -->
                        {{if .Documentation.FAQ}}
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Frequently Asked Questions</h6>
                            </div>
                            <div class="card-body">
                                <div class="accordion" id="faqAccordion">
                                    {{range $index, $faq := .Documentation.FAQ}}
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq{{$index}}">
                                                {{$faq.Question}}
                                            </button>
                                        </h2>
                                        <div id="faq{{$index}}" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                            <div class="accordion-body">
                                                {{$faq.Answer}}
                                            </div>
                                        </div>
                                    </div>
                                    {{end}}
                                </div>
                            </div>
                        </div>
                        {{end}}
                    </div>

                    <div class="col-lg-6">
                        <!-- Known Issues -->
                        {{if .SupportInfo.KnownIssues}}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Known Issues</h6>
                            </div>
                            <div class="card-body">
                                {{range .SupportInfo.KnownIssues}}
                                <div class="alert alert-{{if eq .Severity "high"}}danger{{else if eq .Severity "medium"}}warning{{else}}info{{end}} mb-2">
                                    <h6 class="alert-heading">{{.Title}}</h6>
                                    <p class="mb-1">{{.Description}}</p>
                                    {{if .Workaround}}
                                    <hr>
                                    <p class="mb-0"><strong>Workaround:</strong> {{.Workaround}}</p>
                                    {{end}}
                                </div>
                                {{end}}
                            </div>
                        </div>
                        {{end}}

                        <!-- Contact Form -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Contact Developer</h6>
                            </div>
                            <div class="card-body">
                                <form id="contact-form">
                                    <div class="mb-3">
                                        <label class="form-label">Subject</label>
                                        <select class="form-select" required>
                                            <option value="">Select a topic</option>
                                            <option value="bug">Bug Report</option>
                                            <option value="feature">Feature Request</option>
                                            <option value="support">General Support</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Message</label>
                                        <textarea class="form-control" rows="4" required></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">Send Message</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Addons -->
        {{if .RelatedAddons}}
        <div class="mt-5">
            <h5 class="mb-3">Related Addons</h5>
            <div class="row">
                {{range .RelatedAddons}}
                <div class="col-md-4 mb-3">
                    <div class="card h-100" onclick="window.location.href='/marketplace/addon/{{.ID}}'" style="cursor: pointer;">
                        <div class="card-body">
                            <div class="d-flex align-items-start">
                                <img src="{{.Icon}}" alt="{{.Name}}" class="me-3" style="width: 40px; height: 40px;">
                                <div>
                                    <h6 class="card-title">{{.Name}}</h6>
                                    <p class="card-text small text-muted">{{.ShortDescription}}</p>
                                    <div class="d-flex align-items-center">
                                        <div class="rating me-2" data-rating="{{.Metrics.Rating}}"></div>
                                        <small class="text-muted">({{.Metrics.ReviewCount}})</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{end}}
            </div>
        </div>
        {{end}}
    </div>

    <!-- Screenshot Modal -->
    <div class="modal fade" id="screenshotModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Screenshot</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modal-screenshot" src="" alt="Screenshot" class="img-fluid">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize ratings
        document.addEventListener('DOMContentLoaded', function() {
            initializeRatings();
        });

        function initializeRatings() {
            document.querySelectorAll('[data-rating]').forEach(element => {
                const rating = parseFloat(element.dataset.rating);
                element.innerHTML = generateStarRating(rating);
            });
        }

        function generateStarRating(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 >= 0.5;
            let stars = '';

            for (let i = 0; i < fullStars; i++) {
                stars += '<i class="bi bi-star-fill text-warning"></i>';
            }

            if (hasHalfStar) {
                stars += '<i class="bi bi-star-half text-warning"></i>';
            }

            const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
            for (let i = 0; i < emptyStars; i++) {
                stars += '<i class="bi bi-star text-muted"></i>';
            }

            return stars;
        }

        function installAddon(addonId) {
            // Implementation for addon installation
            fetch(`/api/marketplace/install/${addonId}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Addon installed successfully!');
                } else {
                    alert('Installation failed: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Installation failed');
            });
        }

        function openDemo(demoUrl) {
            window.open(demoUrl, '_blank');
        }

        function addToWishlist(addonId) {
            // Implementation for wishlist
            console.log('Adding to wishlist:', addonId);
        }

        function shareAddon(addonId) {
            if (navigator.share) {
                navigator.share({
                    title: document.title,
                    url: window.location.href
                });
            } else {
                // Fallback to copying URL
                navigator.clipboard.writeText(window.location.href);
                alert('URL copied to clipboard!');
            }
        }

        function reportAddon(addonId) {
            // Implementation for reporting
            console.log('Reporting addon:', addonId);
        }

        function openScreenshot(url) {
            document.getElementById('modal-screenshot').src = url;
            new bootstrap.Modal(document.getElementById('screenshotModal')).show();
        }

        function markHelpful(reviewId) {
            // Implementation for marking review as helpful
            console.log('Marking helpful:', reviewId);
        }

        // Review form submission
        document.getElementById('review-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                rating: 5, // Get from rating input
                title: document.getElementById('review-title').value,
                content: document.getElementById('review-content').value
            };

            fetch(`/api/marketplace/addon/{{.Addon.ID}}/review`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Review submitted successfully!');
                    location.reload();
                } else {
                    alert('Failed to submit review: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to submit review');
            });
        });

        // Contact form submission
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Message sent successfully!');
            this.reset();
        });
    </script>
</body>
</html>