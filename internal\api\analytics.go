package api

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"
)

// AnalyticsCollector collects and analyzes API usage metrics
type AnalyticsCollector struct {
	metrics     map[string]*APIMetrics
	events      chan *APIEvent
	storage     MetricsStorage
	mutex       sync.RWMutex
	aggregators map[string]*MetricsAggregator
	alerts      *AlertManager
}

// APIMetrics represents metrics for API usage
type APIMetrics struct {
	Endpoint       string           `json:"endpoint"`
	Method         string           `json:"method"`
	Version        string           `json:"version"`
	RequestCount   int64            `json:"request_count"`
	ErrorCount     int64            `json:"error_count"`
	TotalLatency   time.Duration    `json:"total_latency"`
	AverageLatency time.Duration    `json:"average_latency"`
	MinLatency     time.Duration    `json:"min_latency"`
	MaxLatency     time.Duration    `json:"max_latency"`
	StatusCodes    map[int]int64    `json:"status_codes"`
	UserAgents     map[string]int64 `json:"user_agents"`
	Countries      map[string]int64 `json:"countries"`
	ResponseSizes  []int64          `json:"response_sizes"`
	Timestamps     []time.Time      `json:"timestamps"`
	LastUpdated    time.Time        `json:"last_updated"`
}

// APIEvent represents a single API request event
type APIEvent struct {
	ID           string                 `json:"id"`
	Timestamp    time.Time              `json:"timestamp"`
	Endpoint     string                 `json:"endpoint"`
	Method       string                 `json:"method"`
	Version      string                 `json:"version"`
	StatusCode   int                    `json:"status_code"`
	Latency      time.Duration          `json:"latency"`
	RequestSize  int64                  `json:"request_size"`
	ResponseSize int64                  `json:"response_size"`
	UserID       string                 `json:"user_id"`
	UserAgent    string                 `json:"user_agent"`
	IPAddress    string                 `json:"ip_address"`
	Country      string                 `json:"country"`
	City         string                 `json:"city"`
	Referer      string                 `json:"referer"`
	ErrorMessage string                 `json:"error_message"`
	GraphQLQuery string                 `json:"graphql_query,omitempty"`
	GraphQLVars  map[string]interface{} `json:"graphql_variables,omitempty"`
	Headers      map[string]string      `json:"headers"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// MetricsAggregator aggregates metrics over time periods
type MetricsAggregator struct {
	Period    time.Duration                 `json:"period"`
	Metrics   map[string]*TimeSeriesMetrics `json:"metrics"`
	LastReset time.Time                     `json:"last_reset"`
	mutex     sync.RWMutex
}

// TimeSeriesMetrics represents time-series metrics
type TimeSeriesMetrics struct {
	Timestamps    []time.Time `json:"timestamps"`
	RequestCounts []int64     `json:"request_counts"`
	ErrorCounts   []int64     `json:"error_counts"`
	Latencies     []float64   `json:"latencies"`
	Throughput    []float64   `json:"throughput"`
}

// AlertManager manages API alerts and notifications
type AlertManager struct {
	rules    []AlertRule    `json:"rules"`
	alerts   []Alert        `json:"alerts"`
	channels []AlertChannel `json:"channels"`
	mutex    sync.RWMutex
}

// AlertRule defines conditions for triggering alerts
type AlertRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Condition   AlertCondition         `json:"condition"`
	Threshold   float64                `json:"threshold"`
	Duration    time.Duration          `json:"duration"`
	Severity    AlertSeverity          `json:"severity"`
	Enabled     bool                   `json:"enabled"`
	Channels    []string               `json:"channels"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// AlertCondition defines alert conditions
type AlertCondition struct {
	Metric     string        `json:"metric"`   // error_rate, latency, throughput, etc.
	Operator   string        `json:"operator"` // gt, lt, eq, gte, lte
	Value      float64       `json:"value"`
	TimeWindow time.Duration `json:"time_window"`
}

// Alert represents an active alert
type Alert struct {
	ID          string                 `json:"id"`
	RuleID      string                 `json:"rule_id"`
	Status      AlertStatus            `json:"status"`
	Severity    AlertSeverity          `json:"severity"`
	Message     string                 `json:"message"`
	Details     map[string]interface{} `json:"details"`
	TriggeredAt time.Time              `json:"triggered_at"`
	ResolvedAt  *time.Time             `json:"resolved_at"`
	AckedAt     *time.Time             `json:"acked_at"`
	AckedBy     string                 `json:"acked_by"`
}

// AlertSeverity represents alert severity levels
type AlertSeverity string

const (
	AlertSeverityLow      AlertSeverity = "low"
	AlertSeverityMedium   AlertSeverity = "medium"
	AlertSeverityHigh     AlertSeverity = "high"
	AlertSeverityCritical AlertSeverity = "critical"
)

// AlertStatus represents alert status
type AlertStatus string

const (
	AlertStatusActive   AlertStatus = "active"
	AlertStatusResolved AlertStatus = "resolved"
	AlertStatusAcked    AlertStatus = "acknowledged"
)

// AlertChannel represents an alert notification channel
type AlertChannel struct {
	ID      string                 `json:"id"`
	Type    string                 `json:"type"` // email, slack, webhook, sms
	Name    string                 `json:"name"`
	Config  map[string]interface{} `json:"config"`
	Enabled bool                   `json:"enabled"`
}

// MetricsStorage interface for storing metrics
type MetricsStorage interface {
	Store(ctx context.Context, event *APIEvent) error
	GetMetrics(ctx context.Context, filters MetricsFilters) (*APIMetrics, error)
	GetTimeSeries(ctx context.Context, filters MetricsFilters) (*TimeSeriesMetrics, error)
	GetTopEndpoints(ctx context.Context, limit int, timeRange time.Duration) ([]EndpointStats, error)
}

// MetricsFilters represents filters for querying metrics
type MetricsFilters struct {
	Endpoint   string    `json:"endpoint"`
	Method     string    `json:"method"`
	Version    string    `json:"version"`
	UserID     string    `json:"user_id"`
	StatusCode int       `json:"status_code"`
	StartTime  time.Time `json:"start_time"`
	EndTime    time.Time `json:"end_time"`
	Country    string    `json:"country"`
	UserAgent  string    `json:"user_agent"`
}

// EndpointStats represents statistics for an endpoint
type EndpointStats struct {
	Endpoint     string        `json:"endpoint"`
	Method       string        `json:"method"`
	RequestCount int64         `json:"request_count"`
	ErrorRate    float64       `json:"error_rate"`
	AvgLatency   time.Duration `json:"avg_latency"`
	Throughput   float64       `json:"throughput"`
}

// NewAnalyticsCollector creates a new analytics collector
func NewAnalyticsCollector(storage MetricsStorage) *AnalyticsCollector {
	ac := &AnalyticsCollector{
		metrics:     make(map[string]*APIMetrics),
		events:      make(chan *APIEvent, 10000),
		storage:     storage,
		aggregators: make(map[string]*MetricsAggregator),
		alerts:      NewAlertManager(),
	}

	// Initialize aggregators for different time periods
	ac.aggregators["1m"] = NewMetricsAggregator(time.Minute)
	ac.aggregators["5m"] = NewMetricsAggregator(5 * time.Minute)
	ac.aggregators["1h"] = NewMetricsAggregator(time.Hour)
	ac.aggregators["1d"] = NewMetricsAggregator(24 * time.Hour)

	// Start event processing
	go ac.processEvents()

	// Start periodic aggregation
	go ac.periodicAggregation()

	return ac
}

// RecordEvent records an API event
func (ac *AnalyticsCollector) RecordEvent(event *APIEvent) {
	select {
	case ac.events <- event:
		// Event queued successfully
	default:
		// Channel is full, drop the event (or implement overflow handling)
		fmt.Printf("Warning: Analytics event dropped due to full queue\n")
	}
}

// processEvents processes API events from the queue
func (ac *AnalyticsCollector) processEvents() {
	for event := range ac.events {
		// Store event in persistent storage
		if ac.storage != nil {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			if err := ac.storage.Store(ctx, event); err != nil {
				fmt.Printf("Error storing analytics event: %v\n", err)
			}
			cancel()
		}

		// Update in-memory metrics
		ac.updateMetrics(event)

		// Update aggregators
		ac.updateAggregators(event)

		// Check alert conditions
		ac.checkAlerts(event)
	}
}

// updateMetrics updates in-memory metrics
func (ac *AnalyticsCollector) updateMetrics(event *APIEvent) {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()

	key := fmt.Sprintf("%s:%s:%s", event.Method, event.Endpoint, event.Version)

	metrics, exists := ac.metrics[key]
	if !exists {
		metrics = &APIMetrics{
			Endpoint:      event.Endpoint,
			Method:        event.Method,
			Version:       event.Version,
			StatusCodes:   make(map[int]int64),
			UserAgents:    make(map[string]int64),
			Countries:     make(map[string]int64),
			ResponseSizes: []int64{},
			Timestamps:    []time.Time{},
			MinLatency:    event.Latency,
			MaxLatency:    event.Latency,
		}
		ac.metrics[key] = metrics
	}

	// Update counters
	metrics.RequestCount++
	if event.StatusCode >= 400 {
		metrics.ErrorCount++
	}

	// Update latency metrics
	metrics.TotalLatency += event.Latency
	metrics.AverageLatency = metrics.TotalLatency / time.Duration(metrics.RequestCount)

	if event.Latency < metrics.MinLatency {
		metrics.MinLatency = event.Latency
	}
	if event.Latency > metrics.MaxLatency {
		metrics.MaxLatency = event.Latency
	}

	// Update status codes
	metrics.StatusCodes[event.StatusCode]++

	// Update user agents
	if event.UserAgent != "" {
		metrics.UserAgents[event.UserAgent]++
	}

	// Update countries
	if event.Country != "" {
		metrics.Countries[event.Country]++
	}

	// Update response sizes (keep last 1000)
	metrics.ResponseSizes = append(metrics.ResponseSizes, event.ResponseSize)
	if len(metrics.ResponseSizes) > 1000 {
		metrics.ResponseSizes = metrics.ResponseSizes[1:]
	}

	// Update timestamps (keep last 1000)
	metrics.Timestamps = append(metrics.Timestamps, event.Timestamp)
	if len(metrics.Timestamps) > 1000 {
		metrics.Timestamps = metrics.Timestamps[1:]
	}

	metrics.LastUpdated = time.Now()
}

// updateAggregators updates time-series aggregators
func (ac *AnalyticsCollector) updateAggregators(event *APIEvent) {
	for _, aggregator := range ac.aggregators {
		aggregator.AddEvent(event)
	}
}

// checkAlerts checks if any alert conditions are met
func (ac *AnalyticsCollector) checkAlerts(event *APIEvent) {
	ac.alerts.CheckConditions(event, ac.getRecentMetrics())
}

// getRecentMetrics gets recent metrics for alert checking
func (ac *AnalyticsCollector) getRecentMetrics() map[string]float64 {
	ac.mutex.RLock()
	defer ac.mutex.RUnlock()

	now := time.Now()
	recentWindow := 5 * time.Minute

	var totalRequests, totalErrors int64
	var totalLatency time.Duration

	for _, metrics := range ac.metrics {
		// Count recent requests
		for i := len(metrics.Timestamps) - 1; i >= 0; i-- {
			if now.Sub(metrics.Timestamps[i]) <= recentWindow {
				totalRequests++
				if metrics.Timestamps[i].After(now.Add(-recentWindow)) {
					// This is a simplified error counting - in real implementation
					// you'd need to track errors per timestamp
				}
			} else {
				break
			}
		}

		totalLatency += metrics.AverageLatency
	}

	errorRate := 0.0
	if totalRequests > 0 {
		errorRate = float64(totalErrors) / float64(totalRequests) * 100
	}

	avgLatency := 0.0
	if len(ac.metrics) > 0 {
		avgLatency = float64(totalLatency) / float64(len(ac.metrics)) / float64(time.Millisecond)
	}

	throughput := float64(totalRequests) / recentWindow.Minutes()

	return map[string]float64{
		"error_rate": errorRate,
		"latency":    avgLatency,
		"throughput": throughput,
		"requests":   float64(totalRequests),
	}
}

// periodicAggregation performs periodic aggregation of metrics
func (ac *AnalyticsCollector) periodicAggregation() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		for _, aggregator := range ac.aggregators {
			aggregator.Aggregate()
		}
	}
}

// GetMetrics returns current metrics
func (ac *AnalyticsCollector) GetMetrics() map[string]*APIMetrics {
	ac.mutex.RLock()
	defer ac.mutex.RUnlock()

	result := make(map[string]*APIMetrics)
	for k, v := range ac.metrics {
		result[k] = v
	}

	return result
}

// GetTimeSeries returns time-series metrics
func (ac *AnalyticsCollector) GetTimeSeries(period string) *TimeSeriesMetrics {
	if aggregator, exists := ac.aggregators[period]; exists {
		return aggregator.GetTimeSeries()
	}
	return nil
}

// AnalyticsMiddleware creates HTTP middleware for analytics collection
func (ac *AnalyticsCollector) AnalyticsMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()

			// Create response recorder to capture response details
			recorder := &responseRecorder{
				ResponseWriter: w,
				statusCode:     200,
				responseSize:   0,
			}

			// Process request
			next.ServeHTTP(recorder, r)

			// Create analytics event
			event := &APIEvent{
				ID:           generateEventID(),
				Timestamp:    start,
				Endpoint:     r.URL.Path,
				Method:       r.Method,
				StatusCode:   recorder.statusCode,
				Latency:      time.Since(start),
				RequestSize:  r.ContentLength,
				ResponseSize: int64(recorder.responseSize),
				UserAgent:    r.UserAgent(),
				IPAddress:    getClientIP(r),
				Referer:      r.Referer(),
				Headers:      extractHeaders(r),
				Metadata:     make(map[string]interface{}),
			}

			// Extract user information if available
			if userID := r.Header.Get("X-User-ID"); userID != "" {
				event.UserID = userID
			}

			// Extract version information
			if versionCtx := GetVersionFromContext(r.Context()); versionCtx != nil {
				event.Version = versionCtx.ResolvedVersion
			}

			// Extract GraphQL information if applicable
			if r.URL.Path == "/graphql" && r.Method == "POST" {
				if body := extractGraphQLInfo(r); body != nil {
					event.GraphQLQuery, _ = body["query"].(string)
					event.GraphQLVars, _ = body["variables"].(map[string]interface{})
				}
			}

			// Record the event
			ac.RecordEvent(event)
		})
	}
}

// responseRecorder captures response details
type responseRecorder struct {
	http.ResponseWriter
	statusCode   int
	responseSize int
}

func (rr *responseRecorder) WriteHeader(code int) {
	rr.statusCode = code
	rr.ResponseWriter.WriteHeader(code)
}

func (rr *responseRecorder) Write(b []byte) (int, error) {
	size, err := rr.ResponseWriter.Write(b)
	rr.responseSize += size
	return size, err
}

// Helper functions
func generateEventID() string {
	return fmt.Sprintf("evt_%d", time.Now().UnixNano())
}

func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		return xff
	}
	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}
	// Fall back to RemoteAddr
	return r.RemoteAddr
}

func extractHeaders(r *http.Request) map[string]string {
	headers := make(map[string]string)
	for name, values := range r.Header {
		if len(values) > 0 {
			headers[name] = values[0]
		}
	}
	return headers
}

func extractGraphQLInfo(r *http.Request) map[string]interface{} {
	// This would parse the GraphQL request body
	// Simplified implementation
	return map[string]interface{}{
		"query":     "query { ... }",
		"variables": map[string]interface{}{},
	}
}

// NewMetricsAggregator creates a new metrics aggregator
func NewMetricsAggregator(period time.Duration) *MetricsAggregator {
	return &MetricsAggregator{
		Period:    period,
		Metrics:   make(map[string]*TimeSeriesMetrics),
		LastReset: time.Now(),
	}
}

// AddEvent adds an event to the aggregator
func (ma *MetricsAggregator) AddEvent(event *APIEvent) {
	ma.mutex.Lock()
	defer ma.mutex.Unlock()

	key := fmt.Sprintf("%s:%s", event.Method, event.Endpoint)

	metrics, exists := ma.Metrics[key]
	if !exists {
		metrics = &TimeSeriesMetrics{
			Timestamps:    []time.Time{},
			RequestCounts: []int64{},
			ErrorCounts:   []int64{},
			Latencies:     []float64{},
			Throughput:    []float64{},
		}
		ma.Metrics[key] = metrics
	}

	// Add data point
	metrics.Timestamps = append(metrics.Timestamps, event.Timestamp)
	metrics.RequestCounts = append(metrics.RequestCounts, 1)

	errorCount := int64(0)
	if event.StatusCode >= 400 {
		errorCount = 1
	}
	metrics.ErrorCounts = append(metrics.ErrorCounts, errorCount)
	metrics.Latencies = append(metrics.Latencies, float64(event.Latency.Milliseconds()))
}

// Aggregate performs aggregation for the time period
func (ma *MetricsAggregator) Aggregate() {
	ma.mutex.Lock()
	defer ma.mutex.Unlock()

	now := time.Now()
	if now.Sub(ma.LastReset) >= ma.Period {
		// Reset aggregation window
		ma.LastReset = now

		// Calculate throughput for each metric
		for _, metrics := range ma.Metrics {
			if len(metrics.RequestCounts) > 0 {
				totalRequests := int64(0)
				for _, count := range metrics.RequestCounts {
					totalRequests += count
				}
				throughput := float64(totalRequests) / ma.Period.Minutes()
				metrics.Throughput = append(metrics.Throughput, throughput)
			}
		}
	}
}

// GetTimeSeries returns the time series metrics
func (ma *MetricsAggregator) GetTimeSeries() *TimeSeriesMetrics {
	ma.mutex.RLock()
	defer ma.mutex.RUnlock()

	// Aggregate all metrics into a single time series
	combined := &TimeSeriesMetrics{
		Timestamps:    []time.Time{},
		RequestCounts: []int64{},
		ErrorCounts:   []int64{},
		Latencies:     []float64{},
		Throughput:    []float64{},
	}

	// This is a simplified aggregation - in a real implementation,
	// you'd properly merge time series data
	for _, metrics := range ma.Metrics {
		combined.Timestamps = append(combined.Timestamps, metrics.Timestamps...)
		combined.RequestCounts = append(combined.RequestCounts, metrics.RequestCounts...)
		combined.ErrorCounts = append(combined.ErrorCounts, metrics.ErrorCounts...)
		combined.Latencies = append(combined.Latencies, metrics.Latencies...)
		combined.Throughput = append(combined.Throughput, metrics.Throughput...)
	}

	return combined
}

// NewAlertManager creates a new alert manager
func NewAlertManager() *AlertManager {
	am := &AlertManager{
		rules:    []AlertRule{},
		alerts:   []Alert{},
		channels: []AlertChannel{},
	}

	// Add default alert rules
	am.addDefaultRules()

	return am
}

// addDefaultRules adds default alert rules
func (am *AlertManager) addDefaultRules() {
	am.rules = []AlertRule{
		{
			ID:          "high_error_rate",
			Name:        "High Error Rate",
			Description: "Alert when error rate exceeds 5%",
			Condition: AlertCondition{
				Metric:     "error_rate",
				Operator:   "gt",
				Value:      5.0,
				TimeWindow: 5 * time.Minute,
			},
			Threshold: 5.0,
			Duration:  5 * time.Minute,
			Severity:  AlertSeverityHigh,
			Enabled:   true,
			Channels:  []string{"email", "slack"},
		},
		{
			ID:          "high_latency",
			Name:        "High Latency",
			Description: "Alert when average latency exceeds 1000ms",
			Condition: AlertCondition{
				Metric:     "latency",
				Operator:   "gt",
				Value:      1000.0,
				TimeWindow: 5 * time.Minute,
			},
			Threshold: 1000.0,
			Duration:  5 * time.Minute,
			Severity:  AlertSeverityMedium,
			Enabled:   true,
			Channels:  []string{"email"},
		},
		{
			ID:          "low_throughput",
			Name:        "Low Throughput",
			Description: "Alert when throughput drops below 10 requests/minute",
			Condition: AlertCondition{
				Metric:     "throughput",
				Operator:   "lt",
				Value:      10.0,
				TimeWindow: 10 * time.Minute,
			},
			Threshold: 10.0,
			Duration:  10 * time.Minute,
			Severity:  AlertSeverityLow,
			Enabled:   true,
			Channels:  []string{"email"},
		},
	}
}

// CheckConditions checks alert conditions against current metrics
func (am *AlertManager) CheckConditions(event *APIEvent, metrics map[string]float64) {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	for _, rule := range am.rules {
		if !rule.Enabled {
			continue
		}

		metricValue, exists := metrics[rule.Condition.Metric]
		if !exists {
			continue
		}

		triggered := false
		switch rule.Condition.Operator {
		case "gt":
			triggered = metricValue > rule.Condition.Value
		case "lt":
			triggered = metricValue < rule.Condition.Value
		case "eq":
			triggered = metricValue == rule.Condition.Value
		case "gte":
			triggered = metricValue >= rule.Condition.Value
		case "lte":
			triggered = metricValue <= rule.Condition.Value
		}

		if triggered {
			am.triggerAlert(rule, metricValue)
		}
	}
}

// triggerAlert triggers an alert
func (am *AlertManager) triggerAlert(rule AlertRule, value float64) {
	// Check if alert is already active
	for _, alert := range am.alerts {
		if alert.RuleID == rule.ID && alert.Status == AlertStatusActive {
			return // Alert already active
		}
	}

	alert := Alert{
		ID:       generateAlertID(),
		RuleID:   rule.ID,
		Status:   AlertStatusActive,
		Severity: rule.Severity,
		Message:  fmt.Sprintf("%s: %s (value: %.2f, threshold: %.2f)", rule.Name, rule.Description, value, rule.Threshold),
		Details: map[string]interface{}{
			"metric_value": value,
			"threshold":    rule.Threshold,
			"rule_name":    rule.Name,
		},
		TriggeredAt: time.Now(),
	}

	am.alerts = append(am.alerts, alert)

	// Send notifications
	am.sendNotifications(alert, rule)
}

// sendNotifications sends alert notifications
func (am *AlertManager) sendNotifications(alert Alert, rule AlertRule) {
	// This would send notifications to configured channels
	fmt.Printf("ALERT: %s - %s\n", alert.Severity, alert.Message)
}

// generateAlertID generates a unique alert ID
func generateAlertID() string {
	return fmt.Sprintf("alert_%d", time.Now().UnixNano())
}
