package subscription

import (
	"context"
	"fmt"
	"time"

	"goVwPlatformAPI/internal/database"
)

type SubscriptionService struct {
	db *database.DB
}

type Tier struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	PriceMonthly float64                `json:"price_monthly"`
	Features     map[string]interface{} `json:"features"`
	Limits       map[string]interface{} `json:"limits"`
	IsActive     bool                   `json:"is_active"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

type UserSubscription struct {
	ID                   string    `json:"id"`
	UserID               string    `json:"user_id"`
	TierID               string    `json:"tier_id"`
	Status               string    `json:"status"`
	CurrentPeriodStart   time.Time `json:"current_period_start"`
	CurrentPeriodEnd     time.Time `json:"current_period_end"`
	StripeSubscriptionID *string   `json:"stripe_subscription_id,omitempty"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
	Tier                 *Tier     `json:"tier,omitempty"`
}

type UsageTracking struct {
	ID           string    `json:"id"`
	UserID       string    `json:"user_id"`
	ResourceType string    `json:"resource_type"`
	UsageAmount  int       `json:"usage_amount"`
	PeriodStart  time.Time `json:"period_start"`
	PeriodEnd    time.Time `json:"period_end"`
	CreatedAt    time.Time `json:"created_at"`
}

type Invoice struct {
	ID              string     `json:"id"`
	UserID          string     `json:"user_id"`
	SubscriptionID  string     `json:"subscription_id"`
	Amount          float64    `json:"amount"`
	Currency        string     `json:"currency"`
	Status          string     `json:"status"`
	DueDate         time.Time  `json:"due_date"`
	PaidAt          *time.Time `json:"paid_at,omitempty"`
	StripeInvoiceID *string    `json:"stripe_invoice_id,omitempty"`
	DownloadURL     *string    `json:"download_url,omitempty"`
	CreatedAt       time.Time  `json:"created_at"`
}

func NewSubscriptionService(db *database.DB) *SubscriptionService {
	return &SubscriptionService{db: db}
}

func (s *SubscriptionService) GetUserSubscription(ctx context.Context, userID string) (*UserSubscription, error) {
	var sub UserSubscription
	var tier Tier

	query := `
		SELECT 
			us.id, us.user_id, us.tier_id, us.status, 
			us.current_period_start, us.current_period_end,
			st.name, st.price_monthly, st.features, st.limits
		FROM user_subscriptions us
		JOIN subscription_tiers st ON us.tier_id = st.id
		WHERE us.user_id = $1 AND us.status = 'active'
		ORDER BY us.created_at DESC
		LIMIT 1
	`

	err := s.db.Pool.QueryRow(ctx, query, userID).Scan(
		&sub.ID, &sub.UserID, &sub.TierID, &sub.Status,
		&sub.CurrentPeriodStart, &sub.CurrentPeriodEnd,
		&tier.Name, &tier.PriceMonthly, &tier.Features, &tier.Limits,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user subscription: %w", err)
	}

	tier.ID = sub.TierID
	sub.Tier = &tier

	return &sub, nil
}

func (s *SubscriptionService) HasFeatureAccess(ctx context.Context, userID string, feature string) (bool, error) {
	sub, err := s.GetUserSubscription(ctx, userID)
	if err != nil {
		return false, err
	}

	if sub.Status != "active" {
		return false, nil
	}

	// Check if feature exists in tier features
	if featureValue, exists := sub.Tier.Features[feature]; exists {
		if boolValue, ok := featureValue.(bool); ok {
			return boolValue, nil
		}
	}

	return false, nil
}

func (s *SubscriptionService) CheckResourceLimit(ctx context.Context, userID string, resource string, currentUsage int) (bool, error) {
	sub, err := s.GetUserSubscription(ctx, userID)
	if err != nil {
		return false, err
	}

	if sub.Status != "active" {
		return false, nil
	}

	// Check resource limits
	if limitValue, exists := sub.Tier.Limits[resource]; exists {
		if intLimit, ok := limitValue.(float64); ok {
			// -1 means unlimited
			if intLimit == -1 {
				return true, nil
			}
			return currentUsage < int(intLimit), nil
		}
	}

	return false, nil
}

func (s *SubscriptionService) GetAllTiers(ctx context.Context) ([]*Tier, error) {
	query := `
		SELECT id, name, price_monthly, features, limits
		FROM subscription_tiers
		WHERE is_active = true
		ORDER BY price_monthly ASC
	`

	rows, err := s.db.Pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get subscription tiers: %w", err)
	}
	defer rows.Close()

	var tiers []*Tier
	for rows.Next() {
		var tier Tier
		err := rows.Scan(&tier.ID, &tier.Name, &tier.PriceMonthly, &tier.Features, &tier.Limits)
		if err != nil {
			return nil, fmt.Errorf("failed to scan tier: %w", err)
		}
		tiers = append(tiers, &tier)
	}

	return tiers, nil
}

func (s *SubscriptionService) UpgradeSubscription(ctx context.Context, userID string, newTierID string) error {
	// Start transaction
	tx, err := s.db.Pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Cancel current subscription
	_, err = tx.Exec(ctx, `
		UPDATE user_subscriptions 
		SET status = 'canceled', updated_at = NOW()
		WHERE user_id = $1 AND status = 'active'
	`, userID)
	if err != nil {
		return fmt.Errorf("failed to cancel current subscription: %w", err)
	}

	// Create new subscription
	_, err = tx.Exec(ctx, `
		INSERT INTO user_subscriptions (user_id, tier_id, status, current_period_start, current_period_end)
		VALUES ($1, $2, 'active', NOW(), NOW() + INTERVAL '1 month')
	`, userID, newTierID)
	if err != nil {
		return fmt.Errorf("failed to create new subscription: %w", err)
	}

	return tx.Commit(ctx)
}

func (s *SubscriptionService) TrackUsage(ctx context.Context, userID string, resourceType string, amount int) error {
	// Get current period
	now := time.Now()
	periodStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	periodEnd := periodStart.AddDate(0, 1, 0).Add(-time.Second)

	// Upsert usage tracking
	_, err := s.db.Pool.Exec(ctx, `
		INSERT INTO usage_tracking (user_id, resource_type, usage_amount, period_start, period_end)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (user_id, resource_type, period_start)
		DO UPDATE SET usage_amount = usage_tracking.usage_amount + $3, updated_at = NOW()
	`, userID, resourceType, amount, periodStart, periodEnd)

	if err != nil {
		return fmt.Errorf("failed to track usage: %w", err)
	}

	return nil
}

func (s *SubscriptionService) GetCurrentUsage(ctx context.Context, userID string, resourceType string) (int, error) {
	now := time.Now()
	periodStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	var usage int
	err := s.db.Pool.QueryRow(ctx, `
		SELECT COALESCE(usage_amount, 0)
		FROM usage_tracking
		WHERE user_id = $1 AND resource_type = $2 AND period_start = $3
	`, userID, resourceType, periodStart).Scan(&usage)

	if err != nil {
		// If no record exists, usage is 0
		return 0, nil
	}

	return usage, nil
}

// Additional subscription management functions

func (s *SubscriptionService) CreateTier(ctx context.Context, name string, priceMonthly float64, features, limits map[string]interface{}) (*Tier, error) {
	tier := &Tier{
		Name:         name,
		PriceMonthly: priceMonthly,
		Features:     features,
		Limits:       limits,
	}

	query := `
		INSERT INTO subscription_tiers (name, price_monthly, features, limits, is_active)
		VALUES ($1, $2, $3, $4, true)
		RETURNING id, created_at, updated_at
	`

	err := s.db.Pool.QueryRow(ctx, query, name, priceMonthly, features, limits).Scan(
		&tier.ID, &tier.CreatedAt, &tier.UpdatedAt)

	if err != nil {
		return nil, fmt.Errorf("failed to create subscription tier: %w", err)
	}

	return tier, nil
}

func (s *SubscriptionService) UpdateTier(ctx context.Context, tierID string, updates map[string]interface{}) (*Tier, error) {
	// Build dynamic update query
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	for field, value := range updates {
		setParts = append(setParts, fmt.Sprintf("%s = $%d", field, argIndex))
		args = append(args, value)
		argIndex++
	}

	// Always update the updated_at field
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add tier ID for WHERE clause
	args = append(args, tierID)

	query := fmt.Sprintf("UPDATE subscription_tiers SET %s WHERE id = $%d",
		fmt.Sprintf("%s", setParts), argIndex)

	_, err := s.db.Pool.Exec(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to update subscription tier: %w", err)
	}

	// Return updated tier
	return s.GetTierByID(ctx, tierID)
}

func (s *SubscriptionService) GetTierByID(ctx context.Context, tierID string) (*Tier, error) {
	var tier Tier
	query := `
		SELECT id, name, price_monthly, features, limits, is_active, created_at, updated_at
		FROM subscription_tiers
		WHERE id = $1
	`

	err := s.db.Pool.QueryRow(ctx, query, tierID).Scan(
		&tier.ID, &tier.Name, &tier.PriceMonthly, &tier.Features, &tier.Limits,
		&tier.IsActive, &tier.CreatedAt, &tier.UpdatedAt)

	if err != nil {
		return nil, fmt.Errorf("failed to get subscription tier: %w", err)
	}

	return &tier, nil
}

func (s *SubscriptionService) DeleteTier(ctx context.Context, tierID string) error {
	// Soft delete by setting is_active to false
	query := `UPDATE subscription_tiers SET is_active = false, updated_at = $1 WHERE id = $2`
	_, err := s.db.Pool.Exec(ctx, query, time.Now(), tierID)
	if err != nil {
		return fmt.Errorf("failed to delete subscription tier: %w", err)
	}
	return nil
}

func (s *SubscriptionService) GetUserSubscriptionHistory(ctx context.Context, userID string) ([]*UserSubscription, error) {
	query := `
		SELECT 
			us.id, us.user_id, us.tier_id, us.status, 
			us.current_period_start, us.current_period_end, us.stripe_subscription_id,
			us.created_at, us.updated_at,
			st.name, st.price_monthly, st.features, st.limits
		FROM user_subscriptions us
		JOIN subscription_tiers st ON us.tier_id = st.id
		WHERE us.user_id = $1
		ORDER BY us.created_at DESC
	`

	rows, err := s.db.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user subscription history: %w", err)
	}
	defer rows.Close()

	var subscriptions []*UserSubscription
	for rows.Next() {
		var sub UserSubscription
		var tier Tier

		err := rows.Scan(
			&sub.ID, &sub.UserID, &sub.TierID, &sub.Status,
			&sub.CurrentPeriodStart, &sub.CurrentPeriodEnd, &sub.StripeSubscriptionID,
			&sub.CreatedAt, &sub.UpdatedAt,
			&tier.Name, &tier.PriceMonthly, &tier.Features, &tier.Limits,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan subscription: %w", err)
		}

		tier.ID = sub.TierID
		sub.Tier = &tier
		subscriptions = append(subscriptions, &sub)
	}

	return subscriptions, nil
}

func (s *SubscriptionService) CancelSubscription(ctx context.Context, userID string) error {
	query := `
		UPDATE user_subscriptions 
		SET status = 'canceled', updated_at = NOW()
		WHERE user_id = $1 AND status = 'active'
	`
	_, err := s.db.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to cancel subscription: %w", err)
	}
	return nil
}

func (s *SubscriptionService) GetUsageHistory(ctx context.Context, userID string, resourceType string, limit int) ([]*UsageTracking, error) {
	var query string
	var args []interface{}

	if resourceType != "" {
		query = `
			SELECT id, user_id, resource_type, usage_amount, period_start, period_end, created_at
			FROM usage_tracking
			WHERE user_id = $1 AND resource_type = $2
			ORDER BY period_start DESC
			LIMIT $3
		`
		args = []interface{}{userID, resourceType, limit}
	} else {
		query = `
			SELECT id, user_id, resource_type, usage_amount, period_start, period_end, created_at
			FROM usage_tracking
			WHERE user_id = $1
			ORDER BY period_start DESC
			LIMIT $2
		`
		args = []interface{}{userID, limit}
	}

	rows, err := s.db.Pool.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get usage history: %w", err)
	}
	defer rows.Close()

	var usageHistory []*UsageTracking
	for rows.Next() {
		var usage UsageTracking
		err := rows.Scan(
			&usage.ID, &usage.UserID, &usage.ResourceType, &usage.UsageAmount,
			&usage.PeriodStart, &usage.PeriodEnd, &usage.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan usage: %w", err)
		}
		usageHistory = append(usageHistory, &usage)
	}

	return usageHistory, nil
}

// Invoice management functions

func (s *SubscriptionService) GenerateInvoice(ctx context.Context, subscriptionID string) (*Invoice, error) {
	// Get subscription details
	var userID string
	var tierID string
	var amount float64

	query := `
		SELECT us.user_id, us.tier_id, st.price_monthly
		FROM user_subscriptions us
		JOIN subscription_tiers st ON us.tier_id = st.id
		WHERE us.id = $1 AND us.status = 'active'
	`

	err := s.db.Pool.QueryRow(ctx, query, subscriptionID).Scan(&userID, &tierID, &amount)
	if err != nil {
		return nil, fmt.Errorf("failed to get subscription details: %w", err)
	}

	// Create invoice
	invoice := &Invoice{
		UserID:         userID,
		SubscriptionID: subscriptionID,
		Amount:         amount,
		Currency:       "USD",
		Status:         "pending",
		DueDate:        time.Now().AddDate(0, 0, 30), // 30 days from now
		CreatedAt:      time.Now(),
	}

	insertQuery := `
		INSERT INTO invoices (user_id, subscription_id, amount, currency, status, due_date)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id, created_at
	`

	err = s.db.Pool.QueryRow(ctx, insertQuery,
		invoice.UserID, invoice.SubscriptionID, invoice.Amount,
		invoice.Currency, invoice.Status, invoice.DueDate).Scan(
		&invoice.ID, &invoice.CreatedAt)

	if err != nil {
		return nil, fmt.Errorf("failed to create invoice: %w", err)
	}

	return invoice, nil
}

func (s *SubscriptionService) GetUserInvoices(ctx context.Context, userID string, limit, offset int) ([]*Invoice, error) {
	query := `
		SELECT id, user_id, subscription_id, amount, currency, status, 
		       due_date, paid_at, stripe_invoice_id, download_url, created_at
		FROM invoices
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := s.db.Pool.Query(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get user invoices: %w", err)
	}
	defer rows.Close()

	var invoices []*Invoice
	for rows.Next() {
		var invoice Invoice
		err := rows.Scan(
			&invoice.ID, &invoice.UserID, &invoice.SubscriptionID, &invoice.Amount,
			&invoice.Currency, &invoice.Status, &invoice.DueDate, &invoice.PaidAt,
			&invoice.StripeInvoiceID, &invoice.DownloadURL, &invoice.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan invoice: %w", err)
		}
		invoices = append(invoices, &invoice)
	}

	return invoices, nil
}

func (s *SubscriptionService) GetInvoiceByID(ctx context.Context, invoiceID string) (*Invoice, error) {
	var invoice Invoice
	query := `
		SELECT id, user_id, subscription_id, amount, currency, status, 
		       due_date, paid_at, stripe_invoice_id, download_url, created_at
		FROM invoices
		WHERE id = $1
	`

	err := s.db.Pool.QueryRow(ctx, query, invoiceID).Scan(
		&invoice.ID, &invoice.UserID, &invoice.SubscriptionID, &invoice.Amount,
		&invoice.Currency, &invoice.Status, &invoice.DueDate, &invoice.PaidAt,
		&invoice.StripeInvoiceID, &invoice.DownloadURL, &invoice.CreatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get invoice: %w", err)
	}

	return &invoice, nil
}

func (s *SubscriptionService) MarkInvoicePaid(ctx context.Context, invoiceID string, stripeInvoiceID string) error {
	now := time.Now()
	query := `
		UPDATE invoices 
		SET status = 'paid', paid_at = $1, stripe_invoice_id = $2, updated_at = $1
		WHERE id = $3
	`

	_, err := s.db.Pool.Exec(ctx, query, now, stripeInvoiceID, invoiceID)
	if err != nil {
		return fmt.Errorf("failed to mark invoice as paid: %w", err)
	}

	return nil
}

func (s *SubscriptionService) DowngradeSubscription(ctx context.Context, userID string, newTierID string) error {
	// Start transaction
	tx, err := s.db.Pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Cancel current subscription
	_, err = tx.Exec(ctx, `
		UPDATE user_subscriptions 
		SET status = 'canceled', updated_at = NOW()
		WHERE user_id = $1 AND status = 'active'
	`, userID)
	if err != nil {
		return fmt.Errorf("failed to cancel current subscription: %w", err)
	}

	// Create new subscription with immediate start
	_, err = tx.Exec(ctx, `
		INSERT INTO user_subscriptions (user_id, tier_id, status, current_period_start, current_period_end)
		VALUES ($1, $2, 'active', NOW(), NOW() + INTERVAL '1 month')
	`, userID, newTierID)
	if err != nil {
		return fmt.Errorf("failed to create downgraded subscription: %w", err)
	}

	return tx.Commit(ctx)
}
