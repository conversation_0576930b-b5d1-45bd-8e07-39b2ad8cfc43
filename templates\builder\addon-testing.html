<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Addon Testing Environment - Velocity Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-lightning-charge"></i>
                VelocityWave
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/builder/addon-builder">
                    <i class="bi bi-arrow-left"></i> Back to Builder
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-play-circle"></i>
                            Test Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="test-config-form">
                            <div class="mb-3">
                                <label class="form-label">Test Environment</label>
                                <select class="form-select" id="test-environment">
                                    <option value="development">Development</option>
                                    <option value="staging">Staging</option>
                                    <option value="production">Production</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Timeout (seconds)</label>
                                <input type="number" class="form-control" id="test-timeout" value="30" min="5" max="300">
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable-performance" checked>
                                    <label class="form-check-label" for="enable-performance">
                                        Performance Testing
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable-security" checked>
                                    <label class="form-check-label" for="enable-security">
                                        Security Checks
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable-compatibility" checked>
                                    <label class="form-check-label" for="enable-compatibility">
                                        Compatibility Testing
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Test Data (JSON)</label>
                                <textarea class="form-control" id="test-data" rows="4" 
                                          placeholder='{"key": "value"}'></textarea>
                            </div>

                            <button type="button" class="btn btn-success w-100" onclick="startTest()">
                                <i class="bi bi-play"></i> Start Test
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Test History -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-clock-history"></i>
                            Test History
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="test-history" class="small">
                            <div class="text-muted">No previous tests</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Testing Area -->
            <div class="col-md-9">
                <!-- Current Test Status -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">
                                <i class="bi bi-bug"></i>
                                Testing Environment
                            </h4>
                            <div id="test-status-badge">
                                <span class="badge bg-secondary">Ready</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="test-progress" class="d-none">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Test Progress</span>
                                <span id="progress-text">0%</span>
                            </div>
                            <div class="progress mb-3">
                                <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>

                        <div id="test-results-container" class="d-none">
                            <h5>Test Results</h5>
                            <div id="test-results"></div>
                        </div>

                        <div id="test-idle" class="text-center py-5">
                            <i class="bi bi-play-circle display-1 text-muted"></i>
                            <h5 class="text-muted mt-3">Ready to Test</h5>
                            <p class="text-muted">Configure your test settings and click "Start Test" to begin</p>
                        </div>
                    </div>
                </div>

                <!-- Test Details Tabs -->
                <div class="card mt-3">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="test-tabs">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#execution-tab">
                                    <i class="bi bi-gear"></i> Execution
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#performance-tab">
                                    <i class="bi bi-speedometer2"></i> Performance
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#security-tab">
                                    <i class="bi bi-shield-check"></i> Security
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#logs-tab">
                                    <i class="bi bi-terminal"></i> Logs
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <!-- Execution Tab -->
                            <div class="tab-pane fade show active" id="execution-tab">
                                <div id="execution-details">
                                    <p class="text-muted">Start a test to see execution details</p>
                                </div>
                            </div>

                            <!-- Performance Tab -->
                            <div class="tab-pane fade" id="performance-tab">
                                <div id="performance-metrics">
                                    <p class="text-muted">Performance metrics will appear here during testing</p>
                                </div>
                            </div>

                            <!-- Security Tab -->
                            <div class="tab-pane fade" id="security-tab">
                                <div id="security-results">
                                    <p class="text-muted">Security check results will appear here</p>
                                </div>
                            </div>

                            <!-- Logs Tab -->
                            <div class="tab-pane fade" id="logs-tab">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">Test Execution Logs</h6>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">
                                        <i class="bi bi-trash"></i> Clear
                                    </button>
                                </div>
                                <div id="test-logs" class="border rounded p-3 bg-dark text-light" 
                                     style="height: 300px; overflow-y: auto; font-family: monospace;">
                                    <div class="text-muted">Test logs will appear here...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentAddonId = null;
        let currentTestSession = null;
        let testPollingInterval = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            currentAddonId = getAddonId();
            if (currentAddonId) {
                loadTestHistory();
            }
        });

        function getAddonId() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('id') || localStorage.getItem('currentAddonId');
        }

        function startTest() {
            if (!currentAddonId) {
                alert('No addon selected for testing');
                return;
            }

            const testOptions = {
                timeout: parseInt(document.getElementById('test-timeout').value) * 1000,
                enablePerformance: document.getElementById('enable-performance').checked,
                enableSecurity: document.getElementById('enable-security').checked,
                enableCompatibility: document.getElementById('enable-compatibility').checked,
                environment: document.getElementById('test-environment').value,
                testData: null,
                mockServices: {}
            };

            // Parse test data
            const testDataInput = document.getElementById('test-data').value.trim();
            if (testDataInput) {
                try {
                    testOptions.testData = JSON.parse(testDataInput);
                } catch (error) {
                    alert('Invalid JSON in test data: ' + error.message);
                    return;
                }
            }

            addLog('info', 'Starting addon test...');
            updateTestStatus('running', 'Starting Test');

            fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    query: `
                        mutation StartAddonTest($id: ID!, $options: AddonTestOptionsInput!) {
                            startAddonTest(id: $id, options: $options) {
                                id
                                addonId
                                status
                                startedAt
                            }
                        }
                    `,
                    variables: {
                        id: currentAddonId,
                        options: testOptions
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.data?.startAddonTest) {
                    currentTestSession = data.data.startAddonTest;
                    addLog('success', `Test session started: ${currentTestSession.id}`);
                    startPollingTestStatus();
                    showTestProgress();
                } else if (data.errors) {
                    addLog('error', 'Failed to start test: ' + data.errors[0].message);
                    updateTestStatus('error', 'Test Failed');
                }
            })
            .catch(error => {
                console.error('Error starting test:', error);
                addLog('error', 'Error starting test: ' + error.message);
                updateTestStatus('error', 'Test Failed');
            });
        }

        function startPollingTestStatus() {
            if (testPollingInterval) {
                clearInterval(testPollingInterval);
            }

            testPollingInterval = setInterval(() => {
                if (currentTestSession) {
                    pollTestStatus(currentTestSession.id);
                }
            }, 2000);
        }

        function pollTestStatus(sessionId) {
            fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    query: `
                        query GetAddonTestSession($sessionId: ID!) {
                            getAddonTestSession(sessionId: $sessionId) {
                                id
                                status
                                results {
                                    success
                                    executionTime
                                    nodesExecuted
                                    nodesTotal
                                    errors {
                                        code
                                        message
                                        severity
                                    }
                                    warnings {
                                        code
                                        message
                                    }
                                    performance {
                                        cpuUsage
                                        memoryUsage
                                        responseTime
                                    }
                                    securityChecks {
                                        passed
                                        sandboxViolations
                                        permissionIssues
                                    }
                                }
                                logs {
                                    level
                                    message
                                    timestamp
                                }
                            }
                        }
                    `,
                    variables: { sessionId }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.data?.getAddonTestSession) {
                    updateTestSession(data.data.getAddonTestSession);
                }
            })
            .catch(error => {
                console.error('Error polling test status:', error);
            });
        }

        function updateTestSession(session) {
            currentTestSession = session;
            
            // Update status
            updateTestStatus(session.status.toLowerCase(), session.status);
            
            // Update progress
            if (session.results) {
                const progress = session.results.nodesTotal > 0 ? 
                    (session.results.nodesExecuted / session.results.nodesTotal) * 100 : 0;
                updateProgress(progress);
            }
            
            // Update logs
            if (session.logs) {
                session.logs.forEach(log => {
                    addLogFromServer(log);
                });
            }
            
            // If test is complete, show results
            if (session.status === 'COMPLETED' || session.status === 'FAILED') {
                clearInterval(testPollingInterval);
                showTestResults(session.results);
                hideTestProgress();
                loadTestHistory(); // Refresh history
            }
        }

        function updateTestStatus(status, text) {
            const badge = document.getElementById('test-status-badge');
            const statusClasses = {
                'ready': 'bg-secondary',
                'running': 'bg-primary',
                'completed': 'bg-success',
                'failed': 'bg-danger',
                'error': 'bg-danger'
            };
            
            badge.innerHTML = `<span class="badge ${statusClasses[status] || 'bg-secondary'}">${text}</span>`;
        }

        function showTestProgress() {
            document.getElementById('test-idle').classList.add('d-none');
            document.getElementById('test-progress').classList.remove('d-none');
            document.getElementById('test-results-container').classList.add('d-none');
        }

        function hideTestProgress() {
            document.getElementById('test-progress').classList.add('d-none');
        }

        function updateProgress(percentage) {
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            
            progressBar.style.width = percentage + '%';
            progressText.textContent = Math.round(percentage) + '%';
        }

        function showTestResults(results) {
            if (!results) return;
            
            document.getElementById('test-results-container').classList.remove('d-none');
            
            const resultsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card ${results.success ? 'border-success' : 'border-danger'}">
                            <div class="card-body text-center">
                                <i class="bi bi-${results.success ? 'check-circle text-success' : 'x-circle text-danger'} display-4"></i>
                                <h5 class="mt-2">${results.success ? 'Test Passed' : 'Test Failed'}</h5>
                                <p class="text-muted">Execution Time: ${results.executionTime}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6>Execution Summary</h6>
                                <ul class="list-unstyled mb-0">
                                    <li><strong>Nodes Executed:</strong> ${results.nodesExecuted}/${results.nodesTotal}</li>
                                    <li><strong>Errors:</strong> ${results.errors?.length || 0}</li>
                                    <li><strong>Warnings:</strong> ${results.warnings?.length || 0}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('test-results').innerHTML = resultsHtml;
            
            // Update detailed tabs
            updateExecutionDetails(results);
            updatePerformanceMetrics(results.performance);
            updateSecurityResults(results.securityChecks);
        }

        function updateExecutionDetails(results) {
            let html = '<h6>Execution Details</h6>';
            
            if (results.errors && results.errors.length > 0) {
                html += '<div class="alert alert-danger"><h6>Errors:</h6><ul class="mb-0">';
                results.errors.forEach(error => {
                    html += `<li><strong>${error.code}:</strong> ${error.message}</li>`;
                });
                html += '</ul></div>';
            }
            
            if (results.warnings && results.warnings.length > 0) {
                html += '<div class="alert alert-warning"><h6>Warnings:</h6><ul class="mb-0">';
                results.warnings.forEach(warning => {
                    html += `<li><strong>${warning.code}:</strong> ${warning.message}</li>`;
                });
                html += '</ul></div>';
            }
            
            if (results.errors.length === 0 && results.warnings.length === 0) {
                html += '<div class="alert alert-success">No issues found during execution</div>';
            }
            
            document.getElementById('execution-details').innerHTML = html;
        }

        function updatePerformanceMetrics(performance) {
            if (!performance) {
                document.getElementById('performance-metrics').innerHTML = 
                    '<p class="text-muted">Performance testing was not enabled</p>';
                return;
            }
            
            const html = `
                <h6>Performance Metrics</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5>${performance.cpuUsage.toFixed(2)}%</h5>
                                <p class="text-muted mb-0">CPU Usage</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5>${(performance.memoryUsage / 1024 / 1024).toFixed(2)} MB</h5>
                                <p class="text-muted mb-0">Memory Usage</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <p><strong>Response Time:</strong> ${performance.responseTime}</p>
                </div>
            `;
            
            document.getElementById('performance-metrics').innerHTML = html;
        }

        function updateSecurityResults(security) {
            if (!security) {
                document.getElementById('security-results').innerHTML = 
                    '<p class="text-muted">Security testing was not enabled</p>';
                return;
            }
            
            let html = `<h6>Security Check Results</h6>`;
            
            if (security.passed) {
                html += '<div class="alert alert-success">All security checks passed</div>';
            } else {
                html += '<div class="alert alert-danger">Security issues found</div>';
                
                if (security.sandboxViolations.length > 0) {
                    html += '<div class="mt-2"><strong>Sandbox Violations:</strong><ul>';
                    security.sandboxViolations.forEach(violation => {
                        html += `<li>${violation}</li>`;
                    });
                    html += '</ul></div>';
                }
                
                if (security.permissionIssues.length > 0) {
                    html += '<div class="mt-2"><strong>Permission Issues:</strong><ul>';
                    security.permissionIssues.forEach(issue => {
                        html += `<li>${issue}</li>`;
                    });
                    html += '</ul></div>';
                }
            }
            
            document.getElementById('security-results').innerHTML = html;
        }

        function addLog(level, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logsContainer = document.getElementById('test-logs');
            const logElement = document.createElement('div');
            
            const levelColors = {
                'info': 'text-info',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            };
            
            logElement.innerHTML = `
                <span class="text-muted">[${timestamp}]</span> 
                <span class="${levelColors[level] || 'text-light'}">[${level.toUpperCase()}]</span> 
                ${message}
            `;
            
            logsContainer.appendChild(logElement);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function addLogFromServer(log) {
            const logsContainer = document.getElementById('test-logs');
            const timestamp = new Date(log.timestamp).toLocaleTimeString();
            const logElement = document.createElement('div');
            
            const levelColors = {
                'info': 'text-info',
                'debug': 'text-secondary',
                'error': 'text-danger',
                'warning': 'text-warning'
            };
            
            logElement.innerHTML = `
                <span class="text-muted">[${timestamp}]</span> 
                <span class="${levelColors[log.level] || 'text-light'}">[${log.level.toUpperCase()}]</span> 
                ${log.message}
            `;
            
            logsContainer.appendChild(logElement);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('test-logs').innerHTML = 
                '<div class="text-muted">Logs cleared</div>';
        }

        function loadTestHistory() {
            if (!currentAddonId) return;
            
            fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    query: `
                        query GetAddonTestHistory($addonId: ID!) {
                            getAddonTestHistory(addonId: $addonId) {
                                id
                                status
                                startedAt
                                completedAt
                                results {
                                    success
                                    executionTime
                                }
                            }
                        }
                    `,
                    variables: { addonId: currentAddonId }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.data?.getAddonTestHistory) {
                    displayTestHistory(data.data.getAddonTestHistory);
                }
            })
            .catch(error => {
                console.error('Error loading test history:', error);
            });
        }

        function displayTestHistory(history) {
            const container = document.getElementById('test-history');
            
            if (history.length === 0) {
                container.innerHTML = '<div class="text-muted">No previous tests</div>';
                return;
            }
            
            let html = '';
            history.slice(0, 5).forEach(test => {
                const statusIcon = test.results?.success ? 'check-circle text-success' : 'x-circle text-danger';
                const date = new Date(test.startedAt).toLocaleDateString();
                
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <i class="bi bi-${statusIcon}"></i>
                            <small>${date}</small>
                        </div>
                        <small class="text-muted">${test.results?.executionTime || 'N/A'}</small>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
    </script>
</body>
</html>