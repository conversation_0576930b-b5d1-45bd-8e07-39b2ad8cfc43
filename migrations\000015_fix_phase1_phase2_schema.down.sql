-- Rollback Phase 1 and Phase 2 schema fixes

-- Remove indexes
DROP INDEX IF EXISTS idx_sites_user_id;
DROP INDEX IF EXISTS idx_sites_tenant_id;
DROP INDEX IF EXISTS idx_sites_status;
DROP INDEX IF EXISTS idx_sites_custom_domain;
DROP INDEX IF EXISTS idx_site_navigation_site_id;
DROP INDEX IF EXISTS idx_site_navigation_parent_id;
DROP INDEX IF EXISTS idx_site_navigation_tenant_id;
DROP INDEX IF EXISTS idx_site_pages_site_id;
DROP INDEX IF EXISTS idx_site_pages_page_id;
DROP INDEX IF EXISTS idx_site_pages_tenant_id;
DROP INDEX IF EXISTS idx_web_addons_tenant_id;
DROP INDEX IF EXISTS idx_predefined_snippets_tenant_id;
DROP INDEX IF EXISTS idx_component_registry_tenant_id;

-- Drop new tables
DROP TABLE IF EXISTS site_pages;
DROP TABLE IF EXISTS site_navigation;

-- Remove tenant_id columns from component tables
ALTER TABLE component_registry DROP COLUMN IF EXISTS tenant_id;
ALTER TABLE predefined_snippets DROP COLUMN IF EXISTS tenant_id;
ALTER TABLE web_addons DROP COLUMN IF EXISTS tenant_id;

-- Remove tenant_id from api_keys
ALTER TABLE api_keys DROP COLUMN IF EXISTS tenant_id;
ALTER TABLE api_keys DROP COLUMN IF EXISTS key_value;

-- Remove new columns from sites table
ALTER TABLE sites DROP COLUMN IF EXISTS seo_config;
ALTER TABLE sites DROP COLUMN IF EXISTS theme_config;
ALTER TABLE sites DROP COLUMN IF EXISTS ssl_enabled;
ALTER TABLE sites DROP COLUMN IF EXISTS custom_domain;
ALTER TABLE sites DROP COLUMN IF EXISTS status;
ALTER TABLE sites DROP COLUMN IF EXISTS description;
ALTER TABLE sites DROP COLUMN IF EXISTS name;
ALTER TABLE sites DROP COLUMN IF EXISTS user_id;

-- Restore original sites table structure
ALTER TABLE sites ADD COLUMN IF NOT EXISTS domain VARCHAR(255) UNIQUE NOT NULL DEFAULT 'example.com';
ALTER TABLE sites ADD COLUMN IF NOT EXISTS owner_id INT NOT NULL REFERENCES users(id) ON DELETE CASCADE DEFAULT 1;