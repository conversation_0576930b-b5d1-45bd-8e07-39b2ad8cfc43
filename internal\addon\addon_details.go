package addon

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// AddonDetailsService handles addon detail page functionality
type AddonDetailsService struct {
	marketplace      *Marketplace
	versioningSystem *VersioningSystem
	reviewSystem     *ReviewSystem
	addonReviews     map[string][]*AddonReview
	addonStats       map[string]*AddonStats
	mutex            sync.RWMutex
}

// AddonDetails contains comprehensive addon information for detail pages
type AddonDetails struct {
	Addon         *MarketplaceAddon   `json:"addon"`
	Versions      []*AddonVersion     `json:"versions"`
	Reviews       []*AddonReview      `json:"reviews"`
	Stats         *AddonStats         `json:"stats"`
	RelatedAddons []*MarketplaceAddon `json:"relatedAddons"`
	Screenshots   []*Screenshot       `json:"screenshots"`
	Documentation *Documentation      `json:"documentation"`
	Changelog     []*ChangelogEntry   `json:"changelog"`
	Dependencies  []*DependencyInfo   `json:"dependencies"`
	Compatibility *CompatibilityInfo  `json:"compatibility"`
	SupportInfo   *SupportInfo        `json:"supportInfo"`
}

// AddonReview represents a user review
type AddonReview struct {
	ID         string                 `json:"id"`
	AddonID    string                 `json:"addonId"`
	UserID     string                 `json:"userId"`
	Username   string                 `json:"username"`
	UserAvatar string                 `json:"userAvatar"`
	Rating     int                    `json:"rating"`
	Title      string                 `json:"title"`
	Content    string                 `json:"content"`
	Pros       []string               `json:"pros"`
	Cons       []string               `json:"cons"`
	Verified   bool                   `json:"verified"`
	Helpful    int                    `json:"helpful"`
	CreatedAt  time.Time              `json:"createdAt"`
	UpdatedAt  time.Time              `json:"updatedAt"`
	Version    string                 `json:"version"`
	Context    map[string]interface{} `json:"context"`
}

// AddonStats contains detailed statistics
type AddonStats struct {
	TotalDownloads   int64                    `json:"totalDownloads"`
	WeeklyDownloads  int64                    `json:"weeklyDownloads"`
	MonthlyDownloads int64                    `json:"monthlyDownloads"`
	ActiveInstalls   int64                    `json:"activeInstalls"`
	TotalViews       int64                    `json:"totalViews"`
	UniqueViews      int64                    `json:"uniqueViews"`
	RatingBreakdown  map[int]int              `json:"ratingBreakdown"`
	VersionStats     map[string]*VersionStats `json:"versionStats"`
	GeographicStats  map[string]int64         `json:"geographicStats"`
	UsageStats       *UsageStats              `json:"usageStats"`
	TrendData        []TrendPoint             `json:"trendData"`
}

// VersionStats contains statistics for a specific version
type VersionStats struct {
	Version          string    `json:"version"`
	Downloads        int64     `json:"downloads"`
	ActiveInstalls   int64     `json:"activeInstalls"`
	CrashRate        float64   `json:"crashRate"`
	PerformanceScore float64   `json:"performanceScore"`
	ReleasedAt       time.Time `json:"releasedAt"`
}

// UsageStats contains usage analytics
type UsageStats struct {
	AverageSessionTime time.Duration      `json:"averageSessionTime"`
	FeatureUsage       map[string]int64   `json:"featureUsage"`
	ErrorRate          float64            `json:"errorRate"`
	PerformanceMetrics map[string]float64 `json:"performanceMetrics"`
	RetentionRate      map[string]float64 `json:"retentionRate"`
}

// TrendPoint represents a point in trend data
type TrendPoint struct {
	Date      time.Time `json:"date"`
	Downloads int64     `json:"downloads"`
	Views     int64     `json:"views"`
	Rating    float64   `json:"rating"`
}

// Screenshot represents an addon screenshot
type Screenshot struct {
	ID           string `json:"id"`
	URL          string `json:"url"`
	ThumbnailURL string `json:"thumbnailUrl"`
	Caption      string `json:"caption"`
	Order        int    `json:"order"`
	Type         string `json:"type"` // screenshot, video, demo
}

// Documentation contains addon documentation
type Documentation struct {
	README          string                `json:"readme"`
	Installation    string                `json:"installation"`
	Configuration   string                `json:"configuration"`
	Usage           string                `json:"usage"`
	API             string                `json:"api"`
	FAQ             []FAQItem             `json:"faq"`
	Examples        []ExampleCode         `json:"examples"`
	Troubleshooting []TroubleshootingItem `json:"troubleshooting"`
}

// FAQItem represents a frequently asked question
type FAQItem struct {
	Question string `json:"question"`
	Answer   string `json:"answer"`
	Category string `json:"category"`
	Order    int    `json:"order"`
}

// ExampleCode represents a code example
type ExampleCode struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Code        string `json:"code"`
	Language    string `json:"language"`
	Category    string `json:"category"`
}

// TroubleshootingItem represents a troubleshooting guide
type TroubleshootingItem struct {
	Problem    string   `json:"problem"`
	Solution   string   `json:"solution"`
	Steps      []string `json:"steps"`
	Category   string   `json:"category"`
	Difficulty string   `json:"difficulty"`
}

// ChangelogEntry represents a changelog entry
type ChangelogEntry struct {
	Version     string    `json:"version"`
	Date        time.Time `json:"date"`
	Type        string    `json:"type"` // major, minor, patch, hotfix
	Changes     []Change  `json:"changes"`
	Breaking    bool      `json:"breaking"`
	Security    bool      `json:"security"`
	Description string    `json:"description"`
}

// Change represents a single change in a version
type Change struct {
	Type        string `json:"type"` // added, changed, deprecated, removed, fixed, security
	Description string `json:"description"`
	Impact      string `json:"impact"` // low, medium, high
}

// DependencyInfo contains information about dependencies
type DependencyInfo struct {
	Name            string `json:"name"`
	Version         string `json:"version"`
	Type            string `json:"type"` // required, optional, development
	Description     string `json:"description"`
	Homepage        string `json:"homepage"`
	License         string `json:"license"`
	SecurityStatus  string `json:"securityStatus"`
	UpdateAvailable bool   `json:"updateAvailable"`
}

// CompatibilityInfo contains compatibility information
type CompatibilityInfo struct {
	PlatformVersions []PlatformVersion `json:"platformVersions"`
	BrowserSupport   []BrowserSupport  `json:"browserSupport"`
	DeviceSupport    []string          `json:"deviceSupport"`
	LanguageSupport  []string          `json:"languageSupport"`
	RegionSupport    []string          `json:"regionSupport"`
	IntegrationTests []IntegrationTest `json:"integrationTests"`
}

// PlatformVersion represents platform compatibility
type PlatformVersion struct {
	Version    string    `json:"version"`
	Supported  bool      `json:"supported"`
	Tested     bool      `json:"tested"`
	Notes      string    `json:"notes"`
	LastTested time.Time `json:"lastTested"`
}

// BrowserSupport represents browser compatibility
type BrowserSupport struct {
	Browser    string `json:"browser"`
	MinVersion string `json:"minVersion"`
	MaxVersion string `json:"maxVersion"`
	Supported  bool   `json:"supported"`
	Notes      string `json:"notes"`
}

// IntegrationTest represents an integration test result
type IntegrationTest struct {
	Name        string        `json:"name"`
	Status      string        `json:"status"`
	LastRun     time.Time     `json:"lastRun"`
	Duration    time.Duration `json:"duration"`
	Environment string        `json:"environment"`
	Details     string        `json:"details"`
}

// SupportInfo contains support information
type SupportInfo struct {
	SupportEmail     string           `json:"supportEmail"`
	DocumentationURL string           `json:"documentationUrl"`
	CommunityURL     string           `json:"communityUrl"`
	IssueTrackerURL  string           `json:"issueTrackerUrl"`
	SupportHours     string           `json:"supportHours"`
	ResponseTime     string           `json:"responseTime"`
	SupportChannels  []SupportChannel `json:"supportChannels"`
	KnownIssues      []KnownIssue     `json:"knownIssues"`
}

// SupportChannel represents a support channel
type SupportChannel struct {
	Type         string `json:"type"` // email, chat, forum, phone
	Contact      string `json:"contact"`
	Hours        string `json:"hours"`
	Language     string `json:"language"`
	ResponseTime string `json:"responseTime"`
}

// KnownIssue represents a known issue
type KnownIssue struct {
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Severity    string    `json:"severity"`
	Status      string    `json:"status"`
	Workaround  string    `json:"workaround"`
	ReportedAt  time.Time `json:"reportedAt"`
	FixedIn     string    `json:"fixedIn,omitempty"`
}

// NewAddonDetailsService creates a new addon details service
func NewAddonDetailsService() *AddonDetailsService {
	return &AddonDetailsService{
		marketplace:      NewMarketplace(MarketplaceConfig{}),
		versioningSystem: NewVersioningSystem(VersioningConfig{}),
		reviewSystem:     NewReviewSystem(ReviewConfig{}),
		addonReviews:     make(map[string][]*AddonReview),
		addonStats:       make(map[string]*AddonStats),
	}
}

// GetAddonDetails retrieves comprehensive addon details
func (ads *AddonDetailsService) GetAddonDetails(ctx context.Context, addonID string) (*AddonDetails, error) {
	ads.mutex.RLock()
	defer ads.mutex.RUnlock()

	// Get addon from marketplace
	addon, exists := ads.marketplace.addons[addonID]
	if !exists {
		return nil, fmt.Errorf("addon not found: %s", addonID)
	}

	// Get versions
	versions, err := ads.versioningSystem.ListVersions(addonID, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get versions: %w", err)
	}

	// Get reviews
	reviews := ads.getAddonReviews(addonID)

	// Get stats
	stats := ads.getAddonStats(addonID)

	// Get related addons
	relatedAddons := ads.getRelatedAddons(addon)

	// Generate other details
	details := &AddonDetails{
		Addon:         addon,
		Versions:      versions,
		Reviews:       reviews,
		Stats:         stats,
		RelatedAddons: relatedAddons,
		Screenshots:   ads.generateScreenshots(addon),
		Documentation: ads.generateDocumentation(addon),
		Changelog:     ads.generateChangelog(versions),
		Dependencies:  ads.generateDependencies(addon),
		Compatibility: ads.generateCompatibility(addon),
		SupportInfo:   ads.generateSupportInfo(addon),
	}

	return details, nil
}

// AddReview adds a review for an addon
func (ads *AddonDetailsService) AddReview(ctx context.Context, addonID, userID, username string, rating int, title, content string, pros, cons []string) (*AddonReview, error) {
	ads.mutex.Lock()
	defer ads.mutex.Unlock()

	review := &AddonReview{
		ID:         generateReviewID(),
		AddonID:    addonID,
		UserID:     userID,
		Username:   username,
		UserAvatar: fmt.Sprintf("/api/users/%s/avatar", userID),
		Rating:     rating,
		Title:      title,
		Content:    content,
		Pros:       pros,
		Cons:       cons,
		Verified:   false, // Would be set based on actual purchase/usage
		Helpful:    0,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Version:    "", // Would be set to the version being reviewed
		Context:    make(map[string]interface{}),
	}

	// Add to reviews
	ads.addonReviews[addonID] = append(ads.addonReviews[addonID], review)

	// Update addon metrics
	ads.updateAddonRating(addonID)

	return review, nil
}

// Helper methods

func (ads *AddonDetailsService) getAddonReviews(addonID string) []*AddonReview {
	reviews, exists := ads.addonReviews[addonID]
	if !exists {
		return []*AddonReview{}
	}
	return reviews
}

func (ads *AddonDetailsService) getAddonStats(addonID string) *AddonStats {
	stats, exists := ads.addonStats[addonID]
	if !exists {
		// Generate default stats
		stats = &AddonStats{
			TotalDownloads:   1000 + int64(len(addonID)*100),
			WeeklyDownloads:  50 + int64(len(addonID)*5),
			MonthlyDownloads: 200 + int64(len(addonID)*20),
			ActiveInstalls:   800 + int64(len(addonID)*80),
			TotalViews:       5000 + int64(len(addonID)*500),
			UniqueViews:      3000 + int64(len(addonID)*300),
			RatingBreakdown:  map[int]int{5: 45, 4: 30, 3: 15, 2: 7, 1: 3},
			VersionStats:     make(map[string]*VersionStats),
			GeographicStats:  map[string]int64{"US": 400, "EU": 300, "ASIA": 200, "OTHER": 100},
			UsageStats: &UsageStats{
				AverageSessionTime: time.Minute * 15,
				FeatureUsage:       map[string]int64{"feature1": 800, "feature2": 600, "feature3": 400},
				ErrorRate:          0.02,
				PerformanceMetrics: map[string]float64{"loadTime": 1.2, "responseTime": 0.8},
				RetentionRate:      map[string]float64{"day1": 0.85, "day7": 0.65, "day30": 0.45},
			},
			TrendData: ads.generateTrendData(),
		}
		ads.addonStats[addonID] = stats
	}
	return stats
}

func (ads *AddonDetailsService) getRelatedAddons(addon *MarketplaceAddon) []*MarketplaceAddon {
	var related []*MarketplaceAddon

	// Find addons in the same category
	for _, otherAddon := range ads.marketplace.addons {
		if otherAddon.ID != addon.ID &&
			otherAddon.Category == addon.Category &&
			otherAddon.Status == AddonStatusPublished {
			related = append(related, otherAddon)
		}
	}

	// Limit to 6 related addons
	if len(related) > 6 {
		related = related[:6]
	}

	return related
}

func (ads *AddonDetailsService) generateScreenshots(addon *MarketplaceAddon) []*Screenshot {
	return []*Screenshot{
		{
			ID:           "screenshot1",
			URL:          fmt.Sprintf("/api/addons/%s/screenshots/1.png", addon.ID),
			ThumbnailURL: fmt.Sprintf("/api/addons/%s/screenshots/1_thumb.png", addon.ID),
			Caption:      "Main interface",
			Order:        1,
			Type:         "screenshot",
		},
		{
			ID:           "screenshot2",
			URL:          fmt.Sprintf("/api/addons/%s/screenshots/2.png", addon.ID),
			ThumbnailURL: fmt.Sprintf("/api/addons/%s/screenshots/2_thumb.png", addon.ID),
			Caption:      "Configuration panel",
			Order:        2,
			Type:         "screenshot",
		},
	}
}

func (ads *AddonDetailsService) generateDocumentation(addon *MarketplaceAddon) *Documentation {
	return &Documentation{
		README:        fmt.Sprintf("# %s\n\n%s\n\n## Features\n\n- Feature 1\n- Feature 2\n- Feature 3", addon.Name, addon.Description),
		Installation:  "## Installation\n\n1. Click the Install button\n2. Configure the addon\n3. Start using it!",
		Configuration: "## Configuration\n\nConfigure the addon through the settings panel.",
		Usage:         "## Usage\n\nUse the addon by accessing it from the main menu.",
		API:           "## API Reference\n\nAPI documentation coming soon.",
		FAQ: []FAQItem{
			{Question: "How do I install this addon?", Answer: "Click the install button and follow the prompts.", Category: "installation", Order: 1},
			{Question: "Is this addon free?", Answer: "Check the pricing information on this page.", Category: "pricing", Order: 2},
		},
		Examples: []ExampleCode{
			{Title: "Basic Usage", Description: "Basic example", Code: "// Example code here", Language: "javascript", Category: "basic"},
		},
		Troubleshooting: []TroubleshootingItem{
			{Problem: "Addon not loading", Solution: "Try refreshing the page", Steps: []string{"Refresh page", "Clear cache"}, Category: "loading", Difficulty: "easy"},
		},
	}
}

func (ads *AddonDetailsService) generateChangelog(versions []*AddonVersion) []*ChangelogEntry {
	var changelog []*ChangelogEntry

	for _, version := range versions {
		entry := &ChangelogEntry{
			Version:     version.Version,
			Date:        version.CreatedAt,
			Type:        string(version.Type),
			Description: "Version " + version.Version + " release",
			Changes: []Change{
				{Type: "added", Description: "New features added", Impact: "medium"},
				{Type: "fixed", Description: "Bug fixes", Impact: "low"},
			},
			Breaking: version.Type == VersionTypeMajor,
			Security: false,
		}
		changelog = append(changelog, entry)
	}

	return changelog
}

func (ads *AddonDetailsService) generateDependencies(addon *MarketplaceAddon) []*DependencyInfo {
	var dependencies []*DependencyInfo

	for _, dep := range addon.Dependencies {
		dependencies = append(dependencies, &DependencyInfo{
			Name:            dep,
			Version:         "^1.0.0",
			Type:            "required",
			Description:     "Required dependency",
			License:         "MIT",
			SecurityStatus:  "secure",
			UpdateAvailable: false,
		})
	}

	return dependencies
}

func (ads *AddonDetailsService) generateCompatibility(addon *MarketplaceAddon) *CompatibilityInfo {
	return &CompatibilityInfo{
		PlatformVersions: []PlatformVersion{
			{Version: "1.0.0", Supported: true, Tested: true, LastTested: time.Now().AddDate(0, 0, -7)},
			{Version: "1.1.0", Supported: true, Tested: true, LastTested: time.Now().AddDate(0, 0, -3)},
		},
		BrowserSupport: []BrowserSupport{
			{Browser: "Chrome", MinVersion: "90", Supported: true},
			{Browser: "Firefox", MinVersion: "88", Supported: true},
			{Browser: "Safari", MinVersion: "14", Supported: true},
		},
		DeviceSupport:   []string{"Desktop", "Tablet", "Mobile"},
		LanguageSupport: []string{"English", "Spanish", "French"},
		RegionSupport:   []string{"Global"},
	}
}

func (ads *AddonDetailsService) generateSupportInfo(addon *MarketplaceAddon) *SupportInfo {
	return &SupportInfo{
		SupportEmail:     fmt.Sprintf("support@%s.com", addon.AuthorID),
		DocumentationURL: fmt.Sprintf("/marketplace/addon/%s/docs", addon.ID),
		ResponseTime:     "24 hours",
		SupportHours:     "9 AM - 5 PM UTC",
		SupportChannels: []SupportChannel{
			{Type: "email", Contact: fmt.Sprintf("support@%s.com", addon.AuthorID), Hours: "24/7", Language: "English"},
		},
		KnownIssues: []KnownIssue{},
	}
}

func (ads *AddonDetailsService) generateTrendData() []TrendPoint {
	var trendData []TrendPoint
	now := time.Now()

	for i := 30; i >= 0; i-- {
		date := now.AddDate(0, 0, -i)
		trendData = append(trendData, TrendPoint{
			Date:      date,
			Downloads: int64(50 + i*2),
			Views:     int64(200 + i*10),
			Rating:    4.2 + float64(i)*0.01,
		})
	}

	return trendData
}

func (ads *AddonDetailsService) updateAddonRating(addonID string) {
	reviews := ads.addonReviews[addonID]
	if len(reviews) == 0 {
		return
	}

	totalRating := 0
	for _, review := range reviews {
		totalRating += review.Rating
	}

	avgRating := float64(totalRating) / float64(len(reviews))

	// Update addon in marketplace
	if addon, exists := ads.marketplace.addons[addonID]; exists {
		addon.Metrics.Rating = avgRating
		addon.Metrics.ReviewCount = len(reviews)
	}
}

func generateReviewID() string {
	return fmt.Sprintf("review_%d", time.Now().UnixNano())
}
