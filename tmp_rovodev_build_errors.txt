# goVwPlatformAPI/internal/database
internal\database\database.go:111:12: snippet.HTML undefined (type model.PredefinedSnippet has no field or method HTML)
internal\database\database.go:111:27: snippet.CSS undefined (type model.PredefinedSnippet has no field or method CSS)
internal\database\database.go:111:41: snippet.JavaScript undefined (type model.PredefinedSnippet has no field or method JavaScript)
internal\database\database.go:111:62: snippet.Preview undefined (type model.PredefinedSnippet has no field or method Preview)
internal\database\database.go:136:12: snippet.HTML undefined (type model.PredefinedSnippet has no field or method HTML)
internal\database\database.go:136:27: snippet.CSS undefined (type model.PredefinedSnippet has no field or method CSS)
internal\database\database.go:136:41: snippet.JavaScript undefined (type model.PredefinedSnippet has no field or method JavaScript)
internal\database\database.go:136:62: snippet.Preview undefined (type model.PredefinedSnippet has no field or method Preview)
internal\database\database.go:256:13: snippet.HTML undefined (type model.PredefinedSnippet has no field or method HTML)
internal\database\database.go:256:28: snippet.CSS undefined (type model.PredefinedSnippet has no field or method CSS)
internal\database\database.go:256:28: too many errors
