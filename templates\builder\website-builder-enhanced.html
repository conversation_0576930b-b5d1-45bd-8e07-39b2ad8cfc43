<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Builder - Velocity Platform</title>
    <meta name="description" content="Professional drag-and-drop website builder with Bootstrap components">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <link href="/static/css/platform.css" rel="stylesheet">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/handlebars@4.7.8/dist/handlebars.min.js"></script>
</head>
<body class="bg-light">
    <!-- Enhanced Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-velocity shadow-sm">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <a class="navbar-brand text-white fw-bold me-4" href="/">
                    <i class="bi bi-lightning-charge-fill me-2"></i>Velocity Platform
                </a>
                <span class="text-white-50">|</span>
                <span class="text-white ms-3">
                    <i class="bi bi-tools me-2"></i>Website Builder
                </span>
            </div>
            
            <!-- Project Info -->
            <div class="d-flex align-items-center text-white me-auto ms-4">
                <div id="project-info" hx-get="/api/builder/project/current" hx-trigger="load" hx-swap="innerHTML">
                    <small class="opacity-75">My Website Project</small>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="d-flex align-items-center gap-2">
                <div class="btn-group">
                    <button class="btn btn-outline-light btn-sm" onclick="builder.undoAction()" title="Undo">
                        <i class="bi bi-arrow-counterclockwise"></i>
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="builder.redoAction()" title="Redo">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
                
                <button class="btn btn-outline-light btn-sm" onclick="previewSite()" title="Preview">
                    <i class="bi bi-eye me-1"></i>Preview
                </button>
                
                <div class="btn-group">
                    <button class="btn btn-success btn-sm" onclick="saveSite()">
                        <i class="bi bi-floppy me-1"></i>Save
                    </button>
                    <button class="btn btn-success btn-sm dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                        <span class="visually-hidden">Toggle Dropdown</span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="saveSite()">
                            <i class="bi bi-floppy me-2"></i>Save
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="saveAsTemplate()">
                            <i class="bi bi-file-earmark-plus me-2"></i>Save as Template
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="exportProject()">
                            <i class="bi bi-download me-2"></i>Export Project
                        </a></li>
                    </ul>
                </div>
                
                <button class="btn btn-warning btn-sm" onclick="publishSite()">
                    <i class="bi bi-cloud-upload me-1"></i>Publish
                </button>
            </div>
        </div>
    </nav>

    <!-- Builder Interface -->
    <div class="container-fluid p-0 h-100">
        <div class="row g-0 h-100">
            <!-- Enhanced Component Palette -->
            <div class="col-md-3 component-palette border-end bg-white">
                <div class="p-3">
                    <!-- Search Components -->
                    <div class="mb-3">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">
                                <i class="bi bi-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="Search components..." 
                                   id="component-search" onkeyup="builder.filterComponents(this.value)">
                        </div>
                    </div>
                    
                    <h6 class="fw-bold mb-3">
                        <i class="bi bi-puzzle me-2"></i>Components
                    </h6>
                    
                    <!-- Component Categories Tabs -->
                    <ul class="nav nav-pills nav-fill mb-3" id="component-tabs">
                        <li class="nav-item">
                            <button class="nav-link active small" data-bs-toggle="pill" data-bs-target="#bootstrap-components">
                                Bootstrap
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link small" data-bs-toggle="pill" data-bs-target="#addon-components">
                                Addons
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link small" data-bs-toggle="pill" data-bs-target="#snippet-components">
                                Snippets
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" style="max-height: calc(100vh - 200px); overflow-y: auto;">
                        <!-- Bootstrap Components -->
                        <div class="tab-pane fade show active" id="bootstrap-components">
                            <div class="mb-3">
                                <h6 class="text-muted small mb-2">LAYOUT</h6>
                                <div class="component-item" draggable="true" data-component="container">
                                    <i class="bi bi-layout-wtf me-2"></i>Container
                                </div>
                                <div class="component-item" draggable="true" data-component="row">
                                    <i class="bi bi-columns me-2"></i>Row
                                </div>
                                <div class="component-item" draggable="true" data-component="hero">
                                    <i class="bi bi-image me-2"></i>Hero Section
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <h6 class="text-muted small mb-2">NAVIGATION</h6>
                                <div class="component-item" draggable="true" data-component="navbar">
                                    <i class="bi bi-menu-button-wide me-2"></i>Navigation Bar
                                </div>
                                <div class="component-item" draggable="true" data-component="breadcrumb">
                                    <i class="bi bi-arrow-right me-2"></i>Breadcrumb
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <h6 class="text-muted small mb-2">CONTENT</h6>
                                <div class="component-item" draggable="true" data-component="card">
                                    <i class="bi bi-card-text me-2"></i>Card
                                </div>
                                <div class="component-item" draggable="true" data-component="accordion">
                                    <i class="bi bi-list me-2"></i>Accordion
                                </div>
                                <div class="component-item" draggable="true" data-component="carousel">
                                    <i class="bi bi-images me-2"></i>Carousel
                                </div>
                                <div class="component-item" draggable="true" data-component="table">
                                    <i class="bi bi-table me-2"></i>Table
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <h6 class="text-muted small mb-2">FORMS</h6>
                                <div class="component-item" draggable="true" data-component="form">
                                    <i class="bi bi-ui-checks me-2"></i>Contact Form
                                </div>
                                <div class="component-item" draggable="true" data-component="input">
                                    <i class="bi bi-input-cursor me-2"></i>Input Field
                                </div>
                                <div class="component-item" draggable="true" data-component="button">
                                    <i class="bi bi-square me-2"></i>Button
                                </div>
                                <div class="component-item" draggable="true" data-component="select">
                                    <i class="bi bi-menu-down me-2"></i>Select
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <h6 class="text-muted small mb-2">MEDIA</h6>
                                <div class="component-item" draggable="true" data-component="image">
                                    <i class="bi bi-image me-2"></i>Image
                                </div>
                                <div class="component-item" draggable="true" data-component="video">
                                    <i class="bi bi-play-circle me-2"></i>Video
                                </div>
                                <div class="component-item" draggable="true" data-component="gallery">
                                    <i class="bi bi-images me-2"></i>Gallery
                                </div>
                            </div>
                        </div>

                        <!-- Web Addons -->
                        <div class="tab-pane fade" id="addon-components">
                            <div id="addon-list" hx-get="/api/builder/addons" hx-trigger="load">
                                <div class="text-center py-3">
                                    <div class="loading-spinner"></div>
                                    <small class="text-muted">Loading addons...</small>
                                </div>
                            </div>
                        </div>

                        <!-- Code Snippets -->
                        <div class="tab-pane fade" id="snippet-components">
                            <div id="snippet-list" hx-get="/api/builder/snippets" hx-trigger="load">
                                <div class="text-center py-3">
                                    <div class="loading-spinner"></div>
                                    <small class="text-muted">Loading snippets...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Website Canvas -->
            <div class="col-md-6 website-builder-canvas bg-light">
                <div class="p-3 h-100">
                    <!-- Canvas Toolbar -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <h6 class="fw-bold mb-0 me-3">
                                <i class="bi bi-layout-text-window me-2"></i>Canvas
                            </h6>
                            <div class="btn-group btn-group-sm me-3">
                                <button class="btn btn-outline-secondary" onclick="builder.zoomOut()" title="Zoom Out">
                                    <i class="bi bi-zoom-out"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="builder.resetZoom()" title="Reset Zoom">
                                    <span class="small">100%</span>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="builder.zoomIn()" title="Zoom In">
                                    <i class="bi bi-zoom-in"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Device Preview Toggle -->
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary active" data-device="desktop" title="Desktop View">
                                <i class="bi bi-display"></i>
                                <small class="d-none d-lg-inline ms-1">Desktop</small>
                            </button>
                            <button class="btn btn-outline-secondary" data-device="tablet" title="Tablet View">
                                <i class="bi bi-tablet"></i>
                                <small class="d-none d-lg-inline ms-1">Tablet</small>
                            </button>
                            <button class="btn btn-outline-secondary" data-device="mobile" title="Mobile View">
                                <i class="bi bi-phone"></i>
                                <small class="d-none d-lg-inline ms-1">Mobile</small>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Canvas Container -->
                    <div class="canvas-container position-relative">
                        <div id="website-canvas" class="border rounded bg-white shadow-sm p-4" 
                             style="min-height: 600px; transition: all 0.3s ease; max-width: 100%;"
                             ondrop="dropComponent(event)" ondragover="allowDrop(event)">
                            
                            <!-- Default Canvas Content -->
                            <div class="text-center text-muted py-5" id="canvas-placeholder">
                                <i class="bi bi-plus-circle display-4 mb-3 text-primary"></i>
                                <h5>Start Building Your Website</h5>
                                <p class="mb-4">Drag components from the left panel to begin creating your masterpiece</p>
                                <div class="d-flex gap-2 justify-content-center">
                                    <button class="btn btn-outline-primary btn-sm" onclick="builder.loadTemplate('blank')">
                                        <i class="bi bi-file-earmark me-1"></i>Start Blank
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="builder.loadTemplate('basic')">
                                        <i class="bi bi-layout-text-window me-1"></i>Basic Template
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Canvas Overlay for Interactions -->
                        <div id="canvas-overlay" class="position-absolute top-0 start-0 w-100 h-100 pointer-events-none">
                            <!-- Rulers, guides, etc. would go here -->
                        </div>
                    </div>
                    
                    <!-- Canvas Status Bar -->
                    <div class="d-flex justify-content-between align-items-center mt-2 small text-muted">
                        <div>
                            <span id="canvas-status">Ready</span>
                            <span class="mx-2">|</span>
                            <span id="component-count">0 components</span>
                        </div>
                        <div>
                            <span id="canvas-dimensions">1200 x 800</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Properties Panel -->
            <div class="col-md-3 theme-editor border-start bg-white">
                <div class="p-3 h-100">
                    <!-- Properties Tabs -->
                    <ul class="nav nav-tabs mb-3" id="properties-tabs">
                        <li class="nav-item">
                            <button class="nav-link active small" data-bs-toggle="tab" data-bs-target="#component-props">
                                <i class="bi bi-sliders me-1"></i>Properties
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link small" data-bs-toggle="tab" data-bs-target="#theme-props">
                                <i class="bi bi-palette me-1"></i>Theme
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link small" data-bs-toggle="tab" data-bs-target="#page-props">
                                <i class="bi bi-file-earmark me-1"></i>Page
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" style="max-height: calc(100vh - 150px); overflow-y: auto;">
                        <!-- Component Properties -->
                        <div class="tab-pane fade show active" id="component-props">
                            <div id="component-properties">
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-cursor me-2"></i>
                                    <small>Select a component to edit properties</small>
                                </div>
                            </div>
                        </div>

                        <!-- Theme Settings -->
                        <div class="tab-pane fade" id="theme-props">
                            <h6 class="fw-bold mb-3">Color Scheme</h6>
                            
                            <div class="color-picker-group">
                                <label class="color-picker-label">Primary Color</label>
                                <input type="color" class="color-picker-input" value="#0d6efd" 
                                       onchange="updateThemeColor('primary', this.value)">
                            </div>
                            
                            <div class="color-picker-group">
                                <label class="color-picker-label">Secondary Color</label>
                                <input type="color" class="color-picker-input" value="#6c757d"
                                       onchange="updateThemeColor('secondary', this.value)">
                            </div>
                            
                            <div class="color-picker-group">
                                <label class="color-picker-label">Success Color</label>
                                <input type="color" class="color-picker-input" value="#198754"
                                       onchange="updateThemeColor('success', this.value)">
                            </div>
                            
                            <div class="color-picker-group">
                                <label class="color-picker-label">Warning Color</label>
                                <input type="color" class="color-picker-input" value="#ffc107"
                                       onchange="updateThemeColor('warning', this.value)">
                            </div>
                            
                            <div class="color-picker-group">
                                <label class="color-picker-label">Danger Color</label>
                                <input type="color" class="color-picker-input" value="#dc3545"
                                       onchange="updateThemeColor('danger', this.value)">
                            </div>
                            
                            <hr>
                            
                            <h6 class="fw-bold mb-3">Typography</h6>
                            <div class="mb-3">
                                <label class="form-label">Font Family</label>
                                <select class="form-select" onchange="updateThemeFont(this.value)">
                                    <option value="system">System Default</option>
                                    <option value="Inter">Inter</option>
                                    <option value="Roboto">Roboto</option>
                                    <option value="Open Sans">Open Sans</option>
                                    <option value="Lato">Lato</option>
                                </select>
                            </div>
                            
                            <hr>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="builder.resetTheme()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Reset Theme
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="builder.saveTheme()">
                                    <i class="bi bi-floppy me-1"></i>Save Theme
                                </button>
                            </div>
                        </div>

                        <!-- Page Settings -->
                        <div class="tab-pane fade" id="page-props">
                            <h6 class="fw-bold mb-3">Page Settings</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">Page Title</label>
                                <input type="text" class="form-control" id="page-title" placeholder="My Awesome Page">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Meta Description</label>
                                <textarea class="form-control" id="page-description" rows="3" 
                                          placeholder="Describe your page for search engines..."></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Page URL</label>
                                <input type="text" class="form-control" id="page-url" placeholder="/my-page">
                            </div>
                            
                            <hr>
                            
                            <h6 class="fw-bold mb-3">SEO Settings</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">Keywords</label>
                                <input type="text" class="form-control" id="page-keywords" 
                                       placeholder="keyword1, keyword2, keyword3">
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="page-index">
                                <label class="form-check-label" for="page-index">
                                    Allow search engine indexing
                                </label>
                            </div>
                            
                            <hr>
                            
                            <div class="d-grid">
                                <button class="btn btn-outline-primary btn-sm" onclick="builder.updatePageSettings()">
                                    <i class="bi bi-check me-1"></i>Update Page Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/website-builder.js"></script>
    <script src="/static/js/dragdrop.js"></script>
    
    <!-- Enhanced Builder JavaScript -->
    <script>
        // Initialize enhanced features
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-save functionality
            setInterval(function() {
                if (builder && builder.hasUnsavedChanges) {
                    builder.autoSave();
                }
            }, 30000); // Auto-save every 30 seconds
            
            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 's':
                            e.preventDefault();
                            saveSite();
                            break;
                        case 'z':
                            e.preventDefault();
                            if (e.shiftKey) {
                                builder.redoAction();
                            } else {
                                builder.undoAction();
                            }
                            break;
                    }
                }
            });
        });
    </script>
</body>
</html>