package pagemanager

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"goVwPlatformAPI/internal/database"
)

type LayoutService struct {
	db          *database.DB
	pageManager *PageManager
}

func NewLayoutService(db *database.DB, pm *PageManager) *LayoutService {
	return &LayoutService{
		db:          db,
		pageManager: pm,
	}
}

func (s *LayoutService) UpdateLayout(ctx context.Context, pageID string, components []json.RawMessage, userID string) (*PageContent, error) {
	content, err := s.pageManager.GetPageContent(ctx, pageID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get page content: %w", err)
	}

	// Update layout in content
	if content.Content == nil {
		content.Content = make(map[string]interface{})
	}
	content.Content["layout"] = components
	content.Version++
	content.UpdatedAt = time.Now()

	// Save updated content
	_, err = s.pageManager.SavePageContent(ctx, pageID, content.Content, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to save layout: %w", err)
	}

	return content, nil
}

func (s *LayoutService) PublishLayout(ctx context.Context, pageID string, userID string) error {
	_, err := s.pageManager.PublishPage(ctx, pageID, userID)
	return err
}
