package admin

import (
	"context"
	"fmt"
	"goVwPlatformAPI/internal/database"
	"time"
)

// ExpertManagementService handles admin expert management operations
type ExpertManagementService struct {
	db *database.DB
}

// NewExpertManagementService creates a new expert management service
func NewExpertManagementService(db *database.DB) *ExpertManagementService {
	return &ExpertManagementService{db: db}
}

// AdminExpertApplication represents an expert application from admin perspective
type AdminExpertApplication struct {
	ID                    string                 `json:"id"`
	UserID                string                 `json:"user_id"`
	UserEmail             string                 `json:"user_email"`
	FirstName             string                 `json:"first_name"`
	LastName              string                 `json:"last_name"`
	Company               string                 `json:"company"`
	Status                string                 `json:"status"`
	ExpertiseAreas        []string               `json:"expertise_areas"`
	YearsOfExperience     int                    `json:"years_of_experience"`
	HourlyRate            float64                `json:"hourly_rate"`
	Currency              string                 `json:"currency"`
	Bio                   string                 `json:"bio"`
	Portfolio             []PortfolioItem        `json:"portfolio"`
	Certifications        []Certification        `json:"certifications"`
	Education             []Education            `json:"education"`
	Languages             []string               `json:"languages"`
	Availability          string                 `json:"availability"`
	PreferredProjectTypes []string               `json:"preferred_project_types"`
	LinkedInURL           string                 `json:"linkedin_url"`
	WebsiteURL            string                 `json:"website_url"`
	ResumeURL             string                 `json:"resume_url"`
	CoverLetter           string                 `json:"cover_letter"`
	ReviewNotes           string                 `json:"review_notes"`
	ReviewedBy            string                 `json:"reviewed_by"`
	ReviewedAt            *time.Time             `json:"reviewed_at"`
	ApprovedBy            string                 `json:"approved_by"`
	ApprovedAt            *time.Time             `json:"approved_at"`
	RejectionReason       string                 `json:"rejection_reason"`
	CreatedAt             time.Time              `json:"created_at"`
	UpdatedAt             time.Time              `json:"updated_at"`
	Metadata              map[string]interface{} `json:"metadata"`
	Score                 float64                `json:"score"`
	Flags                 []string               `json:"flags"`
}

// AdminExpertProfile represents an expert profile from admin perspective
type AdminExpertProfile struct {
	ID                    string                 `json:"id"`
	UserID                string                 `json:"user_id"`
	UserEmail             string                 `json:"user_email"`
	FirstName             string                 `json:"first_name"`
	LastName              string                 `json:"last_name"`
	Company               string                 `json:"company"`
	Status                string                 `json:"status"`
	Tier                  string                 `json:"tier"`
	ExpertiseAreas        []string               `json:"expertise_areas"`
	YearsOfExperience     int                    `json:"years_of_experience"`
	HourlyRate            float64                `json:"hourly_rate"`
	Currency              string                 `json:"currency"`
	Rating                float64                `json:"rating"`
	ReviewCount           int64                  `json:"review_count"`
	CompletedProjects     int64                  `json:"completed_projects"`
	ActiveProjects        int64                  `json:"active_projects"`
	TotalEarnings         float64                `json:"total_earnings"`
	LastActiveAt          *time.Time             `json:"last_active_at"`
	VerificationStatus    string                 `json:"verification_status"`
	BackgroundCheckStatus string                 `json:"background_check_status"`
	CreatedAt             time.Time              `json:"created_at"`
	UpdatedAt             time.Time              `json:"updated_at"`
	Flags                 []string               `json:"flags"`
	Notes                 string                 `json:"notes"`
	Metadata              map[string]interface{} `json:"metadata"`
}

// PortfolioItem represents a portfolio item
type PortfolioItem struct {
	Title        string   `json:"title"`
	Description  string   `json:"description"`
	URL          string   `json:"url"`
	ImageURL     string   `json:"image_url"`
	Technologies []string `json:"technologies"`
	Year         int      `json:"year"`
}

// Certification represents a professional certification
type Certification struct {
	Name         string     `json:"name"`
	Issuer       string     `json:"issuer"`
	IssueDate    time.Time  `json:"issue_date"`
	ExpiryDate   *time.Time `json:"expiry_date"`
	CredentialID string     `json:"credential_id"`
	URL          string     `json:"url"`
}

// Education represents educational background
type Education struct {
	Institution  string `json:"institution"`
	Degree       string `json:"degree"`
	FieldOfStudy string `json:"field_of_study"`
	StartYear    int    `json:"start_year"`
	EndYear      int    `json:"end_year"`
	GPA          string `json:"gpa"`
}

// ExpertApplicationFilters represents filters for expert application search
type ExpertApplicationFilters struct {
	Status          string     `json:"status"`
	ExpertiseArea   string     `json:"expertise_area"`
	MinExperience   *int       `json:"min_experience"`
	MaxExperience   *int       `json:"max_experience"`
	MinHourlyRate   *float64   `json:"min_hourly_rate"`
	MaxHourlyRate   *float64   `json:"max_hourly_rate"`
	SubmittedAfter  *time.Time `json:"submitted_after"`
	SubmittedBefore *time.Time `json:"submitted_before"`
	ReviewedBy      string     `json:"reviewed_by"`
	HasFlags        []string   `json:"has_flags"`
	MinScore        *float64   `json:"min_score"`
}

// ExpertApplicationListResponse represents paginated expert application list
type ExpertApplicationListResponse struct {
	Applications []AdminExpertApplication `json:"applications"`
	Total        int64                    `json:"total"`
	Page         int                      `json:"page"`
	PageSize     int                      `json:"page_size"`
	TotalPages   int                      `json:"total_pages"`
}

// GetExpertApplications retrieves expert applications with filtering and pagination
func (s *ExpertManagementService) GetExpertApplications(ctx context.Context, filters *ExpertApplicationFilters, page, pageSize int) (*ExpertApplicationListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 25
	}

	offset := (page - 1) * pageSize

	// Build query with filters
	query := `
		SELECT 
			ea.id, ea.user_id, u.email, 
			COALESCE(u.first_name, '') as first_name,
			COALESCE(u.last_name, '') as last_name,
			COALESCE(u.company, '') as company,
			ea.status, ea.expertise_areas, ea.years_of_experience,
			ea.hourly_rate, ea.currency, ea.bio, ea.portfolio,
			ea.certifications, ea.education, ea.languages,
			ea.availability, ea.preferred_project_types,
			ea.linkedin_url, ea.website_url, ea.resume_url,
			ea.cover_letter, ea.review_notes, ea.reviewed_by,
			ea.reviewed_at, ea.approved_by, ea.approved_at,
			ea.rejection_reason, ea.created_at, ea.updated_at
		FROM expert_applications ea
		JOIN users u ON ea.user_id = u.id
		WHERE 1=1
	`

	args := []interface{}{}
	argCount := 0

	// Apply filters
	if filters != nil {
		if filters.Status != "" {
			argCount++
			query += fmt.Sprintf(" AND ea.status = $%d", argCount)
			args = append(args, filters.Status)
		}

		if filters.ExpertiseArea != "" {
			argCount++
			query += fmt.Sprintf(" AND $%d = ANY(ea.expertise_areas)", argCount)
			args = append(args, filters.ExpertiseArea)
		}

		if filters.MinExperience != nil {
			argCount++
			query += fmt.Sprintf(" AND ea.years_of_experience >= $%d", argCount)
			args = append(args, *filters.MinExperience)
		}

		if filters.MaxExperience != nil {
			argCount++
			query += fmt.Sprintf(" AND ea.years_of_experience <= $%d", argCount)
			args = append(args, *filters.MaxExperience)
		}

		if filters.MinHourlyRate != nil {
			argCount++
			query += fmt.Sprintf(" AND ea.hourly_rate >= $%d", argCount)
			args = append(args, *filters.MinHourlyRate)
		}

		if filters.MaxHourlyRate != nil {
			argCount++
			query += fmt.Sprintf(" AND ea.hourly_rate <= $%d", argCount)
			args = append(args, *filters.MaxHourlyRate)
		}

		if filters.SubmittedAfter != nil {
			argCount++
			query += fmt.Sprintf(" AND ea.created_at >= $%d", argCount)
			args = append(args, *filters.SubmittedAfter)
		}

		if filters.SubmittedBefore != nil {
			argCount++
			query += fmt.Sprintf(" AND ea.created_at <= $%d", argCount)
			args = append(args, *filters.SubmittedBefore)
		}

		if filters.ReviewedBy != "" {
			argCount++
			query += fmt.Sprintf(" AND ea.reviewed_by = $%d", argCount)
			args = append(args, filters.ReviewedBy)
		}
	}

	// Add ordering and pagination
	query += " ORDER BY ea.created_at DESC"
	argCount++
	query += fmt.Sprintf(" LIMIT $%d", argCount)
	args = append(args, pageSize)
	argCount++
	query += fmt.Sprintf(" OFFSET $%d", argCount)
	args = append(args, offset)

	// Execute query
	rows, err := s.db.Pool.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query expert applications: %w", err)
	}
	defer rows.Close()

	var applications []AdminExpertApplication
	for rows.Next() {
		var app AdminExpertApplication
		var expertiseAreasBytes, portfolioBytes, certificationsBytes, educationBytes, languagesBytes, preferredProjectTypesBytes []byte

		err := rows.Scan(
			&app.ID, &app.UserID, &app.UserEmail,
			&app.FirstName, &app.LastName, &app.Company,
			&app.Status, &expertiseAreasBytes, &app.YearsOfExperience,
			&app.HourlyRate, &app.Currency, &app.Bio, &portfolioBytes,
			&certificationsBytes, &educationBytes, &languagesBytes,
			&app.Availability, &preferredProjectTypesBytes,
			&app.LinkedInURL, &app.WebsiteURL, &app.ResumeURL,
			&app.CoverLetter, &app.ReviewNotes, &app.ReviewedBy,
			&app.ReviewedAt, &app.ApprovedBy, &app.ApprovedAt,
			&app.RejectionReason, &app.CreatedAt, &app.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan expert application: %w", err)
		}

		// Parse JSON fields (simplified for now)
		app.ExpertiseAreas = []string{}        // Would parse from expertiseAreasBytes
		app.Portfolio = []PortfolioItem{}      // Would parse from portfolioBytes
		app.Certifications = []Certification{} // Would parse from certificationsBytes
		app.Education = []Education{}          // Would parse from educationBytes
		app.Languages = []string{}             // Would parse from languagesBytes
		app.PreferredProjectTypes = []string{} // Would parse from preferredProjectTypesBytes

		// Calculate score and add flags
		app.Score = s.calculateApplicationScore(&app)
		app.Flags = s.getApplicationFlags(&app)
		app.Metadata = make(map[string]interface{})

		applications = append(applications, app)
	}

	// Get total count (simplified count query)
	var total int64
	countQuery := "SELECT COUNT(*) FROM expert_applications ea JOIN users u ON ea.user_id = u.id WHERE 1=1"
	err = s.db.Pool.QueryRow(ctx, countQuery).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("failed to count expert applications: %w", err)
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &ExpertApplicationListResponse{
		Applications: applications,
		Total:        total,
		Page:         page,
		PageSize:     pageSize,
		TotalPages:   totalPages,
	}, nil
}

// GetExpertApplication retrieves a single expert application
func (s *ExpertManagementService) GetExpertApplication(ctx context.Context, applicationID string) (*AdminExpertApplication, error) {
	query := `
		SELECT 
			ea.id, ea.user_id, u.email, 
			COALESCE(u.first_name, '') as first_name,
			COALESCE(u.last_name, '') as last_name,
			COALESCE(u.company, '') as company,
			ea.status, ea.expertise_areas, ea.years_of_experience,
			ea.hourly_rate, ea.currency, ea.bio, ea.portfolio,
			ea.certifications, ea.education, ea.languages,
			ea.availability, ea.preferred_project_types,
			ea.linkedin_url, ea.website_url, ea.resume_url,
			ea.cover_letter, ea.review_notes, ea.reviewed_by,
			ea.reviewed_at, ea.approved_by, ea.approved_at,
			ea.rejection_reason, ea.created_at, ea.updated_at
		FROM expert_applications ea
		JOIN users u ON ea.user_id = u.id
		WHERE ea.id = $1
	`

	var app AdminExpertApplication
	var expertiseAreasBytes, portfolioBytes, certificationsBytes, educationBytes, languagesBytes, preferredProjectTypesBytes []byte

	err := s.db.Pool.QueryRow(ctx, query, applicationID).Scan(
		&app.ID, &app.UserID, &app.UserEmail,
		&app.FirstName, &app.LastName, &app.Company,
		&app.Status, &expertiseAreasBytes, &app.YearsOfExperience,
		&app.HourlyRate, &app.Currency, &app.Bio, &portfolioBytes,
		&certificationsBytes, &educationBytes, &languagesBytes,
		&app.Availability, &preferredProjectTypesBytes,
		&app.LinkedInURL, &app.WebsiteURL, &app.ResumeURL,
		&app.CoverLetter, &app.ReviewNotes, &app.ReviewedBy,
		&app.ReviewedAt, &app.ApprovedBy, &app.ApprovedAt,
		&app.RejectionReason, &app.CreatedAt, &app.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get expert application: %w", err)
	}

	// Parse JSON fields and calculate metrics
	app.ExpertiseAreas = []string{}
	app.Portfolio = []PortfolioItem{}
	app.Certifications = []Certification{}
	app.Education = []Education{}
	app.Languages = []string{}
	app.PreferredProjectTypes = []string{}
	app.Score = s.calculateApplicationScore(&app)
	app.Flags = s.getApplicationFlags(&app)
	app.Metadata = make(map[string]interface{})

	return &app, nil
}

// ReviewExpertApplication reviews an expert application
func (s *ExpertManagementService) ReviewExpertApplication(ctx context.Context, applicationID, reviewerID, notes string) error {
	_, err := s.db.Pool.Exec(ctx, `
		UPDATE expert_applications 
		SET status = 'UNDER_REVIEW', 
		    review_notes = $1, 
		    reviewed_by = $2, 
		    reviewed_at = NOW(),
		    updated_at = NOW()
		WHERE id = $3
	`, notes, reviewerID, applicationID)

	if err != nil {
		return fmt.Errorf("failed to review expert application: %w", err)
	}

	return nil
}

// ApproveExpertApplication approves an expert application
func (s *ExpertManagementService) ApproveExpertApplication(ctx context.Context, applicationID, approverID, notes string) error {
	tx, err := s.db.Pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Update application status
	_, err = tx.Exec(ctx, `
		UPDATE expert_applications 
		SET status = 'APPROVED', 
		    review_notes = $1, 
		    approved_by = $2, 
		    approved_at = NOW(),
		    updated_at = NOW()
		WHERE id = $3
	`, notes, approverID, applicationID)
	if err != nil {
		return fmt.Errorf("failed to approve expert application: %w", err)
	}

	// Create expert profile
	_, err = tx.Exec(ctx, `
		INSERT INTO expert_profiles (
			user_id, status, tier, expertise_areas, years_of_experience,
			hourly_rate, currency, bio, portfolio, certifications,
			education, languages, availability, preferred_project_types,
			linkedin_url, website_url, verification_status, created_at, updated_at
		)
		SELECT 
			user_id, 'ACTIVE', 'STANDARD', expertise_areas, years_of_experience,
			hourly_rate, currency, bio, portfolio, certifications,
			education, languages, availability, preferred_project_types,
			linkedin_url, website_url, 'PENDING', NOW(), NOW()
		FROM expert_applications
		WHERE id = $1
	`, applicationID)
	if err != nil {
		return fmt.Errorf("failed to create expert profile: %w", err)
	}

	// Update user role to expert
	_, err = tx.Exec(ctx, `
		UPDATE users 
		SET role = 'expert', updated_at = NOW()
		WHERE id = (SELECT user_id FROM expert_applications WHERE id = $1)
	`, applicationID)
	if err != nil {
		return fmt.Errorf("failed to update user role: %w", err)
	}

	return tx.Commit(ctx)
}

// RejectExpertApplication rejects an expert application
func (s *ExpertManagementService) RejectExpertApplication(ctx context.Context, applicationID, reviewerID, reason string) error {
	_, err := s.db.Pool.Exec(ctx, `
		UPDATE expert_applications 
		SET status = 'REJECTED', 
		    rejection_reason = $1, 
		    reviewed_by = $2, 
		    reviewed_at = NOW(),
		    updated_at = NOW()
		WHERE id = $3
	`, reason, reviewerID, applicationID)

	if err != nil {
		return fmt.Errorf("failed to reject expert application: %w", err)
	}

	return nil
}

// GetExpertProfiles retrieves expert profiles with filtering and pagination
func (s *ExpertManagementService) GetExpertProfiles(ctx context.Context, filters map[string]interface{}, page, pageSize int) ([]AdminExpertProfile, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 25
	}

	offset := (page - 1) * pageSize

	// Simplified query for expert profiles
	query := `
		SELECT 
			ep.id, ep.user_id, u.email,
			COALESCE(u.first_name, '') as first_name,
			COALESCE(u.last_name, '') as last_name,
			COALESCE(u.company, '') as company,
			ep.status, ep.tier, ep.expertise_areas,
			ep.years_of_experience, ep.hourly_rate, ep.currency,
			COALESCE(ep.rating, 0) as rating,
			COALESCE(ep.review_count, 0) as review_count,
			ep.verification_status, ep.created_at, ep.updated_at
		FROM expert_profiles ep
		JOIN users u ON ep.user_id = u.id
		ORDER BY ep.created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := s.db.Pool.Query(ctx, query, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query expert profiles: %w", err)
	}
	defer rows.Close()

	var profiles []AdminExpertProfile
	for rows.Next() {
		var profile AdminExpertProfile
		var expertiseAreasBytes []byte

		err := rows.Scan(
			&profile.ID, &profile.UserID, &profile.UserEmail,
			&profile.FirstName, &profile.LastName, &profile.Company,
			&profile.Status, &profile.Tier, &expertiseAreasBytes,
			&profile.YearsOfExperience, &profile.HourlyRate, &profile.Currency,
			&profile.Rating, &profile.ReviewCount,
			&profile.VerificationStatus, &profile.CreatedAt, &profile.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan expert profile: %w", err)
		}

		// Parse JSON and enrich data
		profile.ExpertiseAreas = []string{} // Would parse from expertiseAreasBytes
		profile.Flags = []string{}
		profile.Metadata = make(map[string]interface{})

		// Get additional metrics
		s.enrichExpertProfileData(ctx, &profile)

		profiles = append(profiles, profile)
	}

	// Get total count
	var total int64
	err = s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM expert_profiles").Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count expert profiles: %w", err)
	}

	return profiles, total, nil
}

// enrichExpertProfileData adds additional metrics to expert profile
func (s *ExpertManagementService) enrichExpertProfileData(ctx context.Context, profile *AdminExpertProfile) {
	// Get completed projects count
	s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM project_engagements 
		WHERE expert_id = $1 AND status = 'COMPLETED'
	`, profile.UserID).Scan(&profile.CompletedProjects)

	// Get active projects count
	s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM project_engagements 
		WHERE expert_id = $1 AND status IN ('ACTIVE', 'IN_PROGRESS')
	`, profile.UserID).Scan(&profile.ActiveProjects)

	// Get total earnings
	s.db.Pool.QueryRow(ctx, `
		SELECT COALESCE(SUM(amount), 0) FROM invoices 
		WHERE user_id = $1 AND status = 'PAID'
	`, profile.UserID).Scan(&profile.TotalEarnings)

	// Add flags based on performance
	if profile.Rating >= 4.8 {
		profile.Flags = append(profile.Flags, "top_rated")
	}
	if profile.CompletedProjects >= 10 {
		profile.Flags = append(profile.Flags, "experienced")
	}
	if profile.TotalEarnings >= 10000 {
		profile.Flags = append(profile.Flags, "high_earner")
	}
}

// calculateApplicationScore calculates a score for an expert application
func (s *ExpertManagementService) calculateApplicationScore(app *AdminExpertApplication) float64 {
	score := 0.0

	// Base score for experience
	score += float64(app.YearsOfExperience) * 5

	// Score for portfolio items
	score += float64(len(app.Portfolio)) * 10

	// Score for certifications
	score += float64(len(app.Certifications)) * 15

	// Score for education
	score += float64(len(app.Education)) * 10

	// Score for languages
	score += float64(len(app.Languages)) * 5

	// Score for having LinkedIn/Website
	if app.LinkedInURL != "" {
		score += 10
	}
	if app.WebsiteURL != "" {
		score += 10
	}

	// Score for cover letter length (quality proxy)
	if len(app.CoverLetter) > 200 {
		score += 20
	}

	// Normalize to 0-100 scale
	if score > 100 {
		score = 100
	}

	return score
}

// getApplicationFlags returns flags for an expert application
func (s *ExpertManagementService) getApplicationFlags(app *AdminExpertApplication) []string {
	var flags []string

	if app.YearsOfExperience >= 10 {
		flags = append(flags, "senior")
	}
	if app.YearsOfExperience < 2 {
		flags = append(flags, "junior")
	}
	if app.HourlyRate >= 100 {
		flags = append(flags, "premium")
	}
	if len(app.Portfolio) >= 5 {
		flags = append(flags, "strong_portfolio")
	}
	if len(app.Certifications) >= 3 {
		flags = append(flags, "certified")
	}
	if app.Score >= 80 {
		flags = append(flags, "high_score")
	}

	return flags
}

// GetExpertApplicationStats returns expert application statistics
func (s *ExpertManagementService) GetExpertApplicationStats(ctx context.Context) (*ExpertApplicationStats, error) {
	stats := &ExpertApplicationStats{}

	// Applications by status
	rows, err := s.db.Pool.Query(ctx, `
		SELECT status, COUNT(*) 
		FROM expert_applications 
		GROUP BY status
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to get application stats by status: %w", err)
	}
	defer rows.Close()

	stats.ByStatus = make(map[string]int64)
	for rows.Next() {
		var status string
		var count int64
		if err := rows.Scan(&status, &count); err != nil {
			return nil, err
		}
		stats.ByStatus[status] = count
	}

	// Applications in last 30 days
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM expert_applications 
		WHERE created_at >= NOW() - INTERVAL '30 days'
	`).Scan(&stats.NewApplicationsLast30Days)
	if err != nil {
		stats.NewApplicationsLast30Days = 0
	}

	// Average approval time
	err = s.db.Pool.QueryRow(ctx, `
		SELECT AVG(EXTRACT(EPOCH FROM (approved_at - created_at))/86400) 
		FROM expert_applications 
		WHERE status = 'APPROVED' AND approved_at IS NOT NULL
	`).Scan(&stats.AverageApprovalTimeDays)
	if err != nil {
		stats.AverageApprovalTimeDays = 0
	}

	return stats, nil
}

// ExpertApplicationStats represents expert application statistics
type ExpertApplicationStats struct {
	ByStatus                  map[string]int64 `json:"by_status"`
	NewApplicationsLast30Days int64            `json:"new_applications_last_30_days"`
	AverageApprovalTimeDays   float64          `json:"average_approval_time_days"`
}
