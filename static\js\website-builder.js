// Velocity Platform Website Builder
class WebsiteBuilder {
    constructor() {
        this.selectedComponent = null;
        this.components = [];
        this.currentPageId = null;
        this.currentSiteId = null;
        this.currentTheme = {
            primary: '#0d6efd',
            secondary: '#6c757d',
            success: '#198754'
        };
        this.undoStack = [];
        this.redoStack = [];
        this.init();
    }

    init() {
        this.setupDragAndDrop();
        this.setupDeviceToggle();
        this.loadComponents();
        this.loadCurrentPage();
        this.setupAutoSave();
    }

    setupDragAndDrop() {
        // Make component items draggable
        document.querySelectorAll('.component-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', item.dataset.component);
            });
        });

        // Setup canvas drop zone
        const canvas = document.getElementById('website-canvas');
        if (canvas) {
            this.setupCanvasSortable(canvas);
        }
        
        // Setup drag handles for existing components
        this.setupDragHandles();
    }

    setupCanvasSortable(canvas) {
        new Sortable(canvas, {
            group: 'components',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            handle: '.drag-handle, .component-item',
            onAdd: (evt) => {
                const componentType = evt.item.dataset.component;
                const snippetId = evt.item.dataset.snippetId;
                
                // Remove the dragged element (it's a clone from palette)
                evt.item.remove();
                
                // Add the actual component
                this.addComponent(componentType, evt.newIndex, snippetId);
            },
            onUpdate: (evt) => {
                // Component was reordered within canvas
                this.saveToUndoStack();
                showAlert('Component reordered', 'info');
            },
            onStart: (evt) => {
                // Add visual feedback when dragging starts
                document.body.classList.add('dragging');
                this.highlightDropZones();
            },
            onEnd: (evt) => {
                // Remove visual feedback when dragging ends
                document.body.classList.remove('dragging');
                this.removeDropZoneHighlights();
            }
        });
    }

    setupDragHandles() {
        document.querySelectorAll('.component-wrapper').forEach(wrapper => {
            const dragHandle = wrapper.querySelector('.drag-handle');
            if (dragHandle) {
                dragHandle.addEventListener('mousedown', () => {
                    wrapper.classList.add('dragging');
                });
                
                dragHandle.addEventListener('mouseup', () => {
                    wrapper.classList.remove('dragging');
                });
            }
        });
    }

    highlightDropZones() {
        // Add drop zone highlighting
        const canvas = document.getElementById('website-canvas');
        canvas.classList.add('drop-zone', 'drag-over');
        
        // Highlight container components that can accept nested components
        document.querySelectorAll('.component-wrapper').forEach(wrapper => {
            const componentType = wrapper.dataset.componentType;
            if (['container', 'row', 'card'].includes(componentType)) {
                wrapper.classList.add('can-drop');
            }
        });
    }

    removeDropZoneHighlights() {
        const canvas = document.getElementById('website-canvas');
        canvas.classList.remove('drop-zone', 'drag-over');
        
        document.querySelectorAll('.component-wrapper').forEach(wrapper => {
            wrapper.classList.remove('can-drop');
        });
    }
    }

    setupDeviceToggle() {
        document.querySelectorAll('[data-device]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('[data-device]').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.switchDevice(e.target.dataset.device);
            });
        });
    }

    switchDevice(device) {
        const canvas = document.getElementById('website-canvas');
        canvas.className = canvas.className.replace(/device-\w+/g, '');
        canvas.classList.add(`device-${device}`);
        
        switch(device) {
            case 'mobile':
                canvas.style.maxWidth = '375px';
                break;
            case 'tablet':
                canvas.style.maxWidth = '768px';
                break;
            default:
                canvas.style.maxWidth = '100%';
        }
    }

    addComponent(type, index, snippetId = null) {
        const component = this.createComponent(type, snippetId);
        const canvas = document.getElementById('website-canvas');
        
        if (canvas.children.length === 1 && canvas.children[0].classList.contains('canvas-empty')) {
            canvas.innerHTML = '';
        }
        
        if (index !== undefined && index < canvas.children.length) {
            canvas.insertBefore(component, canvas.children[index]);
        } else {
            canvas.appendChild(component);
        }
        
        this.selectComponent(component);
        this.saveToUndoStack();
    }

    createComponent(type, snippetId = null) {
        const wrapper = document.createElement('div');
        wrapper.className = 'component-wrapper position-relative mb-3';
        wrapper.dataset.componentType = type;
        wrapper.dataset.componentId = `component-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        let content = '';
        
        if (type === 'snippet' && snippetId) {
            content = this.getSnippetContent(snippetId);
            wrapper.dataset.snippetId = snippetId;
        } else {
            switch(type) {
                case 'container':
                    content = `<div class="container"><div class="row"><div class="col-12"><p class="text-muted">Container - Drop components here</p></div></div></div>`;
                    break;
                case 'row':
                    content = `<div class="row"><div class="col-12"><p class="text-muted">Row - Add columns here</p></div></div>`;
                    break;
                case 'column':
                    content = `<div class="col-md-6"><p class="text-muted">Column - Add content here</p></div>`;
                    break;
                case 'button':
                    content = `<button class="btn btn-primary">Click Me</button>`;
                    break;
                case 'card':
                    content = `
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Card Title</h5>
                                <p class="card-text">Some quick example text to build on the card title.</p>
                                <a href="#" class="btn btn-primary">Go somewhere</a>
                            </div>
                        </div>`;
                    break;
                case 'navbar':
                    content = `
                        <nav class="navbar navbar-expand-lg navbar-light bg-light">
                            <div class="container-fluid">
                                <a class="navbar-brand" href="#">Navbar</a>
                                <div class="navbar-nav">
                                    <a class="nav-link active" href="#">Home</a>
                                    <a class="nav-link" href="#">Features</a>
                                    <a class="nav-link" href="#">About</a>
                                </div>
                            </div>
                        </nav>`;
                    break;
                case 'form':
                    content = `
                        <form>
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Message</label>
                                <textarea class="form-control" rows="3"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Submit</button>
                        </form>`;
                    break;
                case 'hero':
                    content = `
                        <div class="bg-primary text-white p-5 rounded">
                            <div class="container">
                                <h1 class="display-4">Hero Section</h1>
                                <p class="lead">This is a hero section with a call-to-action.</p>
                                <a class="btn btn-light btn-lg" href="#" role="button">Learn more</a>
                            </div>
                        </div>`;
                    break;
                case 'text':
                    content = `<p class="lead">Edit this text content...</p>`;
                    break;
                case 'heading':
                    content = `<h2>Your Heading Here</h2>`;
                    break;
                case 'image':
                    content = `<img src="https://via.placeholder.com/400x200" class="img-fluid" alt="Placeholder Image">`;
                    break;
                case 'alert':
                    content = `<div class="alert alert-info" role="alert">This is an info alert!</div>`;
                    break;
                default:
                    content = `<div class="p-3 border rounded"><p class="text-muted">Unknown component type: ${type}</p></div>`;
            }
        }
        
        wrapper.innerHTML = content + `
            <div class="component-controls position-absolute top-0 end-0 p-1" style="z-index: 1000;">
                <button class="btn btn-sm btn-outline-secondary me-1 drag-handle" title="Drag to reorder">
                    <i class="bi bi-grip-vertical"></i>
                </button>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="builder.editComponent(this.parentElement.parentElement)" title="Edit">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-secondary me-1" onclick="builder.duplicateComponent(this.parentElement.parentElement)" title="Duplicate">
                    <i class="bi bi-copy"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="builder.deleteComponent(this.parentElement.parentElement)" title="Delete">
                    <i class="bi bi-trash"></i>
                </button>
            </div>`;
        
        wrapper.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectComponent(wrapper);
        });
        
        return wrapper;
    }

    selectComponent(component) {
        document.querySelectorAll('.component-wrapper').forEach(c => c.classList.remove('selected'));
        component.classList.add('selected');
        this.selectedComponent = component;
        this.showComponentProperties(component);
    }

    showComponentProperties(component) {
        const type = component.dataset.componentType;
        const propertiesPanel = document.getElementById('component-properties');
        
        let propertiesHTML = `
            <h6 class="fw-bold mb-3">${type.charAt(0).toUpperCase() + type.slice(1)} Properties</h6>
            <div class="mb-3">
                <label class="form-label">CSS Classes</label>
                <input type="text" class="form-control" value="${component.firstElementChild.className}" 
                       onchange="builder.updateComponentClasses(this.value)">
            </div>`;
        
        if (type === 'button') {
            const button = component.querySelector('button');
            propertiesHTML += `
                <div class="mb-3">
                    <label class="form-label">Button Text</label>
                    <input type="text" class="form-control" value="${button.textContent}" 
                           onchange="builder.updateButtonText(this.value)">
                </div>
                <div class="mb-3">
                    <label class="form-label">Button Style</label>
                    <select class="form-select" onchange="builder.updateButtonStyle(this.value)">
                        <option value="btn-primary">Primary</option>
                        <option value="btn-secondary">Secondary</option>
                        <option value="btn-success">Success</option>
                        <option value="btn-danger">Danger</option>
                        <option value="btn-outline-primary">Outline Primary</option>
                    </select>
                </div>`;
        }
        
        propertiesPanel.innerHTML = propertiesHTML;
    }

    updateComponentClasses(classes) {
        if (this.selectedComponent) {
            this.selectedComponent.firstElementChild.className = classes;
        }
    }

    updateButtonText(text) {
        if (this.selectedComponent) {
            const button = this.selectedComponent.querySelector('button');
            if (button) button.textContent = text;
        }
    }

    updateButtonStyle(style) {
        if (this.selectedComponent) {
            const button = this.selectedComponent.querySelector('button');
            if (button) {
                button.className = button.className.replace(/btn-\w+/g, '') + ' ' + style;
            }
        }
    }

    editComponent(component) {
        this.selectComponent(component);
    }

    deleteComponent(component) {
        if (confirm('Are you sure you want to delete this component?')) {
            component.remove();
            document.getElementById('component-properties').innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="bi bi-cursor me-2"></i>
                    <small>Select a component to edit properties</small>
                </div>`;
            this.saveToUndoStack();
        }
    }

    duplicateComponent(component) {
        const clone = component.cloneNode(true);
        const newId = `component-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        clone.dataset.componentId = newId;
        
        component.parentNode.insertBefore(clone, component.nextSibling);
        
        clone.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectComponent(clone);
        });
        
        this.selectComponent(clone);
        this.saveToUndoStack();
        showAlert('Component duplicated', 'success');
    }

    loadComponents() {
        this.loadPredefinedSnippets();
    }

    async loadPredefinedSnippets() {
        try {
            const response = await fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    query: `
                        query GetPredefinedSnippets {
                            predefinedSnippets {
                                id
                                name
                                category
                                description
                                htmlContent
                                cssContent
                                jsContent
                            }
                        }
                    `
                })
            });
            
            const data = await response.json();
            if (data.data?.predefinedSnippets) {
                this.predefinedSnippets = data.data.predefinedSnippets;
                this.addSnippetsToComponentPalette(data.data.predefinedSnippets);
            }
        } catch (error) {
            console.error('Failed to load predefined snippets:', error);
        }
    }

    addSnippetsToComponentPalette(snippets) {
        const snippetAccordion = document.querySelector('#componentAccordion');
        if (!snippetAccordion) return;

        const categories = {};
        snippets.forEach(snippet => {
            if (!categories[snippet.category]) {
                categories[snippet.category] = [];
            }
            categories[snippet.category].push(snippet);
        });

        Object.keys(categories).forEach(category => {
            const categoryId = `snippets-${category.toLowerCase().replace(/\s+/g, '-')}`;
            const accordionItem = document.createElement('div');
            accordionItem.className = 'accordion-item';
            accordionItem.innerHTML = `
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#${categoryId}">
                        <i class="bi bi-code-square me-2"></i>${category}
                    </button>
                </h2>
                <div id="${categoryId}" class="accordion-collapse collapse">
                    <div class="accordion-body">
                        ${categories[category].map(snippet => `
                            <div class="component-item snippet-item" draggable="true" data-component="snippet" data-snippet-id="${snippet.id}" title="${snippet.description || snippet.name}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-puzzle me-2"></i>${snippet.name}
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="builder.previewSnippet('${snippet.id}')" title="Preview">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <small class="text-muted d-block mt-1">${snippet.description || ''}</small>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            snippetAccordion.appendChild(accordionItem);
        });

        this.setupDragAndDrop();
    }

    getCurrentPageId() {
        return this.currentPageId || new URLSearchParams(window.location.search).get('pageId');
    }

    getCurrentSiteId() {
        return this.currentSiteId || new URLSearchParams(window.location.search).get('siteId');
    }

    async loadCurrentPage() {
        const pageId = this.getCurrentPageId();
        if (!pageId) return;

        try {
            const response = await fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    query: `
                        query GetPage($id: ID!) {
                            page(id: $id) {
                                id
                                title
                                content
                                settings
                            }
                        }
                    `,
                    variables: { id: pageId }
                })
            });
            
            const data = await response.json();
            if (data.data?.page) {
                this.loadPageContent(data.data.page);
            }
        } catch (error) {
            console.error('Failed to load page:', error);
        }
    }

    loadPageContent(page) {
        this.currentPageId = page.id;
        
        const canvas = document.getElementById('website-canvas');
        if (page.content) {
            canvas.innerHTML = page.content;
        }

        if (page.settings?.theme) {
            this.currentTheme = { ...this.currentTheme, ...page.settings.theme };
            this.applyTheme();
        }

        this.setupComponentInteractions();
    }

    setupComponentInteractions() {
        document.querySelectorAll('.component-wrapper').forEach(component => {
            component.addEventListener('click', () => this.selectComponent(component));
        });
    }

    async savePageContent(pageId, content, silent = false) {
        try {
            const response = await fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    query: `
                        mutation UpdatePage($id: ID!, $input: UpdatePageInput!) {
                            updatePage(id: $id, input: $input) {
                                id
                                title
                                content
                                updatedAt
                            }
                        }
                    `,
                    variables: {
                        id: pageId,
                        input: {
                            content: content.html,
                            settings: {
                                theme: content.theme,
                                components: content.components,
                                lastSaved: content.timestamp
                            }
                        }
                    }
                })
            });
            
            const data = await response.json();
            if (data.data?.updatePage) {
                if (!silent) {
                    showAlert('Page saved successfully!', 'success');
                    this.saveToUndoStack();
                }
            } else {
                throw new Error(data.errors?.[0]?.message || 'Failed to save page');
            }
        } catch (error) {
            console.error('Failed to save page:', error);
            if (!silent) {
                showAlert('Failed to save page: ' + error.message, 'error');
            }
        }
    }

    getComponentsData() {
        const canvas = document.getElementById('website-canvas');
        const components = [];
        
        canvas.querySelectorAll('.component-wrapper').forEach((wrapper, index) => {
            components.push({
                id: wrapper.dataset.componentId || `component-${index}`,
                type: wrapper.dataset.componentType,
                position: index,
                html: wrapper.innerHTML,
                styles: wrapper.getAttribute('style') || ''
            });
        });
        
        return components;
    }

    setupAutoSave() {
        setInterval(() => {
            if (this.getCurrentPageId()) {
                this.autoSave();
            }
        }, 30000);

        window.addEventListener('beforeunload', () => {
            if (this.getCurrentPageId()) {
                this.autoSave();
            }
        });
    }

    autoSave() {
        const canvas = document.getElementById('website-canvas');
        const pageContent = {
            html: canvas.innerHTML,
            components: this.getComponentsData(),
            theme: this.currentTheme,
            timestamp: new Date().toISOString()
        };
        
        this.savePageContent(this.getCurrentPageId(), pageContent, true);
    }

    getAuthToken() {
        return localStorage.getItem('authToken') || sessionStorage.getItem('authToken') || '';
    }

    applyTheme() {
        Object.keys(this.currentTheme).forEach(key => {
            document.documentElement.style.setProperty(`--bs-${key}`, this.currentTheme[key]);
        });
    }

    saveToUndoStack() {
        const canvas = document.getElementById('website-canvas');
        this.undoStack.push({
            html: canvas.innerHTML,
            theme: { ...this.currentTheme },
            timestamp: Date.now()
        });
        
        if (this.undoStack.length > 50) {
            this.undoStack.shift();
        }
        
        this.redoStack = [];
    }

    undoAction() {
        if (this.undoStack.length === 0) return;
        
        const canvas = document.getElementById('website-canvas');
        
        this.redoStack.push({
            html: canvas.innerHTML,
            theme: { ...this.currentTheme },
            timestamp: Date.now()
        });
        
        const previousState = this.undoStack.pop();
        canvas.innerHTML = previousState.html;
        this.currentTheme = previousState.theme;
        this.applyTheme();
        this.setupComponentInteractions();
        
        showAlert('Undo successful', 'info');
    }

    redoAction() {
        if (this.redoStack.length === 0) return;
        
        const canvas = document.getElementById('website-canvas');
        
        this.undoStack.push({
            html: canvas.innerHTML,
            theme: { ...this.currentTheme },
            timestamp: Date.now()
        });
        
        const nextState = this.redoStack.pop();
        canvas.innerHTML = nextState.html;
        this.currentTheme = nextState.theme;
        this.applyTheme();
        this.setupComponentInteractions();
        
        showAlert('Redo successful', 'info');
    }

    getSnippetContent(snippetId) {
        const snippet = this.predefinedSnippets.find(s => s.id === snippetId);
        if (snippet) {
            let content = snippet.htmlContent;
            
            // Add CSS if present
            if (snippet.cssContent) {
                content += `<style>${snippet.cssContent}</style>`;
            }
            
            // Add JavaScript if present (wrapped in script tag)
            if (snippet.jsContent) {
                content += `<script>${snippet.jsContent}</script>`;
            }
            
            return content;
        }
        return `<div class="snippet-content" data-snippet-id="${snippetId}">
            <p class="text-muted">Snippet content will be loaded here...</p>
        </div>`;
    }

    previewSnippet(snippetId) {
        const snippet = this.predefinedSnippets.find(s => s.id === snippetId);
        if (!snippet) return;
        
        // Create preview modal
        const modalHTML = `
            <div class="modal fade" id="snippetPreviewModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${snippet.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p class="text-muted">${snippet.description || 'No description available'}</p>
                            <div class="border rounded p-3 bg-light mb-3">
                                ${snippet.htmlContent}
                                ${snippet.cssContent ? `<style>${snippet.cssContent}</style>` : ''}
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button class="btn btn-primary" onclick="builder.insertSnippet('${snippetId}'); bootstrap.Modal.getInstance(document.getElementById('snippetPreviewModal')).hide();">
                                    <i class="bi bi-plus-circle me-1"></i>Insert into Page
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Remove existing modal if present
        const existingModal = document.getElementById('snippetPreviewModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('snippetPreviewModal'));
        modal.show();
    }

    insertSnippet(snippetId) {
        this.addComponent('snippet', undefined, snippetId);
        showAlert('Snippet inserted successfully!', 'success');
    }
}

// Global functions
function allowDrop(ev) {
    ev.preventDefault();
}

function dropComponent(ev) {
    ev.preventDefault();
    const componentType = ev.dataTransfer.getData("text");
    if (componentType) {
        builder.addComponent(componentType);
    }
}

function updateThemeColor(colorType, value) {
    builder.currentTheme[colorType] = value;
    document.documentElement.style.setProperty(`--bs-${colorType}`, value);
}

function previewSite() {
    const canvas = document.getElementById('website-canvas');
    const previewWindow = window.open('', '_blank');
    previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Preview</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                :root {
                    --bs-primary: ${builder.currentTheme.primary};
                    --bs-secondary: ${builder.currentTheme.secondary};
                    --bs-success: ${builder.currentTheme.success};
                }
                .component-controls { display: none !important; }
            </style>
        </head>
        <body>
            ${canvas.innerHTML}
        </body>
        </html>
    `);
}

function saveSite() {
    const canvas = document.getElementById('website-canvas');
    const currentPageId = builder.getCurrentPageId();
    
    if (!currentPageId) {
        showAlert('No page selected to save', 'error');
        return;
    }
    
    const pageContent = {
        html: canvas.innerHTML,
        components: builder.getComponentsData(),
        theme: builder.currentTheme,
        timestamp: new Date().toISOString()
    };
    
    builder.savePageContent(currentPageId, pageContent);
}

function publishSite() {
    if (confirm('Are you sure you want to publish this site?')) {
        showAlert('Site published successfully!', 'success');
    }
}

function showAlert(message, type) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    document.body.insertAdjacentHTML('afterbegin', alertHTML);
}

// Initialize the website builder
const builder = new WebsiteBuilder();