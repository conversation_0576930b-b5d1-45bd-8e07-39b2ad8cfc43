package pagemanager

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"goVwPlatformAPI/internal/database"
)

type PageManager struct {
	db *database.DB
}

type PageContent struct {
	ID          string                 `json:"id"`
	PageID      string                 `json:"page_id"`
	Content     map[string]interface{} `json:"content"`
	Version     int                    `json:"version"`
	IsPublished bool                   `json:"is_published"`
	CreatedBy   string                 `json:"created_by"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

type Page struct {
	ID        string    `json:"id"`
	Title     string    `json:"title"`
	Slug      string    `json:"slug"`
	ParentID  *string   `json:"parent_id"`
	Position  int       `json:"position"`
	IsHidden  bool      `json:"is_hidden"`
	UserID    string    `json:"user_id"`
	TenantID  string    `json:"tenant_id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func NewPageManager(db *database.DB) *PageManager {
	return &PageManager{db: db}
}

func (pm *PageManager) SavePageContent(ctx context.Context, pageID string, content map[string]interface{}, userID string) (*PageContent, error) {
	// Verify user owns the page
	var pageUserID string
	err := pm.db.Pool.QueryRow(ctx, "SELECT user_id FROM pages WHERE id = $1", pageID).Scan(&pageUserID)
	if err != nil {
		return nil, fmt.Errorf("page not found: %w", err)
	}

	if pageUserID != userID {
		return nil, fmt.Errorf("unauthorized: user does not own this page")
	}

	// Get next version number
	var nextVersion int
	err = pm.db.Pool.QueryRow(ctx, `
		SELECT COALESCE(MAX(version), 0) + 1 
		FROM page_content 
		WHERE page_id = $1
	`, pageID).Scan(&nextVersion)
	if err != nil {
		return nil, fmt.Errorf("failed to get next version: %w", err)
	}

	// Convert content to JSON
	contentJSON, err := json.Marshal(content)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal content: %w", err)
	}

	// Save new version
	var pageContent PageContent
	err = pm.db.Pool.QueryRow(ctx, `
		INSERT INTO page_content (page_id, content, version, created_by)
		VALUES ($1, $2, $3, $4)
		RETURNING id, page_id, content, version, is_published, created_by, created_at, updated_at
	`, pageID, contentJSON, nextVersion, userID).Scan(
		&pageContent.ID, &pageContent.PageID, &contentJSON, &pageContent.Version,
		&pageContent.IsPublished, &pageContent.CreatedBy, &pageContent.CreatedAt, &pageContent.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to save page content: %w", err)
	}

	// Unmarshal content back
	err = json.Unmarshal(contentJSON, &pageContent.Content)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal content: %w", err)
	}

	return &pageContent, nil
}

func (pm *PageManager) GetPageContent(ctx context.Context, pageID string, userID string) (*PageContent, error) {
	// Verify user owns the page
	var pageUserID string
	err := pm.db.Pool.QueryRow(ctx, "SELECT user_id FROM pages WHERE id = $1", pageID).Scan(&pageUserID)
	if err != nil {
		return nil, fmt.Errorf("page not found: %w", err)
	}

	if pageUserID != userID {
		return nil, fmt.Errorf("unauthorized: user does not own this page")
	}

	// Get latest content
	var pageContent PageContent
	var contentJSON []byte

	err = pm.db.Pool.QueryRow(ctx, `
		SELECT id, page_id, content, version, is_published, created_by, created_at, updated_at
		FROM page_content
		WHERE page_id = $1
		ORDER BY version DESC
		LIMIT 1
	`, pageID).Scan(
		&pageContent.ID, &pageContent.PageID, &contentJSON, &pageContent.Version,
		&pageContent.IsPublished, &pageContent.CreatedBy, &pageContent.CreatedAt, &pageContent.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get page content: %w", err)
	}

	// Unmarshal content
	err = json.Unmarshal(contentJSON, &pageContent.Content)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal content: %w", err)
	}

	return &pageContent, nil
}

func (pm *PageManager) PublishPage(ctx context.Context, pageID string, userID string) (*PageContent, error) {
	// Verify user owns the page
	var pageUserID string
	err := pm.db.Pool.QueryRow(ctx, "SELECT user_id FROM pages WHERE id = $1", pageID).Scan(&pageUserID)
	if err != nil {
		return nil, fmt.Errorf("page not found: %w", err)
	}

	if pageUserID != userID {
		return nil, fmt.Errorf("unauthorized: user does not own this page")
	}

	// Get latest content and mark as published
	var pageContent PageContent
	var contentJSON []byte

	err = pm.db.Pool.QueryRow(ctx, `
		UPDATE page_content 
		SET is_published = true, updated_at = NOW()
		WHERE page_id = $1 AND version = (
			SELECT MAX(version) FROM page_content WHERE page_id = $1
		)
		RETURNING id, page_id, content, version, is_published, created_by, created_at, updated_at
	`, pageID).Scan(
		&pageContent.ID, &pageContent.PageID, &contentJSON, &pageContent.Version,
		&pageContent.IsPublished, &pageContent.CreatedBy, &pageContent.CreatedAt, &pageContent.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to publish page: %w", err)
	}

	// Unmarshal content
	err = json.Unmarshal(contentJSON, &pageContent.Content)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal content: %w", err)
	}

	return &pageContent, nil
}

func (pm *PageManager) CreatePage(ctx context.Context, title, slug string, parentID *string, userID, tenantID string) (*Page, error) {
	// Get next position
	var position int
	if parentID != nil {
		err := pm.db.Pool.QueryRow(ctx, `
			SELECT COALESCE(MAX(position), 0) + 1 
			FROM pages 
			WHERE parent_id = $1 AND user_id = $2
		`, *parentID, userID).Scan(&position)
		if err != nil {
			return nil, fmt.Errorf("failed to get next position: %w", err)
		}
	} else {
		err := pm.db.Pool.QueryRow(ctx, `
			SELECT COALESCE(MAX(position), 0) + 1 
			FROM pages 
			WHERE parent_id IS NULL AND user_id = $1
		`, userID).Scan(&position)
		if err != nil {
			return nil, fmt.Errorf("failed to get next position: %w", err)
		}
	}

	// Create page
	var page Page
	err := pm.db.Pool.QueryRow(ctx, `
		INSERT INTO pages (title, slug, parent_id, position, user_id, tenant_id)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id, title, slug, parent_id, position, is_hidden, user_id, tenant_id, created_at, updated_at
	`, title, slug, parentID, position, userID, tenantID).Scan(
		&page.ID, &page.Title, &page.Slug, &page.ParentID, &page.Position,
		&page.IsHidden, &page.UserID, &page.TenantID, &page.CreatedAt, &page.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create page: %w", err)
	}

	return &page, nil
}

func (pm *PageManager) GetUserPages(ctx context.Context, userID string) ([]*Page, error) {
	rows, err := pm.db.Pool.Query(ctx, `
		SELECT id, title, slug, parent_id, position, is_hidden, user_id, tenant_id, created_at, updated_at
		FROM pages
		WHERE user_id = $1
		ORDER BY parent_id NULLS FIRST, position ASC
	`, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user pages: %w", err)
	}
	defer rows.Close()

	var pages []*Page
	for rows.Next() {
		var page Page
		err := rows.Scan(
			&page.ID, &page.Title, &page.Slug, &page.ParentID, &page.Position,
			&page.IsHidden, &page.UserID, &page.TenantID, &page.CreatedAt, &page.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan page: %w", err)
		}
		pages = append(pages, &page)
	}

	return pages, nil
}

func (pm *PageManager) DeletePage(ctx context.Context, pageID string, userID string) error {
	// Verify user owns the page
	var pageUserID string
	err := pm.db.Pool.QueryRow(ctx, "SELECT user_id FROM pages WHERE id = $1", pageID).Scan(&pageUserID)
	if err != nil {
		return fmt.Errorf("page not found: %w", err)
	}

	if pageUserID != userID {
		return fmt.Errorf("unauthorized: user does not own this page")
	}

	// Start transaction
	tx, err := pm.db.Pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Delete page content
	_, err = tx.Exec(ctx, "DELETE FROM page_content WHERE page_id = $1", pageID)
	if err != nil {
		return fmt.Errorf("failed to delete page content: %w", err)
	}

	// Delete page
	_, err = tx.Exec(ctx, "DELETE FROM pages WHERE id = $1", pageID)
	if err != nil {
		return fmt.Errorf("failed to delete page: %w", err)
	}

	return tx.Commit(ctx)
}
