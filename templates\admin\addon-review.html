{{ define "content" }}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Addon Review Queue</h1>
    
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Author</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {{ range .Addons }}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">{{ .Name }}</div>
                                <div class="text-sm text-gray-500">{{ .Description }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ .Author.Name }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            {{ if eq .Status "pending" }}bg-yellow-100 text-yellow-800{{ end }}
                            {{ if eq .Status "approved" }}bg-green-100 text-green-800{{ end }}
                            {{ if eq .Status "rejected" }}bg-red-100 text-red-800{{ end }}">
                            {{ .Status }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2">
                            <select class="status-select border rounded px-2 py-1" data-addon-id="{{ .ID }}">
                                <option value="pending" {{ if eq .Status "pending" }}selected{{ end }}>Pending</option>
                                <option value="approved" {{ if eq .Status "approved" }}selected{{ end }}>Approved</option>
                                <option value="rejected" {{ if eq .Status "rejected" }}selected{{ end }}>Rejected</option>
                            </select>
                            <button onclick="window.location.href='/builder/addon/{{ .ID }}'" 
                                class="text-indigo-600 hover:text-indigo-900">Preview</button>
                        </div>
                    </td>
                </tr>
                {{ end }}
            </tbody>
        </table>
    </div>
</div>

<script>
document.querySelectorAll('.status-select').forEach(select => {
    select.addEventListener('change', async (e) => {
        const addonId = e.target.dataset.addonId;
        const newStatus = e.target.value;
        
        try {
            const response = await fetch('/admin/api/addon/status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    addonId,
                    newStatus
                })
            });
            
            if (!response.ok) {
                throw new Error('Status update failed');
            }
            
            location.reload();
        } catch (error) {
            console.error('Error:', error);
            alert('Failed to update status');
        }
    });
});
</script>
{{ end }}