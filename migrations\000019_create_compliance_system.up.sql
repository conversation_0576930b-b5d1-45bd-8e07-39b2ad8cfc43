-- Create compliance frameworks table
CREATE TABLE IF NOT EXISTS compliance_frameworks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    framework_name VARCHAR(200) NOT NULL,
    framework_code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    regulatory_body VARCHAR(200),
    industry_sector VARCHAR(100),
    framework_type VARCHAR(50) DEFAULT 'regulatory', -- regulatory, standard, certification
    is_mandatory BOOLEAN DEFAULT false,
    effective_date DATE,
    review_frequency_months INTEGER DEFAULT 12,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create compliance requirements table
CREATE TABLE IF NOT EXISTS compliance_requirements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    framework_id UUID NOT NULL REFERENCES compliance_frameworks(id) ON DELETE CASCADE,
    requirement_code VARCHAR(100) NOT NULL,
    requirement_title VARCHAR(300) NOT NULL,
    requirement_description TEXT NOT NULL,
    compliance_level VARCHAR(50) DEFAULT 'mandatory', -- mandatory, recommended, optional
    category VARCHAR(100),
    subcategory VARCHAR(100),
    evidence_required TEXT[],
    review_frequency_months INTEGER DEFAULT 12,
    penalty_description TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create business compliance tracking table
CREATE TABLE IF NOT EXISTS business_compliance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    framework_id UUID NOT NULL REFERENCES compliance_frameworks(id) ON DELETE CASCADE,
    
    -- Compliance status
    compliance_status VARCHAR(50) DEFAULT 'not_started', -- not_started, in_progress, compliant, non_compliant, expired
    assigned_to UUID REFERENCES users(id),
    priority_level VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
    
    -- Dates
    target_completion_date DATE,
    last_review_date DATE,
    next_review_date DATE,
    compliance_achieved_date DATE,
    
    -- Documentation
    notes TEXT,
    evidence_documents JSONB DEFAULT '[]',
    action_items JSONB DEFAULT '[]',
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ensure one compliance record per tenant per framework
    UNIQUE(tenant_id, framework_id)
);

-- Create compliance requirement tracking table
CREATE TABLE IF NOT EXISTS compliance_requirement_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    business_compliance_id UUID NOT NULL REFERENCES business_compliance(id) ON DELETE CASCADE,
    requirement_id UUID NOT NULL REFERENCES compliance_requirements(id) ON DELETE CASCADE,
    
    -- Requirement status
    requirement_status VARCHAR(50) DEFAULT 'pending', -- pending, in_progress, completed, not_applicable, failed
    completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    
    -- Dates
    started_date DATE,
    completed_date DATE,
    due_date DATE,
    
    -- Evidence and notes
    evidence_provided JSONB DEFAULT '[]',
    implementation_notes TEXT,
    assessor_notes TEXT,
    
    -- Risk assessment
    risk_level VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
    impact_description TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ensure one tracking record per compliance per requirement
    UNIQUE(business_compliance_id, requirement_id)
);

-- Create compliance alerts table
CREATE TABLE IF NOT EXISTS compliance_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    business_compliance_id UUID REFERENCES business_compliance(id) ON DELETE CASCADE,
    requirement_tracking_id UUID REFERENCES compliance_requirement_tracking(id) ON DELETE CASCADE,
    
    -- Alert details
    alert_type VARCHAR(50) NOT NULL, -- deadline_approaching, overdue, review_required, status_change
    alert_title VARCHAR(200) NOT NULL,
    alert_message TEXT NOT NULL,
    severity VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
    
    -- Status
    alert_status VARCHAR(50) DEFAULT 'active', -- active, acknowledged, resolved, dismissed
    acknowledged_by UUID REFERENCES users(id),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    
    -- Scheduling
    trigger_date DATE NOT NULL,
    expiry_date DATE,
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert default UK compliance frameworks
INSERT INTO compliance_frameworks (framework_name, framework_code, description, regulatory_body, industry_sector, is_mandatory, effective_date) VALUES
('General Data Protection Regulation', 'GDPR', 'EU regulation on data protection and privacy', 'Information Commissioner''s Office (ICO)', 'All', true, '2018-05-25'),
('Companies House Filing', 'CH_FILING', 'Mandatory company registration and filing requirements', 'Companies House', 'All', true, '2006-10-01'),
('Health and Safety at Work', 'HSWA', 'Workplace health and safety regulations', 'Health and Safety Executive (HSE)', 'All', true, '1974-07-31'),
('Employment Law Compliance', 'EMPLOYMENT', 'UK employment rights and obligations', 'ACAS', 'All', true, '1996-05-22'),
('Anti-Money Laundering', 'AML', 'Prevention of money laundering regulations', 'Financial Conduct Authority (FCA)', 'Financial Services', true, '2017-06-26'),
('Food Safety Standards', 'FSS', 'Food hygiene and safety regulations', 'Food Standards Agency (FSA)', 'Food & Beverage', true, '2006-01-01'),
('ISO 27001', 'ISO27001', 'Information security management standard', 'ISO', 'Technology', false, '2013-10-01'),
('PCI DSS', 'PCI_DSS', 'Payment card industry data security standard', 'PCI Security Standards Council', 'Retail/E-commerce', false, '2018-05-01');

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_compliance_frameworks_code ON compliance_frameworks(framework_code);
CREATE INDEX IF NOT EXISTS idx_compliance_frameworks_industry ON compliance_frameworks(industry_sector);

CREATE INDEX IF NOT EXISTS idx_compliance_requirements_framework ON compliance_requirements(framework_id);
CREATE INDEX IF NOT EXISTS idx_compliance_requirements_code ON compliance_requirements(requirement_code);
CREATE INDEX IF NOT EXISTS idx_compliance_requirements_category ON compliance_requirements(category);

CREATE INDEX IF NOT EXISTS idx_business_compliance_tenant ON business_compliance(tenant_id);
CREATE INDEX IF NOT EXISTS idx_business_compliance_framework ON business_compliance(framework_id);
CREATE INDEX IF NOT EXISTS idx_business_compliance_status ON business_compliance(compliance_status);
CREATE INDEX IF NOT EXISTS idx_business_compliance_assigned ON business_compliance(assigned_to);
CREATE INDEX IF NOT EXISTS idx_business_compliance_next_review ON business_compliance(next_review_date);

CREATE INDEX IF NOT EXISTS idx_requirement_tracking_compliance ON compliance_requirement_tracking(business_compliance_id);
CREATE INDEX IF NOT EXISTS idx_requirement_tracking_requirement ON compliance_requirement_tracking(requirement_id);
CREATE INDEX IF NOT EXISTS idx_requirement_tracking_status ON compliance_requirement_tracking(requirement_status);
CREATE INDEX IF NOT EXISTS idx_requirement_tracking_due_date ON compliance_requirement_tracking(due_date);

CREATE INDEX IF NOT EXISTS idx_compliance_alerts_tenant ON compliance_alerts(tenant_id);
CREATE INDEX IF NOT EXISTS idx_compliance_alerts_status ON compliance_alerts(alert_status);
CREATE INDEX IF NOT EXISTS idx_compliance_alerts_trigger_date ON compliance_alerts(trigger_date);

-- Add triggers for updated_at
CREATE TRIGGER update_compliance_frameworks_updated_at BEFORE UPDATE ON compliance_frameworks FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();
CREATE TRIGGER update_compliance_requirements_updated_at BEFORE UPDATE ON compliance_requirements FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();
CREATE TRIGGER update_business_compliance_updated_at BEFORE UPDATE ON business_compliance FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();
CREATE TRIGGER update_compliance_requirement_tracking_updated_at BEFORE UPDATE ON compliance_requirement_tracking FOR EACH ROW EXECUTE FUNCTION update_expert_updated_at_column();