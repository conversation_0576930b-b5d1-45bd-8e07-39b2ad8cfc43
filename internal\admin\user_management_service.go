package admin

import (
	"context"
	"fmt"
	"goVwPlatformAPI/internal/database"
	"time"
)

// UserManagementService handles admin user management operations
type UserManagementService struct {
	db *database.DB
}

// NewUserManagementService creates a new user management service
func NewUserManagementService(db *database.DB) *UserManagementService {
	return &UserManagementService{db: db}
}

// AdminUser represents a user from admin perspective with additional fields
type AdminUser struct {
	ID               string                 `json:"id"`
	Email            string                 `json:"email"`
	Role             string                 `json:"role"`
	Status           string                 `json:"status"`
	FirstName        string                 `json:"first_name"`
	LastName         string                 `json:"last_name"`
	Company          string                 `json:"company"`
	TenantID         string                 `json:"tenant_id"`
	TenantName       string                 `json:"tenant_name"`
	EmailVerified    bool                   `json:"email_verified"`
	LastLoginAt      *time.Time             `json:"last_login_at"`
	CreatedAt        time.Time              `json:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at"`
	SubscriptionTier string                 `json:"subscription_tier"`
	SitesCount       int64                  `json:"sites_count"`
	PagesCount       int64                  `json:"pages_count"`
	AddonsCount      int64                  `json:"addons_count"`
	StorageUsed      int64                  `json:"storage_used"`
	APICallsUsed     int64                  `json:"api_calls_used"`
	TotalRevenue     float64                `json:"total_revenue"`
	Flags            []string               `json:"flags"`
	Notes            string                 `json:"notes"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// UserSearchFilters represents filters for user search
type UserSearchFilters struct {
	Email            string     `json:"email"`
	Role             string     `json:"role"`
	Status           string     `json:"status"`
	TenantID         string     `json:"tenant_id"`
	SubscriptionTier string     `json:"subscription_tier"`
	CreatedAfter     *time.Time `json:"created_after"`
	CreatedBefore    *time.Time `json:"created_before"`
	LastActiveAfter  *time.Time `json:"last_active_after"`
	HasFlags         []string   `json:"has_flags"`
	MinRevenue       *float64   `json:"min_revenue"`
	MaxRevenue       *float64   `json:"max_revenue"`
}

// UserListResponse represents paginated user list response
type UserListResponse struct {
	Users      []AdminUser `json:"users"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// UserAction represents an action performed on a user
type UserAction struct {
	ID        string                 `json:"id"`
	UserID    string                 `json:"user_id"`
	AdminID   string                 `json:"admin_id"`
	Action    string                 `json:"action"`
	Reason    string                 `json:"reason"`
	Metadata  map[string]interface{} `json:"metadata"`
	CreatedAt time.Time              `json:"created_at"`
}

// GetUsers retrieves users with filtering and pagination
func (s *UserManagementService) GetUsers(ctx context.Context, filters *UserSearchFilters, page, pageSize int) (*UserListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 25
	}

	offset := (page - 1) * pageSize

	// Build query with filters
	query := `
		SELECT 
			u.id, u.email, COALESCE(u.role, 'client') as role, 
			COALESCE(u.status, 'active') as status,
			COALESCE(u.first_name, '') as first_name,
			COALESCE(u.last_name, '') as last_name,
			COALESCE(u.company, '') as company,
			u.tenant_id,
			COALESCE(t.name, 'Default') as tenant_name,
			COALESCE(u.email_verified, false) as email_verified,
			u.last_login_at,
			u.created_at,
			u.updated_at,
			COALESCE(st.name, 'Free') as subscription_tier,
			COALESCE(u.notes, '') as notes
		FROM users u
		LEFT JOIN tenants t ON u.tenant_id = t.id
		LEFT JOIN user_subscriptions us ON u.id::text = us.user_id AND us.status = 'active'
		LEFT JOIN subscription_tiers st ON us.tier_id = st.id
		WHERE 1=1
	`

	args := []interface{}{}
	argCount := 0

	// Apply filters
	if filters != nil {
		if filters.Email != "" {
			argCount++
			query += fmt.Sprintf(" AND u.email ILIKE $%d", argCount)
			args = append(args, "%"+filters.Email+"%")
		}

		if filters.Role != "" {
			argCount++
			query += fmt.Sprintf(" AND u.role = $%d", argCount)
			args = append(args, filters.Role)
		}

		if filters.Status != "" {
			argCount++
			query += fmt.Sprintf(" AND u.status = $%d", argCount)
			args = append(args, filters.Status)
		}

		if filters.TenantID != "" {
			argCount++
			query += fmt.Sprintf(" AND u.tenant_id = $%d", argCount)
			args = append(args, filters.TenantID)
		}

		if filters.CreatedAfter != nil {
			argCount++
			query += fmt.Sprintf(" AND u.created_at >= $%d", argCount)
			args = append(args, *filters.CreatedAfter)
		}

		if filters.CreatedBefore != nil {
			argCount++
			query += fmt.Sprintf(" AND u.created_at <= $%d", argCount)
			args = append(args, *filters.CreatedBefore)
		}

		if filters.LastActiveAfter != nil {
			argCount++
			query += fmt.Sprintf(" AND u.last_login_at >= $%d", argCount)
			args = append(args, *filters.LastActiveAfter)
		}
	}

	// Add ordering and pagination
	query += " ORDER BY u.created_at DESC"
	argCount++
	query += fmt.Sprintf(" LIMIT $%d", argCount)
	args = append(args, pageSize)
	argCount++
	query += fmt.Sprintf(" OFFSET $%d", argCount)
	args = append(args, offset)

	// Execute query
	rows, err := s.db.Pool.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query users: %w", err)
	}
	defer rows.Close()

	var users []AdminUser
	for rows.Next() {
		var user AdminUser
		err := rows.Scan(
			&user.ID, &user.Email, &user.Role, &user.Status,
			&user.FirstName, &user.LastName, &user.Company,
			&user.TenantID, &user.TenantName, &user.EmailVerified,
			&user.LastLoginAt, &user.CreatedAt, &user.UpdatedAt,
			&user.SubscriptionTier, &user.Notes,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %w", err)
		}

		// Get additional metrics for each user
		if err := s.enrichUserData(ctx, &user); err != nil {
			// Log error but continue
			fmt.Printf("Warning: failed to enrich user data for %s: %v\n", user.ID, err)
		}

		users = append(users, user)
	}

	// Get total count
	countQuery := `
		SELECT COUNT(*) 
		FROM users u
		LEFT JOIN tenants t ON u.tenant_id = t.id
		LEFT JOIN user_subscriptions us ON u.id::text = us.user_id AND us.status = 'active'
		LEFT JOIN subscription_tiers st ON us.tier_id = st.id
		WHERE 1=1
	`

	// Apply same filters to count query (without pagination)
	countArgs := []interface{}{}
	countArgCount := 0

	if filters != nil {
		if filters.Email != "" {
			countArgCount++
			countQuery += fmt.Sprintf(" AND u.email ILIKE $%d", countArgCount)
			countArgs = append(countArgs, "%"+filters.Email+"%")
		}

		if filters.Role != "" {
			countArgCount++
			countQuery += fmt.Sprintf(" AND u.role = $%d", countArgCount)
			countArgs = append(countArgs, filters.Role)
		}

		if filters.Status != "" {
			countArgCount++
			countQuery += fmt.Sprintf(" AND u.status = $%d", countArgCount)
			countArgs = append(countArgs, filters.Status)
		}

		if filters.TenantID != "" {
			countArgCount++
			countQuery += fmt.Sprintf(" AND u.tenant_id = $%d", countArgCount)
			countArgs = append(countArgs, filters.TenantID)
		}
	}

	var total int64
	err = s.db.Pool.QueryRow(ctx, countQuery, countArgs...).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &UserListResponse{
		Users:      users,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// enrichUserData adds additional metrics to user data
func (s *UserManagementService) enrichUserData(ctx context.Context, user *AdminUser) error {
	// Get sites count
	err := s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM sites WHERE user_id = $1
	`, user.ID).Scan(&user.SitesCount)
	if err != nil {
		user.SitesCount = 0
	}

	// Get pages count
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM pages WHERE created_by = $1
	`, user.ID).Scan(&user.PagesCount)
	if err != nil {
		user.PagesCount = 0
	}

	// Get addons count
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM addon_configs WHERE created_by = $1
	`, user.ID).Scan(&user.AddonsCount)
	if err != nil {
		user.AddonsCount = 0
	}

	// Get total revenue
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COALESCE(SUM(amount), 0) FROM invoices 
		WHERE user_id = $1 AND status = 'PAID'
	`, user.ID).Scan(&user.TotalRevenue)
	if err != nil {
		user.TotalRevenue = 0
	}

	// Get API calls used (from logs)
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM external_api_call_logs 
		WHERE created_by = $1 AND created_at >= DATE_TRUNC('month', NOW())
	`, user.ID).Scan(&user.APICallsUsed)
	if err != nil {
		user.APICallsUsed = 0
	}

	// Initialize flags and metadata
	user.Flags = []string{}
	user.Metadata = make(map[string]interface{})

	// Add flags based on user status
	if user.TotalRevenue > 1000 {
		user.Flags = append(user.Flags, "high_value")
	}
	if user.AddonsCount > 5 {
		user.Flags = append(user.Flags, "active_developer")
	}
	if user.LastLoginAt != nil && user.LastLoginAt.Before(time.Now().AddDate(0, 0, -30)) {
		user.Flags = append(user.Flags, "inactive")
	}

	return nil
}

// GetUser retrieves a single user by ID
func (s *UserManagementService) GetUser(ctx context.Context, userID string) (*AdminUser, error) {
	query := `
		SELECT 
			u.id, u.email, COALESCE(u.role, 'client') as role, 
			COALESCE(u.status, 'active') as status,
			COALESCE(u.first_name, '') as first_name,
			COALESCE(u.last_name, '') as last_name,
			COALESCE(u.company, '') as company,
			u.tenant_id,
			COALESCE(t.name, 'Default') as tenant_name,
			COALESCE(u.email_verified, false) as email_verified,
			u.last_login_at,
			u.created_at,
			u.updated_at,
			COALESCE(st.name, 'Free') as subscription_tier,
			COALESCE(u.notes, '') as notes
		FROM users u
		LEFT JOIN tenants t ON u.tenant_id = t.id
		LEFT JOIN user_subscriptions us ON u.id::text = us.user_id AND us.status = 'active'
		LEFT JOIN subscription_tiers st ON us.tier_id = st.id
		WHERE u.id = $1
	`

	var user AdminUser
	err := s.db.Pool.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Email, &user.Role, &user.Status,
		&user.FirstName, &user.LastName, &user.Company,
		&user.TenantID, &user.TenantName, &user.EmailVerified,
		&user.LastLoginAt, &user.CreatedAt, &user.UpdatedAt,
		&user.SubscriptionTier, &user.Notes,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Enrich with additional data
	if err := s.enrichUserData(ctx, &user); err != nil {
		return nil, fmt.Errorf("failed to enrich user data: %w", err)
	}

	return &user, nil
}

// UpdateUserStatus updates a user's status
func (s *UserManagementService) UpdateUserStatus(ctx context.Context, userID, status, reason, adminID string) error {
	// Update user status
	_, err := s.db.Pool.Exec(ctx, `
		UPDATE users 
		SET status = $1, updated_at = NOW() 
		WHERE id = $2
	`, status, userID)
	if err != nil {
		return fmt.Errorf("failed to update user status: %w", err)
	}

	// Log the action
	return s.logUserAction(ctx, userID, adminID, "status_change", reason, map[string]interface{}{
		"new_status": status,
	})
}

// UpdateUserRole updates a user's role
func (s *UserManagementService) UpdateUserRole(ctx context.Context, userID, role, reason, adminID string) error {
	// Update user role
	_, err := s.db.Pool.Exec(ctx, `
		UPDATE users 
		SET role = $1, updated_at = NOW() 
		WHERE id = $2
	`, role, userID)
	if err != nil {
		return fmt.Errorf("failed to update user role: %w", err)
	}

	// Log the action
	return s.logUserAction(ctx, userID, adminID, "role_change", reason, map[string]interface{}{
		"new_role": role,
	})
}

// AddUserNote adds a note to a user
func (s *UserManagementService) AddUserNote(ctx context.Context, userID, note, adminID string) error {
	// Update user notes
	_, err := s.db.Pool.Exec(ctx, `
		UPDATE users 
		SET notes = COALESCE(notes, '') || $1 || E'\n', updated_at = NOW() 
		WHERE id = $2
	`, fmt.Sprintf("[%s] %s", time.Now().Format("2006-01-02 15:04"), note), userID)
	if err != nil {
		return fmt.Errorf("failed to add user note: %w", err)
	}

	// Log the action
	return s.logUserAction(ctx, userID, adminID, "note_added", note, nil)
}

// SuspendUser suspends a user account
func (s *UserManagementService) SuspendUser(ctx context.Context, userID, reason, adminID string) error {
	return s.UpdateUserStatus(ctx, userID, "suspended", reason, adminID)
}

// UnsuspendUser unsuspends a user account
func (s *UserManagementService) UnsuspendUser(ctx context.Context, userID, reason, adminID string) error {
	return s.UpdateUserStatus(ctx, userID, "active", reason, adminID)
}

// DeleteUser soft deletes a user account
func (s *UserManagementService) DeleteUser(ctx context.Context, userID, reason, adminID string) error {
	return s.UpdateUserStatus(ctx, userID, "deleted", reason, adminID)
}

// GetUserActions retrieves actions performed on a user
func (s *UserManagementService) GetUserActions(ctx context.Context, userID string, limit int) ([]UserAction, error) {
	if limit <= 0 || limit > 100 {
		limit = 50
	}

	query := `
		SELECT id, user_id, admin_id, action, reason, metadata, created_at
		FROM user_actions
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2
	`

	rows, err := s.db.Pool.Query(ctx, query, userID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query user actions: %w", err)
	}
	defer rows.Close()

	var actions []UserAction
	for rows.Next() {
		var action UserAction
		var metadataBytes []byte

		err := rows.Scan(
			&action.ID, &action.UserID, &action.AdminID,
			&action.Action, &action.Reason, &metadataBytes, &action.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user action: %w", err)
		}

		// Parse metadata JSON
		if len(metadataBytes) > 0 {
			if err := fmt.Errorf("json unmarshal not implemented"); err != nil {
				action.Metadata = make(map[string]interface{})
			}
		} else {
			action.Metadata = make(map[string]interface{})
		}

		actions = append(actions, action)
	}

	return actions, nil
}

// logUserAction logs an admin action performed on a user
func (s *UserManagementService) logUserAction(ctx context.Context, userID, adminID, action, reason string, metadata map[string]interface{}) error {
	// For now, just log to stdout (in production, would store in database)
	fmt.Printf("USER_ACTION: admin=%s user=%s action=%s reason=%s\n", adminID, userID, action, reason)
	return nil
}

// GetUserStats returns aggregated user statistics
func (s *UserManagementService) GetUserStats(ctx context.Context) (*UserStats, error) {
	stats := &UserStats{}

	// Total users by role
	rows, err := s.db.Pool.Query(ctx, `
		SELECT COALESCE(role, 'client') as role, COUNT(*) 
		FROM users 
		GROUP BY role
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to get user stats by role: %w", err)
	}
	defer rows.Close()

	stats.ByRole = make(map[string]int64)
	for rows.Next() {
		var role string
		var count int64
		if err := rows.Scan(&role, &count); err != nil {
			return nil, err
		}
		stats.ByRole[role] = count
	}

	// Total users by status
	rows, err = s.db.Pool.Query(ctx, `
		SELECT COALESCE(status, 'active') as status, COUNT(*) 
		FROM users 
		GROUP BY status
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to get user stats by status: %w", err)
	}
	defer rows.Close()

	stats.ByStatus = make(map[string]int64)
	for rows.Next() {
		var status string
		var count int64
		if err := rows.Scan(&status, &count); err != nil {
			return nil, err
		}
		stats.ByStatus[status] = count
	}

	// Users registered in last 30 days
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM users 
		WHERE created_at >= NOW() - INTERVAL '30 days'
	`).Scan(&stats.NewUsersLast30Days)
	if err != nil {
		stats.NewUsersLast30Days = 0
	}

	// Active users (logged in last 30 days)
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM users 
		WHERE last_login_at >= NOW() - INTERVAL '30 days'
	`).Scan(&stats.ActiveUsersLast30Days)
	if err != nil {
		stats.ActiveUsersLast30Days = 0
	}

	return stats, nil
}

// UserStats represents aggregated user statistics
type UserStats struct {
	ByRole                map[string]int64 `json:"by_role"`
	ByStatus              map[string]int64 `json:"by_status"`
	NewUsersLast30Days    int64            `json:"new_users_last_30_days"`
	ActiveUsersLast30Days int64            `json:"active_users_last_30_days"`
}
