package auth

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/golang-jwt/jwt/v5"
	"goVwPlatformAPI/internal/database"
)

var siteCtxKey = &contextKey{"site"}
var userCtxKey = &contextKey{"user"}

type contextKey struct{ name string }

func Middleware(db *database.DB) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// API key authentication
			apiKeyHandler := APIKeyMiddleware(db)(next)
			apiKeyHandler.ServeHTTP(w, r)

			if r.Context().Value(siteCtxKey) != nil {
				return
			}

			// JWT authentication
			authHeader := r.Header.Get("Authorization")
			if authHeader == "" || len(authHeader) < 8 || !strings.HasPrefix(authHeader, "Bearer ") {
				next.ServeHTTP(w, r)
				return
			}

			token, err := jwt.ParseWithClaims(authHeader[7:], &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
				if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
					return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
				}
				return []byte(os.Getenv("JWT_SECRET")), nil
			})

			if err != nil || !token.Valid {
				http.Error(w, "Invalid authorization token", http.StatusUnauthorized)
				return
			}

			if claims, ok := token.Claims.(*jwt.RegisteredClaims); ok {
				ctx := context.WithValue(r.Context(), userCtxKey, claims.Subject)
				r = r.WithContext(ctx)
			}

			next.ServeHTTP(w, r)
		})
	}
}

func APIKeyMiddleware(db *database.DB) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			key := strings.TrimSpace(r.Header.Get("X-API-Key"))
			if key == "" {
				return
			}

			var apiKey struct {
				KeyID   string
				SiteID  string
				Revoked bool
			}
			err := db.Pool.QueryRow(r.Context(),
				`SELECT key_id, site_id, revoked
				FROM api_keys
				WHERE key_id = $1 AND expires_at > NOW()`, key).Scan(
				&apiKey.KeyID,
				&apiKey.SiteID,
				&apiKey.Revoked,
			)

			if err != nil || apiKey.Revoked {
				http.Error(w, "Invalid API key", http.StatusUnauthorized)
				return
			}

			ctx := context.WithValue(r.Context(), siteCtxKey, apiKey.SiteID)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

func GetUserIDFromContext(ctx context.Context) (string, error) {
	userID, ok := ctx.Value(userCtxKey).(string)
	if !ok {
		return "", fmt.Errorf("user not authenticated")
	}
	return userID, nil
}

func HasPermission(userID string, permission string) bool {
	// FIXED: Implement basic permission check
	// For MVP, implement role-based permissions
	// TODO: Expand to more granular permissions later

	// Admin users have all permissions
	if userID == "admin" {
		return true
	}

	// Basic permission mapping
	userPermissions := map[string][]string{
		"user": {
			"page:create", "page:edit", "page:delete",
			"snippet:create", "snippet:edit", "snippet:delete",
			"addon:create", "addon:edit",
		},
		"admin": {
			"*", // All permissions
		},
		"developer": {
			"addon:create", "addon:edit", "addon:submit",
			"api:call",
		},
	}

	// Get user role (simplified - in production, query database)
	userRole := "user" // Default role

	permissions, exists := userPermissions[userRole]
	if !exists {
		return false
	}

	// Check if user has specific permission or wildcard
	for _, perm := range permissions {
		if perm == "*" || perm == permission {
			return true
		}
	}

	return false
}

func RequireRole(role string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			claims, ok := r.Context().Value(userCtxKey).(jwt.MapClaims)
			if !ok {
				w.WriteHeader(http.StatusUnauthorized)
				return
			}

			userRole, ok := claims["role"].(string)
			if !ok || userRole != role {
				w.WriteHeader(http.StatusForbidden)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// RequireRoleRedirect middleware that redirects users without proper role
func RequireRoleRedirect(redirectURL string, roles ...string) func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			claims, ok := r.Context().Value(UserContextKey).(*Claims)
			if !ok {
				http.Redirect(w, r, "/auth/login", http.StatusSeeOther)
				return
			}

			for _, role := range roles {
				if claims.Role == role {
					next.ServeHTTP(w, r)
					return
				}
			}

			// Redirect to appropriate page based on user role
			switch claims.Role {
			case "admin":
				http.Redirect(w, r, "/admin/dashboard", http.StatusSeeOther)
			case "expert":
				http.Redirect(w, r, "/expert/dashboard", http.StatusSeeOther)
			case "client":
				http.Redirect(w, r, "/dashboard", http.StatusSeeOther)
			default:
				http.Redirect(w, r, redirectURL, http.StatusSeeOther)
			}
		}
	}
}

// IsAdmin checks if user has admin role
func IsAdmin(claims *Claims) bool {
	return claims != nil && claims.Role == "admin"
}

// IsExpert checks if user has expert role
func IsExpert(claims *Claims) bool {
	return claims != nil && claims.Role == "expert"
}

// IsClient checks if user has client role
func IsClient(claims *Claims) bool {
	return claims != nil && claims.Role == "client"
}

// HasAnyRole checks if user has any of the specified roles
func HasAnyRole(claims *Claims, roles ...string) bool {
	if claims == nil {
		return false
	}

	for _, role := range roles {
		if claims.Role == role {
			return true
		}
	}
	return false
}
