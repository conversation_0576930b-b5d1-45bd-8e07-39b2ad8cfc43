-- Add subscription system tables
CREATE TABLE IF NOT EXISTS subscription_tiers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    price_monthly DECIMAL(10,2) NOT NULL DEFAULT 0,
    features <PERSON><PERSON><PERSON><PERSON> NOT NULL DEFAULT '{}',
    limits J<PERSON>NB NOT NULL DEFAULT '{}',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tier_id UUID NOT NULL REFERENCES subscription_tiers(id),
    stripe_subscription_id VARCHAR(255),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'past_due', 'unpaid')),
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS usage_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    resource_type VARCHAR(50) NOT NULL,
    usage_amount INTEGER NOT NULL DEFAULT 0,
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_id ON usage_tracking(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_period ON usage_tracking(period_start, period_end);

-- Insert default subscription tiers
INSERT INTO subscription_tiers (name, price_monthly, features, limits) VALUES
('Free', 0, 
 '{"websites": 1, "addons": 3, "storage": "100MB", "bandwidth": "1GB", "custom_domain": false, "ssl": false}',
 '{"websites": 1, "addons": 3, "storage_mb": 100, "bandwidth_gb": 1, "api_calls_per_month": 1000}'
),
('Pro', 29, 
 '{"websites": 10, "addons": 50, "storage": "5GB", "bandwidth": "50GB", "custom_domain": true, "ssl": true, "priority_support": true}',
 '{"websites": 10, "addons": 50, "storage_mb": 5000, "bandwidth_gb": 50, "api_calls_per_month": 100000}'
),
('Enterprise', 99, 
 '{"websites": -1, "addons": -1, "storage": "50GB", "bandwidth": "500GB", "custom_domain": true, "ssl": true, "priority_support": true, "white_label": true}',
 '{"websites": -1, "addons": -1, "storage_mb": 50000, "bandwidth_gb": 500, "api_calls_per_month": 1000000}'
)
ON CONFLICT (name) DO NOTHING;

-- Assign all existing users to Free tier
INSERT INTO user_subscriptions (user_id, tier_id, status, current_period_start, current_period_end)
SELECT 
    u.id,
    st.id,
    'active',
    NOW(),
    NOW() + INTERVAL '1 month'
FROM users u
CROSS JOIN subscription_tiers st
WHERE st.name = 'Free'
ON CONFLICT DO NOTHING;

-- Add user profile fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS first_name VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_name VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS company VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_expires TIMESTAMPTZ;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMPTZ;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_active BOOLEAN NOT NULL DEFAULT true;