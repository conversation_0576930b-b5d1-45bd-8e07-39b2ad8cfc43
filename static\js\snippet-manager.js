// Velocity Platform Snippet Manager
class SnippetManager {
    constructor() {
        this.snippets = [];
        this.filteredSnippets = [];
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.currentView = 'grid';
        this.selectedSnippet = null;
        this.init();
    }

    init() {
        this.loadSnippets();
        this.setupEventListeners();
        this.loadSampleSnippets();
    }

    setupEventListeners() {
        // Search input
        document.getElementById('search-input').addEventListener('input', (e) => {
            this.applyFilters();
        });

        // Category filter
        document.getElementById('category-filter').addEventListener('change', (e) => {
            this.applyFilters();
        });

        // View toggle
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('[data-view]').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentView = e.target.dataset.view;
                this.renderSnippets();
            });
        });

        // Code input listeners for preview
        ['snippet-html', 'snippet-css', 'snippet-js'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => {
                this.updatePreview();
            });
        });
    }

    loadSampleSnippets() {
        // Sample snippets for demonstration
        const sampleSnippets = [
            {
                id: '1',
                name: 'Primary Button',
                category: 'buttons',
                description: 'Bootstrap primary button with hover effects',
                htmlContent: '<button type="button" class="btn btn-primary">Primary Button</button>',
                cssContent: '.btn-primary:hover { transform: translateY(-2px); transition: all 0.2s; }',
                jsContent: '',
                version: '1.0.0',
                createdAt: new Date().toISOString()
            },
            {
                id: '2',
                name: 'Feature Card',
                category: 'cards',
                description: 'Card component with icon and description',
                htmlContent: `<div class="card h-100">
    <div class="card-body text-center">
        <i class="bi bi-star display-4 text-primary mb-3"></i>
        <h5 class="card-title">Feature Title</h5>
        <p class="card-text">Feature description goes here.</p>
        <a href="#" class="btn btn-primary">Learn More</a>
    </div>
</div>`,
                cssContent: '.card:hover { box-shadow: 0 4px 8px rgba(0,0,0,0.1); transition: all 0.3s; }',
                jsContent: '',
                version: '1.0.0',
                createdAt: new Date().toISOString()
            },
            {
                id: '3',
                name: 'Contact Form',
                category: 'forms',
                description: 'Bootstrap contact form with validation',
                htmlContent: `<form class="contact-form">
    <div class="mb-3">
        <label class="form-label">Name</label>
        <input type="text" class="form-control" required>
    </div>
    <div class="mb-3">
        <label class="form-label">Email</label>
        <input type="email" class="form-control" required>
    </div>
    <div class="mb-3">
        <label class="form-label">Message</label>
        <textarea class="form-control" rows="4" required></textarea>
    </div>
    <button type="submit" class="btn btn-primary">Send Message</button>
</form>`,
                cssContent: '.contact-form { max-width: 500px; }',
                jsContent: `document.querySelector('.contact-form').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('Form submitted!');
});`,
                version: '1.0.0',
                createdAt: new Date().toISOString()
            },
            {
                id: '4',
                name: 'Navigation Bar',
                category: 'navigation',
                description: 'Responsive Bootstrap navbar',
                htmlContent: `<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="#">Brand</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item"><a class="nav-link" href="#">Home</a></li>
                <li class="nav-item"><a class="nav-link" href="#">About</a></li>
                <li class="nav-item"><a class="nav-link" href="#">Contact</a></li>
            </ul>
        </div>
    </div>
</nav>`,
                cssContent: '',
                jsContent: '',
                version: '1.0.0',
                createdAt: new Date().toISOString()
            }
        ];

        this.snippets = sampleSnippets;
        this.applyFilters();
    }

    async loadSnippets() {
        try {
            const response = await fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    query: `
                        query GetPredefinedSnippets {
                            predefinedSnippets {
                                id
                                name
                                category
                                description
                                htmlContent
                                cssContent
                                jsContent
                                version
                                createdAt
                                updatedAt
                            }
                        }
                    `
                })
            });
            
            const data = await response.json();
            if (data.data?.predefinedSnippets) {
                // Merge API snippets with sample snippets
                this.snippets = [...this.snippets, ...data.data.predefinedSnippets];
                this.applyFilters();
            }
        } catch (error) {
            console.error('Failed to load snippets from API:', error);
            // Fall back to localStorage
            const saved = localStorage.getItem('velocity-snippets');
            if (saved) {
                const savedSnippets = JSON.parse(saved);
                this.snippets = [...this.snippets, ...savedSnippets];
            }
        }
    }

    applyFilters() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        const category = document.getElementById('category-filter').value;
        
        this.filteredSnippets = this.snippets.filter(snippet => {
            const matchesSearch = !searchTerm || 
                snippet.name.toLowerCase().includes(searchTerm) ||
                snippet.description.toLowerCase().includes(searchTerm);
            
            const matchesCategory = !category || snippet.category === category;
            
            return matchesSearch && matchesCategory;
        });

        this.currentPage = 1;
        this.renderSnippets();
        this.renderPagination();
    }

    renderSnippets() {
        const container = document.getElementById('snippets-container');
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageSnippets = this.filteredSnippets.slice(startIndex, endIndex);

        if (pageSnippets.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-search display-4 text-muted"></i>
                    <h5 class="mt-3">No snippets found</h5>
                    <p class="text-muted">Try adjusting your search or filters</p>
                </div>`;
            return;
        }

        let html = '';
        pageSnippets.forEach(snippet => {
            if (this.currentView === 'grid') {
                html += this.renderSnippetCard(snippet);
            } else {
                html += this.renderSnippetListItem(snippet);
            }
        });

        container.innerHTML = html;
    }

    renderSnippetCard(snippet) {
        return `
            <div class="col-md-4">
                <div class="card card-velocity h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${snippet.name}</h6>
                            <span class="badge bg-primary">${snippet.category}</span>
                        </div>
                        <p class="card-text text-muted small">${snippet.description}</p>
                        
                        <div class="snippet-preview border rounded p-2 mb-3" style="height: 120px; overflow: hidden;">
                            ${this.renderSnippetPreview(snippet)}
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm flex-fill" 
                                    onclick="snippetManager.viewSnippet('${snippet.id}')">
                                <i class="bi bi-eye me-1"></i>View
                            </button>
                            <button class="btn btn-velocity btn-sm flex-fill" 
                                    onclick="snippetManager.useSnippet('${snippet.id}')">
                                <i class="bi bi-plus me-1"></i>Use
                            </button>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <small class="text-muted">
                            <i class="bi bi-clock me-1"></i>
                            ${new Date(snippet.createdAt).toLocaleDateString()}
                        </small>
                    </div>
                </div>
            </div>`;
    }

    renderSnippetListItem(snippet) {
        return `
            <div class="col-12">
                <div class="card card-velocity">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <h6 class="mb-0 me-3">${snippet.name}</h6>
                                    <span class="badge bg-primary me-2">${snippet.category}</span>
                                    <small class="text-muted">${snippet.description}</small>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-outline-primary btn-sm me-2" 
                                        onclick="snippetManager.viewSnippet('${snippet.id}')">
                                    <i class="bi bi-eye me-1"></i>View
                                </button>
                                <button class="btn btn-velocity btn-sm" 
                                        onclick="snippetManager.useSnippet('${snippet.id}')">
                                    <i class="bi bi-plus me-1"></i>Use
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;
    }

    renderSnippetPreview(snippet) {
        // Create a safe preview of the snippet
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = snippet.htmlContent;
        
        // Apply inline styles for preview
        if (snippet.cssContent) {
            const style = document.createElement('style');
            style.textContent = snippet.cssContent;
            tempDiv.appendChild(style);
        }
        
        return tempDiv.innerHTML;
    }

    renderPagination() {
        const totalPages = Math.ceil(this.filteredSnippets.length / this.itemsPerPage);
        const pagination = document.getElementById('pagination');
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let html = '';
        
        // Previous button
        html += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="snippetManager.goToPage(${this.currentPage - 1})">Previous</a>
            </li>`;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage || i === 1 || i === totalPages || 
                (i >= this.currentPage - 1 && i <= this.currentPage + 1)) {
                html += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="snippetManager.goToPage(${i})">${i}</a>
                    </li>`;
            } else if (i === this.currentPage - 2 || i === this.currentPage + 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        // Next button
        html += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="snippetManager.goToPage(${this.currentPage + 1})">Next</a>
            </li>`;

        pagination.innerHTML = html;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredSnippets.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderSnippets();
            this.renderPagination();
        }
    }

    viewSnippet(id) {
        const snippet = this.snippets.find(s => s.id === id);
        if (!snippet) return;

        this.selectedSnippet = snippet;
        
        document.getElementById('detail-title').textContent = snippet.name;
        document.getElementById('detail-body').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Details</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Name:</strong></td><td>${snippet.name}</td></tr>
                        <tr><td><strong>Category:</strong></td><td>${snippet.category}</td></tr>
                        <tr><td><strong>Version:</strong></td><td>${snippet.version}</td></tr>
                        <tr><td><strong>Created:</strong></td><td>${new Date(snippet.createdAt).toLocaleDateString()}</td></tr>
                    </table>
                    <p><strong>Description:</strong><br>${snippet.description}</p>
                </div>
                <div class="col-md-6">
                    <h6>Preview</h6>
                    <div class="border rounded p-3 bg-light">
                        ${this.renderSnippetPreview(snippet)}
                    </div>
                </div>
            </div>
            
            <ul class="nav nav-tabs mt-4" id="detail-tabs">
                <li class="nav-item">
                    <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#detail-html">HTML</button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#detail-css">CSS</button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#detail-js">JavaScript</button>
                </li>
            </ul>
            
            <div class="tab-content border border-top-0 p-3">
                <div class="tab-pane fade show active" id="detail-html">
                    <pre><code class="language-html">${this.escapeHtml(snippet.htmlContent)}</code></pre>
                </div>
                <div class="tab-pane fade" id="detail-css">
                    <pre><code class="language-css">${snippet.cssContent || '/* No CSS */'}</code></pre>
                </div>
                <div class="tab-pane fade" id="detail-js">
                    <pre><code class="language-javascript">${snippet.jsContent || '// No JavaScript'}</code></pre>
                </div>
            </div>`;

        const modal = new bootstrap.Modal(document.getElementById('snippetDetailModal'));
        modal.show();
    }

    useSnippet(id) {
        const snippet = this.snippets.find(s => s.id === id);
        if (!snippet) return;

        // Copy to clipboard
        const code = `<!-- ${snippet.name} -->
${snippet.htmlContent}

<style>
${snippet.cssContent}
</style>

<script>
${snippet.jsContent}
</script>`;

        navigator.clipboard.writeText(code).then(() => {
            this.showAlert('Snippet copied to clipboard!', 'success');
        });
    }

    saveSnippet() {
        const name = document.getElementById('snippet-name').value;
        const category = document.getElementById('snippet-category').value;
        const description = document.getElementById('snippet-description').value;
        const htmlContent = document.getElementById('snippet-html').value;
        const cssContent = document.getElementById('snippet-css').value;
        const jsContent = document.getElementById('snippet-js').value;

        if (!name || !category) {
            this.showAlert('Please fill in required fields', 'warning');
            return;
        }

        const snippet = {
            id: Date.now().toString(),
            name,
            category,
            description,
            htmlContent,
            cssContent,
            jsContent,
            version: '1.0.0',
            createdAt: new Date().toISOString()
        };

        // Save to API
        await this.saveSnippetToAPI(snippet);
        
        this.snippets.push(snippet);
        this.saveToStorage();
        this.applyFilters();

        // Close modal and reset form
        const modal = bootstrap.Modal.getInstance(document.getElementById('createSnippetModal'));
        modal.hide();
        document.getElementById('snippet-form').reset();

        this.showAlert('Snippet created successfully!', 'success');
    }

    updatePreview() {
        const html = document.getElementById('snippet-html').value;
        const css = document.getElementById('snippet-css').value;
        const js = document.getElementById('snippet-js').value;
        
        const preview = document.getElementById('snippet-preview');
        
        let content = html;
        if (css) {
            content += `<style>${css}</style>`;
        }
        
        preview.innerHTML = content;
        
        if (js) {
            try {
                // Execute JavaScript in a safe way
                const script = document.createElement('script');
                script.textContent = js;
                preview.appendChild(script);
            } catch (error) {
                console.warn('JavaScript execution error:', error);
            }
        }
    }

    copyToClipboard() {
        if (!this.selectedSnippet) return;

        const code = `<!-- ${this.selectedSnippet.name} -->
${this.selectedSnippet.htmlContent}

<style>
${this.selectedSnippet.cssContent}
</style>

<script>
${this.selectedSnippet.jsContent}
</script>`;

        navigator.clipboard.writeText(code).then(() => {
            this.showAlert('Code copied to clipboard!', 'success');
        });
    }

    useInBuilder() {
        if (!this.selectedSnippet) return;
        
        // This would integrate with the website builder
        this.showAlert('Snippet added to website builder!', 'success');
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('snippetDetailModal'));
        modal.hide();
    }

    saveToStorage() {
        const userSnippets = this.snippets.filter(s => !['1', '2', '3', '4'].includes(s.id));
        localStorage.setItem('velocity-snippets', JSON.stringify(userSnippets));
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showAlert(message, type) {
        const alertHTML = `
            <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>`;
        document.body.insertAdjacentHTML('afterbegin', alertHTML);
    }
}

    async saveSnippetToAPI(snippet) {
        try {
            const response = await fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    query: `
                        mutation CreateSnippet($input: CreatePredefinedSnippetInput!) {
                            createSnippet(input: $input) {
                                id
                                name
                                category
                                description
                                htmlContent
                                cssContent
                                jsContent
                                version
                                createdAt
                                updatedAt
                            }
                        }
                    `,
                    variables: {
                        input: {
                            name: snippet.name,
                            category: snippet.category,
                            description: snippet.description,
                            htmlContent: snippet.htmlContent,
                            cssContent: snippet.cssContent || '',
                            jsContent: snippet.jsContent || '',
                            version: snippet.version
                        }
                    }
                })
            });
            
            const data = await response.json();
            if (data.errors) {
                throw new Error(data.errors[0].message);
            }
            
            return data.data.createSnippet;
        } catch (error) {
            console.error('Failed to save snippet to API:', error);
            this.showAlert('Failed to save snippet: ' + error.message, 'danger');
            throw error;
        }
    }

    getAuthToken() {
        return localStorage.getItem('authToken') || sessionStorage.getItem('authToken') || '';
    }
}

// Initialize snippet manager
const snippetManager = new SnippetManager();