<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VelocityWave Platform - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/platform.css" rel="stylesheet">
    <link href="/api/theme.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/">
                <i class="bi bi-lightning-charge me-2"></i>VelocityWave
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/builder">
                            <i class="bi bi-tools me-1"></i>Builder
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/addon-builder">
                            <i class="bi bi-puzzle me-1"></i>Addons
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/snippets">
                            <i class="bi bi-code-square me-1"></i>Snippets
                        </a>
                    </li>
                </ul>
                
                <div hx-get="/api/user/menu" hx-trigger="load" class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>Loading...
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm bg-primary text-white">
                    <div class="card-body p-4">
                        <div hx-get="/api/dashboard/welcome" hx-trigger="load">
                            <h1 class="display-5 fw-bold mb-2">Welcome back!</h1>
                            <p class="lead mb-0">Ready to build something amazing?</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div hx-get="/api/dashboard/stats/websites" hx-trigger="load">
                            <i class="bi bi-globe display-4 text-primary mb-2"></i>
                            <h3 class="mb-1">0</h3>
                            <small class="text-muted">Websites Built</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div hx-get="/api/dashboard/stats/addons" hx-trigger="load">
                            <i class="bi bi-puzzle display-4 text-success mb-2"></i>
                            <h3 class="mb-1">0</h3>
                            <small class="text-muted">Addons Created</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div hx-get="/api/dashboard/stats/snippets" hx-trigger="load">
                            <i class="bi bi-code-square display-4 text-warning mb-2"></i>
                            <h3 class="mb-1">0</h3>
                            <small class="text-muted">Code Snippets</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div hx-get="/api/dashboard/stats/deployments" hx-trigger="load">
                            <i class="bi bi-cloud-upload display-4 text-info mb-2"></i>
                            <h3 class="mb-1">5</h3>
                            <small class="text-muted">Deployments</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <div class="row">
            <!-- Recent Projects -->
            <div class="col-lg-8 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0 py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 fw-bold">Recent Projects</h5>
                            <a href="/builder" class="btn btn-primary btn-sm">
                                <i class="bi bi-plus-circle me-1"></i>New Project
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div hx-get="/api/dashboard/projects/recent" hx-trigger="load">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Feed -->
            <div class="col-lg-4 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0 py-3">
                        <h5 class="mb-0 fw-bold">Recent Activity</h5>
                    </div>
                    <div class="card-body p-0">
                        <div hx-get="/api/dashboard/activity/recent" hx-trigger="load">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0 py-3">
                        <h5 class="mb-0 fw-bold">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="/builder" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-4 text-decoration-none">
                                    <i class="bi bi-tools display-6 mb-2"></i>
                                    <span class="fw-medium">Website Builder</span>
                                    <small class="text-muted">Create new websites</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="/addon-builder" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-4 text-decoration-none">
                                    <i class="bi bi-puzzle display-6 mb-2"></i>
                                    <span class="fw-medium">Addon Builder</span>
                                    <small class="text-muted">Create custom addons</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="/snippets" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-4 text-decoration-none">
                                    <i class="bi bi-code-square display-6 mb-2"></i>
                                    <span class="fw-medium">Code Snippets</span>
                                    <small class="text-muted">Manage code snippets</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="/theme-editor" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-4 text-decoration-none">
                                    <i class="bi bi-palette display-6 mb-2"></i>
                                    <span class="fw-medium">Theme Editor</span>
                                    <small class="text-muted">Customize themes</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
</body>
</html>