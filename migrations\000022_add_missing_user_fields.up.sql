-- Add missing fields to users table that are referenced in GraphQL schema and templates

-- Add profile picture field to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(500);

-- Add first_name and last_name if they don't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS first_name <PERSON><PERSON><PERSON><PERSON>(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_name VA<PERSON>HAR(100);

-- Add phone field
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(20);

-- Add email verification fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT false;
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verification_token VARCHAR(255);

-- Add password reset fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_reset_token VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_reset_expires TIMESTAMP WITH TIME ZONE;

-- Add last login tracking
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_ip INET;

-- Add user status and role fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'active'; -- active, inactive, suspended, deleted
ALTER TABLE users ADD COLUMN IF NOT EXISTS user_role VARCHAR(50) DEFAULT 'client'; -- client, expert, admin

-- Add missing fields to sites table
ALTER TABLE sites ADD COLUMN IF NOT EXISTS favicon_url VARCHAR(500);
ALTER TABLE sites ADD COLUMN IF NOT EXISTS logo_url VARCHAR(500);
ALTER TABLE sites ADD COLUMN IF NOT EXISTS meta_image VARCHAR(500);
ALTER TABLE sites ADD COLUMN IF NOT EXISTS analytics_code TEXT;
ALTER TABLE sites ADD COLUMN IF NOT EXISTS custom_css TEXT;
ALTER TABLE sites ADD COLUMN IF NOT EXISTS custom_js TEXT;
ALTER TABLE sites ADD COLUMN IF NOT EXISTS robots_txt TEXT;
ALTER TABLE sites ADD COLUMN IF NOT EXISTS sitemap_url VARCHAR(500);

-- Add missing fields to pages table
ALTER TABLE pages ADD COLUMN IF NOT EXISTS featured_image VARCHAR(500);
ALTER TABLE pages ADD COLUMN IF NOT EXISTS excerpt TEXT;
ALTER TABLE pages ADD COLUMN IF NOT EXISTS author_id UUID REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE pages ADD COLUMN IF NOT EXISTS published_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE pages ADD COLUMN IF NOT EXISTS page_type VARCHAR(50) DEFAULT 'page'; -- page, post, landing
ALTER TABLE pages ADD COLUMN IF NOT EXISTS template_name VARCHAR(100);
ALTER TABLE pages ADD COLUMN IF NOT EXISTS custom_css TEXT;
ALTER TABLE pages ADD COLUMN IF NOT EXISTS custom_js TEXT;

-- Add missing fields to tenants table
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS company_logo VARCHAR(500);
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS company_website VARCHAR(500);
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS company_phone VARCHAR(20);
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS company_address TEXT;
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS billing_email VARCHAR(255);
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS tax_id VARCHAR(50);
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS currency VARCHAR(3) DEFAULT 'GBP';
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS timezone VARCHAR(100) DEFAULT 'Europe/London';
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}';

-- Create indexes for new fields
CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(user_role);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login_at);

CREATE INDEX IF NOT EXISTS idx_pages_author_id ON pages(author_id);
CREATE INDEX IF NOT EXISTS idx_pages_published_at ON pages(published_at);
CREATE INDEX IF NOT EXISTS idx_pages_page_type ON pages(page_type);

-- Add some default predefined snippets
INSERT INTO predefined_snippets (name, category, description, html_content, css_content, tags) VALUES
('Bootstrap Button', 'Buttons', 'A standard Bootstrap button component', '<button class="btn btn-primary">Click Me</button>', '', ARRAY['button', 'bootstrap', 'basic']),
('Hero Section', 'Layout', 'A responsive hero section with call-to-action', '<div class="bg-primary text-white p-5 text-center"><h1 class="display-4">Welcome</h1><p class="lead">Your amazing tagline here</p><a class="btn btn-light btn-lg" href="#" role="button">Get Started</a></div>', '', ARRAY['hero', 'header', 'cta']),
('Feature Card', 'Cards', 'A feature card with icon and description', '<div class="card h-100"><div class="card-body text-center"><i class="bi bi-star display-4 text-primary mb-3"></i><h5 class="card-title">Feature Title</h5><p class="card-text">Feature description goes here.</p></div></div>', '', ARRAY['card', 'feature', 'icon']),
('Contact Form', 'Forms', 'A simple contact form with validation', '<form><div class="mb-3"><label for="name" class="form-label">Name</label><input type="text" class="form-control" id="name" required></div><div class="mb-3"><label for="email" class="form-label">Email</label><input type="email" class="form-control" id="email" required></div><div class="mb-3"><label for="message" class="form-label">Message</label><textarea class="form-control" id="message" rows="4" required></textarea></div><button type="submit" class="btn btn-primary">Send Message</button></form>', '', ARRAY['form', 'contact', 'validation']),
('Navigation Bar', 'Navigation', 'Responsive Bootstrap navigation bar', '<nav class="navbar navbar-expand-lg navbar-light bg-light"><div class="container"><a class="navbar-brand" href="#">Brand</a><button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"><span class="navbar-toggler-icon"></span></button><div class="collapse navbar-collapse" id="navbarNav"><ul class="navbar-nav ms-auto"><li class="nav-item"><a class="nav-link" href="#">Home</a></li><li class="nav-item"><a class="nav-link" href="#">About</a></li><li class="nav-item"><a class="nav-link" href="#">Services</a></li><li class="nav-item"><a class="nav-link" href="#">Contact</a></li></ul></div></div></nav>', '', ARRAY['navigation', 'navbar', 'responsive'])
ON CONFLICT DO NOTHING;

-- Add default site themes
INSERT INTO site_themes (theme_name, theme_config, css_variables, is_default) VALUES
('Default', '{"name": "Default", "description": "Clean and professional default theme"}', '{"primary": "#0d6efd", "secondary": "#6c757d", "success": "#198754", "info": "#0dcaf0", "warning": "#ffc107", "danger": "#dc3545"}', true),
('Dark Mode', '{"name": "Dark Mode", "description": "Modern dark theme for better readability"}', '{"primary": "#375a7f", "secondary": "#444", "success": "#00bc8c", "info": "#3498db", "warning": "#f39c12", "danger": "#e74c3c", "dark": "#222", "light": "#f8f9fa"}', false),
('Minimal', '{"name": "Minimal", "description": "Clean and minimal design"}', '{"primary": "#2c3e50", "secondary": "#95a5a6", "success": "#27ae60", "info": "#3498db", "warning": "#f39c12", "danger": "#e74c3c"}', false)
ON CONFLICT DO NOTHING;