$ErrorActionPreference = "Continue"
Write-Host "Checking for Go build errors..."

# Capture stderr and stdout
$process = Start-Process -FilePath "C:\Program Files\Go\bin\go.exe" -ArgumentList "build", "./..." -RedirectStandardError "tmp_errors.txt" -RedirectStandardOutput "tmp_output.txt" -Wait -PassThru

if (Test-Path "tmp_errors.txt") {
    $errors = Get-Content "tmp_errors.txt"
    if ($errors) {
        Write-Host "Build errors found:"
        $errors | ForEach-Object { Write-Host $_ }
    } else {
        Write-Host "No build errors in stderr"
    }
    Remove-Item "tmp_errors.txt" -ErrorAction SilentlyContinue
}

if (Test-Path "tmp_output.txt") {
    $output = Get-Content "tmp_output.txt"
    if ($output) {
        Write-Host "Build output:" 
        $output | ForEach-Object { Write-Host $_ }
    }
    Remove-Item "tmp_output.txt" -ErrorAction SilentlyContinue
}

Write-Host "Exit code: $($process.ExitCode)"      
