package addon

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"
)

// APIProxyMediator handles proxying and mediating API calls
type APIProxyMediator struct {
	configManager     *APIConfigurationManager
	credentialManager *SecureCredentialManager
	rateLimiters      map[string]*RateLimiter
	connections       map[string]*APIConnection
	httpClient        *http.Client
	mutex             sync.RWMutex
}

// NewAPIProxyMediator creates a new API proxy mediator
func NewAPIProxyMediator(configManager *APIConfigurationManager, credentialManager *SecureCredentialManager) *APIProxyMediator {
	return &APIProxyMediator{
		configManager:     configManager,
		credentialManager: credentialManager,
		rateLimiters:      make(map[string]*RateLimiter),
		connections:       make(map[string]*APIConnection),
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 10,
				IdleConnTimeout:     90 * time.Second,
			},
		},
	}
}

// ProxyAPICall proxies an API call through the mediation layer
func (p *APIProxyMediator) ProxyAPICall(ctx context.Context, request *APIProxyRequest, userID string) (*APIProxyResponse, error) {
	// Get API configuration
	apiConfig, err := p.configManager.GetAPIConfiguration(ctx, request.APIConfigID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get API configuration: %w", err)
	}

	// Check API status
	if apiConfig.Status != APIStatusActive {
		return nil, fmt.Errorf("API is not active (status: %s)", apiConfig.Status)
	}

	// Get decrypted credentials
	credentials, err := p.credentialManager.GetDecryptedCredentials(ctx, request.CredentialsID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get credentials: %w", err)
	}

	// Validate security constraints
	if err := p.validateSecurityConstraints(apiConfig, request); err != nil {
		return nil, fmt.Errorf("security validation failed: %w", err)
	}

	// Check rate limits
	if err := p.checkRateLimit(apiConfig, request); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	// Build the actual HTTP request
	httpReq, err := p.buildHTTPRequest(apiConfig, credentials, request)
	if err != nil {
		return nil, fmt.Errorf("failed to build HTTP request: %w", err)
	}

	// Execute the request
	startTime := time.Now()
	httpResp, err := p.httpClient.Do(httpReq)
	responseTime := time.Since(startTime)

	// Update connection statistics
	p.updateConnectionStats(request.APIConfigID, request.CredentialsID, err == nil, responseTime)

	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// Read response body
	respBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Build proxy response
	response := &APIProxyResponse{
		StatusCode:   httpResp.StatusCode,
		Headers:      make(map[string]string),
		Body:         respBody,
		ResponseTime: responseTime,
		Timestamp:    time.Now(),
	}

	// Copy response headers
	for key, values := range httpResp.Header {
		if len(values) > 0 {
			response.Headers[key] = values[0]
		}
	}

	// Log the request for audit purposes
	p.logAPICall(request, response, userID)

	return response, nil
}

// TestAPIConnection tests an API connection
func (p *APIProxyMediator) TestAPIConnection(ctx context.Context, request *APIConnectionTestRequest, userID string) (*APIConnection, error) {
	// Create a simple test request
	testRequest := &APIProxyRequest{
		APIConfigID:   request.APIConfigID,
		CredentialsID: request.CredentialsID,
		Endpoint:      request.Endpoint,
		Method:        "GET",
		Headers:       make(map[string]string),
	}

	if request.TestData != nil {
		testRequest.Payload = request.TestData
	}

	// Execute test call
	startTime := time.Now()
	response, err := p.ProxyAPICall(ctx, testRequest, userID)
	responseTime := time.Since(startTime)

	// Create connection record
	connection := &APIConnection{
		ID:            generateUUID(),
		UserID:        userID,
		APIConfigID:   request.APIConfigID,
		CredentialsID: request.CredentialsID,
		LastPing:      time.Now(),
		ResponseTime:  responseTime,
		CreatedAt:     time.Now(),
		Metadata:      make(map[string]interface{}),
	}

	if err != nil {
		connection.Status = ConnectionStatusError
		connection.ErrorCount = 1
		connection.Metadata["last_error"] = err.Error()
	} else {
		connection.Status = ConnectionStatusConnected
		connection.SuccessCount = 1
		connection.Metadata["last_response_code"] = response.StatusCode
	}

	connection.TotalRequests = 1

	// Store connection record
	if err := p.configManager.db.CreateAPIConnection(ctx, connection, userID); err != nil {
		return nil, fmt.Errorf("failed to store connection record: %w", err)
	}

	return connection, nil
}

// buildHTTPRequest builds the actual HTTP request
func (p *APIProxyMediator) buildHTTPRequest(apiConfig *APIConfiguration, credentials *CredentialData, request *APIProxyRequest) (*http.Request, error) {
	// Build full URL
	baseURL := strings.TrimSuffix(apiConfig.BaseURL, "/")
	endpoint := strings.TrimPrefix(request.Endpoint, "/")
	fullURL := fmt.Sprintf("%s/%s", baseURL, endpoint)

	// Parse URL to validate
	parsedURL, err := url.Parse(fullURL)
	if err != nil {
		return nil, fmt.Errorf("invalid URL: %w", err)
	}

	// Prepare request body
	var body io.Reader
	if request.Payload != nil {
		payloadBytes, err := json.Marshal(request.Payload)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal payload: %w", err)
		}
		body = bytes.NewReader(payloadBytes)
	}

	// Create HTTP request
	httpReq, err := http.NewRequest(request.Method, parsedURL.String(), body)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set default headers
	httpReq.Header.Set("User-Agent", "VelocityWave-API-Proxy/1.0")
	httpReq.Header.Set("Accept", "application/json")

	if request.Payload != nil {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// Add API configuration headers
	for key, value := range apiConfig.Headers {
		httpReq.Header.Set(key, value)
	}

	// Add request-specific headers
	for key, value := range request.Headers {
		httpReq.Header.Set(key, value)
	}

	// Add authentication headers
	if err := p.addAuthenticationHeaders(httpReq, apiConfig.AuthType, credentials); err != nil {
		return nil, fmt.Errorf("failed to add authentication: %w", err)
	}

	return httpReq, nil
}

// addAuthenticationHeaders adds authentication headers based on auth type
func (p *APIProxyMediator) addAuthenticationHeaders(req *http.Request, authType AuthenticationType, credentials *CredentialData) error {
	switch authType {
	case AuthTypeAPIKey:
		if credentials.APIKey == "" {
			return fmt.Errorf("API key not found in credentials")
		}
		// Common API key header patterns
		if credentials.Headers != nil {
			for key, value := range credentials.Headers {
				req.Header.Set(key, value)
			}
		} else {
			req.Header.Set("X-API-Key", credentials.APIKey)
		}

	case AuthTypeBearer:
		if credentials.Token == "" {
			return fmt.Errorf("bearer token not found in credentials")
		}
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", credentials.Token))

	case AuthTypeBasic:
		if credentials.Username == "" || credentials.Password == "" {
			return fmt.Errorf("username/password not found in credentials")
		}
		req.SetBasicAuth(credentials.Username, credentials.Password)

	case AuthTypeOAuth2:
		if credentials.Token == "" {
			return fmt.Errorf("OAuth2 token not found in credentials")
		}
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", credentials.Token))

	case AuthTypeCustom:
		// For custom auth, use headers from credentials
		if credentials.Headers != nil {
			for key, value := range credentials.Headers {
				req.Header.Set(key, value)
			}
		}

	case AuthTypeNone:
		// No authentication required

	default:
		return fmt.Errorf("unsupported authentication type: %s", authType)
	}

	return nil
}

// validateSecurityConstraints validates security constraints
func (p *APIProxyMediator) validateSecurityConstraints(apiConfig *APIConfiguration, request *APIProxyRequest) error {
	if apiConfig.Security == nil {
		return nil
	}

	security := apiConfig.Security

	// Check HTTPS requirement
	if security.RequireHTTPS && !strings.HasPrefix(apiConfig.BaseURL, "https://") {
		return fmt.Errorf("HTTPS is required but API base URL is not HTTPS")
	}

	// Check allowed domains
	if len(security.AllowedDomains) > 0 {
		parsedURL, err := url.Parse(apiConfig.BaseURL)
		if err != nil {
			return fmt.Errorf("invalid base URL: %w", err)
		}

		allowed := false
		for _, domain := range security.AllowedDomains {
			if strings.Contains(parsedURL.Host, domain) {
				allowed = true
				break
			}
		}

		if !allowed {
			return fmt.Errorf("domain %s is not in allowed domains list", parsedURL.Host)
		}
	}

	// Check blocked domains
	if len(security.BlockedDomains) > 0 {
		parsedURL, err := url.Parse(apiConfig.BaseURL)
		if err != nil {
			return fmt.Errorf("invalid base URL: %w", err)
		}

		for _, domain := range security.BlockedDomains {
			if strings.Contains(parsedURL.Host, domain) {
				return fmt.Errorf("domain %s is blocked", parsedURL.Host)
			}
		}
	}

	// Check allowed methods
	if len(security.AllowedMethods) > 0 {
		allowed := false
		for _, method := range security.AllowedMethods {
			if method == request.Method {
				allowed = true
				break
			}
		}

		if !allowed {
			return fmt.Errorf("HTTP method %s is not allowed", request.Method)
		}
	}

	// Check request size (if payload exists)
	if request.Payload != nil && security.MaxRequestSize > 0 {
		payloadBytes, _ := json.Marshal(request.Payload)
		if int64(len(payloadBytes)) > security.MaxRequestSize {
			return fmt.Errorf("request payload size exceeds maximum allowed size")
		}
	}

	return nil
}

// checkRateLimit checks rate limiting constraints
func (p *APIProxyMediator) checkRateLimit(apiConfig *APIConfiguration, request *APIProxyRequest) error {
	if apiConfig.RateLimit == nil {
		return nil
	}

	rateLimitKey := fmt.Sprintf("%s:%s", request.APIConfigID, request.CredentialsID)

	p.mutex.Lock()
	rateLimiter, exists := p.rateLimiters[rateLimitKey]
	if !exists {
		rateLimiter = &RateLimiter{
			RequestsPerSecond: apiConfig.RateLimit.RequestsPerSecond,
			RequestsPerMinute: apiConfig.RateLimit.RequestsPerMinute,
			RequestsPerHour:   apiConfig.RateLimit.RequestsPerHour,
			SecondWindow:      make([]time.Time, 0),
			MinuteWindow:      make([]time.Time, 0),
			HourWindow:        make([]time.Time, 0),
		}
		p.rateLimiters[rateLimitKey] = rateLimiter
	}
	p.mutex.Unlock()

	return rateLimiter.CheckLimit()
}

// updateConnectionStats updates connection statistics
func (p *APIProxyMediator) updateConnectionStats(apiConfigID, credentialsID string, success bool, responseTime time.Duration) {
	connectionKey := fmt.Sprintf("%s:%s", apiConfigID, credentialsID)

	p.mutex.Lock()
	defer p.mutex.Unlock()

	connection, exists := p.connections[connectionKey]
	if !exists {
		connection = &APIConnection{
			ID:            generateUUID(),
			APIConfigID:   apiConfigID,
			CredentialsID: credentialsID,
			CreatedAt:     time.Now(),
		}
		p.connections[connectionKey] = connection
	}

	connection.LastPing = time.Now()
	connection.ResponseTime = responseTime
	connection.TotalRequests++

	if success {
		connection.SuccessCount++
		connection.Status = ConnectionStatusConnected
	} else {
		connection.ErrorCount++
		connection.Status = ConnectionStatusError
	}
}

// logAPICall logs API calls for audit purposes
func (p *APIProxyMediator) logAPICall(request *APIProxyRequest, response *APIProxyResponse, userID string) {
	// In a production system, this would log to a proper audit system
	logEntry := map[string]interface{}{
		"timestamp":      time.Now(),
		"user_id":        userID,
		"api_config_id":  request.APIConfigID,
		"credentials_id": request.CredentialsID,
		"method":         request.Method,
		"endpoint":       request.Endpoint,
		"status_code":    response.StatusCode,
		"response_time":  response.ResponseTime.Milliseconds(),
	}

	// Log to stdout for now (in production, use proper logging)
	logBytes, _ := json.Marshal(logEntry)
	fmt.Printf("API_CALL_LOG: %s\n", string(logBytes))
}

// APIProxyRequest represents a request to proxy an API call
type APIProxyRequest struct {
	APIConfigID   string            `json:"api_config_id"`
	CredentialsID string            `json:"credentials_id"`
	Endpoint      string            `json:"endpoint"`
	Method        string            `json:"method"`
	Payload       interface{}       `json:"payload,omitempty"`
	Headers       map[string]string `json:"headers"`
}

// APIProxyResponse represents the response from a proxied API call
type APIProxyResponse struct {
	StatusCode   int               `json:"status_code"`
	Headers      map[string]string `json:"headers"`
	Body         []byte            `json:"body"`
	ResponseTime time.Duration     `json:"response_time"`
	Timestamp    time.Time         `json:"timestamp"`
}

// APIConnectionTestRequest represents a request to test an API connection
type APIConnectionTestRequest struct {
	APIConfigID   string      `json:"api_config_id"`
	CredentialsID string      `json:"credentials_id"`
	Endpoint      string      `json:"endpoint,omitempty"`
	TestData      interface{} `json:"test_data,omitempty"`
}

// CheckLimit checks if the rate limit allows a new request
func (rl *RateLimiter) CheckLimit() error {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()

	// Clean old entries and check limits
	if rl.RequestsPerSecond > 0 {
		rl.cleanWindow(&rl.SecondWindow, now, time.Second)
		if len(rl.SecondWindow) >= rl.RequestsPerSecond {
			return fmt.Errorf("rate limit exceeded: %d requests per second", rl.RequestsPerSecond)
		}
	}

	if rl.RequestsPerMinute > 0 {
		rl.cleanWindow(&rl.MinuteWindow, now, time.Minute)
		if len(rl.MinuteWindow) >= rl.RequestsPerMinute {
			return fmt.Errorf("rate limit exceeded: %d requests per minute", rl.RequestsPerMinute)
		}
	}

	if rl.RequestsPerHour > 0 {
		rl.cleanWindow(&rl.HourWindow, now, time.Hour)
		if len(rl.HourWindow) >= rl.RequestsPerHour {
			return fmt.Errorf("rate limit exceeded: %d requests per hour", rl.RequestsPerHour)
		}
	}

	// Add current request to windows
	if rl.RequestsPerSecond > 0 {
		rl.SecondWindow = append(rl.SecondWindow, now)
	}
	if rl.RequestsPerMinute > 0 {
		rl.MinuteWindow = append(rl.MinuteWindow, now)
	}
	if rl.RequestsPerHour > 0 {
		rl.HourWindow = append(rl.HourWindow, now)
	}

	return nil
}

// cleanWindow removes old entries from a time window
func (rl *RateLimiter) cleanWindow(window *[]time.Time, now time.Time, duration time.Duration) {
	cutoff := now.Add(-duration)
	var newWindow []time.Time

	for _, t := range *window {
		if t.After(cutoff) {
			newWindow = append(newWindow, t)
		}
	}

	*window = newWindow
}
