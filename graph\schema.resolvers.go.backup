package graph

import (
	"context"
	"fmt"
	"time"
	"os"
	"encoding/json"
	"bytes"
	"net/http"
	
	"goVwPlatformAPI/graph/generated"
	"github.com/google/uuid"
	"goVwPlatformAPI/graph/model"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"goVwPlatformAPI/internal/crypto"
	"goVwPlatformAPI/internal/database"
	"goVwPlatformAPI/internal/auth"
)

func (r *Resolver) Mutation() generated.MutationResolver {
	return &mutationResolver{
		Resolver: r,
		DB: r.DB,
	}
}

func (r *Resolver) Query() generated.QueryResolver {
	return &queryResolver{r}
}

type mutationResolver struct{
	*Resolver
	DB *database.DB
}

type queryResolver struct{ *Resolver }

func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), 14)
	return string(bytes), err
}

func (r *mutationResolver) GenerateSiteApiKey(ctx context.Context, siteID string, permissions int) (*model.APIKey, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	return r.DB.GenerateSiteApiKey(ctx, siteID, permissions, user)
}

func (r *mutationResolver) RevokeApiKey(ctx context.Context, keyID string) (bool, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	err = r.DB.RevokeApiKey(ctx, keyID, user)
	return err == nil, err
}

func (r *mutationResolver) RotateApiKey(ctx context.Context, keyID string) (*model.APIKey, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	return r.DB.RotateApiKey(ctx, keyID, user)
}

func (r *mutationResolver) Login(ctx context.Context, email string, password string) (*model.AuthPayload, error) {
	user, err := r.DB.GetUserByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("invalid credentials")
	}

	// TODO: Implement proper password verification with bcrypt
	// For now, just create a token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{
		Subject:   user.ID,
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 24 * 7)),
	})

	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET")))
	if err != nil {
		return nil, fmt.Errorf("failed to generate token")
	}
	
	return &model.AuthPayload{
		Token: tokenString,
		User:  user,
	}, nil
}

func (r *mutationResolver) Register(ctx context.Context, email string, password string) (*model.AuthPayload, error) {
	passwordHash, err := HashPassword(password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}
	
	user, err := r.DB.CreateUser(ctx, email, passwordHash)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{
		Subject:   user.ID,
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 24 * 7)),
	})

	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET")))
	if err != nil {
		return nil, fmt.Errorf("failed to generate token")
	}
	
	return &model.AuthPayload{
		Token: tokenString,
		User:  user,
	}, nil
}

func (r *queryResolver) Health(ctx context.Context) (string, error) {
	return "OK", nil
}

func (r *mutationResolver) CreateVehicle(ctx context.Context, input model.VehicleInput) (*model.Vehicle, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.DB.CreateVehicle(ctx, input, user)
}

func (r *mutationResolver) UpdateVehicle(ctx context.Context, vin string, input model.VehicleInput) (*model.Vehicle, error) {
	return &model.Vehicle{
		Vin:     vin,
		Make:    input.Make,
		Model:   input.Model,
		Year:    input.Year,
		Owner:   &model.User{ID: "1", Email: "<EMAIL>"},
	}, nil
}

func (r *mutationResolver) DeleteVehicle(ctx context.Context, vin string) (bool, error) {
	return true, nil
}

func (r *mutationResolver) CreateSnippet(ctx context.Context, input model.CreatePredefinedSnippetInput) (*model.PredefinedSnippet, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "snippet:create") {
		return nil, fmt.Errorf("unauthorized")
	}

	snippet, err := r.DB.CreateSnippet(ctx, input, user)
	if err != nil {
		return nil, fmt.Errorf("failed to create snippet: %w", err)
	}

	return snippet, nil
}

func (r *mutationResolver) MovePage(ctx context.Context, id string, parentID *string, position int) (*model.Page, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "page:edit") {
		return nil, fmt.Errorf("unauthorized")
	}

	page, err := r.DB.MovePage(ctx, id, parentID, position, user)
	if err != nil {
		return nil, fmt.Errorf("failed to move page: %w", err)
	}

	return page, nil
}

func (r *mutationResolver) SaveAddonConfig(ctx context.Context, input model.AddonConfigInput) (*model.AddonConfig, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "addon:edit") {
		return nil, fmt.Errorf("unauthorized")
	}

	return r.DB.SaveAddonConfig(ctx, input, user)
}

func (r *mutationResolver) SubmitToReview(ctx context.Context, id string) (*model.AddonConfig, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "addon:edit") {
		return nil, fmt.Errorf("unauthorized")
	}

	addon, err := r.DB.LoadAddonConfig(ctx, id, user)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	if addon.State != model.AddonStateDevelopment {
		return nil, fmt.Errorf("addon must be in development state to submit for review")
	}

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateReview, user)
}

func (r *mutationResolver) CallExternalAPI(
	ctx context.Context,
	name string,
	payload map[string]interface{},
	headers []*model.HeaderInput,
) (map[string]interface{}, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "api:call") {
		return nil, fmt.Errorf("unauthorized")
	}

	// Get API credentials from database
	creds, err := r.DB.GetAPICredentials(ctx, name, user)
	if err != nil {
		return nil, fmt.Errorf("failed to get API credentials: %w", err)
	}

	// Prepare headers
	headerMap := make(http.Header)
	for _, h := range headers {
		headerMap.Add(h.Key, h.Value)
	}

	// Call the API via gateway
	resp, err := r.Gateway.CallAPI(ctx, creds.Endpoint, creds.AuthHeader, bytes.NewReader(payload))
	if err != nil {
		return nil, fmt.Errorf("API call failed: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode API response: %w", err)
	}

	return result, nil
}

func (r *mutationResolver) ApproveAddon(ctx context.Context, id string) (*model.AddonConfig, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "addon:approve") {
		return nil, fmt.Errorf("unauthorized")
	}

	addon, err := r.DB.LoadAddonConfig(ctx, id, user)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	if addon.State != model.AddonStateReview {
		return nil, fmt.Errorf("addon must be in review state to approve")
	}

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateProduction, user)
}

func (r *mutationResolver) CallExternalAPI(
	ctx context.Context,
	name string,
	payload string,
	headers []*model.HeaderInput,
) (string, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return "", err
	}

	if !auth.HasPermission(user, "api:call") {
		return "", fmt.Errorf("unauthorized")
	}

	// Get API credentials from database
	creds, err := r.DB.GetAPICredentials(ctx, name, user)
	if err != nil {
		return "", fmt.Errorf("failed to get API credentials: %w", err)
	}

	// For now, return a mock response since Gateway is not implemented
	result := fmt.Sprintf(`{"status": "success", "data": %s, "api": "%s"}`, payload, name)

	return result, nil
}

func (r *mutationResolver) CallExternalAPI(
	ctx context.Context,
	name string,
	payload map[string]interface{},
	headers []*model.HeaderInput,
) (map[string]interface{}, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "api:call") {
		return nil, fmt.Errorf("unauthorized")
	}

	// Get API credentials from database
	creds, err := r.DB.GetAPICredentials(ctx, name, user)
	if err != nil {
		return nil, fmt.Errorf("failed to get API credentials: %w", err)
	}

	// Prepare headers
	headerMap := make(http.Header)
	for _, h := range headers {
		headerMap.Add(h.Key, h.Value)
	}

	// Call the API via gateway
	resp, err := r.Gateway.CallAPI(ctx, creds.Endpoint, creds.AuthHeader, bytes.NewReader(payload))
	if err != nil {
		return nil, fmt.Errorf("API call failed: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode API response: %w", err)
	}

	return result, nil
}

func (r *queryResolver) GetAddonsInReview(ctx context.Context) ([]*model.AddonConfig, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "addon:review") {
		return nil, fmt.Errorf("unauthorized")
	}

	return r.DB.GetAddonsInReview(ctx)
}

func (r *mutationResolver) UpdateAddonStatus(ctx context.Context, id string, status model.AddonState) (*model.AddonConfig, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "addon:review") {
		return nil, fmt.Errorf("unauthorized")
	}

	return r.DB.UpdateAddonState(ctx, id, status, user)
}

func (r *mutationResolver) ApproveAddon(ctx context.Context, id string) (*model.AddonConfig, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "addon:approve") {
		return nil, fmt.Errorf("unauthorized")
	}

	addon, err := r.DB.LoadAddonConfig(ctx, id, user)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	if addon.State != model.AddonStateReview {
		return nil, fmt.Errorf("addon must be in review state to approve")
	}

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateProduction, user)
}

func (r *mutationResolver) RevertAddonToDevelopment(ctx context.Context, id string) (*model.AddonConfig, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "addon:approve") {
		return nil, fmt.Errorf("unauthorized")
	}

	addon, err := r.DB.LoadAddonConfig(ctx, id, user)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	if addon.State != model.AddonStateProduction && addon.State != model.AddonStateReview {
		return nil, fmt.Errorf("addon must be in production or review state to revert")
	}

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateDevelopment, user)
}

func (r *mutationResolver) DisableAddon(ctx context.Context, id string) (*model.AddonConfig, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "addon:edit") {
		return nil, fmt.Errorf("unauthorized")
	}

	addon, err := r.DB.LoadAddonConfig(ctx, id, user)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	if addon.State != model.AddonStateProduction {
		return nil, fmt.Errorf("addon must be in production state to disable")
	}

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateDisabled, user)
}

func (r *mutationResolver) EnableAddon(ctx context.Context, id string) (*model.AddonConfig, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "addon:edit") {
		return nil, fmt.Errorf("unauthorized")
	}

	addon, err := r.DB.LoadAddonConfig(ctx, id, user)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	if addon.State != model.AddonStateDisabled {
		return nil, fmt.Errorf("addon must be in disabled state to enable")
	}

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateProduction, user)
}


func (r *queryResolver) LoadAddonConfig(ctx context.Context, id string) (*model.AddonConfig, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "addon:view") {
		return nil, fmt.Errorf("unauthorized")
	}

	return r.DB.LoadAddonConfig(ctx, id, user)
}

func (r *queryResolver) GetNavigation(ctx context.Context) ([]*model.Page, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "page:view") {
		return nil, fmt.Errorf("unauthorized")
	}

	pages, err := r.DB.GetNavigation(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("failed to get navigation: %w", err)
	}

	return pages, nil
}

func (r *mutationResolver) UpdateSnippet(ctx context.Context, input model.UpdatePredefinedSnippetInput) (*model.PredefinedSnippet, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(user, "snippet:update") {
		return nil, fmt.Errorf("unauthorized")
	}

	snippet, err := r.DB.UpdateSnippet(ctx, input, user)
	if err != nil {
		return nil, fmt.Errorf("failed to update snippet: %w", err)
	}

	return snippet, nil
}

func (r *mutationResolver) DeleteSnippet(ctx context.Context, id string) (bool, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	if !auth.HasPermission(user, "snippet:delete") {
		return false, fmt.Errorf("unauthorized")
	}

	err = r.DB.DeleteSnippet(ctx, id, user)
	if err != nil {
		return false, fmt.Errorf("failed to delete snippet: %w", err)
	}

	return true, nil
}

func (r *queryResolver) Vehicles(ctx context.Context) ([]*model.Vehicle, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.DB.GetVehicles(ctx, user)
}

func (r *queryResolver) VehicleByVin(ctx context.Context, vin string) (*model.Vehicle, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.DB.GetVehicleByVIN(ctx, vin, user)
}

func (r *queryResolver) PredefinedSnippets(ctx context.Context, category *string) ([]*model.PredefinedSnippet, error) {
	return r.DB.GetPredefinedSnippets(ctx, category)
}

func (r *queryResolver) WebAddons(ctx context.Context, status *string) ([]*model.WebAddon, error) {
	return r.DB.GetWebAddons(ctx, status)
}

func (r *queryResolver) Component(ctx context.Context, id string, componentType model.ComponentType) (model.ComponentResult, error) {
	switch componentType {
	case model.ComponentTypeAddon:
		// Return web addon
		addons, err := r.DB.GetWebAddons(ctx, nil)
		if err != nil {
			return nil, err
		}
		for _, addon := range addons {
			if addon.ID == id {
				return addon, nil
			}
		}
		return nil, fmt.Errorf("addon not found")
	case model.ComponentTypeSnippet:
		// Return snippet
		snippets, err := r.DB.GetPredefinedSnippets(ctx, nil)
		if err != nil {
			return nil, err
		}
		for _, snippet := range snippets {
			if snippet.ID == id {
				return snippet, nil
			}
		}
		return nil, fmt.Errorf("snippet not found")
	default:
		return nil, fmt.Errorf("unknown component type")
	}
}

func (r *mutationResolver) CreateWebAddon(ctx context.Context, input model.WebAddonInput) (*model.WebAddon, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.DB.CreateWebAddon(ctx, input, user)
}

func (r *mutationResolver) UpdateWebAddon(ctx context.Context, id string, input model.WebAddonInput) (*model.WebAddon, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.DB.UpdateWebAddon(ctx, id, input, user)
}

func (r *mutationResolver) CreatePredefinedSnippet(ctx context.Context, input model.PredefinedSnippetInput) (*model.PredefinedSnippet, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	createInput := model.CreatePredefinedSnippetInput{
		Name:        input.Name,
		Category:    input.Category,
		HTMLContent: input.HTMLContent,
		CSSContent:  input.CSSContent,
		JsContent:   input.JsContent,
		Version:     input.Version,
	}

	return r.DB.CreateSnippet(ctx, createInput, user)
}

func (r *mutationResolver) UpdatePredefinedSnippet(ctx context.Context, id string, input model.PredefinedSnippetInput) (*model.PredefinedSnippet, error) {
	user, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	updateInput := model.UpdatePredefinedSnippetInput{
		ID:          id,
		Name:        &input.Name,
		Category:    &input.Category,
		HTMLContent: &input.HTMLContent,
		CSSContent:  input.CSSContent,
		JsContent:   input.JsContent,
		Version:     &input.Version,
	}

	return r.DB.UpdateSnippet(ctx, updateInput, user)
}