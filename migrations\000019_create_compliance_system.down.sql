-- Drop triggers
DROP TRIGGER IF EXISTS update_compliance_frameworks_updated_at ON compliance_frameworks;
DROP TRIGGER IF EXISTS update_compliance_requirements_updated_at ON compliance_requirements;
DROP TRIGGER IF EXISTS update_business_compliance_updated_at ON business_compliance;
DROP TRIGGER IF EXISTS update_compliance_requirement_tracking_updated_at ON compliance_requirement_tracking;

-- Drop indexes
DROP INDEX IF EXISTS idx_compliance_frameworks_code;
DROP INDEX IF EXISTS idx_compliance_frameworks_industry;

DROP INDEX IF EXISTS idx_compliance_requirements_framework;
DROP INDEX IF EXISTS idx_compliance_requirements_code;
DROP INDEX IF EXISTS idx_compliance_requirements_category;

DROP INDEX IF EXISTS idx_business_compliance_tenant;
DROP INDEX IF EXISTS idx_business_compliance_framework;
DROP INDEX IF EXISTS idx_business_compliance_status;
DROP INDEX IF EXISTS idx_business_compliance_assigned;
DROP INDEX IF EXISTS idx_business_compliance_next_review;

DROP INDEX IF EXISTS idx_requirement_tracking_compliance;
DROP INDEX IF EXISTS idx_requirement_tracking_requirement;
DROP INDEX IF EXISTS idx_requirement_tracking_status;
DROP INDEX IF EXISTS idx_requirement_tracking_due_date;

DROP INDEX IF EXISTS idx_compliance_alerts_tenant;
DROP INDEX IF EXISTS idx_compliance_alerts_status;
DROP INDEX IF EXISTS idx_compliance_alerts_trigger_date;

-- Drop tables
DROP TABLE IF EXISTS compliance_alerts;
DROP TABLE IF EXISTS compliance_requirement_tracking;
DROP TABLE IF EXISTS business_compliance;
DROP TABLE IF EXISTS compliance_requirements;
DROP TABLE IF EXISTS compliance_frameworks;