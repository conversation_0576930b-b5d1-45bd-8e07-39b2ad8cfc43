-- Add missing constraints and indexes for better performance and data integrity

-- Ensure addon_configs table has the necessary fields for admin review
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'addon_configs') THEN
        ALTER TABLE addon_configs ADD COLUMN IF NOT EXISTS created_by INTEGER REFERENCES users(id);
        ALTER TABLE addon_configs ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();
        
        -- Add index for admin queries
        CREATE INDEX IF NOT EXISTS idx_addon_configs_state ON addon_configs(state);
        CREATE INDEX IF NOT EXISTS idx_addon_configs_created_by ON addon_configs(created_by);
        CREATE INDEX IF NOT EXISTS idx_addon_configs_created_at ON addon_configs(created_at);
    END IF;
END $$;

-- Ensure pages table has the necessary fields
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'pages') THEN
        ALTER TABLE pages ADD COLUMN IF NOT EXISTS created_by INTEGER REFERENCES users(id);
        ALTER TABLE pages ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'draft';
        ALTER TABLE pages ADD COLUMN IF NOT EXISTS is_hidden BOOLEAN DEFAULT false;
        ALTER TABLE pages ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();
        
        -- Add indexes for admin queries
        CREATE INDEX IF NOT EXISTS idx_pages_created_by ON pages(created_by);
        CREATE INDEX IF NOT EXISTS idx_pages_status ON pages(status);
        CREATE INDEX IF NOT EXISTS idx_pages_created_at ON pages(created_at);
    END IF;
END $$;

-- Add revenue tracking fields to invoices table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invoices') THEN
        ALTER TABLE invoices ADD COLUMN IF NOT EXISTS user_id INTEGER REFERENCES users(id);
        ALTER TABLE invoices ADD COLUMN IF NOT EXISTS payment_method VARCHAR(50);
        ALTER TABLE invoices ADD COLUMN IF NOT EXISTS currency VARCHAR(3) DEFAULT 'USD';
        ALTER TABLE invoices ADD COLUMN IF NOT EXISTS refunded_at TIMESTAMPTZ;
        ALTER TABLE invoices ADD COLUMN IF NOT EXISTS refund_reason TEXT;
        
        -- Add indexes for revenue analytics
        CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON invoices(user_id);
        CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
        CREATE INDEX IF NOT EXISTS idx_invoices_created_at ON invoices(created_at);
        CREATE INDEX IF NOT EXISTS idx_invoices_payment_method ON invoices(payment_method);
    END IF;
END $$;

-- Create admin_dashboard_cache table for caching KPI data
CREATE TABLE IF NOT EXISTS admin_dashboard_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cache_key VARCHAR(255) NOT NULL UNIQUE,
    cache_data JSONB NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create admin_notifications table for admin alerts and notifications
CREATE TABLE IF NOT EXISTS admin_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    target_admin_id INTEGER REFERENCES users(id),
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMPTZ,
    action_url TEXT,
    metadata JSONB DEFAULT '{}',
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create system_metrics table for storing historical system metrics
CREATE TABLE IF NOT EXISTS system_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_type VARCHAR(100) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    unit VARCHAR(20),
    tags JSONB DEFAULT '{}',
    recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE
);

-- Add update triggers for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers to tables that need them
DO $$
BEGIN
    -- Support tickets
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_support_tickets_updated_at') THEN
        CREATE TRIGGER update_support_tickets_updated_at 
            BEFORE UPDATE ON support_tickets 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Expert applications
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_expert_applications_updated_at') THEN
        CREATE TRIGGER update_expert_applications_updated_at 
            BEFORE UPDATE ON expert_applications 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Expert profiles
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_expert_profiles_updated_at') THEN
        CREATE TRIGGER update_expert_profiles_updated_at 
            BEFORE UPDATE ON expert_profiles 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- User subscriptions
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_subscriptions_updated_at') THEN
        CREATE TRIGGER update_user_subscriptions_updated_at 
            BEFORE UPDATE ON user_subscriptions 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Platform configurations
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_platform_configurations_updated_at') THEN
        CREATE TRIGGER update_platform_configurations_updated_at 
            BEFORE UPDATE ON platform_configurations 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Admin dashboard cache
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_admin_dashboard_cache_updated_at') THEN
        CREATE TRIGGER update_admin_dashboard_cache_updated_at 
            BEFORE UPDATE ON admin_dashboard_cache 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Admin notifications
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_admin_notifications_updated_at') THEN
        CREATE TRIGGER update_admin_notifications_updated_at 
            BEFORE UPDATE ON admin_notifications 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Add performance indexes
CREATE INDEX IF NOT EXISTS idx_admin_dashboard_cache_key ON admin_dashboard_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_admin_dashboard_cache_expires_at ON admin_dashboard_cache(expires_at);

CREATE INDEX IF NOT EXISTS idx_admin_notifications_target_admin_id ON admin_notifications(target_admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_is_read ON admin_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_created_at ON admin_notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_severity ON admin_notifications(severity);

CREATE INDEX IF NOT EXISTS idx_system_metrics_type_name ON system_metrics(metric_type, metric_name);
CREATE INDEX IF NOT EXISTS idx_system_metrics_recorded_at ON system_metrics(recorded_at);

-- Add composite indexes for common admin queries
CREATE INDEX IF NOT EXISTS idx_users_role_status ON users(role, status) WHERE role IS NOT NULL AND status IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_expert_applications_status_created_at ON expert_applications(status, created_at);
CREATE INDEX IF NOT EXISTS idx_support_tickets_status_priority ON support_tickets(status, priority);

-- Add partial indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_active ON users(id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_expert_profiles_active ON expert_profiles(id) WHERE status = 'ACTIVE';
CREATE INDEX IF NOT EXISTS idx_support_tickets_open ON support_tickets(id) WHERE status IN ('open', 'in_progress');