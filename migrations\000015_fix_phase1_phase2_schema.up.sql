-- Fix and complete Phase 1 and Phase 2 database schema

-- Fix sites table structure
ALTER TABLE sites DROP COLUMN IF EXISTS domain;
ALTER TABLE sites DROP COLUMN IF EXISTS owner_id;

-- Add proper sites table columns
ALTER TABLE sites ADD COLUMN IF NOT EXISTS name VARCHAR(255) NOT NULL DEFAULT 'My Website';
ALTER TABLE sites ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE sites ADD COLUMN IF NOT EXISTS user_id INT NOT NULL REFERENCES users(id) ON DELETE CASCADE DEFAULT 1;
ALTER TABLE sites ADD COLUMN IF NOT EXISTS status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived'));
ALTER TABLE sites ADD COLUMN IF NOT EXISTS custom_domain VARCHAR(255);
ALTER TABLE sites ADD COLUMN IF NOT EXISTS ssl_enabled BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE sites ADD COLUMN IF NOT EXISTS theme_config JSONB NOT NULL DEFAULT '{}';
ALTER TABLE sites ADD COLUMN IF NOT EXISTS seo_config JSONB NOT NULL DEFAULT '{}';

-- Ensure sites has tenant_id (should already exist from migration 000002)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'sites' AND column_name = 'tenant_id') THEN
        ALTER TABLE sites ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
    END IF;
END $$;

-- Add missing key column to api_keys table
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS key_value VARCHAR(255) UNIQUE;

-- Update api_keys to have proper structure
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'api_keys' AND column_name = 'tenant_id') THEN
        ALTER TABLE api_keys ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
    END IF;
END $$;

-- Ensure all component tables have tenant_id
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'web_addons' AND column_name = 'tenant_id') THEN
        ALTER TABLE web_addons ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'predefined_snippets' AND column_name = 'tenant_id') THEN
        ALTER TABLE predefined_snippets ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'component_registry' AND column_name = 'tenant_id') THEN
        ALTER TABLE component_registry ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
    END IF;
END $$;

-- Ensure addon_config table has proper structure
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'addon_config') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'addon_config' AND column_name = 'tenant_id') THEN
            ALTER TABLE addon_config ADD COLUMN tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111';
        END IF;
    END IF;
END $$;

-- Create navigation table for site navigation management
CREATE TABLE IF NOT EXISTS site_navigation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    site_id INT NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    page_id UUID REFERENCES pages(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES site_navigation(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    url VARCHAR(500),
    position INT NOT NULL DEFAULT 0,
    is_external BOOLEAN NOT NULL DEFAULT false,
    is_visible BOOLEAN NOT NULL DEFAULT true,
    tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create site_pages junction table for better site-page relationship
CREATE TABLE IF NOT EXISTS site_pages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    site_id INT NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    page_id UUID NOT NULL REFERENCES pages(id) ON DELETE CASCADE,
    is_homepage BOOLEAN NOT NULL DEFAULT false,
    position INT NOT NULL DEFAULT 0,
    tenant_id UUID REFERENCES tenants(id) NOT NULL DEFAULT '11111111-1111-1111-1111-111111111111',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(site_id, page_id)
);

-- Add missing indexes for performance
CREATE INDEX IF NOT EXISTS idx_sites_user_id ON sites(user_id);
CREATE INDEX IF NOT EXISTS idx_sites_tenant_id ON sites(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sites_status ON sites(status);
CREATE INDEX IF NOT EXISTS idx_sites_custom_domain ON sites(custom_domain);

CREATE INDEX IF NOT EXISTS idx_site_navigation_site_id ON site_navigation(site_id);
CREATE INDEX IF NOT EXISTS idx_site_navigation_parent_id ON site_navigation(parent_id);
CREATE INDEX IF NOT EXISTS idx_site_navigation_tenant_id ON site_navigation(tenant_id);

CREATE INDEX IF NOT EXISTS idx_site_pages_site_id ON site_pages(site_id);
CREATE INDEX IF NOT EXISTS idx_site_pages_page_id ON site_pages(page_id);
CREATE INDEX IF NOT EXISTS idx_site_pages_tenant_id ON site_pages(tenant_id);

CREATE INDEX IF NOT EXISTS idx_web_addons_tenant_id ON web_addons(tenant_id);
CREATE INDEX IF NOT EXISTS idx_predefined_snippets_tenant_id ON predefined_snippets(tenant_id);
CREATE INDEX IF NOT EXISTS idx_component_registry_tenant_id ON component_registry(tenant_id);

-- Update existing data to have proper relationships
-- Create default site for users who don't have one
INSERT INTO sites (name, description, user_id, tenant_id, status)
SELECT 
    'My First Website',
    'Welcome to my website built with VelocityWave',
    u.id,
    u.tenant_id,
    'draft'
FROM users u
WHERE NOT EXISTS (
    SELECT 1 FROM sites s WHERE s.user_id = u.id
)
ON CONFLICT DO NOTHING;

-- Ensure all existing pages have proper user_id and tenant_id
UPDATE pages 
SET user_id = (SELECT id FROM users LIMIT 1),
    tenant_id = '11111111-1111-1111-1111-111111111111'
WHERE user_id IS NULL OR tenant_id IS NULL;

-- Create default homepage for sites that don't have pages
INSERT INTO pages (title, slug, content, user_id, tenant_id)
SELECT 
    'Welcome to ' || s.name,
    'home',
    '<h1>Welcome to ' || s.name || '</h1><p>This is your homepage. Start building your amazing website!</p>',
    s.user_id,
    s.tenant_id
FROM sites s
WHERE NOT EXISTS (
    SELECT 1 FROM site_pages sp WHERE sp.site_id = s.id
)
ON CONFLICT DO NOTHING;

-- Link newly created pages to their sites
INSERT INTO site_pages (site_id, page_id, is_homepage, tenant_id)
SELECT DISTINCT
    s.id,
    p.id,
    true,
    s.tenant_id
FROM sites s
JOIN pages p ON p.user_id = s.user_id AND p.tenant_id = s.tenant_id
WHERE p.slug = 'home'
AND NOT EXISTS (
    SELECT 1 FROM site_pages sp WHERE sp.site_id = s.id AND sp.page_id = p.id
)
ON CONFLICT DO NOTHING;