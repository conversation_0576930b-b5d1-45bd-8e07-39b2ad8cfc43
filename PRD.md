1. Introduction
Velocity Platform is a dynamic, API-first platform designed for building and deploying multi-page websites and feature-rich user dashboards. Its core philosophy is simplicity in the platform itself, with complexity and customization driven by a powerful addon system. Crucially, the Velocity Platform's own public-facing user interface (including the website builder and dashboards) will also be constructed using this component-based system, allowing platform administrators to customize its appearance and functionality using the same tools available to end-users. Users will leverage a component-based website builder, drawing components from an Addon Builder or a library of Predefined Code Snippets, and an intuitive drag-and-drop addon builder (with client-side UI configuration via Rete.js) capable of integrating with, manipulating, and transforming data from common external API technologies or data sources. All computations, business logic, and complex data transformations will be performed solely by the platform's API/server-side components; the frontend will primarily serve as a visual UI for display and triggering server-side operations. All client-side addon logic will operate within a secure, sandboxed environment, transitioning to a production state only after thorough review and approval by platform administrators. Published websites will include a unique, securely shipped API key to enable dynamic interactions and data retrieval via HTMX calls back to the platform's API. The platform will utilize a specific, modern tech stack (Bootstrap, HTMX, Handlebars.js, Rete.js, PostgreSQL with JSONB for addon data and storage for code snippets, and Go with GraphQL hosted directly on the physical machine) to ensure efficient development and deployment.

2. Goals
Empower Users: Enable users with varying technical skills to build and manage websites and dashboards using a flexible component system.

Foster Extensibility: Provide a robust addon builder for users and third parties to create dynamic "Web Addons," alongside a curated library of "Predefined Code Snippets."

API-First Architecture: Ensure all platform operations and data transfers are handled via a secure and well-documented GraphQL API built with Go, including auto-generated endpoints (exposed as GraphQL fields/types) for addons and retrieval of code snippets. All significant computations, business logic, and complex data transformations will reside solely within the API.

Simplified Deployment: Allow websites to be compiled into static HTML/HTMX, JS and CSS for easy publishing to external hosting.

Maintain Platform Simplicity: Focus complexity within addons rather than the core platform, providing a straightforward user experience for core platform functionalities.

Secure and Compliant Data Management: Implement multi-tenancy with strict data isolation, ensuring GDPR and DPA adherence, and encrypting all data.

Enable Flexible Monetization: Provide robust mechanisms for tiered platform access (Free, Basic, Pro, Enterprise) and for third-party addons (free/paid) from day one. Resource limits will be configurable per subscription level. Platform subscriptions and addon purchases will be managed through a dedicated, platform-managed addon.

Self-Configurable Platform UI: Allow platform administrators to customize the platform's own frontend using its internal component-based building tools.

External Data Integration: Enable Addon Creators to securely integrate with, manipulate, and transform data from common external API technologies and data sources within their addons, with server-side mediation.

Secure Published Site API Access: Ensure secure and controlled API access for published websites back to the platform for dynamic content and interactions using a unique API key.

Controlled Addon Execution Environment: Implement a secure, isolated sandbox environment for addon development and testing, requiring explicit approval for promotion to production, and allowing admins to control/revert addon status for safety.

Automated Error Notification: Provide automated notifications of crashes or errors to platform administrators and relevant addon developers.

3. Target Users
Platform Owner: Responsible for overall platform management, maintenance, core feature development, curating Predefined Code Snippets, ensuring compliance, and overseeing monetization strategies.

Platform Admins: Manage user accounts, platform settings, review and approve addons, manage the library of Predefined Code Snippets, monitor platform health, configure platform tiers and addon pricing, and customize the platform's own public-facing UI. They are also responsible for the review and approval of addons transitioning from sandbox to production, and for managing addon statuses (e.g., disabling, reverting to sandbox) for safety. They will receive automated error notifications.

Platform Users (Website/Dashboard Creators): Individuals or businesses using the Velocity Platform to build, deploy, and manage their websites and internal dashboards, choosing platform tiers and acquiring addons. They will also manage the publishing of their websites, which involves API key generation.

Third-Party Developers (Addon Creators): Developers creating, publishing, and potentially distributing dynamic, free, or paid addons for the Velocity Platform, now with the ability to integrate external data sources and understanding the sandbox development environment. They will receive automated error notifications related to their specific addons.

4. System Architecture
The Velocity Platform will be built exclusively using the following components:

Platform Frontend (Public-Facing UI): This refers to the Velocity Platform's own administrative and user-facing interfaces (e.g., dashboards, the site builder interface itself). It will be built and managed using the platform's own Component-Based Website Builder, leveraging Web Addons and Predefined Code Snippets, and compiled into static HTML, HTMX, JS, and CSS (Bootstrap). This ensures the platform's UI is as flexible and customizable as any user-created website.

Webpage Creator (Frontend - Site Builder): This is the tool within the Platform Frontend that allows users (and platform admins for the platform's own UI) to visually assemble multi-page websites. It utilizes a custom frontend built with Bootstrap (HTML, CSS, JS) for the visual, component-based website building interface. Runs client-side. This builder will rely on pre-built Bootstrap components and a theming system to ensure consistency and customizability.

Platform Frontend Framework: HTMX - For dynamic and interactive user interfaces within the compiled platform frontend itself. Runs client-side.

Platform Frontend Templating: Handlebars.js - For rendering dynamic content within the HTMX-driven platform frontend. Runs client-side.

Addon Builder Interface: Rete.js - To provide the drag-and-drop interface for creating dynamic "Web Addons" and "Dashboard Addons." Runs entirely client-side. Rete.js will serve as a visual configuration tool to define server-side data processing workflows and API call sequences for addons. All computations, complex data manipulations, and business logic stemming from these definitions will be performed by the Go GraphQL API. Addon configurations will be serialized and loaded via API calls. During development and testing, addons and their configured logic will operate within a strictly sandboxed client-side environment.

Database: PostgreSQL - For all persistent data storage, including:

User accounts, site configurations, addon definitions (Rete.js configurations).

Predefined Code Snippets (HTML, CSS, JS content and metadata) for the website builder.

Tenant data.

Data specific to each addon instance will be stored as JSONB.

Monetization Data: User tier information, addon pricing details, addon purchase/subscription records.

Platform UI Configuration Data: The structure and content of the platform's own frontend (as built by admins using the Website Builder).

External Data Source Configurations: Securely stored connection details and credentials for external APIs and data sources.

Published Website API Keys: Securely generated, site-specific API keys for published websites.

Addon Deployment Status: Addon status (e.g., 'development', 'pending review', 'approved', 'rejected', 'disabled', 'sandbox') and associated environment configurations (e.g., sandbox vs. production API endpoints).

API Backbone: Go with GraphQL - This will serve as the robust backend, handling:

Data transfer for all platform operations through a single GraphQL endpoint.

All computations, complex data manipulations, and business logic.

Saving and loading addon configurations from Rete.js via GraphQL mutations.

Retrieval of Predefined Code Snippets for the website builder via GraphQL queries.

Automatic generation and management of GraphQL fields/types for each created addon, interacting with their respective JSONB data stores.

Authentication and Authorization for platform access and external published sites via GraphQL. Published websites will use a dedicated, securely generated API key for authentication when making calls back to the platform.

Interaction with the PostgreSQL database.

Ensuring data encryption for all data handled.

Monetization Logic: Enforcement of platform tier-based feature access and addon purchase/subscription entitlements based on configurable resource limits per subscription level. This logic will primarily interact with a dedicated, platform-managed payment addon.

Secure External Data Integration: The GraphQL API will serve as the secure intermediary for all external data source integrations initiated by addons. It will manage authentication credentials for external APIs, perform server-side data fetching, aggregation, and complex transformations before exposing the data via GraphQL.

Addon Environment Enforcement: The API will enforce strict access controls based on an addon's deployment status (development sandbox vs. production) and its overall active/disabled status, limiting the scope of API calls accordingly.

Deployment Environment: Directly on the physical machine (VPS), running the Go backend and serving platform frontend static assets.

Diagrammatic Flow (Conceptual - Updated):
(Diagram remains largely the same, but the flow for "Webpage Creator" now implicitly includes fetching "Predefined Code Snippets" from PostgreSQL via the Go GraphQL API, similar to how addon configurations are handled. The "Platform Frontend" itself is now shown as being built and served by the platform's own mechanisms. New arrows/nodes for "External Data Source" flow into the API Backbone, and then to Addons. A new connection from "Published Website" back to "API Backbone" is shown, labeled with "API Key".)

5. Key Features
5.1. Component-Based Website Builder
Functionality:

Users (and platform admins for the platform's own UI) can create multi-page websites.

Pages can be visually assembled by dragging and dropping components. These components will be styled using Bootstrap's classes and JavaScript components and can be:

Dynamic "Web Addons": Created via the Addon Builder, potentially with configurable settings, their own data logic, and exposed data via auto-generated GraphQL types/fields.

"Predefined Code Snippets": A library of ready-to-use HTML, CSS (using Bootstrap classes), and JavaScript blocks (e.g., stylized buttons, content sections, simple interactive elements) stored in the platform's database and managed by Platform Admins/Owners. These are generally more static in nature or offer simpler client-side interactions. Bootstrap Icons will be available for use within these snippets.

The Website Builder interface will provide a way to browse and select from both Web Addons and Predefined Code Snippets, leveraging Bootstrap's visual components for display.

Intuitive interface for managing pages, page hierarchy, and navigation, also styled with Bootstrap.

Bootstrap Theming Integration: The Website Builder will include tools (e.g., a color picker) to update Bootstrap's default theme colors. Changes will be saved to a dedicated CSS theme file which will be shipped with all compiled websites, allowing users to customize the look and feel of Bootstrap components and predefined snippets.

Ability to link between pages within the same website.

Web Addons can display static content or connect to dynamic data sources by querying the platform's GraphQL API, which can in turn fetch data from external sources. Predefined Code Snippets will primarily render their stored content, with their JavaScript strictly for UI interactions and initiating HTMX requests (no business logic or data transformation), and will inherit the website's defined Bootstrap theme/colors.

Site structure and component configurations saved via the Go GraphQL API.

Output:

Websites must be compilable into pure HTML, HTMX, JavaScript, and CSS (primarily Bootstrap).

The compiled output should be deployable to any standard external web hosting.

5.2. Dashboards
Functionality: Owner, Admin, and Platform User Dashboards will provide their respective functionalities.

Interface: These dashboards will be constructed using the platform's own Component-Based Website Builder, leveraging Web Addons and Predefined Code Snippets, styled with Bootstrap and using Bootstrap Icons. All underlying data computations and business logic will be handled by the Go GraphQL API, with the frontend primarily displaying results and triggering API calls.

5.3. Addon Builder
Functionality:

Drag-and-drop interface (client-side Rete.js) for creating dynamic "Web Addons" and "Dashboard Addons". These are distinct from the centrally managed "Predefined Code Snippets."

Addon configurations serialized and saved/loaded via Go GraphQL API mutations.

Automatic GraphQL Type and Field Generation for these addons, allowing querying and mutation of their data.

Addons include HTML, CSS (Bootstrap classes), and client-side JavaScript (strictly for UI interactions and initiating HTMX requests), and can define data requirements. Bootstrap Icons can be included in addon designs.

Metadata managed via Go GraphQL API, including options to mark addons as Free or Paid and set pricing.

Addon Review and Lifecycle:

Development Sandbox: During creation and initial testing, addons will run within a highly restrictive client-side sandbox environment. In this phase, direct external API calls from the client-side may be proxied through controlled platform endpoints, and access to the platform's GraphQL API will be limited to a development scope. The client-side JavaScript of addons within the sandbox will be strictly limited to UI interactions and triggering API calls; no complex computations or data manipulations will be performed client-side.

Addon Review by Platform Admin: Once an addon developer believes their addon is ready, they can submit it for review by Platform Admins.

Production Deployment: Upon successful review and approval by Platform Owners/Admins, the addon will be promoted to production. In production, the addon will have access to its full intended capabilities, including mediated external API calls via the Go backend, and will be made available to platform users based on their subscription level and if they have purchased it.

Admin Control for Safety: Platform Admins will have the ability to immediately disable ("turn off") an addon in production, rendering it inactive on all websites. They can also revert an addon back to its sandbox state for further investigation or remediation if safety concerns arise.

External Data Integration within Addons:

Ability to define and configure connections to common external API technologies (e.g., generic REST/HTTP endpoints, GraphQL endpoints, webhook definitions, or basic authenticated services). This involves configuring API endpoints, authentication methods (e.g., API keys, OAuth tokens – managed securely by the backend).

Visual tools within Rete.js for defining server-side data manipulation and transformation workflows (e.g., mapping fields, applying filters, reformatting data). These definitions will be executed exclusively by the Go GraphQL API backend.

Ability to define GraphQL mutations to push transformed data to the platform's database (JSONB) or to other external APIs (mediated via backend proxy). Addons are intended to create "micro-apps" capable of varied tasks such as collecting customer data via forms, interfacing with onsite machinery (via API mediation), or initiating product delivery after payment processing, with no artificial restrictions on their conceptual application.

Interface: Rete.js (client-side) for visual building; HTMX/Handlebars.js for overall UI and API interaction, all styled with Bootstrap.

5.4. API First Backbone (Go with GraphQL)
Functionality: Central Go API handles all platform interactions exclusively via a single GraphQL endpoint. This includes:

Serving and managing dynamic "Web Addons" and "Dashboard Addons" (configurations, auto-generated GraphQL types/fields, JSONB data).

Listing and retrieving "Predefined Code Snippets" for use in the Website Builder through GraphQL queries.

All data for published sites accessed via GraphQL.

All data transfer is encrypted.

All computations, complex data manipulations, and business logic, including executing data transformation logic defined by Rete.js.

Authentication & Authorization: Centralized (e.g., JWT-based authentication) integrated into the GraphQL context, with API key/token system for tenant data access. Published websites will authenticate using a unique, site-specific API key sent in request headers. This system will also enforce platform tier-based feature access and addon purchase/subscription entitlements based on configurable resource limits per subscription level. The API will enforce strict access controls based on an addon's deployment status (development sandbox vs. production) and its overall active/disabled status, limiting the scope of API calls accordingly.

Secure External Data Integration: The GraphQL API will serve as the secure intermediary for all external data source integrations initiated by addons. It will store and manage sensitive credentials, execute external API calls on the server-side, and perform server-side data aggregation and complex transformations before delivering to the client.

Technology: Go with a robust GraphQL server implementation.

5.5. Data Management & Tenancy
Database: PostgreSQL. Will store:

Core platform data (users, sites, addon definitions).

A dedicated table or structure for "Predefined Code Snippets" (storing their HTML, CSS, JS, name, description, category, etc.).

Tenant data.

Data specific to each addon instance will be stored as JSONB.

Monetization Data: User tier information, addon pricing details, addon purchase/subscription records, payment gateway transaction IDs (if applicable).

Platform UI Configuration Data: The structured data representing the platform's own frontend layout, components, and content, managed through the Website Builder.

External Data Source Configurations: Securely stored connection details, credentials (e.g., API keys, OAuth tokens), and schema/endpoint configurations for external APIs and data sources used by addons.

Published Website API Keys: Securely generated, site-specific API keys for published websites, linked to tenant and site IDs.

Addon Deployment Status: Addon status (e.g., 'development', 'pending review', 'approved', 'rejected', 'disabled', 'sandbox') and associated environment configurations (e.g., sandbox vs. production API endpoints).

Data Access: Exclusively via Go GraphQL API.

Data Security: All data encrypted (at rest and in transit). Adherence to GDPR and DPA.

Data Structure: Supports platform data, user data, addon definitions (Rete.js configs), Predefined Code Snippets, and tenant-specific website data (including JSONB for addons).

5.6. Website Publishing
Compilation: Websites (composed of Web Addons and Predefined Code Snippets) compile to pure HTML, HTMX, JavaScript, and CSS (Bootstrap).

The compiled output should be deployable to any standard external web hosting.

API Key Generation and Inclusion: During the publishing process, a unique API key will be securely generated for the published website. This key will be embedded within the compiled website files (e.g., as a client-side configuration variable or within HTMX script tags) to enable authenticated HTMX calls back to the platform's GraphQL API for dynamic content and interactions.

Dynamic Data: Powered by HTMX calling platform Go GraphQL API for data retrieval and submission, including data sourced from external APIs. Predefined snippets will have their JavaScript strictly limited to UI interactions and initiating HTMX requests.

5.7. Monetization & Tier Management
Functionality:

Platform Tiers: Define and manage different platform tiers (Free, Basic, Pro, Enterprise) with associated feature sets and configurable resource limits (e.g., number of sites, storage, bandwidth limits if applicable). This management will be available via the Admin Dashboard.

Addon Pricing: Enable third-party developers to designate their addons as "Free" or "Paid" and set pricing (one-time or recurring, if applicable). Addon pricing details will be managed through the Addon Builder and Admin Dashboard.

Subscription Management (Platform) via Addon: Platform subscriptions and addon purchases will be handled by a dedicated, platform-managed addon (e.g., a "Stripe Addon"). This addon, built using the platform's own Addon Builder, will contain the necessary server-side logic (executed by the Go API) and client-side UI components to manage payment gateway interactions (e.g., Stripe, PayPal) for recurring subscriptions and one-time addon purchases. The platform's core system will manage user entitlements (which tiers/addons they have access to) based on the successful transactions reported by this payment addon.

Addon Purchase/Activation (Client): Ability for platform users to browse, subscribe to, or purchase paid addons from an addon listing, interacting with the payment addon's UI and logic.

Access Control by Tier: Implement logic in the Go backend (via GraphQL resolvers and business logic) to strictly enforce feature and resource limits based on a user's active platform tier.

Addon Access by Purchase: Implement logic in the Go backend to verify a user's entitlement to use paid addons based on their purchase/subscription records.

Interface: Admin dashboard for tier/pricing configuration; User dashboard for subscription/addon management, primarily driven by the payment addon's UI.

6. Guiding Principles
Simplicity: Core platform UX is straightforward; complexity is in addons.

API-First: Central Go GraphQL API with auto-generated addon types/fields and snippet retrieval. All computations and business logic reside in the API, with the frontend as a pure UI layer.

Strict Technology Adherence: Bootstrap (including Bootstrap Icons), HTMX, Handlebars.js, Rete.js (client-side), PostgreSQL (with JSONB & snippet storage), Go with GraphQL running directly on a physical machine (VPS). No exceptions.

Clear Data Separation & Security: Multi-tenancy, API-gated access, full encryption, GDPR/DPA compliance.

Flexible Monetization: Provide robust mechanisms for tiered platform access and paid third-party addons from the outset, with payment processing managed by a dedicated platform-level addon.

Self-Configurable Platform UI: The platform's own frontend will be built and managed using its internal component-based system.

External Data Integration: Provide secure and flexible mechanisms for addons to connect to and transform data from external API sources, with server-side mediation for sensitive operations.

Published Site Security: Ensure published websites can securely interact with the platform's API via dedicated, managed API keys.

Controlled Addon Execution: Implement a robust sandbox-to-production lifecycle for addon execution, ensuring security and quality through strict client-side sandboxing and backend controls.

Comprehensive Logging & Analytics: Capture all significant platform actions and errors for detailed monitoring, auditing, and automated notifications.

7. Success Metrics / Key Performance Indicators (KPIs)
Number of active platform users.

Number of created and deployed websites/dashboards.

Number of unique addons created by third-party developers.

Platform uptime and performance metrics.

GraphQL query performance (response times, error rates) and usage patterns.

Number of paid platform subscriptions.

Number of paid addon purchases/subscriptions.

Revenue generated from platform subscriptions and addon sales.

Detailed platform usage metrics (e.g., most active sites, popular features, common user flows, frequently used addons/snippets, resource consumption per user/tier).

8. Release Criteria (for MVP/V1)
The full functionality must be available, comprising:

Core platform infrastructure.

Functional component-based Website Builder supporting both dynamic "Web Addons" from the Addon Builder and "Predefined Code Snippets" from the database, all styled with Bootstrap and including theming capabilities.

Ability to compile and publish a completed multi-page website to external hosting, including secure API key generation and embedding for HTMX calls.

Functional Addon Builder for dynamic Web Addons and Dashboard Addons, including the ability to configure server-side external API data ingestion and transformation. The addon development and review process, including the client-side sandbox environment, backend enforcement, and production promotion workflow, must be functional. Admin controls to disable and revert addons to sandbox must be in place.

Admin dashboard with addon review and Predefined Code Snippet management capabilities.

User, Owner, and Admin dashboards. The platform's own public-facing UI and dashboards must be configurable via the Website Builder.

Comprehensive GraphQL schema and resolvers for all platform operations, addon data, and snippet retrieval.

Implementation of security requirements, including external API credential management and client-side sandboxing.

Basic monetization capabilities (platform tiers, configurable resource limits, free/paid addons) must be functional, with a core payment addon (e.g., Stripe Addon) integrated and managing platform subscriptions and addon purchases.

Automated testing (unit, integration) implemented where possible for the Go backend and GraphQL API, ensuring core functionality and data integrity. Manual testing plans will be prepared and executed by testers for end-to-end functionality and user experience.

Basic automated notification system for crashes and errors, alerting platform admins and addon developers.

9. Security
Compliance: GDPR and DPA.

Data Encryption: All data at rest and in transit.

Addon Security & Sandboxing Environment: Human review for addons. Client-side sandboxing (e.g., iframes with sandbox attributes, strict CSP, and potentially Web Workers where appropriate for heavy client-side tasks) will be implemented for all user-contributed JavaScript within addons and snippets to mitigate malicious code execution and ensure isolation. Crucially, the client-side JavaScript will be restricted to UI interactions and triggering API calls; no business logic, complex computations, or data transformations will occur client-side. During development and testing, addons will execute within a highly restrictive sandbox environment that limits access to host page resources, restricts network calls (proxying them through controlled backend endpoints), and prevents potentially harmful operations. Upon approval by platform owners/admins, addons will be promoted to a production environment where they gain the necessary permissions for their intended functionality, while still operating within strict client-side sandboxing to protect the host environment.

Input Validation & Output Encoding: Standard practices to prevent common web vulnerabilities (e.g., XSS, SQL injection). This includes robust input validation within GraphQL resolvers and for data ingested from external APIs.

Authentication & Authorization: Secure platform and API access (e.g., JWT for GraphQL authentication, robust user authentication). Authorization logic applied at the GraphQL resolver level, enforcing tier-based feature access and addon entitlement.

Payment Processing Security: Sensitive payment data will be handled directly by the chosen payment gateway (e.g., Stripe) via the dedicated payment addon's server-side logic, minimizing direct platform exposure. The platform will securely store only transaction IDs and subscription statuses. The payment addon itself will adhere to PCI DSS compliance standards for any data it processes, and its integration with the gateway will be secure.

External API Credentials Management: Secure storage and retrieval of API keys, tokens, and other sensitive credentials for external data sources within the Go backend. These credentials must never be exposed client-side. The Go backend will handle all server-to-server calls to external APIs.

Published Website API Keys: Implement robust generation, storage, and validation mechanisms for unique, site-specific API keys. For V1, keys will be generated upon publication and securely embedded within the compiled static files. This will primarily involve embedding the key as a configuration variable accessible by client-side HTMX. Initial revocation will be a manual process managed by platform admins via the backend. Mechanisms for API key rotation and automated revocation for compromised sites will be considered for future phases, along with rate-limiting and abuse prevention measures.

Data Flow Validation: Rigorous validation of data ingested from external APIs and data transformed client-side before it is stored or further processed by the platform's backend.

Dependency Management: Regular updates and vulnerability scanning of all third-party libraries and components.

10. Deployment and Operations
Platform Hosting: The Go backend will be deployed directly as an executable on the physical machine, which will be a Virtual Private Server (VPS). The frontend static assets will also be served from this machine. Scalability will be a focus after V1.

Database Management: PostgreSQL procedures for backups, replication, and performance tuning. The current setup involves daily snapshots of the server and 6-hourly database backups. For MVP, this provides a Recovery Point Objective (RPO) of 6 hours and a Recovery Time Objective (RTO) dependent on manual restoration from backups. Data resilience will include basic monitoring for backup job completion and success, and backup verification processes. As the database moves to a separate server (future phase), a more robust, automated disaster recovery plan will be developed, potentially including continuous archiving (WAL shipping) for near-zero RPO.

Monitoring: Comprehensive logging and monitoring (e.g., Prometheus, Grafana, or suitable system-level monitoring tools) for API performance, error rates, resource utilization, and detailed platform usage analytics. All actions and errors must be logged comprehensively (e.g., clicks, logins, page loads, API calls, form submissions, data transformations). Each log entry will include a meaningful description, precise date and time, the user's IP address, and User ID (if logged in) to enable thorough issue tracking and auditing. GraphQL-specific monitoring for query complexity, performance, and field usage will be implemented to inform future schema evolution.

Automated Testing Integration: CI/CD (even if manual deployment) will include automated tests for API endpoints and critical business logic.

Addon Lifecycle Management: Implementation of processes and tooling to facilitate the secure transition of addons from development/sandbox environments to production, tied to the approval workflow. This includes controls for disabling and reverting addons.

Automated Error Notifications: An integrated system will detect crashes and errors from logs/monitoring and automatically send notifications (e.g., via email, Slack) to platform administrators and the specific developers responsible for the affected addon/micro-app.

11. Future Considerations / Roadmap (Beyond V1)
AI-driven analytics and content generation for website and dashboard components.

Advanced addon marketplace features, including more sophisticated monetization models, reviews, and versioning.

Versioning for websites, addons, and snippets (automated migration).

Platform-level analytics for site owners to track their website performance.

Integrated CDN for optimized asset delivery.

GraphQL Subscriptions for real-time updates.

Full internationalization/localization of the platform UI (for future UK business expansion).

Automated CI/CD pipelines for user-created sites.

Enhanced API key management features for published sites (e.g., user-initiated key rotation, detailed usage dashboards).

Dedicated database server for improved scalability and resilience.

12. Non-Goals (Explicitly Out of Scope for V1)
Direct server-side code execution in published addons/snippets.

Integrated hosting solution for user-created websites (beyond serving the platform's own compiled frontend).

Complex e-commerce backends or payment gateways within the core platform (focus on robust third-party integration via addons).

Granular roles and permissions management within individual published sites.