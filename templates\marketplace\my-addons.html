<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Addons - Velocity Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/bootstrap-theme.css" rel="stylesheet">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-lightning-charge"></i>
                VelocityWave
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/marketplace">
                    <i class="bi bi-shop"></i> Browse Marketplace
                </a>
                <a class="nav-link" href="/builder/addon-builder">
                    <i class="bi bi-plus-circle"></i> Create Addon
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>My Addons</h2>
                <p class="text-muted">Manage your installed addons and wishlist</p>
            </div>
            <div>
                <button class="btn btn-outline-secondary me-2" onclick="checkForUpdates()">
                    <i class="bi bi-arrow-clockwise"></i> Check Updates
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#settingsModal">
                    <i class="bi bi-gear"></i> Settings
                </button>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-primary" id="installed-count">0</h4>
                        <p class="mb-0">Installed</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-warning" id="updates-count">0</h4>
                        <p class="mb-0">Updates Available</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-info" id="wishlist-count">0</h4>
                        <p class="mb-0">Wishlist</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-success" id="active-count">0</h4>
                        <p class="mb-0">Active</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="myAddonsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="installed-tab" data-bs-toggle="tab" data-bs-target="#installed" type="button">
                    <i class="bi bi-collection"></i> Installed Addons
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="wishlist-tab" data-bs-toggle="tab" data-bs-target="#wishlist" type="button">
                    <i class="bi bi-heart"></i> Wishlist
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="updates-tab" data-bs-toggle="tab" data-bs-target="#updates" type="button">
                    <i class="bi bi-arrow-up-circle"></i> Updates
                </button>
            </li>
        </ul>

        <div class="tab-content mt-4" id="myAddonsTabsContent">
            <!-- Installed Addons Tab -->
            <div class="tab-pane fade show active" id="installed" role="tabpanel">
                <!-- Filter and Sort -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="search-installed" placeholder="Search installed addons...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filter-category">
                            <option value="">All Categories</option>
                            <option value="productivity">Productivity</option>
                            <option value="communication">Communication</option>
                            <option value="analytics">Analytics</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="sort-installed">
                            <option value="name">Sort by Name</option>
                            <option value="last_used">Last Used</option>
                            <option value="install_date">Install Date</option>
                            <option value="usage">Most Used</option>
                        </select>
                    </div>
                </div>

                <!-- Installed Addons List -->
                <div id="installed-addons-list">
                    <!-- Addons will be populated here -->
                </div>
            </div>

            <!-- Wishlist Tab -->
            <div class="tab-pane fade" id="wishlist" role="tabpanel">
                <div id="wishlist-addons-list">
                    <!-- Wishlist addons will be populated here -->
                </div>
            </div>

            <!-- Updates Tab -->
            <div class="tab-pane fade" id="updates" role="tabpanel">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Available Updates</h5>
                    <button class="btn btn-primary" onclick="updateAllAddons()">
                        <i class="bi bi-download"></i> Update All
                    </button>
                </div>
                <div id="updates-list">
                    <!-- Updates will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Addon Settings</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="settings-form">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="auto-update" checked>
                                <label class="form-check-label" for="auto-update">
                                    Auto-update addons
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="notifications" checked>
                                <label class="form-check-label" for="notifications">
                                    Enable notifications
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="usage-tracking" checked>
                                <label class="form-check-label" for="usage-tracking">
                                    Usage tracking
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="show-beta">
                                <label class="form-check-label" for="show-beta">
                                    Show beta versions
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="group-by-category" checked>
                                <label class="form-check-label" for="group-by-category">
                                    Group by category
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveSettings()">Save Settings</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let userAddons = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadUserAddons();
        });

        function loadUserAddons() {
            fetch('/api/my-addons', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            })
            .then(response => response.json())
            .then(data => {
                userAddons = data;
                updateStats();
                displayInstalledAddons();
                displayWishlistAddons();
                displayUpdates();
            })
            .catch(error => {
                console.error('Error loading user addons:', error);
            });
        }

        function updateStats() {
            if (!userAddons) return;

            document.getElementById('installed-count').textContent = userAddons.installedAddons.length;
            document.getElementById('wishlist-count').textContent = userAddons.wishlistAddons.length;
            
            const updatesAvailable = userAddons.installedAddons.filter(addon => addon.updateAvailable).length;
            document.getElementById('updates-count').textContent = updatesAvailable;
            
            const activeAddons = userAddons.installedAddons.filter(addon => addon.installation.status === 'active').length;
            document.getElementById('active-count').textContent = activeAddons;
        }

        function displayInstalledAddons() {
            const container = document.getElementById('installed-addons-list');
            container.innerHTML = '';

            if (!userAddons || userAddons.installedAddons.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-collection display-1 text-muted"></i>
                        <h4 class="mt-3">No addons installed</h4>
                        <p class="text-muted">Browse the marketplace to find addons for your needs</p>
                        <a href="/marketplace" class="btn btn-primary">Browse Marketplace</a>
                    </div>
                `;
                return;
            }

            userAddons.installedAddons.forEach(userAddon => {
                const addonCard = createInstalledAddonCard(userAddon);
                container.appendChild(addonCard);
            });
        }

        function createInstalledAddonCard(userAddon) {
            const card = document.createElement('div');
            card.className = 'card mb-3';
            
            const statusBadge = getStatusBadge(userAddon.installation.status);
            const updateBadge = userAddon.updateAvailable ? 
                '<span class="badge bg-warning">Update Available</span>' : '';

            card.innerHTML = `
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-start">
                                <img src="${userAddon.addonInfo.icon || '/static/images/default-addon-icon.svg'}" 
                                     alt="${userAddon.addonInfo.name}" class="me-3" style="width: 48px; height: 48px;">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">${userAddon.addonInfo.name}</h6>
                                    <p class="text-muted small mb-2">${userAddon.addonInfo.shortDescription || ''}</p>
                                    <div class="d-flex align-items-center">
                                        ${statusBadge}
                                        ${updateBadge}
                                        <span class="badge bg-light text-dark ms-2">v${userAddon.installation.version}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary" onclick="configureAddon('${userAddon.installation.id}')">
                                    <i class="bi bi-gear"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="viewAddonDetails('${userAddon.addonInfo.id}')">
                                    <i class="bi bi-info-circle"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="uninstallAddon('${userAddon.installation.id}')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return card;
        }

        function getStatusBadge(status) {
            const statusMap = {
                'active': '<span class="badge bg-success">Active</span>',
                'inactive': '<span class="badge bg-secondary">Inactive</span>',
                'updating': '<span class="badge bg-primary">Updating</span>',
                'failed': '<span class="badge bg-danger">Failed</span>',
                'suspended': '<span class="badge bg-warning">Suspended</span>'
            };
            return statusMap[status] || '<span class="badge bg-secondary">Unknown</span>';
        }

        function displayWishlistAddons() {
            const container = document.getElementById('wishlist-addons-list');
            container.innerHTML = '';

            if (!userAddons || userAddons.wishlistAddons.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-heart display-1 text-muted"></i>
                        <h4 class="mt-3">Your wishlist is empty</h4>
                        <p class="text-muted">Add addons to your wishlist while browsing</p>
                    </div>
                `;
                return;
            }

            // Load wishlist addon details and display
            userAddons.wishlistAddons.forEach(addonId => {
                // Fetch addon details and create card
                // This would typically be done server-side
            });
        }

        function displayUpdates() {
            const container = document.getElementById('updates-list');
            container.innerHTML = '';

            if (!userAddons) return;

            const updatesAvailable = userAddons.installedAddons.filter(addon => addon.updateAvailable);

            if (updatesAvailable.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-check-circle display-1 text-success"></i>
                        <h4 class="mt-3">All addons are up to date</h4>
                        <p class="text-muted">Your addons are running the latest versions</p>
                    </div>
                `;
                return;
            }

            updatesAvailable.forEach(userAddon => {
                const updateCard = createUpdateCard(userAddon);
                container.appendChild(updateCard);
            });
        }

        function createUpdateCard(userAddon) {
            const card = document.createElement('div');
            card.className = 'card mb-3';
            
            card.innerHTML = `
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-start">
                                <img src="${userAddon.addonInfo.icon || '/static/images/default-addon-icon.svg'}" 
                                     alt="${userAddon.addonInfo.name}" class="me-3" style="width: 48px; height: 48px;">
                                <div>
                                    <h6 class="mb-1">${userAddon.addonInfo.name}</h6>
                                    <p class="text-muted small mb-1">
                                        Update from v${userAddon.installation.version} to v${userAddon.latestVersion}
                                    </p>
                                    <span class="badge bg-warning">Update Available</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-primary" onclick="updateAddon('${userAddon.installation.id}', '${userAddon.latestVersion}')">
                                <i class="bi bi-download"></i> Update
                            </button>
                        </div>
                    </div>
                </div>
            `;

            return card;
        }

        // Action functions
        function configureAddon(installationId) {
            // Open addon configuration
            console.log('Configure addon:', installationId);
        }

        function viewAddonDetails(addonId) {
            window.location.href = `/marketplace/addon/${addonId}`;
        }

        function uninstallAddon(installationId) {
            if (confirm('Are you sure you want to uninstall this addon?')) {
                fetch(`/api/my-addons/uninstall/${installationId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadUserAddons(); // Refresh
                    } else {
                        alert('Failed to uninstall addon');
                    }
                });
            }
        }

        function updateAddon(installationId, version) {
            fetch(`/api/my-addons/update/${installationId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({ version })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadUserAddons(); // Refresh
                } else {
                    alert('Failed to update addon');
                }
            });
        }

        function updateAllAddons() {
            if (confirm('Update all addons to their latest versions?')) {
                // Implementation for bulk update
                console.log('Updating all addons');
            }
        }

        function checkForUpdates() {
            loadUserAddons(); // Refresh data
        }

        function saveSettings() {
            const settings = {
                autoUpdate: document.getElementById('auto-update').checked,
                notifications: document.getElementById('notifications').checked,
                usageTracking: document.getElementById('usage-tracking').checked,
                showBeta: document.getElementById('show-beta').checked,
                groupByCategory: document.getElementById('group-by-category').checked
            };

            fetch('/api/my-addons/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    bootstrap.Modal.getInstance(document.getElementById('settingsModal')).hide();
                } else {
                    alert('Failed to save settings');
                }
            });
        }
    </script>
</body>
</html>