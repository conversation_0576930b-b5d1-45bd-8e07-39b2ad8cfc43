package api

import (
	"context"
	"errors"
	"io"
	"net/http"
	"time"

	"github.com/redis/go-redis/v9"
	"golang.org/x/time/rate"
)

type Gateway struct {
	client      *http.Client
	redis       *redis.Client
	rateLimiter *rate.Limiter
}

func NewGateway(redisAddr string) *Gateway {
	return &Gateway{
		client: &http.Client{Timeout: 30 * time.Second},
		redis: redis.NewClient(&redis.Options{
			Addr:     redisAddr,
			Password: "",
			DB:       0,
		}),
		rateLimiter: rate.NewLimiter(rate.Every(time.Minute), 100),
	}
}

func (g *Gateway) CallAPI(ctx context.Context, url string, authHeader string, body io.Reader) (*http.Response, error) {
	if !g.rateLimiter.Allow() {
		return nil, errors.New("rate limit exceeded")
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, body)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", authHeader)
	req.Header.Set("Content-Type", "application/json")

	return g.client.Do(req)
}

func (g *Gateway) CacheResponse(ctx context.Context, key string, value []byte, ttl time.Duration) error {
	return g.redis.Set(ctx, key, value, ttl).Err()
}

func (g *Gateway) GetCachedResponse(ctx context.Context, key string) ([]byte, error) {
	return g.redis.Get(ctx, key).Bytes()
}
