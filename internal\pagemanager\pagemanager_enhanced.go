package pagemanager

import (
	"context"
	"fmt"
	"strings"
	"time"

	"goVwPlatformAPI/internal/database"
)

type EnhancedPageManager struct {
	db *database.DB
}

type PageWithContent struct {
	ID        string    `json:"id"`
	Title     string    `json:"title"`
	Slug      string    `json:"slug"`
	Content   string    `json:"content"`
	ParentID  *string   `json:"parent_id"`
	Position  int       `json:"position"`
	IsHidden  bool      `json:"is_hidden"`
	UserID    int       `json:"user_id"`
	TenantID  string    `json:"tenant_id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Enhanced fields
	MetaTitle       string                 `json:"meta_title"`
	MetaDescription string                 `json:"meta_description"`
	MetaKeywords    string                 `json:"meta_keywords"`
	CustomCSS       string                 `json:"custom_css"`
	CustomJS        string                 `json:"custom_js"`
	FeaturedImage   string                 `json:"featured_image"`
	Status          string                 `json:"status"`
	PublishedAt     *time.Time             `json:"published_at"`
	Template        string                 `json:"template"`
	Settings        map[string]interface{} `json:"settings"`

	// Content versioning
	ContentVersion int  `json:"content_version"`
	IsPublished    bool `json:"is_published"`

	// Relationships
	Children []*PageWithContent `json:"children,omitempty"`
	Parent   *PageWithContent   `json:"parent,omitempty"`
}

type CreatePageInput struct {
	Title           string                 `json:"title"`
	Slug            string                 `json:"slug"`
	Content         string                 `json:"content"`
	ParentID        *string                `json:"parent_id"`
	IsHidden        bool                   `json:"is_hidden"`
	MetaTitle       string                 `json:"meta_title"`
	MetaDescription string                 `json:"meta_description"`
	MetaKeywords    string                 `json:"meta_keywords"`
	CustomCSS       string                 `json:"custom_css"`
	CustomJS        string                 `json:"custom_js"`
	FeaturedImage   string                 `json:"featured_image"`
	Template        string                 `json:"template"`
	Settings        map[string]interface{} `json:"settings"`
}

type UpdatePageInput struct {
	Title           *string                 `json:"title"`
	Slug            *string                 `json:"slug"`
	Content         *string                 `json:"content"`
	ParentID        *string                 `json:"parent_id"`
	Position        *int                    `json:"position"`
	IsHidden        *bool                   `json:"is_hidden"`
	MetaTitle       *string                 `json:"meta_title"`
	MetaDescription *string                 `json:"meta_description"`
	MetaKeywords    *string                 `json:"meta_keywords"`
	CustomCSS       *string                 `json:"custom_css"`
	CustomJS        *string                 `json:"custom_js"`
	FeaturedImage   *string                 `json:"featured_image"`
	Status          *string                 `json:"status"`
	Template        *string                 `json:"template"`
	Settings        *map[string]interface{} `json:"settings"`
}

func NewEnhancedPageManager(db *database.DB) *EnhancedPageManager {
	return &EnhancedPageManager{db: db}
}

// CreatePage creates a new page with enhanced features
func (pm *EnhancedPageManager) CreatePage(ctx context.Context, siteID int, userID int, tenantID string, input *CreatePageInput) (*PageWithContent, error) {
	// Start transaction
	tx, err := pm.db.Pool.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Generate slug if not provided
	slug := input.Slug
	if slug == "" {
		slug = pm.generateSlug(input.Title)
	}

	// Ensure slug is unique within the site
	slug, err = pm.ensureUniqueSlug(ctx, slug, siteID, tenantID, "")
	if err != nil {
		return nil, fmt.Errorf("failed to ensure unique slug: %w", err)
	}

	// Set default values
	template := input.Template
	if template == "" {
		template = "default"
	}

	settings := input.Settings
	if settings == nil {
		settings = map[string]interface{}{
			"layout":   "default",
			"sidebar":  true,
			"comments": false,
		}
	}

	metaTitle := input.MetaTitle
	if metaTitle == "" {
		metaTitle = input.Title
	}

	// Get next position for the parent
	position, err := pm.getNextPosition(ctx, input.ParentID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get next position: %w", err)
	}

	// Create the page
	var pageID string
	pageQuery := `
		INSERT INTO pages (title, slug, content, parent_id, position, is_hidden, user_id, tenant_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
		RETURNING id
	`

	err = tx.QueryRow(ctx, pageQuery, input.Title, slug, input.Content, input.ParentID,
		position, input.IsHidden, userID, tenantID).Scan(&pageID)
	if err != nil {
		return nil, fmt.Errorf("failed to create page: %w", err)
	}

	// Create page content entry
	contentQuery := `
		INSERT INTO page_content (page_id, content, version, is_published, created_by, created_at, updated_at)
		VALUES ($1, $2, 1, false, $3, NOW(), NOW())
	`

	contentData := map[string]interface{}{
		"html":          input.Content,
		"meta_title":    metaTitle,
		"meta_desc":     input.MetaDescription,
		"meta_keywords": input.MetaKeywords,
		"custom_css":    input.CustomCSS,
		"custom_js":     input.CustomJS,
		"featured_img":  input.FeaturedImage,
		"template":      template,
		"settings":      settings,
	}

	_, err = tx.Exec(ctx, contentQuery, pageID, contentData, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to create page content: %w", err)
	}

	// Link page to site
	_, err = tx.Exec(ctx, `
		INSERT INTO site_pages (site_id, page_id, is_homepage, position, tenant_id, created_at)
		VALUES ($1, $2, false, $3, $4, NOW())
	`, siteID, pageID, position, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to link page to site: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(ctx); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return the created page
	return pm.GetPageByID(ctx, pageID, userID, tenantID)
}

// GetPageByID retrieves a page with all its content and metadata
func (pm *EnhancedPageManager) GetPageByID(ctx context.Context, pageID string, userID int, tenantID string) (*PageWithContent, error) {
	query := `
		SELECT p.id, p.title, p.slug, p.content, p.parent_id, p.position, p.is_hidden,
		       p.user_id, p.tenant_id, p.created_at, p.updated_at,
		       COALESCE(pc.content->>'meta_title', p.title) as meta_title,
		       COALESCE(pc.content->>'meta_desc', '') as meta_description,
		       COALESCE(pc.content->>'meta_keywords', '') as meta_keywords,
		       COALESCE(pc.content->>'custom_css', '') as custom_css,
		       COALESCE(pc.content->>'custom_js', '') as custom_js,
		       COALESCE(pc.content->>'featured_img', '') as featured_image,
		       COALESCE(pc.content->>'template', 'default') as template,
		       COALESCE(pc.content->'settings', '{}') as settings,
		       COALESCE(pc.version, 1) as content_version,
		       COALESCE(pc.is_published, false) as is_published,
		       'draft' as status
		FROM pages p
		LEFT JOIN page_content pc ON p.id = pc.page_id
		WHERE p.id = $1 AND p.user_id = $2 AND p.tenant_id = $3
		ORDER BY pc.version DESC
		LIMIT 1
	`

	var page PageWithContent
	var settingsJSON interface{}

	err := pm.db.Pool.QueryRow(ctx, query, pageID, userID, tenantID).Scan(
		&page.ID, &page.Title, &page.Slug, &page.Content, &page.ParentID, &page.Position,
		&page.IsHidden, &page.UserID, &page.TenantID, &page.CreatedAt, &page.UpdatedAt,
		&page.MetaTitle, &page.MetaDescription, &page.MetaKeywords, &page.CustomCSS,
		&page.CustomJS, &page.FeaturedImage, &page.Template, &settingsJSON,
		&page.ContentVersion, &page.IsPublished, &page.Status,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get page: %w", err)
	}

	// Convert settings JSON to map
	if settingsMap, ok := settingsJSON.(map[string]interface{}); ok {
		page.Settings = settingsMap
	} else {
		page.Settings = make(map[string]interface{})
	}

	return &page, nil
}

// UpdatePage updates an existing page
func (pm *EnhancedPageManager) UpdatePage(ctx context.Context, pageID string, userID int, tenantID string, input *UpdatePageInput) (*PageWithContent, error) {
	// Start transaction
	tx, err := pm.db.Pool.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Build dynamic update query for pages table
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if input.Title != nil {
		setParts = append(setParts, fmt.Sprintf("title = $%d", argIndex))
		args = append(args, *input.Title)
		argIndex++
	}

	if input.Slug != nil {
		// Ensure slug is unique
		slug, err := pm.ensureUniqueSlug(ctx, *input.Slug, 0, tenantID, pageID)
		if err != nil {
			return nil, fmt.Errorf("failed to ensure unique slug: %w", err)
		}
		setParts = append(setParts, fmt.Sprintf("slug = $%d", argIndex))
		args = append(args, slug)
		argIndex++
	}

	if input.Content != nil {
		setParts = append(setParts, fmt.Sprintf("content = $%d", argIndex))
		args = append(args, *input.Content)
		argIndex++
	}

	if input.ParentID != nil {
		setParts = append(setParts, fmt.Sprintf("parent_id = $%d", argIndex))
		args = append(args, *input.ParentID)
		argIndex++
	}

	if input.Position != nil {
		setParts = append(setParts, fmt.Sprintf("position = $%d", argIndex))
		args = append(args, *input.Position)
		argIndex++
	}

	if input.IsHidden != nil {
		setParts = append(setParts, fmt.Sprintf("is_hidden = $%d", argIndex))
		args = append(args, *input.IsHidden)
		argIndex++
	}

	// Update pages table if there are changes
	if len(setParts) > 0 {
		setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
		args = append(args, time.Now())
		argIndex++

		args = append(args, pageID, userID, tenantID)

		pageQuery := fmt.Sprintf(`
			UPDATE pages 
			SET %s 
			WHERE id = $%d AND user_id = $%d AND tenant_id = $%d
		`, strings.Join(setParts, ", "), argIndex, argIndex+1, argIndex+2)

		_, err = tx.Exec(ctx, pageQuery, args...)
		if err != nil {
			return nil, fmt.Errorf("failed to update page: %w", err)
		}
	}

	// Update page content if metadata fields are provided
	if pm.hasContentUpdates(input) {
		// Get current content
		var currentContent map[string]interface{}
		err = tx.QueryRow(ctx, `
			SELECT COALESCE(content, '{}') FROM page_content 
			WHERE page_id = $1 ORDER BY version DESC LIMIT 1
		`, pageID).Scan(&currentContent)

		if err != nil {
			// Create new content entry if none exists
			currentContent = make(map[string]interface{})
		}

		// Update content fields
		if input.MetaTitle != nil {
			currentContent["meta_title"] = *input.MetaTitle
		}
		if input.MetaDescription != nil {
			currentContent["meta_desc"] = *input.MetaDescription
		}
		if input.MetaKeywords != nil {
			currentContent["meta_keywords"] = *input.MetaKeywords
		}
		if input.CustomCSS != nil {
			currentContent["custom_css"] = *input.CustomCSS
		}
		if input.CustomJS != nil {
			currentContent["custom_js"] = *input.CustomJS
		}
		if input.FeaturedImage != nil {
			currentContent["featured_img"] = *input.FeaturedImage
		}
		if input.Template != nil {
			currentContent["template"] = *input.Template
		}
		if input.Settings != nil {
			currentContent["settings"] = *input.Settings
		}

		// Create new version of content
		_, err = tx.Exec(ctx, `
			INSERT INTO page_content (page_id, content, version, is_published, created_by, created_at, updated_at)
			VALUES ($1, $2, (SELECT COALESCE(MAX(version), 0) + 1 FROM page_content WHERE page_id = $1), false, $3, NOW(), NOW())
		`, pageID, currentContent, userID)

		if err != nil {
			return nil, fmt.Errorf("failed to update page content: %w", err)
		}
	}

	// Commit transaction
	if err = tx.Commit(ctx); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return updated page
	return pm.GetPageByID(ctx, pageID, userID, tenantID)
}

// DeletePage deletes a page and all its children
func (pm *EnhancedPageManager) DeletePage(ctx context.Context, pageID string, userID int, tenantID string) error {
	// Start transaction
	tx, err := pm.db.Pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Get all child pages recursively
	childIDs, err := pm.getChildPageIDs(ctx, pageID, tenantID)
	if err != nil {
		return fmt.Errorf("failed to get child pages: %w", err)
	}

	// Add the current page to the list
	allPageIDs := append(childIDs, pageID)

	// Delete page content for all pages
	for _, id := range allPageIDs {
		_, err = tx.Exec(ctx, `DELETE FROM page_content WHERE page_id = $1`, id)
		if err != nil {
			return fmt.Errorf("failed to delete page content: %w", err)
		}

		// Remove from site_pages
		_, err = tx.Exec(ctx, `DELETE FROM site_pages WHERE page_id = $1 AND tenant_id = $2`, id, tenantID)
		if err != nil {
			return fmt.Errorf("failed to remove page from site: %w", err)
		}

		// Remove from site navigation
		_, err = tx.Exec(ctx, `DELETE FROM site_navigation WHERE page_id = $1 AND tenant_id = $2`, id, tenantID)
		if err != nil {
			return fmt.Errorf("failed to remove page from navigation: %w", err)
		}
	}

	// Delete all pages
	for _, id := range allPageIDs {
		_, err = tx.Exec(ctx, `DELETE FROM pages WHERE id = $1 AND user_id = $2 AND tenant_id = $3`, id, userID, tenantID)
		if err != nil {
			return fmt.Errorf("failed to delete page: %w", err)
		}
	}

	return tx.Commit(ctx)
}

// GetSitePages retrieves all pages for a site with hierarchy
func (pm *EnhancedPageManager) GetSitePages(ctx context.Context, siteID int, userID int, tenantID string) ([]*PageWithContent, error) {
	query := `
		SELECT p.id, p.title, p.slug, p.content, p.parent_id, p.position, p.is_hidden,
		       p.user_id, p.tenant_id, p.created_at, p.updated_at,
		       COALESCE(pc.content->>'meta_title', p.title) as meta_title,
		       COALESCE(pc.content->>'meta_desc', '') as meta_description,
		       COALESCE(pc.content->>'meta_keywords', '') as meta_keywords,
		       COALESCE(pc.content->>'custom_css', '') as custom_css,
		       COALESCE(pc.content->>'custom_js', '') as custom_js,
		       COALESCE(pc.content->>'featured_img', '') as featured_image,
		       COALESCE(pc.content->>'template', 'default') as template,
		       COALESCE(pc.content->'settings', '{}') as settings,
		       COALESCE(pc.version, 1) as content_version,
		       COALESCE(pc.is_published, false) as is_published,
		       'draft' as status,
		       sp.is_homepage
		FROM pages p
		JOIN site_pages sp ON p.id = sp.page_id
		LEFT JOIN page_content pc ON p.id = pc.page_id
		WHERE sp.site_id = $1 AND p.user_id = $2 AND p.tenant_id = $3
		ORDER BY p.parent_id NULLS FIRST, p.position ASC, pc.version DESC
	`

	rows, err := pm.db.Pool.Query(ctx, query, siteID, userID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get site pages: %w", err)
	}
	defer rows.Close()

	var pages []*PageWithContent
	pageMap := make(map[string]*PageWithContent)

	for rows.Next() {
		var page PageWithContent
		var settingsJSON interface{}
		var isHomepage bool

		err := rows.Scan(
			&page.ID, &page.Title, &page.Slug, &page.Content, &page.ParentID, &page.Position,
			&page.IsHidden, &page.UserID, &page.TenantID, &page.CreatedAt, &page.UpdatedAt,
			&page.MetaTitle, &page.MetaDescription, &page.MetaKeywords, &page.CustomCSS,
			&page.CustomJS, &page.FeaturedImage, &page.Template, &settingsJSON,
			&page.ContentVersion, &page.IsPublished, &page.Status, &isHomepage,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to scan page: %w", err)
		}

		// Convert settings JSON to map
		if settingsMap, ok := settingsJSON.(map[string]interface{}); ok {
			page.Settings = settingsMap
		} else {
			page.Settings = make(map[string]interface{})
		}

		// Skip duplicates (due to multiple content versions)
		if _, exists := pageMap[page.ID]; !exists {
			pageMap[page.ID] = &page
			pages = append(pages, &page)
		}
	}

	// Build hierarchy
	return pm.buildPageHierarchy(pages), nil
}

// Helper functions

func (pm *EnhancedPageManager) generateSlug(title string) string {
	slug := strings.ToLower(title)
	slug = strings.ReplaceAll(slug, " ", "-")
	slug = strings.ReplaceAll(slug, "_", "-")
	// Remove special characters (basic implementation)
	allowedChars := "abcdefghijklmnopqrstuvwxyz0123456789-"
	var result strings.Builder
	for _, char := range slug {
		if strings.ContainsRune(allowedChars, char) {
			result.WriteRune(char)
		}
	}
	return result.String()
}

func (pm *EnhancedPageManager) ensureUniqueSlug(ctx context.Context, slug string, siteID int, tenantID string, excludePageID string) (string, error) {
	originalSlug := slug
	counter := 1

	for {
		var count int
		var query string
		var args []interface{}

		if excludePageID != "" {
			query = `SELECT COUNT(*) FROM pages WHERE slug = $1 AND tenant_id = $2 AND id != $3`
			args = []interface{}{slug, tenantID, excludePageID}
		} else {
			query = `SELECT COUNT(*) FROM pages WHERE slug = $1 AND tenant_id = $2`
			args = []interface{}{slug, tenantID}
		}

		err := pm.db.Pool.QueryRow(ctx, query, args...).Scan(&count)
		if err != nil {
			return "", err
		}

		if count == 0 {
			return slug, nil
		}

		slug = fmt.Sprintf("%s-%d", originalSlug, counter)
		counter++
	}
}

func (pm *EnhancedPageManager) getNextPosition(ctx context.Context, parentID *string, tenantID string) (int, error) {
	var maxPosition int
	var query string
	var args []interface{}

	if parentID != nil {
		query = `SELECT COALESCE(MAX(position), -1) FROM pages WHERE parent_id = $1 AND tenant_id = $2`
		args = []interface{}{*parentID, tenantID}
	} else {
		query = `SELECT COALESCE(MAX(position), -1) FROM pages WHERE parent_id IS NULL AND tenant_id = $1`
		args = []interface{}{tenantID}
	}

	err := pm.db.Pool.QueryRow(ctx, query, args...).Scan(&maxPosition)
	if err != nil {
		return 0, err
	}

	return maxPosition + 1, nil
}

func (pm *EnhancedPageManager) hasContentUpdates(input *UpdatePageInput) bool {
	return input.MetaTitle != nil || input.MetaDescription != nil || input.MetaKeywords != nil ||
		input.CustomCSS != nil || input.CustomJS != nil || input.FeaturedImage != nil ||
		input.Template != nil || input.Settings != nil
}

func (pm *EnhancedPageManager) getChildPageIDs(ctx context.Context, pageID string, tenantID string) ([]string, error) {
	var childIDs []string

	rows, err := pm.db.Pool.Query(ctx, `
		SELECT id FROM pages WHERE parent_id = $1 AND tenant_id = $2
	`, pageID, tenantID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var childID string
		if err := rows.Scan(&childID); err != nil {
			return nil, err
		}

		// Recursively get children of children
		grandChildren, err := pm.getChildPageIDs(ctx, childID, tenantID)
		if err != nil {
			return nil, err
		}

		childIDs = append(childIDs, childID)
		childIDs = append(childIDs, grandChildren...)
	}

	return childIDs, nil
}

func (pm *EnhancedPageManager) buildPageHierarchy(pages []*PageWithContent) []*PageWithContent {
	pageMap := make(map[string]*PageWithContent)
	var rootPages []*PageWithContent

	// Create map for quick lookup
	for _, page := range pages {
		pageMap[page.ID] = page
		page.Children = []*PageWithContent{} // Initialize children slice
	}

	// Build hierarchy
	for _, page := range pages {
		if page.ParentID != nil {
			if parent, exists := pageMap[*page.ParentID]; exists {
				parent.Children = append(parent.Children, page)
				page.Parent = parent
			} else {
				rootPages = append(rootPages, page)
			}
		} else {
			rootPages = append(rootPages, page)
		}
	}

	return rootPages
}
