package addon

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"goVwPlatformAPI/internal/crypto"
)

// SecureCredentialManager handles secure storage and retrieval of API credentials
type SecureCredentialManager struct {
	db     DatabaseInterface
	crypto *crypto.Service
}

// NewSecureCredentialManager creates a new secure credential manager
func NewSecureCredentialManager(db DatabaseInterface, cryptoService *crypto.Service) *SecureCredentialManager {
	return &SecureCredentialManager{
		db:     db,
		crypto: cryptoService,
	}
}

// EncryptedCredentials represents encrypted credential data
type EncryptedCredentials struct {
	EncryptedData string    `json:"encrypted_data"`
	KeyID         string    `json:"key_id"`
	Algorithm     string    `json:"algorithm"`
	CreatedAt     time.Time `json:"created_at"`
}

// CredentialData represents the actual credential information
type CredentialData struct {
	APIKey       string                 `json:"api_key,omitempty"`
	Secret       string                 `json:"secret,omitempty"`
	Username     string                 `json:"username,omitempty"`
	Password     string                 `json:"password,omitempty"`
	Token        string                 `json:"token,omitempty"`
	RefreshToken string                 `json:"refresh_token,omitempty"`
	ClientID     string                 `json:"client_id,omitempty"`
	ClientSecret string                 `json:"client_secret,omitempty"`
	Headers      map[string]string      `json:"headers,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// CreateAPICredentials creates new encrypted API credentials
func (m *SecureCredentialManager) CreateAPICredentials(ctx context.Context, creds *APICredentials, userID string) (*APICredentials, error) {
	// Validate credentials
	if err := m.validateCredentials(creds); err != nil {
		return nil, fmt.Errorf("invalid credentials: %w", err)
	}

	// Encrypt sensitive data
	encryptedCreds, err := m.encryptCredentials(creds.Credentials)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt credentials: %w", err)
	}

	// Set metadata
	creds.ID = generateUUID()
	creds.UserID = userID
	creds.IsActive = true
	creds.CreatedAt = time.Now()
	creds.UpdatedAt = time.Now()

	// Replace sensitive data with encrypted version
	creds.Credentials = map[string]interface{}{
		"encrypted": encryptedCreds,
	}

	// Store in database
	if err := m.db.CreateAPICredentials(ctx, creds, userID); err != nil {
		return nil, fmt.Errorf("failed to create API credentials: %w", err)
	}

	// Return credentials without sensitive data
	safeCreds := *creds
	safeCreds.Credentials = map[string]interface{}{
		"type":   creds.AuthType,
		"masked": true,
	}

	return &safeCreds, nil
}

// UpdateAPICredentials updates existing API credentials
func (m *SecureCredentialManager) UpdateAPICredentials(ctx context.Context, id string, updates *APICredentials, userID string) (*APICredentials, error) {
	// Get existing credentials
	existing, err := m.db.GetAPICredentials(ctx, id, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing credentials: %w", err)
	}

	// Apply updates
	if updates.Name != "" {
		existing.Name = updates.Name
	}
	if updates.IsActive != existing.IsActive {
		existing.IsActive = updates.IsActive
	}
	if updates.ExpiresAt != nil {
		existing.ExpiresAt = updates.ExpiresAt
	}

	// If new credentials are provided, encrypt them
	if updates.Credentials != nil {
		encryptedCreds, err := m.encryptCredentials(updates.Credentials)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt new credentials: %w", err)
		}

		existing.Credentials = map[string]interface{}{
			"encrypted": encryptedCreds,
		}
	}

	existing.UpdatedAt = time.Now()

	// Update in database
	if err := m.db.UpdateAPICredentials(ctx, id, existing, userID); err != nil {
		return nil, fmt.Errorf("failed to update API credentials: %w", err)
	}

	// Return safe version
	safeCreds := *existing
	safeCreds.Credentials = map[string]interface{}{
		"type":   existing.AuthType,
		"masked": true,
	}

	return &safeCreds, nil
}

// GetAPICredentials retrieves API credentials (returns masked version)
func (m *SecureCredentialManager) GetAPICredentials(ctx context.Context, id string, userID string) (*APICredentials, error) {
	creds, err := m.db.GetAPICredentials(ctx, id, userID)
	if err != nil {
		return nil, err
	}

	// Return masked version for security
	safeCreds := *creds
	safeCreds.Credentials = map[string]interface{}{
		"type":   creds.AuthType,
		"masked": true,
	}

	return &safeCreds, nil
}

// GetDecryptedCredentials retrieves and decrypts API credentials for internal use
func (m *SecureCredentialManager) GetDecryptedCredentials(ctx context.Context, id string, userID string) (*CredentialData, error) {
	creds, err := m.db.GetAPICredentials(ctx, id, userID)
	if err != nil {
		return nil, err
	}

	if !creds.IsActive {
		return nil, fmt.Errorf("credentials are inactive")
	}

	if creds.ExpiresAt != nil && creds.ExpiresAt.Before(time.Now()) {
		return nil, fmt.Errorf("credentials have expired")
	}

	// Decrypt credentials
	encryptedData, ok := creds.Credentials["encrypted"].(EncryptedCredentials)
	if !ok {
		return nil, fmt.Errorf("invalid encrypted credential format")
	}

	decryptedData, err := m.decryptCredentials(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt credentials: %w", err)
	}

	// Update last used timestamp
	creds.LastUsed = &time.Time{}
	*creds.LastUsed = time.Now()
	m.db.UpdateAPICredentials(ctx, id, creds, userID)

	return decryptedData, nil
}

// ListAPICredentials lists API credentials (returns masked versions)
func (m *SecureCredentialManager) ListAPICredentials(ctx context.Context, userID string, apiConfigID *string) ([]*APICredentials, error) {
	credsList, err := m.db.ListAPICredentials(ctx, userID, apiConfigID)
	if err != nil {
		return nil, err
	}

	// Return masked versions
	var safeCredsList []*APICredentials
	for _, creds := range credsList {
		safeCreds := *creds
		safeCreds.Credentials = map[string]interface{}{
			"type":   creds.AuthType,
			"masked": true,
		}
		safeCredsList = append(safeCredsList, &safeCreds)
	}

	return safeCredsList, nil
}

// DeleteAPICredentials deletes API credentials
func (m *SecureCredentialManager) DeleteAPICredentials(ctx context.Context, id string, userID string) error {
	return m.db.DeleteAPICredentials(ctx, id, userID)
}

// RotateCredentials rotates API credentials by creating new ones and marking old ones as inactive
func (m *SecureCredentialManager) RotateCredentials(ctx context.Context, id string, newCredentials map[string]interface{}, userID string) (*APICredentials, error) {
	// Get existing credentials
	existing, err := m.db.GetAPICredentials(ctx, id, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing credentials: %w", err)
	}

	// Mark existing credentials as inactive
	existing.IsActive = false
	existing.UpdatedAt = time.Now()
	if err := m.db.UpdateAPICredentials(ctx, id, existing, userID); err != nil {
		return nil, fmt.Errorf("failed to deactivate old credentials: %w", err)
	}

	// Create new credentials
	newCreds := &APICredentials{
		Name:        existing.Name + "_rotated",
		APIConfigID: existing.APIConfigID,
		AuthType:    existing.AuthType,
		Credentials: newCredentials,
		ExpiresAt:   existing.ExpiresAt,
	}

	return m.CreateAPICredentials(ctx, newCreds, userID)
}

// encryptCredentials encrypts credential data
func (m *SecureCredentialManager) encryptCredentials(credentials map[string]interface{}) (EncryptedCredentials, error) {
	// Convert to JSON
	credData, err := json.Marshal(credentials)
	if err != nil {
		return EncryptedCredentials{}, fmt.Errorf("failed to marshal credentials: %w", err)
	}

	// Encrypt data
	encryptedData, err := m.crypto.Encrypt(credData)
	if err != nil {
		return EncryptedCredentials{}, fmt.Errorf("failed to encrypt data: %w", err)
	}

	return EncryptedCredentials{
		EncryptedData: string(encryptedData),
		KeyID:         "default",
		Algorithm:     "AES-256-GCM",
		CreatedAt:     time.Now(),
	}, nil
}

// decryptCredentials decrypts credential data
func (m *SecureCredentialManager) decryptCredentials(encrypted EncryptedCredentials) (*CredentialData, error) {
	// Decrypt data
	decryptedData, err := m.crypto.Decrypt([]byte(encrypted.EncryptedData))
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt data: %w", err)
	}

	// Parse JSON
	var credData CredentialData
	if err := json.Unmarshal(decryptedData, &credData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal decrypted data: %w", err)
	}

	return &credData, nil
}

// validateCredentials validates credential data
func (m *SecureCredentialManager) validateCredentials(creds *APICredentials) error {
	if creds.Name == "" {
		return fmt.Errorf("name is required")
	}
	if creds.APIConfigID == "" {
		return fmt.Errorf("API configuration ID is required")
	}
	if creds.AuthType == "" {
		return fmt.Errorf("authentication type is required")
	}
	if creds.Credentials == nil || len(creds.Credentials) == 0 {
		return fmt.Errorf("credentials data is required")
	}

	// Validate based on auth type
	switch creds.AuthType {
	case AuthTypeAPIKey:
		if _, ok := creds.Credentials["api_key"]; !ok {
			return fmt.Errorf("api_key is required for API key authentication")
		}
	case AuthTypeBearer:
		if _, ok := creds.Credentials["token"]; !ok {
			return fmt.Errorf("token is required for bearer authentication")
		}
	case AuthTypeBasic:
		if _, ok := creds.Credentials["username"]; !ok {
			return fmt.Errorf("username is required for basic authentication")
		}
		if _, ok := creds.Credentials["password"]; !ok {
			return fmt.Errorf("password is required for basic authentication")
		}
	case AuthTypeOAuth2:
		if _, ok := creds.Credentials["client_id"]; !ok {
			return fmt.Errorf("client_id is required for OAuth2 authentication")
		}
		if _, ok := creds.Credentials["client_secret"]; !ok {
			return fmt.Errorf("client_secret is required for OAuth2 authentication")
		}
	}

	return nil
}
