package addon

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"
)

// MetadataManager handles addon metadata operations
type MetadataManager struct {
	serializer *AddonSerializer
}

// NewMetadataManager creates a new metadata manager
func NewMetadataManager() *MetadataManager {
	return &MetadataManager{
		serializer: NewAddonSerializer(),
	}
}

// CreateMetadata creates new addon metadata with validation
func (m *MetadataManager) CreateMetadata(name, description, category, author string) (*AddonMetadata, error) {
	if err := m.validateBasicFields(name, description, category, author); err != nil {
		return nil, err
	}

	metadata := &AddonMetadata{
		ID:          generateAddonID(name, author),
		Name:        name,
		Description: description,
		Category:    category,
		Author:      author,
		Version:     "1.0.0",
		Tags:        []string{},
		Icon:        "default-addon-icon.svg",
		IsPublic:    false,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Properties:  make(map[string]string),
	}

	return metadata, nil
}

// UpdateMetadata updates existing addon metadata
func (m *MetadataManager) UpdateMetadata(existing *AddonMetadata, updates map[string]interface{}) (*AddonMetadata, error) {
	if existing == nil {
		return nil, fmt.Errorf("existing metadata cannot be nil")
	}

	// Create a copy to avoid modifying the original
	updated := *existing
	updated.UpdatedAt = time.Now()

	// Apply updates
	for field, value := range updates {
		switch field {
		case "name":
			if name, ok := value.(string); ok {
				if err := m.validateName(name); err != nil {
					return nil, fmt.Errorf("invalid name: %w", err)
				}
				updated.Name = name
			}
		case "description":
			if desc, ok := value.(string); ok {
				if err := m.validateDescription(desc); err != nil {
					return nil, fmt.Errorf("invalid description: %w", err)
				}
				updated.Description = desc
			}
		case "category":
			if cat, ok := value.(string); ok {
				if err := m.validateCategory(cat); err != nil {
					return nil, fmt.Errorf("invalid category: %w", err)
				}
				updated.Category = cat
			}
		case "version":
			if ver, ok := value.(string); ok {
				if err := m.validateVersion(ver); err != nil {
					return nil, fmt.Errorf("invalid version: %w", err)
				}
				updated.Version = ver
			}
		case "tags":
			if tags, ok := value.([]string); ok {
				if err := m.validateTags(tags); err != nil {
					return nil, fmt.Errorf("invalid tags: %w", err)
				}
				updated.Tags = tags
			}
		case "icon":
			if icon, ok := value.(string); ok {
				updated.Icon = icon
			}
		case "isPublic":
			if public, ok := value.(bool); ok {
				updated.IsPublic = public
			}
		case "properties":
			if props, ok := value.(map[string]string); ok {
				updated.Properties = props
			}
		}
	}

	return &updated, nil
}

// ValidateMetadata validates addon metadata
func (m *MetadataManager) ValidateMetadata(metadata *AddonMetadata) error {
	if metadata == nil {
		return fmt.Errorf("metadata cannot be nil")
	}

	if err := m.validateName(metadata.Name); err != nil {
		return fmt.Errorf("invalid name: %w", err)
	}

	if err := m.validateDescription(metadata.Description); err != nil {
		return fmt.Errorf("invalid description: %w", err)
	}

	if err := m.validateCategory(metadata.Category); err != nil {
		return fmt.Errorf("invalid category: %w", err)
	}

	if err := m.validateVersion(metadata.Version); err != nil {
		return fmt.Errorf("invalid version: %w", err)
	}

	if err := m.validateTags(metadata.Tags); err != nil {
		return fmt.Errorf("invalid tags: %w", err)
	}

	return nil
}

// GetMetadataTemplate returns a template for creating new addon metadata
func (m *MetadataManager) GetMetadataTemplate() map[string]interface{} {
	return map[string]interface{}{
		"name":        "",
		"description": "",
		"category":    "utility",
		"author":      "",
		"version":     "1.0.0",
		"tags":        []string{},
		"icon":        "default-addon-icon.svg",
		"isPublic":    false,
		"properties": map[string]string{
			"license":    "MIT",
			"homepage":   "",
			"repository": "",
			"bugTracker": "",
			"keywords":   "",
		},
	}
}

// GetAvailableCategories returns list of available addon categories
func (m *MetadataManager) GetAvailableCategories() []string {
	return []string{
		"utility",
		"productivity",
		"communication",
		"analytics",
		"marketing",
		"ecommerce",
		"social",
		"content",
		"design",
		"development",
		"integration",
		"automation",
		"security",
		"finance",
		"education",
		"entertainment",
		"other",
	}
}

// SerializeMetadata converts metadata to JSON
func (m *MetadataManager) SerializeMetadata(metadata *AddonMetadata) ([]byte, error) {
	if err := m.ValidateMetadata(metadata); err != nil {
		return nil, fmt.Errorf("metadata validation failed: %w", err)
	}

	data, err := json.MarshalIndent(metadata, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to serialize metadata: %w", err)
	}

	return data, nil
}

// DeserializeMetadata converts JSON to metadata
func (m *MetadataManager) DeserializeMetadata(data []byte) (*AddonMetadata, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("empty metadata data")
	}

	var metadata AddonMetadata
	if err := json.Unmarshal(data, &metadata); err != nil {
		return nil, fmt.Errorf("failed to deserialize metadata: %w", err)
	}

	if err := m.ValidateMetadata(&metadata); err != nil {
		return nil, fmt.Errorf("deserialized metadata validation failed: %w", err)
	}

	return &metadata, nil
}

// Private validation methods

func (m *MetadataManager) validateBasicFields(name, description, category, author string) error {
	if err := m.validateName(name); err != nil {
		return err
	}
	if err := m.validateDescription(description); err != nil {
		return err
	}
	if err := m.validateCategory(category); err != nil {
		return err
	}
	if err := m.validateAuthor(author); err != nil {
		return err
	}
	return nil
}

func (m *MetadataManager) validateName(name string) error {
	if name == "" {
		return fmt.Errorf("name is required")
	}
	if len(name) < 3 {
		return fmt.Errorf("name must be at least 3 characters long")
	}
	if len(name) > 50 {
		return fmt.Errorf("name must be less than 50 characters")
	}

	// Check for valid characters (alphanumeric, spaces, hyphens, underscores)
	validName := regexp.MustCompile(`^[a-zA-Z0-9\s\-_]+$`)
	if !validName.MatchString(name) {
		return fmt.Errorf("name contains invalid characters")
	}

	return nil
}

func (m *MetadataManager) validateDescription(description string) error {
	if description == "" {
		return fmt.Errorf("description is required")
	}
	if len(description) < 10 {
		return fmt.Errorf("description must be at least 10 characters long")
	}
	if len(description) > 500 {
		return fmt.Errorf("description must be less than 500 characters")
	}
	return nil
}

func (m *MetadataManager) validateCategory(category string) error {
	if category == "" {
		return fmt.Errorf("category is required")
	}

	validCategories := m.GetAvailableCategories()
	for _, valid := range validCategories {
		if category == valid {
			return nil
		}
	}

	return fmt.Errorf("invalid category: %s", category)
}

func (m *MetadataManager) validateAuthor(author string) error {
	if author == "" {
		return fmt.Errorf("author is required")
	}
	if len(author) < 2 {
		return fmt.Errorf("author must be at least 2 characters long")
	}
	if len(author) > 100 {
		return fmt.Errorf("author must be less than 100 characters")
	}
	return nil
}

func (m *MetadataManager) validateVersion(version string) error {
	if version == "" {
		return fmt.Errorf("version is required")
	}

	// Basic semantic version validation (x.y.z)
	semverRegex := regexp.MustCompile(`^(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9\-]+))?(?:\+([a-zA-Z0-9\-]+))?$`)
	if !semverRegex.MatchString(version) {
		return fmt.Errorf("version must follow semantic versioning (x.y.z)")
	}

	return nil
}

func (m *MetadataManager) validateTags(tags []string) error {
	if len(tags) > 10 {
		return fmt.Errorf("maximum 10 tags allowed")
	}

	for _, tag := range tags {
		if len(tag) == 0 {
			return fmt.Errorf("empty tags are not allowed")
		}
		if len(tag) > 20 {
			return fmt.Errorf("tag '%s' is too long (max 20 characters)", tag)
		}

		// Check for valid tag characters
		validTag := regexp.MustCompile(`^[a-zA-Z0-9\-_]+$`)
		if !validTag.MatchString(tag) {
			return fmt.Errorf("tag '%s' contains invalid characters", tag)
		}
	}

	return nil
}

// generateAddonID generates a unique ID for an addon
func generateAddonID(name, author string) string {
	// Create a simple ID based on name and author
	cleanName := strings.ToLower(strings.ReplaceAll(name, " ", "-"))
	cleanAuthor := strings.ToLower(strings.ReplaceAll(author, " ", "-"))
	timestamp := time.Now().Unix()

	return fmt.Sprintf("%s-%s-%d", cleanAuthor, cleanName, timestamp)
}
