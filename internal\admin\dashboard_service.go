package admin

import (
	"context"
	"fmt"
	"goVwPlatformAPI/internal/database"
	"time"
)

// DashboardService handles admin dashboard operations
type DashboardService struct {
	db *database.DB
}

// NewDashboardService creates a new dashboard service
func NewDashboardService(db *database.DB) *DashboardService {
	return &DashboardService{db: db}
}

// AdminDashboardKPIs represents key performance indicators for the admin dashboard
type AdminDashboardKPIs struct {
	// User Metrics
	TotalUsers        int64 `json:"total_users"`
	ActiveUsers       int64 `json:"active_users"`
	NewUsersToday     int64 `json:"new_users_today"`
	NewUsersThisWeek  int64 `json:"new_users_this_week"`
	NewUsersThisMonth int64 `json:"new_users_this_month"`

	// Expert Metrics
	TotalExperts       int64   `json:"total_experts"`
	ActiveExperts      int64   `json:"active_experts"`
	PendingExpertApps  int64   `json:"pending_expert_applications"`
	ExpertApprovalRate float64 `json:"expert_approval_rate"`

	// Site & Content Metrics
	TotalSites     int64 `json:"total_sites"`
	PublishedSites int64 `json:"published_sites"`
	TotalPages     int64 `json:"total_pages"`
	TotalAddons    int64 `json:"total_addons"`
	AddonsInReview int64 `json:"addons_in_review"`

	// Revenue Metrics
	TotalRevenue          float64 `json:"total_revenue"`
	MonthlyRevenue        float64 `json:"monthly_revenue"`
	AverageRevenuePerUser float64 `json:"average_revenue_per_user"`

	// System Metrics
	SystemUptime  string  `json:"system_uptime"`
	DatabaseSize  string  `json:"database_size"`
	StorageUsed   string  `json:"storage_used"`
	APICallsToday int64   `json:"api_calls_today"`
	ErrorRate     float64 `json:"error_rate"`

	// Activity Metrics
	RecentActivity      []AdminActivityItem `json:"recent_activity"`
	TopPerformingAddons []AddonPerformance  `json:"top_performing_addons"`
	TopActiveUsers      []UserActivity      `json:"top_active_users"`

	LastUpdated time.Time `json:"last_updated"`
}

// AdminActivityItem represents a recent activity item
type AdminActivityItem struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	UserID      string                 `json:"user_id"`
	UserEmail   string                 `json:"user_email"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata"`
	Severity    string                 `json:"severity"` // info, warning, error, critical
}

// AddonPerformance represents addon performance metrics
type AddonPerformance struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Author      string  `json:"author"`
	Downloads   int64   `json:"downloads"`
	Rating      float64 `json:"rating"`
	Revenue     float64 `json:"revenue"`
	ActiveUsers int64   `json:"active_users"`
}

// UserActivity represents user activity metrics
type UserActivity struct {
	UserID       string    `json:"user_id"`
	Email        string    `json:"email"`
	Role         string    `json:"role"`
	LastActive   time.Time `json:"last_active"`
	SitesCreated int64     `json:"sites_created"`
	PagesCreated int64     `json:"pages_created"`
	APICallsUsed int64     `json:"api_calls_used"`
}

// GetDashboardKPIs retrieves all KPIs for the admin dashboard
func (s *DashboardService) GetDashboardKPIs(ctx context.Context) (*AdminDashboardKPIs, error) {
	kpis := &AdminDashboardKPIs{
		LastUpdated: time.Now(),
	}

	// Get user metrics
	if err := s.getUserMetrics(ctx, kpis); err != nil {
		return nil, fmt.Errorf("failed to get user metrics: %w", err)
	}

	// Get expert metrics
	if err := s.getExpertMetrics(ctx, kpis); err != nil {
		return nil, fmt.Errorf("failed to get expert metrics: %w", err)
	}

	// Get site and content metrics
	if err := s.getContentMetrics(ctx, kpis); err != nil {
		return nil, fmt.Errorf("failed to get content metrics: %w", err)
	}

	// Get revenue metrics
	if err := s.getRevenueMetrics(ctx, kpis); err != nil {
		return nil, fmt.Errorf("failed to get revenue metrics: %w", err)
	}

	// Get system metrics
	if err := s.getSystemMetrics(ctx, kpis); err != nil {
		return nil, fmt.Errorf("failed to get system metrics: %w", err)
	}

	// Get activity data
	if err := s.getActivityData(ctx, kpis); err != nil {
		return nil, fmt.Errorf("failed to get activity data: %w", err)
	}

	return kpis, nil
}

// getUserMetrics retrieves user-related metrics
func (s *DashboardService) getUserMetrics(ctx context.Context, kpis *AdminDashboardKPIs) error {
	// Total users
	err := s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM users").Scan(&kpis.TotalUsers)
	if err != nil {
		return err
	}

	// Active users (logged in within last 30 days)
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM users 
		WHERE last_login_at > NOW() - INTERVAL '30 days'
	`).Scan(&kpis.ActiveUsers)
	if err != nil {
		kpis.ActiveUsers = 0 // If column doesn't exist, default to 0
	}

	// New users today
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM users 
		WHERE created_at >= CURRENT_DATE
	`).Scan(&kpis.NewUsersToday)
	if err != nil {
		return err
	}

	// New users this week
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM users 
		WHERE created_at >= DATE_TRUNC('week', NOW())
	`).Scan(&kpis.NewUsersThisWeek)
	if err != nil {
		return err
	}

	// New users this month
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM users 
		WHERE created_at >= DATE_TRUNC('month', NOW())
	`).Scan(&kpis.NewUsersThisMonth)
	if err != nil {
		return err
	}

	return nil
}

// getExpertMetrics retrieves expert-related metrics
func (s *DashboardService) getExpertMetrics(ctx context.Context, kpis *AdminDashboardKPIs) error {
	// Total experts
	err := s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM expert_profiles
	`).Scan(&kpis.TotalExperts)
	if err != nil {
		kpis.TotalExperts = 0 // Table might not exist yet
	}

	// Active experts
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM expert_profiles 
		WHERE status = 'ACTIVE'
	`).Scan(&kpis.ActiveExperts)
	if err != nil {
		kpis.ActiveExperts = 0
	}

	// Pending expert applications
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM expert_applications 
		WHERE status = 'PENDING'
	`).Scan(&kpis.PendingExpertApps)
	if err != nil {
		kpis.PendingExpertApps = 0
	}

	// Expert approval rate
	var totalApps, approvedApps int64
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM expert_applications
	`).Scan(&totalApps)
	if err == nil && totalApps > 0 {
		err = s.db.Pool.QueryRow(ctx, `
			SELECT COUNT(*) FROM expert_applications 
			WHERE status = 'APPROVED'
		`).Scan(&approvedApps)
		if err == nil {
			kpis.ExpertApprovalRate = float64(approvedApps) / float64(totalApps) * 100
		}
	}

	return nil
}

// getContentMetrics retrieves content-related metrics
func (s *DashboardService) getContentMetrics(ctx context.Context, kpis *AdminDashboardKPIs) error {
	// Total sites
	err := s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM sites
	`).Scan(&kpis.TotalSites)
	if err != nil {
		kpis.TotalSites = 0
	}

	// Published sites
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM sites 
		WHERE is_published = true
	`).Scan(&kpis.PublishedSites)
	if err != nil {
		kpis.PublishedSites = 0
	}

	// Total pages
	err = s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM pages").Scan(&kpis.TotalPages)
	if err != nil {
		kpis.TotalPages = 0
	}

	// Total addons
	err = s.db.Pool.QueryRow(ctx, "SELECT COUNT(*) FROM addon_configs").Scan(&kpis.TotalAddons)
	if err != nil {
		kpis.TotalAddons = 0
	}

	// Addons in review
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM addon_configs 
		WHERE state = 'REVIEW'
	`).Scan(&kpis.AddonsInReview)
	if err != nil {
		kpis.AddonsInReview = 0
	}

	return nil
}

// getRevenueMetrics retrieves revenue-related metrics
func (s *DashboardService) getRevenueMetrics(ctx context.Context, kpis *AdminDashboardKPIs) error {
	// Total revenue
	err := s.db.Pool.QueryRow(ctx, `
		SELECT COALESCE(SUM(amount), 0) FROM invoices 
		WHERE status = 'PAID'
	`).Scan(&kpis.TotalRevenue)
	if err != nil {
		kpis.TotalRevenue = 0
	}

	// Monthly revenue
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COALESCE(SUM(amount), 0) FROM invoices 
		WHERE status = 'PAID' 
		AND created_at >= DATE_TRUNC('month', NOW())
	`).Scan(&kpis.MonthlyRevenue)
	if err != nil {
		kpis.MonthlyRevenue = 0
	}

	// Average revenue per user
	if kpis.TotalUsers > 0 {
		kpis.AverageRevenuePerUser = kpis.TotalRevenue / float64(kpis.TotalUsers)
	}

	return nil
}

// getSystemMetrics retrieves system-related metrics
func (s *DashboardService) getSystemMetrics(ctx context.Context, kpis *AdminDashboardKPIs) error {
	// System uptime (placeholder - would need actual system monitoring)
	kpis.SystemUptime = "99.9%"

	// Database size
	var dbSize string
	err := s.db.Pool.QueryRow(ctx, `
		SELECT pg_size_pretty(pg_database_size(current_database()))
	`).Scan(&dbSize)
	if err != nil {
		kpis.DatabaseSize = "Unknown"
	} else {
		kpis.DatabaseSize = dbSize
	}

	// Storage used (placeholder)
	kpis.StorageUsed = "2.5 GB"

	// API calls today
	err = s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*) FROM external_api_call_logs 
		WHERE created_at >= CURRENT_DATE
	`).Scan(&kpis.APICallsToday)
	if err != nil {
		kpis.APICallsToday = 0
	}

	// Error rate (placeholder)
	kpis.ErrorRate = 0.1

	return nil
}

// getActivityData retrieves recent activity and performance data
func (s *DashboardService) getActivityData(ctx context.Context, kpis *AdminDashboardKPIs) error {
	// Recent activity
	kpis.RecentActivity = []AdminActivityItem{
		{
			ID:          "1",
			Type:        "user_registration",
			Description: "New user registered",
			UserEmail:   "<EMAIL>",
			Timestamp:   time.Now().Add(-1 * time.Hour),
			Severity:    "info",
		},
		{
			ID:          "2",
			Type:        "addon_submitted",
			Description: "New addon submitted for review",
			UserEmail:   "<EMAIL>",
			Timestamp:   time.Now().Add(-2 * time.Hour),
			Severity:    "info",
		},
		{
			ID:          "3",
			Type:        "expert_application",
			Description: "New expert application received",
			UserEmail:   "<EMAIL>",
			Timestamp:   time.Now().Add(-3 * time.Hour),
			Severity:    "info",
		},
	}

	// Top performing addons (placeholder)
	kpis.TopPerformingAddons = []AddonPerformance{
		{
			ID:          "addon1",
			Name:        "Contact Form Builder",
			Author:      "John Doe",
			Downloads:   1250,
			Rating:      4.8,
			Revenue:     2500.00,
			ActiveUsers: 450,
		},
		{
			ID:          "addon2",
			Name:        "Analytics Dashboard",
			Author:      "Jane Smith",
			Downloads:   890,
			Rating:      4.6,
			Revenue:     1800.00,
			ActiveUsers: 320,
		},
	}

	// Top active users (placeholder)
	kpis.TopActiveUsers = []UserActivity{
		{
			UserID:       "user1",
			Email:        "<EMAIL>",
			Role:         "client",
			LastActive:   time.Now().Add(-30 * time.Minute),
			SitesCreated: 5,
			PagesCreated: 25,
			APICallsUsed: 1500,
		},
	}

	return nil
}

// GetSystemHealth returns current system health status
func (s *DashboardService) GetSystemHealth(ctx context.Context) (*SystemHealth, error) {
	health := &SystemHealth{
		Status:    "healthy",
		Timestamp: time.Now(),
		Services:  make(map[string]ServiceHealth),
	}

	// Check database connectivity
	err := s.db.Pool.Ping(ctx)
	if err != nil {
		health.Services["database"] = ServiceHealth{
			Status:  "unhealthy",
			Message: err.Error(),
		}
		health.Status = "degraded"
	} else {
		health.Services["database"] = ServiceHealth{
			Status:  "healthy",
			Message: "Connected",
		}
	}

	// Check other services (placeholder)
	health.Services["storage"] = ServiceHealth{
		Status:  "healthy",
		Message: "Available",
	}

	health.Services["external_apis"] = ServiceHealth{
		Status:  "healthy",
		Message: "All APIs responding",
	}

	return health, nil
}

// SystemHealth represents overall system health
type SystemHealth struct {
	Status    string                   `json:"status"`
	Timestamp time.Time                `json:"timestamp"`
	Services  map[string]ServiceHealth `json:"services"`
}

// ServiceHealth represents individual service health
type ServiceHealth struct {
	Status  string `json:"status"`
	Message string `json:"message"`
}
