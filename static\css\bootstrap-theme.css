/* Velocity Platform Bootstrap Theme */
:root {
  /* Primary Brand Colors */
  --bs-primary: #0d6efd;
  --bs-primary-rgb: 13, 110, 253;
  --bs-secondary: #6c757d;
  --bs-secondary-rgb: 108, 117, 125;
  --bs-success: #198754;
  --bs-success-rgb: 25, 135, 84;
  --bs-info: #0dcaf0;
  --bs-info-rgb: 13, 202, 240;
  --bs-warning: #ffc107;
  --bs-warning-rgb: 255, 193, 7;
  --bs-danger: #dc3545;
  --bs-danger-rgb: 220, 53, 69;
  --bs-light: #f8f9fa;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark: #212529;
  --bs-dark-rgb: 33, 37, 41;

  /* Velocity Platform Custom Colors */
  --velocity-primary: #2563eb;
  --velocity-secondary: #64748b;
  --velocity-accent: #f59e0b;
  --velocity-success: #10b981;
  --velocity-warning: #f59e0b;
  --velocity-danger: #ef4444;
  
  /* Component Colors */
  --component-bg: #ffffff;
  --component-border: #e2e8f0;
  --component-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  
  /* Addon Builder Colors */
  --addon-node-bg: #f8fafc;
  --addon-node-border: #cbd5e1;
  --addon-connection: #3b82f6;
  
  /* Website Builder Colors */
  --builder-canvas: #f1f5f9;
  --builder-sidebar: #ffffff;
  --builder-toolbar: #1e293b;
}

/* Custom Bootstrap Component Overrides */
.btn-velocity {
  background-color: var(--velocity-primary);
  border-color: var(--velocity-primary);
  color: white;
}

.btn-velocity:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  color: white;
}

.card-velocity {
  border: 1px solid var(--component-border);
  box-shadow: var(--component-shadow);
}

.navbar-velocity {
  background-color: var(--velocity-primary) !important;
}

/* Addon Builder Specific Styles */
.addon-builder-canvas {
  background-color: var(--builder-canvas);
  min-height: 600px;
  border: 2px dashed var(--component-border);
  border-radius: 8px;
}

.addon-node {
  background-color: var(--addon-node-bg);
  border: 1px solid var(--addon-node-border);
  border-radius: 6px;
  padding: 12px;
  margin: 8px;
  cursor: move;
}

.addon-node:hover {
  border-color: var(--velocity-primary);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}

/* Website Builder Specific Styles */
.website-builder-canvas {
  background-color: var(--builder-canvas);
  min-height: 800px;
  padding: 20px;
}

.component-palette {
  background-color: var(--builder-sidebar);
  border-right: 1px solid var(--component-border);
  height: 100vh;
  overflow-y: auto;
}

.component-item {
  padding: 12px;
  margin: 8px;
  background-color: var(--component-bg);
  border: 1px solid var(--component-border);
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s ease;
}

.component-item:hover {
  border-color: var(--velocity-primary);
  transform: translateY(-2px);
  box-shadow: var(--component-shadow);
}

.component-item:active {
  cursor: grabbing;
}

/* Theme Editor Styles */
.theme-editor {
  background-color: var(--builder-sidebar);
  border-left: 1px solid var(--component-border);
  padding: 20px;
}

.color-picker-group {
  margin-bottom: 20px;
}

.color-picker-label {
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.color-picker-input {
  width: 100%;
  height: 40px;
  border: 1px solid var(--component-border);
  border-radius: 6px;
  cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
  .component-palette {
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--component-border);
  }
  
  .website-builder-canvas {
    padding: 10px;
  }
  
  .theme-editor {
    border-left: none;
    border-top: 1px solid var(--component-border);
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--velocity-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success/Error States */
.alert-velocity-success {
  background-color: #dcfce7;
  border-color: #bbf7d0;
  color: #166534;
}

.alert-velocity-error {
  background-color: #fef2f2;
  border-color: #fecaca;
  color: #991b1b;
}

.alert-velocity-warning {
  background-color: #fefce8;
  border-color: #fef3c7;
  color: #92400e;
}

/* Utility Classes */
.text-velocity-primary { color: var(--velocity-primary); }
.text-velocity-secondary { color: var(--velocity-secondary); }
.bg-velocity-primary { background-color: var(--velocity-primary); }
.bg-velocity-secondary { background-color: var(--velocity-secondary); }
.border-velocity { border-color: var(--component-border); }