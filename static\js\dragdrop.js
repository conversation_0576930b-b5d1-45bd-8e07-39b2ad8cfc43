document.addEventListener('DOMContentLoaded', () => {
    // Drag and Drop Handlers
    let draggedElement = null;

    document.addEventListener('dragstart', (e) => {
        if (e.target.classList.contains('draggable-component')) {
            draggedElement = e.target.cloneNode(true);
            e.target.style.opacity = '0.5';
        }
    });

    document.addEventListener('dragover', (e) => {
        e.preventDefault();
        const dropZone = e.target.closest('.drop-zone');
        if (dropZone) {
            e.dataTransfer.dropEffect = 'move';
        }
    });

    document.addEventListener('drop', (e) => {
        e.preventDefault();
        const canvas = e.target.closest('#main-canvas');
        if (canvas && draggedElement) {
            const newElement = draggedElement.cloneNode(true);
            newElement.classList.add('position-absolute');
            newElement.style.top = `${e.offsetY - 10}px`;
            newElement.style.left = `${e.offsetX - 10}px`;
            
            // Add delete button
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-danger btn-sm position-absolute top-0 end-0';
            deleteBtn.innerHTML = '×';
            deleteBtn.onclick = () => newElement.remove();
            
            newElement.appendChild(deleteBtn);
            canvas.appendChild(newElement);
            
            // Save position through HTMX
            htmx.trigger(canvas, 'drop');
        }
    });

    // Initialize HTMX after dynamic content loads
    document.body.addEventListener('htmx:afterProcessNode', (e) => {
        htmx.process(e.target);
    });
});