<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Vehicle Management System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.1/spectrum.min.css">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <style>
        :root { --primary: #2563eb; --danger: #dc2626; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .auth-form { max-width: 400px; margin: 2rem auto; }
        .vehicle-table { width: 100%; border-collapse: collapse; margin-top: 1rem; }
        .vehicle-table th, .vehicle-table td { padding: 0.75rem; border: 1px solid #e5e7eb; }
    </style>
</head>
<body hx-boost="true">
    <nav class="container">
        <div style="display: flex; gap: 1rem; align-items: center">
            <a href="/">Home</a>
            <div hx-get="/auth/status" hx-trigger="load"></div>
        </div>
    </nav>
    <main class="container">
        {{ template "content" . }}
    </main>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete@1.4.4/build/rete.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-connection-plugin@0.9.0/build/connection-plugin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rete-vue-render-plugin@0.5.1/build/vue-render-plugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.1/spectrum.min.js"></script>
    <script type="module" src="/static/js/addon-builder.js"></script>
    <script src="/static/js/theme-generator.js"></script>
</body>
</html>