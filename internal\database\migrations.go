package database

import (
	"context"
	"fmt"
	"io/ioutil"
	"log"
	"path/filepath"
	"runtime/debug"
	"sort"
	"strings"
)

// RunMigrations executes all pending database migrations
func (db *DB) RunMigrations() error {
	log.Println("Starting database migrations...")
	log.Println("MIGRATION CALLED FROM:", string(debug.Stack()))

	// First, let's check what migration tables exist
	log.Println("Checking existing migration tables...")
	rows, err := db.Pool.Query(context.Background(), `
		SELECT table_name FROM information_schema.tables 
		WHERE table_schema = 'public' 
		AND table_name LIKE '%migration%'
	`)
	if err != nil {
		log.Printf("Error checking existing tables: %v", err)
	} else {
		defer rows.Close()
		for rows.Next() {
			var tableName string
			rows.Scan(&tableName)
			log.Printf("Found migration table: %s", tableName)
		}
	}

	// Check for existing migration tables with different schemas and handle them
	log.Println("Checking for existing migration tables with different schemas...")

	// Try to detect if there's a migration table with bigint version
	var tableExists bool
	err = db.Pool.QueryRow(context.Background(), `
		SELECT EXISTS (
			SELECT 1 FROM information_schema.tables 
			WHERE table_schema = 'public' 
			AND table_name = 'schema_migrations'
		)
	`).Scan(&tableExists)

	if err == nil && tableExists {
		log.Println("Found existing schema_migrations table, checking its structure...")
		var dataType string
		err = db.Pool.QueryRow(context.Background(), `
			SELECT data_type FROM information_schema.columns 
			WHERE table_schema = 'public' 
			AND table_name = 'schema_migrations' 
			AND column_name = 'version'
		`).Scan(&dataType)

		if err == nil {
			log.Printf("Existing version column type: %s", dataType)
			if dataType == "bigint" {
				log.Println("Found bigint version column, this is likely from another migration system")
				log.Println("Renaming existing table to avoid conflicts...")
				_, err = db.Pool.Exec(context.Background(), `
					ALTER TABLE schema_migrations RENAME TO schema_migrations_old;
				`)
				if err != nil {
					log.Printf("Error renaming existing migration table: %v", err)
				}
			}
		}
	}

	// Drop all possible migration tables and recreate
	log.Println("Dropping existing migration tables...")
	_, err = db.Pool.Exec(context.Background(), `
		DROP TABLE IF EXISTS schema_migrations CASCADE;
		DROP TABLE IF EXISTS schema_migration CASCADE;
		DROP TABLE IF EXISTS migrations CASCADE;
		DROP TABLE IF EXISTS gorp_migrations CASCADE;
		DROP TABLE IF EXISTS migrate_version CASCADE;
	`)
	if err != nil {
		log.Printf("Error dropping migration tables: %v", err)
	}

	log.Println("Creating new migration table...")
	_, err = db.Pool.Exec(context.Background(), `
		CREATE TABLE schema_migrations (
			version VARCHAR(255) PRIMARY KEY,
			applied_at TIMESTAMPTZ DEFAULT NOW()
		)
	`)
	if err != nil {
		log.Printf("Error creating migrations table: %v", err)
		return fmt.Errorf("failed to create migrations table: %w", err)
	}
	log.Println("Migration table created successfully")

	// Get list of migration files
	migrationFiles, err := filepath.Glob("migrations/*.up.sql")
	if err != nil {
		return fmt.Errorf("failed to read migration files: %w", err)
	}

	sort.Strings(migrationFiles)

	for _, file := range migrationFiles {
		filename := filepath.Base(file)
		fullVersion := strings.TrimSuffix(filename, ".up.sql")

		// Extract numeric version from filename (e.g., "000001" from "000001_create_initial_tables")
		parts := strings.Split(fullVersion, "_")
		if len(parts) == 0 {
			log.Printf("Skipping invalid migration file: %s", filename)
			continue
		}
		version := parts[0] // Use only the numeric part

		// Check if migration already applied
		var count int
		log.Printf("Checking migration status for version: %s (from file: %s)", version, filename)
		err := db.Pool.QueryRow(context.Background(),
			"SELECT COUNT(*) FROM schema_migrations WHERE version = $1", version).Scan(&count)
		if err != nil {
			log.Printf("Error checking migration status for version %s: %v", version, err)
			return fmt.Errorf("failed to check migration status: %w", err)
		}

		if count > 0 {
			log.Printf("Migration %s already applied, skipping", version)
			continue
		}

		// Read and execute migration
		content, err := ioutil.ReadFile(file)
		if err != nil {
			return fmt.Errorf("failed to read migration file %s: %w", file, err)
		}

		log.Printf("Applying migration: %s (file: %s)", version, filename)
		_, err = db.Pool.Exec(context.Background(), string(content))
		if err != nil {
			return fmt.Errorf("failed to execute migration %s: %w", fullVersion, err)
		}

		// Record migration as applied
		_, err = db.Pool.Exec(context.Background(),
			"INSERT INTO schema_migrations (version) VALUES ($1)", version)
		if err != nil {
			return fmt.Errorf("failed to record migration %s: %w", version, err)
		}

		log.Printf("Migration %s applied successfully", version)
	}

	log.Println("All migrations completed successfully")
	return nil
}

// SeedDatabase adds initial data for development
func (db *DB) SeedDatabase() error {
	log.Println("Seeding database with initial data...")

	// Create default tenant
	_, err := db.Pool.Exec(context.Background(), `
		INSERT INTO tenants (id, name, created_at, updated_at) 
		VALUES ('00000000-0000-0000-0000-000000000001', 'Default Tenant', NOW(), NOW())
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		return fmt.Errorf("failed to create default tenant: %w", err)
	}

	// Create sample user
	_, err = db.Pool.Exec(context.Background(), `
		INSERT INTO users (id, email, password_hash, tenant_id, created_at, updated_at)
		VALUES (
			1,
			'<EMAIL>',
			'$2a$14$example_hash_for_password123',
			'00000000-0000-0000-0000-000000000001',
			NOW(),
			NOW()
		)
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		return fmt.Errorf("failed to create sample user: %w", err)
	}

	// Create sample predefined snippets
	snippets := []struct {
		name, category, html, css, js string
	}{
		{
			name:     "Primary Button",
			category: "buttons",
			html:     `<button type="button" class="btn btn-primary">Click Me</button>`,
			css:      `.btn-primary:hover { transform: translateY(-2px); transition: all 0.2s; }`,
			js:       ``,
		},
		{
			name:     "Feature Card",
			category: "cards",
			html:     `<div class="card h-100"><div class="card-body text-center"><i class="bi bi-star display-4 text-primary mb-3"></i><h5 class="card-title">Feature Title</h5><p class="card-text">Feature description goes here.</p><a href="#" class="btn btn-primary">Learn More</a></div></div>`,
			css:      `.card:hover { box-shadow: 0 4px 8px rgba(0,0,0,0.1); transition: all 0.3s; }`,
			js:       ``,
		},
		{
			name:     "Contact Form",
			category: "forms",
			html:     `<form class="contact-form"><div class="mb-3"><label class="form-label">Name</label><input type="text" class="form-control" required></div><div class="mb-3"><label class="form-label">Email</label><input type="email" class="form-control" required></div><div class="mb-3"><label class="form-label">Message</label><textarea class="form-control" rows="4" required></textarea></div><button type="submit" class="btn btn-primary">Send Message</button></form>`,
			css:      `.contact-form { max-width: 500px; }`,
			js:       `document.querySelector('.contact-form').addEventListener('submit', function(e) { e.preventDefault(); alert('Form submitted!'); });`,
		},
	}

	for _, snippet := range snippets {
		_, err = db.Pool.Exec(context.Background(), `
			INSERT INTO predefined_snippets (name, category, html_content, css_content, js_content, version, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, '1.0.0', NOW(), NOW())
			ON CONFLICT (name) DO NOTHING
		`, snippet.name, snippet.category, snippet.html, snippet.css, snippet.js)
		if err != nil {
			log.Printf("Warning: failed to create snippet %s: %v", snippet.name, err)
		}
	}

	// Create sample web addons
	addons := []struct {
		name, description, reteConfig string
	}{
		{
			name:        "Weather Widget",
			description: "Display current weather information",
			reteConfig:  `{"nodes": [{"id": "1", "name": "API Input", "data": {"url": "https://api.weather.com"}}], "connections": []}`,
		},
		{
			name:        "Contact Form Addon",
			description: "Advanced contact form with validation",
			reteConfig:  `{"nodes": [{"id": "1", "name": "Form Input"}, {"id": "2", "name": "Email Output"}], "connections": []}`,
		},
	}

	for _, addon := range addons {
		_, err = db.Pool.Exec(context.Background(), `
			INSERT INTO web_addons (name, description, rete_config, version, dependencies, status, created_at, updated_at)
			VALUES ($1, $2, $3, '1.0.0', '[]', 'production', NOW(), NOW())
			ON CONFLICT (name) DO NOTHING
		`, addon.name, addon.description, addon.reteConfig)
		if err != nil {
			log.Printf("Warning: failed to create addon %s: %v", addon.name, err)
		}
	}

	log.Println("Database seeding completed")
	return nil
}
