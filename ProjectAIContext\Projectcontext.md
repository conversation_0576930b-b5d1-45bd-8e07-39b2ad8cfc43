VELOCITYWAVE PLATFORM ACCEPTABLE USE POLICY (AUP)
Last Updated: 5 July 2025
1. Introduction
This Acceptable Use Policy (“AUP”) outlines the rules and guidelines that govern your
use of the VelocityWave platform, including our website, tools, and all related services
(collectively, the “Platform”). Its purpose is to protect the integrity, security, and
reliability of our Platform and to ensure it is used responsibly and lawfully by all.
This AUP is an integral part of our main Terms of Service and is incorporated by
reference. By accessing or using our Platform, you agree to comply with this policy.
This policy applies to all users of the Platform (“Users,” “you,” “your”), including
Clients and Expert Advisors.
2. Prohibited Activities
You may not use the Platform to engage in, promote, or facilitate any of the following
activities. Violating these rules may result in the immediate suspension or termination
of your account.
a) Illegal, Harmful, or Fraudulent Activities
● Illegal Acts: Violating any applicable local, national, or international laws or
regulations.
● Harmful to Minors: Exploiting, harming, or attempting to exploit or harm minors
in any way, including by exposing them to inappropriate content or seeking to
obtain personally identifiable information. This includes a zero-tolerance policy
for any content related to child sexual abuse material (CSAM).
● Fraud and Deception: Engaging in any activity that is fraudulent or has a
fraudulent purpose or effect. This includes phishing, pharming, pyramid schemes,
or any other deceptive practices designed to trick users into disclosing personal
or financial information.
● Malicious Software: Distributing, hosting, or transmitting malware, viruses,
worms, Trojan horses, spyware, or any other computer code, files, or programs
designed to interrupt, destroy, or limit the functionality of any computer
software, hardware, or telecommunications equipment.
b) Security and Network Abuse
● System Interference: Any action that attempts to interfere with, compromise
the system integrity or security of, or decipher any transmissions to or from the
servers running the Platform.
● Unauthorized Access: Accessing or attempting to access any account, system,
or network you are not authorised to access.
● Network Attacks: Initiating or participating in any form of network attack,
including denial of service (DoS) attacks, mail bombing, or packet flooding.
● Circumventing Security: Testing or reverse-engineering our Platform to find
limitations or vulnerabilities or to bypass security measures without our express
written permission.
c) Email and Communication Abuse
● Spam: Sending, or facilitating the sending of, unsolicited bulk email or other
communications, promotions, or advertisements (i.e., "spam").
● Deceptive Communications: Forging email headers, using misleading sender
information, or otherwise falsifying the origin of any communication.
● List Harvesting: Collecting or harvesting email addresses or other contact
information from our Platform or from websites hosted on our Platform without
the consent of the information owner.
3. Prohibited Content
You may not create, upload, post, display, or transmit any content on or through the
Platform (including on any website you build with our tools) that falls into the
following categories:
● Infringement of Intellectual Property: Content that infringes on the copyright,
trademark, patent, trade secret, or other intellectual property rights of any third
party.
● Hate Speech and Harassment: Content that promotes violence, incites hatred,
promotes discrimination, or disparages an individual or group based on race or
ethnic origin, religion, disability, age, nationality, veteran status, sexual
orientation, sex, gender identity, caste, or any other characteristic that is
associated with systemic discrimination or marginalisation. This includes content
that harasses, intimidates, or bullies an individual or group of individuals.
● Violent, Graphic, or Obscene Content: Content that is gratuitously graphic,
contains gore, or depicts acts of violence for a sadistic purpose. This also
includes content that is obscene or pornographic.
● Sexually Explicit Material: Content containing nudity or sexually explicit acts for
a pornographic purpose. We reserve the right to determine, in our sole discretion,
what constitutes pornographic content.
● Regulated or Illegal Goods and Services: Content that promotes or facilitates
the sale or provision of illegal goods or services, or regulated goods and services,
including but not limited to:
○ Firearms, ammunition, and explosives.
○ Illegal drugs and controlled substances.
○ Counterfeit goods.
○ Unregulated financial services, high-risk investments, or get-rich-quick
schemes.
○ Gambling services in jurisdictions where it is illegal.
● Misinformation and Disinformation: Content that is demonstrably false or
misleading and has the potential to cause significant harm to individuals, groups,
or the public (e.g., harmful health misinformation or content that undermines
civic processes).
● Impersonation: Content that impersonates any person or entity, including any of
our employees or representatives, in a manner that is intended to or does
mislead, confuse, or deceive others.
4. Enforcement
VelocityWave reserves the right to investigate any suspected violation of this AUP.
We will be the sole arbiter in determining whether a violation has occurred.
If we determine that a violation has occurred, we may take any or all of the following
actions, with or without prior notice:
● Issue a warning to you.
● Remove or disable access to the offending content.
● Suspend your access to the Platform.
● Terminate your Account and any related services permanently.
● Report the activity to law enforcement authorities if we believe it is illegal.
● Take legal action against you for reimbursement of all costs (including, but not
limited to, reasonable administrative and legal costs) resulting from the breach.
5. Reporting Violations
If you become aware of any violation of this AUP, you agree to notify us immediately.
Please report any suspected violations to us <NAME_EMAIL>
with the subject line "AUP Violation". Please provide as much detail as possible,
including links to the offending content and a description of the violation.
6. Revisions to this Policy
We reserve the right to modify this AUP at any time. We will notify you of any
significant changes by posting the new policy on this page and updating the “Last
Updated” date. Your continued use of the Platform after any such changes
constitutes your acceptance of the new AUP. It is your responsibility to review this
policy periodically.
architectural overview.
A. Public-Facing Pages (Unauthenticated Users)
These pages are visible to anyone visiting the main VelocityWave domain. Their primary
goal is to explain the value proposition and convert visitors into users (Clients or Experts).
1. Homepage: The main landing page with a clear call-to-action (CTA), overview of
features, benefits, and social proof (testimonials, case studies).
2. Features: A detailed breakdown of the platform's core offerings (Website Builder,
Business Tools, Add-on Marketplace, Expert Network).
3. Pricing / Plans: A clear, comparative table of all subscription tiers (Free, Business
From Home, Sole Tradies, Growth, Pro, Enterprise).
4. For Experts / Become a Partner: A dedicated landing page to attract freelance
professionals, outlining the benefits (low commission, quality clients, no bidding) and
linking to the application process.
5. About Us: Company story, mission, vision, and team to build trust and credibility.
6. Contact Us: A form for general inquiries, sales questions, and support, with contact
details.
7. Blog / Resources: Content marketing hub with articles, guides, and resources for
UK startups and SMEs.
8. Legal Hub / Footer Links:
○ Terms of Service
○ Privacy Policy
○ Cookie Policy
○ Acceptable Use Policy (AUP)
○ Service Level Agreement (SLA)
9. Add-on Marketplace Showcase: A public-facing view of the most popular or
featured addons available on the platform.
10. Expert Directory Showcase: A public-facing view highlighting some of the top-rated
experts to showcase the quality of the network.
B. Platform User (Client) Dashboard & Tools
This is the core logged-in experience for your business customers.
1. Onboarding & Setup: 11. Welcome / Onboarding Wizard: A guided step-by-step
process for new users to set up their business profile, connect a domain, and choose initial
add-ons.
2. Main Dashboard: 12. Client Dashboard: The central hub after login, displaying an
overview of website performance (analytics snapshot), recent activity, notifications, token
balance, and quick links to key areas.
3. Website Management: 13. My Sites: A gallery or list view of all websites the user has
created. 14. Website Builder Interface: The core visual drag-and-drop editor for building
and editing website pages. 15. Site Settings: A multi-tabbed page for a specific website,
including: * General Settings (Site Name, Favicon) * Domain Management (Connect custom
domain) * SEO Settings (Meta titles, descriptions, keywords) * API Key Management
(View/regenerate the site-specific API key) * Analytics (Connect Google Analytics, etc.) 16.
Publishing Status & History: A page to compile, publish, and view the history of published
versions of a site.
4. Business Tools: 17. My Business Plan: The interface for the Business Plan Builder. 18.
My Financial Plan: The interface for the Financial Plan Builder. 19. Regulations
Researcher: A tool to search and view compliance information. 20. Competitor
Researcher: A tool to input and track competitors.
5. Expert & Add-on Marketplaces: 21. Browse Experts: A searchable and filterable
directory of all available Expert Advisors by profession, tier, and rating. 22. Expert Profile
Page: A detailed view of a single expert, showing their qualifications, experience, portfolio,
and client reviews. 23. My Engagements / My Jobs: A dashboard listing all active, pending,
and completed projects with experts. 24. Engagement Detail Page: A dedicated workspace
for a single project, including a chat/messaging interface, file sharing capabilities,
timeline/milestone tracking, and the "Mark as Complete" function. 25. Browse Add-ons: A
searchable and filterable marketplace of all available platform add-ons. 26. Add-on Detail
Page: A detailed view of a single add-on, with description, features, pricing, and reviews. 27.
My Add-ons: A page listing all add-ons the user has purchased or installed.
6. Account & Financials: 28. My Profile: Page for managing personal user details (name,
email, password). 29. Business Profile: Page for managing the business's details
(company name, address, VAT number). 30. Subscription & Billing: A page to view the
current plan, upgrade/downgrade, view payment history, download invoices, and manage
payment methods. 31. Token Management: A page to view the current token balance, see
transaction history, and purchase additional "Token Top-ups". 32. Team Management: (For
Pro/Enterprise) A page to invite and manage team members' access to the platform.
C. Expert Advisor (Freelancer) Dashboard
This is the logged-in experience for your vetted professional experts.
33. Expert Application Form: The initial multi-step form for freelancers to apply.
34. Expert Onboarding & Document Upload: A secure portal for applicants to upload
proof of ID, qualifications, and insurance.
35. Expert Dashboard: The main hub after login, showing an overview of earnings, new
engagement requests, active projects, and platform announcements.
36. My Public Profile: The editor for the expert to build and update their public-facing
profile that clients will see.
37. Engagement Requests: A queue of new job opportunities available for the expert to
review and accept/decline.
38. My Active Engagements: A dashboard of all current projects.
39. My Earnings & Payouts: A dashboard showing total earnings, pending payouts,
transaction history, and management of payout settings (e.g., bank details).
40. My Products (Templates/Add-ons): A page for experts to manage any pre-made
templates or custom addons they have created and listed for sale on the platform.
D. Platform Admin Dashboard (Internal Team)
This is the comprehensive backend for your team to manage the entire platform.
41. Admin Master Dashboard: A high-level overview of key platform metrics (KPIs):
new users, active subscriptions, total revenue, platform uptime, number of open
support tickets.
42. User Management (Clients): A searchable list of all clients, with the ability to
view/edit profiles, manage subscriptions, and impersonate users for support
purposes.
43. Expert Management:
○ Expert Application Queue: A dedicated page to review and approve/reject
new expert applications.
○ Expert Directory (Admin View): A list of all approved experts, with tools to
manage their status (active, suspended) and tier level.
44. Marketplace Management:
○ Add-on Review Queue: The critical page for admins to review, test (in the
sandbox), and approve/reject third-party addons.
○ Predefined Code Snippets Library: A CRUD (Create, Read, Update,
Delete) interface for managing the library of code snippets available in the
Website Builder.
45. Platform Configuration:
○ Subscription Plan Editor: An interface to edit the pricing, features, and
token allocations for each subscription tier.
○ Platform UI Builder: The admin's access to the Website Builder to customize
the platform's own frontend and dashboards.
46. Support & Moderation:
○ Support Ticket Dashboard: A system to manage and respond to all user
support requests.
○ Dispute Resolution Center: A dedicated interface to mediate any disputes
between clients and experts.
47. Financial Reporting: A dashboard to view detailed reports on revenue,
subscriptions, token sales, and expert payouts.
E. Shared / Utility Pages
These are functional pages accessible to multiple user types.
48. Login Page: The main sign-in page.
49. Sign Up Page: The registration page for new Clients.
50. Forgot Password / Reset Password Flow: The sequence of pages for users to
recover their account access.
51. Error Pages:
○ 404 Not Found
○ 500 Internal Server Error
○ 403 Forbidden / Access Denied
52. Generic Confirmation/Success Pages: Reusable pages to confirm actions like
form submissions or password changes.
53. API Documentation: (For Third-Party Developers) A public or semi-private page
detailing how to use the platform's API to build addons.
Business Idea Analysis - VelocityWave
This analysis evaluates the core strengths, weaknesses, opportunities, and threats (SWOT)
of the VelocityWave business concept based on all the information provided.
Strengths
● Integrated Ecosystem: The "all-in-one" platform is a massive strength. It directly
addresses a major pain point for SMEs: the complexity and cost of managing dozens
of disconnected tools for websites, finance, marketing, and compliance.
● Unique Value Proposition: Combining a DIY platform with an expert marketplace is
a powerful and unique model. It creates a "sticky" ecosystem where users not only
build their business but also get the human support they need to grow, all in one
place.
● Scalable Revenue Model: The tiered subscription model provides predictable
recurring revenue. The "token" system for expert services and add-ons is an
intelligent way to encourage upsells and manage service delivery without direct
financial loss to the platform.
● Strong Two-Sided Network Effect: The platform becomes more valuable to clients
as more high-quality experts join. Conversely, it becomes more attractive to experts
as more clients join. This creates a powerful, self-reinforcing growth loop.
● Clear UK Focus: The deep focus on UK-specific regulations (GDPR, MTD,
Companies House), payment gateways, and business practices provides a
significant competitive advantage over generic global platforms.
● Low Commission for Experts: The 1% commission model is a disruptive
advantage over competitors like Upwork or Fiverr (which charge 10-20%). This will
be a major draw for top-tier freelance talent.
Weaknesses
● Complexity of Execution: Building and maintaining such a wide-ranging platform is
an immense technical and operational challenge. Integrating a website builder,
numerous business tools, an add-on marketplace, and an expert booking system
requires significant development resources.
● "Chicken and Egg" Problem: Like all marketplaces, it faces the initial challenge of
attracting both clients and experts simultaneously. Without enough clients, experts
won't stay. Without enough experts, clients won't see value in the premium tiers.
● Dependence on Third-Party Experts: The quality of the expert network is
paramount. A few bad experiences could tarnish the platform's reputation. The
vetting and quality control process must be rigorous and continuous.
● Fixed Rate Model Rigidity: While simple, the fixed hourly rate system for experts
may not be flexible enough for all professions or project types and could deter top-tier
talent who are used to setting their own, often higher, rates.
Opportunities
● Underserved SME Market: The UK has millions of small and medium-sized
enterprises that are often underserved by expensive enterprise software or overly
simplistic consumer tools. VelocityWave fits perfectly into this gap.
● Growing Gig Economy: The freelance market is expanding rapidly. More
professionals are seeking flexible work, creating a large talent pool to draw from.
● AI Integration: The platform is perfectly positioned to leverage AI. The planned AI
analytics, content generation, and recommendation tools can provide immense value
and create a strong technological moat.
● Data Monetization (Ethical): Aggregated, anonymized data on SME performance,
challenges, and tool usage could be incredibly valuable for generating industry
reports, insights, and benchmarks, creating an additional revenue stream.
● Expansion to Other Markets: Once the UK model is proven, the entire framework
can be adapted and rolled out to other English-speaking markets like Australia,
Canada, or the US, with localized compliance and expert networks.
Threats
● Intense Competition: The market is crowded. VelocityWave competes indirectly
with multiple players:
○ Website Builders: Wix, Squarespace, Shopify.
○ Freelance Marketplaces: Upwork, Fiverr, PeoplePerHour.
○ Accounting Software: Xero, QuickBooks, FreeAgent.
○ CRM Platforms: HubSpot, Zoho. The key threat is a large, established player
deciding to build out a similar integrated ecosystem.
● Regulatory Changes: The business model is heavily tied to UK regulations.
Changes in data protection (ICO), tax (HMRC), or employment law (determining
freelancer status - IR35) could require significant platform adjustments.
● Economic Downturn: In a recession, small businesses are often the first to cut
costs, which could lead to subscription cancellations and reduced spending on expert
services.
● Cybersecurity: As a central hub for business data, the platform will be a high-value
target for cyberattacks. A single significant data breach could be catastrophic for its
reputation and legal standing.
Business Plan - VelocityWave Platform
1.0 Executive Summary
Mission: To empower UK startups and small-to-medium enterprises (SMEs) to launch,
manage, and scale their businesses by providing a single, integrated platform that combines
powerful digital tools with on-demand access to a network of vetted professional experts.
Vision: To become the essential operating system for modern UK businesses, eliminating
the friction and complexity of digital transformation and fostering a thriving entrepreneurial
ecosystem.
The Opportunity: The UK is home to over 5.5 million SMEs, the vast majority of which
struggle with the fragmentation and cost of essential business services. They are forced to
piece together solutions for website hosting, financial management, legal compliance,
marketing, and HR. VelocityWave addresses this critical market gap with an all-in-one
platform that is affordable, scalable, and tailored specifically to UK business needs.
Our Solution: The VelocityWave Platform is a multi-faceted ecosystem offering:
● An intuitive Website & E-commerce Builder.
● A suite of integrated Business & Financial Tools.
● An Add-on Marketplace for industry-specific functionality.
● A unique Expert Marketplace connecting businesses with vetted UK professionals in
HR, Legal, Accounting, H&S, and more, using a transparent "Token" system.
Financial Projections: The company's revenue is generated through a tiered monthly
subscription model and a 1% commission on expert services. With a clear path to profitability
on its premium plans, we project steady growth driven by user acquisition and upselling. Our
primary financial goal is to achieve a critical mass of 5,000 paying subscribers within the first
three years.
The Ask: We are seeking to onboard our foundational network of freelance experts and
attract our first 500 beta clients by offering a compelling value proposition of fair pay and low
commissions for experts, and affordable, integrated tools for businesses.
2.0 Company Description
● Company Name: VelocityWave Ltd.
● Legal Structure: Private Limited Company registered in England and Wales.
● Core Business: A Software-as-a-Service (SaaS) platform providing an integrated
suite of business-building tools and a marketplace for professional services.
● Value Proposition: We save businesses time and money by replacing a complex
web of disconnected software and expensive consultants with a single, affordable,
and easy-to-use platform. We provide the tools to build and the expertise to grow.
3.0 Market Analysis
● Target Market: Our primary target market is the 5.5 million Small and Medium-sized
Enterprises (SMEs) in the UK. We will initially focus on two key segments:
○ Startups & Sole Traders: Businesses in their first 0-2 years of operation,
including freelancers and home-based businesses, who need foundational
tools at an affordable price point. (Addressed by our Free, Business From
Home, and Sole Tradies plans).
○ Growing Small Businesses: Established businesses with 1-20 employees
that are looking to scale, professionalise their operations, and need access to
specialised advice without the cost of full-time staff or expensive agencies.
(Addressed by our Growth, Pro, and Enterprise plans).
● Market Need: This market segment consistently cites three major challenges: (1)
lack of time, (2) managing cash flow, and (3) navigating complex regulations. Our
platform directly addresses all three.
● Competitive Landscape:
○ Direct Competitors: There are currently no direct competitors offering the
same fully integrated suite of DIY tools and an expert marketplace specifically
for UK SMEs.
○ Indirect Competitors:
■ Website Builders (e.g., Wix, Shopify): Strong at web presence but
lack integrated business management tools and expert access.
■ Freelance Platforms (e.g., Upwork, Fiverr): Strong for finding talent
but are purely transactional, with high commissions and no integrated
software tools.
■ Accounting Software (e.g., Xero, QuickBooks): Excellent for
finance but do not offer website, marketing, or broader compliance
tools.
○ Our Competitive Advantage: Our "all-in-one" ecosystem is our key
differentiator. Our low 1% commission for experts is a disruptive market
advantage that will attract top talent away from competitors.
4.0 Organization and Management
● Management Team (Hypothetical):
○ CEO: Responsible for overall strategy, investor relations, and vision.
○ CTO: Leads the technical development of the platform, security, and
infrastructure.
○ COO: Manages daily operations, customer support, and the expert
onboarding/vetting process.
○ CMO: Drives user acquisition for both clients and experts, and manages
brand strategy.
● Expert Network: Our Expert Advisors are independent contractors, not employees.
They are vetted through a rigorous onboarding process requiring proof of
qualifications, professional indemnity insurance, and identity verification to ensure
quality and compliance.
5.0 Products and Services
A. The Core Platform & Tools:
● Website Builder: Drag-and-drop editor, e-commerce functionality, PWA-ready.
● Basic Tools (Free Tier): Business Plan Builder, Financial Plan Builder, Simple
Accounting Ledger.
● Advanced Tools (Paid Tiers): Advanced financial planning, competitor analysis,
regulation compliance trackers, SEO toolkit, social media planners.
B. The Add-on Ecosystem:
● A marketplace of specialised tools (e.g., advanced booking systems, specific CRM
integrations) tailored to UK industries like Retail, Hospitality, Trades, and more.
C. The Expert Marketplace:
● A network of vetted UK professionals available for on-demand consultation.
● Token System: Clients purchase or receive "Tokens" (£1 = 1 Token) through their
subscription plan. These tokens are used to pay for expert services, custom
document creation, or pre-made templates from experts.
● Expert Tiers: Experts are categorised into Associate, Professional, and Senior tiers,
with corresponding hourly rates from £22 to £40, ensuring clients can choose the
level of expertise that fits their budget.
D. Subscription Plans:
● Free: Foundational tools.
● Business From Home (£10/mo): Adds custom domain, basic add-ons.
● Sole Tradies (£20/mo): Adds trade-specific tools and payment integrations.
● Growth (£55/mo): Includes 40 tokens for expert services.
● Pro (£139/mo): Includes 100 tokens and custom document creation.
● Enterprise (£350/mo): Includes 200 tokens, a dedicated account manager, and
bundled utilities.
6.0 Marketing and Sales Strategy
Our strategy is two-pronged, focusing on acquiring both Clients and Experts.
A. Acquiring Clients (Businesses):
● Content Marketing: Create high-value content (blogs, guides, webinars) targeting
UK startup pain points around topics like "how to register a business in the UK,"
"understanding VAT," and "first marketing steps." This will drive organic traffic.
● Digital Advertising: Targeted ads on platforms like Google, LinkedIn, and
Facebook, focusing on keywords related to starting a business, small business
software, and specific industry needs.
● Partnerships: Collaborate with accountants, business banks, and startup
accelerators who can refer their clients to our platform.
● Freemium Model: The robust Free Plan will act as our primary lead generation tool,
allowing users to experience the platform's value before upgrading.
B. Acquiring Expert Advisors:
● Direct Outreach: Target highly-rated UK freelancers on existing platforms like
Upwork and LinkedIn, highlighting our key value proposition.
● Highlight the "Why": Our marketing message to experts will focus on three key
benefits:
1. Fair Pay: A take-home rate of 99% is market-leading.
2. No Bidding: We bring qualified, pre-paid clients directly to them.
3. Quality Clients: Our clients are invested in their business growth through our
platform.
● Professional Body Partnerships: Engage with professional bodies like the CIPD
(for HR), ACCA (for accountants), and the Law Society to promote the platform as a
source of flexible work for their members.
7.0 Financial Plan
● Key Revenue Streams:
○ Monthly Subscription Fees: Predictable recurring revenue from our five
paid plans.
○ Token Top-up Sales: Revenue from users purchasing additional tokens
when their project costs exceed their included allowance.
○ Commission Fees: A 1% commission on all expert service fees after an
expert's initial 15 hours.
● Cost Structure:
○ Variable Costs: Payouts to Expert Advisors for services rendered.
○ Fixed Costs: Staff salaries (development, marketing, support), platform
hosting and infrastructure costs, marketing and advertising spend, office and
utility costs.
● Profitability:
○ The platform is designed to be profitable on every expert engagement, as
clients must have sufficient tokens to cover the cost before work begins.
○ The primary profit drivers are the high-margin subscription fees on the Pro
and Enterprise plans, supplemented by token top-up sales and the scalable
1% commission.
● Financial Projections (3-Year High-Level Goals):
○ Year 1: Achieve 500 paying subscribers. Focus on user acquisition and
platform stability. Achieve break-even on a monthly basis by Q4.
○ Year 2: Scale to 2,500 paying subscribers. Expand the add-on marketplace
and expert network. Achieve consistent profitability.
○ Year 3: Reach 5,000+ paying subscribers. Explore international expansion
opportunities. Solidify market position as the leading platform for UK SMEs.
● Break-Even Analysis: Based on estimated fixed costs, break-even is achieved
when the gross profit from subscriptions (Monthly Fee minus Cost of Included
Tokens) and platform commissions exceeds monthly operational costs. Our model
indicates this is highly achievable with our target user acquisition numbers.
Connection Details
Database:
DBname: VW_Platform_DB
Server: **************:5432
Username: PLATFORMDB
Password: $Jf6sSkfyPb&v7r1
Email:
 "SmtpHost": "velocitywave.co.uk",
 "SmtpPort": 587,
 "SmtpUsername": "<EMAIL>",
 "SmtpPassword": "m76t9n3M#",
 "EnableSsl": true,
 "FromEmail": "<EMAIL>",
 "FromName": "VelocityPlatform",
DATA PROCESSING AGREEMENT (DPA)
This Data Processing Agreement (“DPA”) is entered into as of [Date] (“Effective Date”) by
and between:
VelocityWave Ltd., a company incorporated in England and Wales with registered number
[Company Number] and whose registered office is at [Registered Address] (“VelocityWave”);
and
[Expert Advisor Full Legal Name], with address at [Expert Advisor Address] or, if a company,
incorporated in [Jurisdiction] with registered number [Company Number] and whose registered
office is at [Registered Address] (the “Expert Advisor”).
(Each a “Party” and together the “Parties”)
BACKGROUND
(A) This DPA is incorporated into and forms part of the Expert Advisor Agreement or any
other master agreement between VelocityWave and the Expert Advisor (the “Principal
Agreement”).
(B) The Expert Advisor provides professional services (the “Services”) to business users of
the VelocityWave platform (the “Clients”).
(C) In providing the Services, the Expert Advisor will process Personal Data on behalf of the
Clients. The Client is the Data Controller. VelocityWave acts as a Data Processor for the
Client. The Expert Advisor acts as a Sub-processor to VelocityWave.
(D) This DPA sets out the terms and conditions governing the processing of Personal Data
by the Expert Advisor to ensure compliance with Data Protection Laws.
IT IS AGREED as follows:
1. Definitions
1.1 In this DPA, the following terms shall have the meanings set out below: * “Client Data”
means any Personal Data provided by or on behalf of a Client, or accessed by the Expert
Advisor on the VelocityWave platform, for the purpose of receiving the Services. * “Data
Controller”, “Data Processor”, “Data Subject”, “Personal Data”, “Personal Data
Breach”, and “Processing” shall have the meanings given to them in the Data Protection
Laws. * “Data Protection Laws” means the UK General Data Protection Regulation (UK
GDPR); the Data Protection Act 2018; the Privacy and Electronic Communications
Regulations 2003 (as amended); and any other applicable data protection and privacy
legislation in force in the United Kingdom. * “Sub-processor” means any third party
appointed by the Expert Advisor to process Client Data. * “Technical and Organisational
Security Measures” means the security measures set out in Annex 2.
2. Roles and Responsibilities
2.1 Roles of the Parties: The Parties acknowledge and agree that with respect to Client
Data: a) The Client is the Data Controller. b) VelocityWave is the Data Processor,
processing Client Data on behalf of the Client. c) The Expert Advisor is appointed by
VelocityWave as a Sub-processor to process Client Data for the purposes of providing the
Services as set out in the Principal Agreement.
2.2 Expert Advisor Obligations: The Expert Advisor agrees that it shall: a) Comply with all
applicable Data Protection Laws in the Processing of Client Data. b) Not process Client Data
for any purpose other than as instructed by the Client via the VelocityWave platform and as
necessary to provide the Services.
3. Details of Data Processing
3.1 The details of the Processing of Client Data under this DPA (as required by Article 28(3)
of the UK GDPR) are set out in Annex 1 to this DPA.
4. Obligations of the Expert Advisor
4.1 Processing Instructions: The Expert Advisor shall only Process Client Data in
accordance with the documented instructions of the Client as communicated via the
VelocityWave platform, unless required to do so by law. The Expert Advisor shall
immediately inform VelocityWave if, in its opinion, an instruction infringes Data Protection
Laws.
4.2 Confidentiality: The Expert Advisor shall ensure that all its personnel authorised to
Process Client Data are subject to a strict duty of confidentiality (whether a contractual or
statutory duty).
4.3 Security: The Expert Advisor shall implement and maintain the Technical and
Organisational Security Measures set out in Annex 2 to protect Client Data against
unauthorised or unlawful Processing and against accidental loss, destruction, damage,
alteration, or disclosure.
4.4 Sub-processing: The Expert Advisor shall not engage any other Sub-processor to
Process Client Data without the prior specific written consent of VelocityWave. Where
consent is given, the Expert Advisor shall enter into a written agreement with the Subprocessor containing data protection obligations no less protective than those in this DPA.
The Expert Advisor remains fully liable to VelocityWave for the performance of the Subprocessor’s obligations.
4.5 Data Subject Rights: The Expert Advisor shall promptly notify VelocityWave, and in any
event within 48 hours, if it receives a request from a Data Subject to exercise their rights
under Data Protection Laws (e.g., right of access, rectification, erasure, etc.). The Expert
Advisor shall provide VelocityWave with all reasonable assistance required to enable
VelocityWave and the Client to respond to such requests. The Expert Advisor shall not
respond directly to the Data Subject.
4.6 Personal Data Breaches: The Expert Advisor shall notify VelocityWave without undue
delay, and in any event within 24 hours of becoming aware, of any Personal Data Breach
affecting Client Data. The notification shall include, at a minimum: a) A description of the
nature of the breach, including the categories and approximate number of Data Subjects and
Personal Data records concerned. b) The name and contact details of a point of contact for
more information. c) A description of the likely consequences of the breach. d) A description
of the measures taken or proposed to be taken to address the breach and mitigate its
possible adverse effects.
4.7 Data Protection Impact Assessments: The Expert Advisor shall provide reasonable
assistance to VelocityWave and the Client with any data protection impact assessments and
prior consultations with supervisory authorities which are required under Data Protection
Laws.
4.8 Data Return and Deletion: Upon termination of the Principal Agreement or completion
of a specific job, the Expert Advisor shall, at VelocityWave’s instruction, securely delete or
return all Client Data from all systems and media in its possession or control. The Expert
Advisor shall provide written certification of such deletion upon request.
4.9 Audits and Inspections: The Expert Advisor shall make available to VelocityWave,
upon reasonable request, all information necessary to demonstrate compliance with this
DPA and shall allow for and contribute to audits, including inspections, conducted by
VelocityWave or another auditor mandated by VelocityWave or the Client.
5. International Data Transfers
5.1 The Expert Advisor shall not transfer any Client Data to a country outside the United
Kingdom without the prior written consent of VelocityWave. Where such a transfer is
permitted, it shall only be done so in compliance with the requirements of the Data
Protection Laws, for example, by ensuring the recipient country has an adequacy decision or
by entering into the UK’s approved standard contractual clauses (e.g., the International Data
Transfer Agreement).
6. Liability and Indemnity
6.1 The Expert Advisor shall be liable for and shall indemnify VelocityWave against all costs,
claims, damages, losses, and expenses incurred by VelocityWave as a result of any failure
by the Expert Advisor, its employees, or its Sub-processors to comply with any of its
obligations under this DPA.
7. General Provisions
7.1 Term: This DPA shall commence on the Effective Date and shall continue in full force
and effect until the termination of the Principal Agreement. 7.2 Governing Law and
Jurisdiction: This DPA and any dispute or claim arising out of it shall be governed by and
construed in accordance with the law of England and Wales. The Parties irrevocably agree
that the courts of England and Wales shall have exclusive jurisdiction to settle any dispute or
claim. 7.3 Severability: If any provision of this DPA is held to be invalid, illegal, or
unenforceable, the remaining provisions shall remain in full force and effect.
IN WITNESS WHEREOF, the Parties have caused this Data Processing Agreement to be
executed by their duly authorised representatives as of the Effective Date.
For and on behalf of VelocityWave Ltd. Signature: _________________________ Name:
_________________________ Title: _________________________ Date:
_________________________
For and on behalf of [Expert Advisor Full Legal Name] Signature:
_________________________ Name: _________________________ Title:
_________________________ Date: _________________________
ANNEX 1: DETAILS OF PROCESSING
Item Description
Subject matter
of the
processing
The provision of professional advisory services by the Expert Advisor to
the Client via the VelocityWave platform as defined in the Principal
Agreement.
Duration of the
processing
For the term of the Principal Agreement and for as long as is necessary
to provide the Services for each specific job or engagement.
Nature and
purpose of the
processing
To access, analyse, manipulate, store, and communicate Client Data as
strictly necessary to deliver the agreed-upon expert advice, reports,
analysis, or other deliverables to the Client. This includes activities such
as reviewing financial records for accounting advice, analysing
employee data for HR consulting, reviewing contracts for legal advice,
etc.
Types of
Personal Data
The specific types of Personal Data will vary depending on the nature of
the Services provided, but may include:
• Identity Data: Name, date of birth, usernames.
• Contact Data: Email address, telephone number, postal address.
• Financial Data: Bank account details, tax information, salary details,
company financial records.
• Employee Data: Employment contracts, performance reviews,
disciplinary records, payroll data, HR records.
• Business Data: Information relating to clients, suppliers, and
customers of the Client.
• Special Category Data: (Only where strictly necessary for the
Services and with explicit consent/legal basis) e.g., health data for
wellness consulting, trade union membership for HR advice.
Categories of
Data Subjects
The specific categories of Data Subjects will vary but may include:
• Employees, contractors, and directors of the Client.
• Customers and clients of the Client.
• Suppliers and business partners of the Client.
• Users of the Client’s websites or services.
ANNEX 2: TECHNICAL AND ORGANISATIONAL SECURITY MEASURES
The Expert Advisor shall implement and maintain the following measures, as a minimum:
1. Access Control:
○ Implement and maintain strong, unique passwords for all systems accessing
Client Data.
○ Use multi-factor authentication (MFA) where available.
○ Ensure that access to Client Data is limited strictly to authorised personnel on
a need-to-know basis.
2. Encryption:
○ All Client Data stored on portable devices (laptops, USB drives) must be
encrypted using industry-standard algorithms (e.g., BitLocker, FileVault).
○ All transmissions of Client Data over public networks (e.g., email, file
transfers) must be encrypted using strong protocols (e.g., TLS 1.2 or higher).
3. Physical Security:
○ Implement appropriate security measures to protect any physical premises
where Client Data is processed or stored.
○ Ensure secure disposal of physical media (e.g., paper records) through crosscut shredding or professional destruction services.
4. Endpoint Security:
○ Maintain up-to-date anti-virus and anti-malware software on all computer
systems used to process Client Data.
○ Ensure operating systems and software are kept updated with the latest
security patches.
5. Data Minimisation and Secure Deletion:
○ Only download or copy Client Data from the VelocityWave platform when
strictly necessary for the performance of the Services.
○ Securely delete any local copies of Client Data immediately upon completion
of the specific job or termination of the contract, in accordance with Section
4.8 of this DPA.
6. Personnel Training:
○ Ensure all personnel authorised to handle Client Data receive regular training
on data protection, security best practices, and their obligations under this
DPA.
7. Incident Response:
○ Maintain an incident response plan to be enacted in the event of a Personal
Data Breach.
EXPERT ADVISOR AGREEMENT
This Expert Advisor Agreement (the “Agreement”) is made and entered into as of [Date] (the
“Effective Date”).
BETWEEN:
VelocityWave Ltd., a company incorporated in England and Wales with registered number
[Company Number] and whose registered office is at [Registered Address] (“VelocityWave”);
AND
[Expert Advisor Full Legal Name], with an address at [Expert Advisor Address] or, if a
company, incorporated in [Jurisdiction] with registered number [Company Number] and whose
registered office is at [Registered Address] (the “Expert Advisor”).
(Each a “Party” and together the “Parties”)
BACKGROUND
(A) VelocityWave operates an online platform designed to provide businesses with tools and
services for growth (the “Platform”). (B) The Expert Advisor has professional expertise and
qualifications in [e.g., Human Resources, Accounting, Law] (the “Field of Expertise”). (C)
VelocityWave wishes to engage the Expert Advisor, and the Expert Advisor wishes to be
engaged, to provide professional services to users of the Platform (“Clients”) on the terms
and conditions set out in this Agreement.
IT IS AGREED as follows:
1. Definitions
1.1 In this Agreement, the following terms shall have the meanings set out below: * “Client”
means a business user of the Platform who requests Services. * “Confidential
Information” means any information of a confidential nature concerning the business,
affairs, customers, clients, or suppliers of a Party or a Client. * “Data Processing
Agreement” or “DPA” means the data processing agreement entered into between the
Parties, which is incorporated by reference into this Agreement. * “Deliverables” means all
documents, products, and materials developed by the Expert Advisor specifically for a Client
as part of an Engagement. * “Engagement” means a specific project or task requested by a
Client and accepted by the Expert Advisor via the Platform. * “Fees” means the fees
payable to the Expert Advisor for the Services, calculated based on the Hourly Rate. *
“Hourly Rate” means the standardised hourly rate for the Expert Advisor’s Field of
Expertise, as determined and communicated by VelocityWave from time to time. *
“Intellectual Property Rights” means patents, rights to inventions, copyright, trademarks,
business names, rights in get-up, goodwill, rights in computer software, database rights, and
all other intellectual property rights, in each case whether registered or unregistered. *
“Platform Fee” means the percentage fee payable to VelocityWave as set out in Clause
5.3. * “Services” means the expert advisory services in the Field of Expertise provided by
the Expert Advisor to Clients.
2. Appointment and Status
2.1 Appointment: VelocityWave engages the Expert Advisor to provide the Services on a
non-exclusive basis in accordance with the terms of this Agreement. 2.2 Independent
Contractor Status: The relationship between the Expert Advisor and VelocityWave is that of
an independent contractor. Nothing in this Agreement shall be construed as creating an
employer-employee relationship, partnership, or joint venture between the Parties. The
Expert Advisor: a) Is solely responsible for all income tax, National Insurance contributions,
and any other tax liabilities arising from the Fees paid under this Agreement. b) Is not
entitled to any employment benefits from VelocityWave, including but not limited to holiday
pay, sick pay, maternity/paternity pay, or pension contributions. c) Shall have complete
control over their working methods, hours, and location, subject to the requirements of any
specific Engagement. d) Shall be responsible for providing their own equipment, materials,
and insurance necessary for the provision of the Services.
3. The Services and Engagements
3.1 Standard of Services: The Expert Advisor shall perform the Services with the highest
level of professionalism, skill, care, and diligence, in accordance with best practice in their
industry and all applicable laws and regulations. 3.2 Engagement Process: a)
VelocityWave will, via the Platform, present the Expert Advisor with requests for Services
from Clients. b) The Expert Advisor is under no obligation to accept any Engagement
offered. c) For each Engagement the Expert Advisor wishes to accept, they shall provide the
Client with a good faith estimate of the number of hours required to complete the task. d) An
Engagement is formed once a Client accepts the Expert Advisor’s estimate via the Platform.
3.3 Authority: The Expert Advisor has no authority to bind VelocityWave or any Client in
any way, or to enter into any contract on behalf of VelocityWave or any Client.
4. VelocityWave's Obligations
4.1 VelocityWave shall: a) Operate and maintain the Platform to facilitate the connection
between Clients and the Expert Advisor. b) Manage the system for Clients to purchase and
redeem "Professional Hours" for the Services. c) Facilitate the payment of Fees to the
Expert Advisor in accordance with Clause 5. d) Provide reasonable support to the Expert
Advisor in relation to their use of the Platform.
5. Fees and Payment
5.1 Fees: In consideration for the Services, the Expert Advisor shall be entitled to Fees
calculated as the Hourly Rate multiplied by the number of hours approved by the Client for
an Engagement. 5.2 Hourly Rate: VelocityWave shall set the Hourly Rate and reserves the
right to review and adjust it on an annual basis, providing the Expert Advisor with at least 30
days' written notice of any change. 5.3 Platform Fee: VelocityWave shall be entitled to a
Platform Fee calculated as one percent (1%) of the total Fees for all Engagements.
However, the Platform Fee shall be waived for the first fifteen (15) hours of Services
provided by the Expert Advisor across all Engagements. 5.4 Payment Process: a) Upon
completion of an Engagement, the Expert Advisor shall mark the Engagement as complete
on the Platform. b) VelocityWave shall facilitate payment to the Expert Advisor within [e.g., 14
days] of the Engagement being marked as complete. c) Payment shall be made to the UK
bank account or other payment method nominated by the Expert Advisor. d) VelocityWave
will provide the Expert Advisor with a statement detailing the Fees earned and any Platform
Fee deducted. 5.5 Taxes: The Expert Advisor is solely responsible for the declaration and
payment of all taxes, duties, and charges arising from the Fees.
6. Confidentiality
6.1 Each Party shall hold the other’s Confidential Information in confidence and not make the
other’s Confidential Information available to any third party, or use the other’s Confidential
Information for any purpose other than the implementation of this Agreement. 6.2 This
obligation extends to all Confidential Information belonging to Clients which is accessed by
the Expert Advisor. 6.3 These obligations shall not apply to any information that is public
knowledge, already in the receiving Party’s possession, or required to be disclosed by law.
7. Data Protection
7.1 The Parties shall comply with all applicable requirements of the Data Protection Laws.
7.2 The Parties agree that the processing of any Personal Data in connection with the
Services is governed by the Data Processing Agreement (DPA) executed between them,
which is hereby incorporated into this Agreement by reference.
8. Intellectual Property
8.1 Pre-Existing IP: Each Party shall retain ownership of all its pre-existing Intellectual
Property Rights. 8.2 Deliverables: The Expert Advisor hereby assigns to the Client, with full
title guarantee, all Intellectual Property Rights in any Deliverables created specifically for that
Client during an Engagement. This assignment shall be effective upon receipt of full
payment for the relevant Engagement. 8.3 Platform IP: The Expert Advisor acknowledges
that all Intellectual Property Rights in and to the Platform are and shall remain the exclusive
property of VelocityWave.
9. Warranties and Indemnity
9.1 Warranties: The Expert Advisor warrants and represents that they: a) Have the full right,
power, and authority to enter into and perform this Agreement. b) Possess the necessary
qualifications, registrations, and experience to provide the Services to the required standard.
c) Will not infringe the Intellectual Property Rights of any third party in the performance of the
Services. 9.2 Indemnity: The Expert Advisor agrees to indemnify and hold harmless
VelocityWave from and against all claims, costs, damages, losses, liabilities, and expenses
(including legal fees) arising out of or in connection with: a) Any breach by the Expert
Advisor of the terms of this Agreement. b) Any claim of negligence or misconduct by the
Expert Advisor in the provision of the Services. c) Any claim by any third party that the
provision of the Services by the Expert Advisor infringes their rights.
10. Term and Termination
10.1 Term: This Agreement shall commence on the Effective Date and shall continue in full
force until terminated by either Party. 10.2 Termination for Convenience: Either Party may
terminate this Agreement for any reason by giving the other Party at least thirty (30) days'
written notice. 10.3 Termination for Cause: Either Party may terminate this Agreement with
immediate effect by giving written notice if the other Party: a) Commits a material breach of
any term of this Agreement and (if such breach is remediable) fails to remedy that breach
within a period of 14 days after being notified in writing to do so. b) Becomes insolvent or is
unable to pay its debts. 10.4 Consequences of Termination: Upon termination of this
Agreement, the Expert Advisor shall cease all Services, complete any Engagements in
progress where instructed by VelocityWave, and both Parties shall return or destroy all
Confidential Information of the other Party. Any outstanding Fees due to the Expert Advisor
shall be paid in accordance with Clause 5.
11. General
11.1 Entire Agreement: This Agreement, together with the Data Processing Agreement,
constitutes the entire agreement between the Parties and supersedes all previous
agreements, promises, or representations. 11.2 Assignment: The Expert Advisor may not
assign, transfer, or subcontract any of its rights or obligations under this Agreement without
the prior written consent of VelocityWave. 11.3 Notices: Any notice given under this
Agreement shall be in writing and sent to the address of the relevant Party as set out at the
beginning of this Agreement. 11.4 Governing Law and Jurisdiction: This Agreement and
any dispute or claim arising out of it shall be governed by and construed in accordance with
the law of England and Wales. The Parties irrevocably agree that the courts of England and
Wales shall have exclusive jurisdiction.
IN WITNESS WHEREOF, the Parties have executed this Agreement as of the Effective
Date.
For and on behalf of VelocityWave Ltd. Signature: _________________________ Name:
_________________________ Title: _________________________ Date:
_________________________
For and on behalf of [Expert Advisor Full Legal Name] Signature:
_________________________ Name: _________________________ Title:
_________________________ Date: _________________________
Professional RoleTier Hourly Rate Paid t Minimum QualificMinimum Experience
Accountant / BooAssociate £25 Part-qualified (AA1-2 years in bookkeeping/junior role
Professional £35 Fully qualified (AA3+ years, VAT returns, draft accounts
Senior £40 Qualified Accounta 5+ years post-qualification, strategic advice
Business ConsulAssociate £30 Business degree o 2-4 years in a business environment
Professional £35 Business degree (MB 5+ years in a strategic/management role
Senior £40 10+ years industr8+ years with demonstrable growth results
Regulations & ComAssociate £30 Legal/business de1-2 years in a compliance support role
Professional £35 Professional certs3-5 years in a direct compliance role
Senior £40 Advanced degrees 7+ years dedicated compliance experience
Social Media ManAssociate £22 Proficiency acros1-2 years managing social accounts
Professional £30 Strong portfolio, prof 3-5 years dedicated social media experience
Senior £38 Extensive portfoli 5+ years in a senior social/digital role
UK Legal ProfessAssociate £30 Law degree (LLB1-2 years in a law firm/in-house role
Professional £35 Experienced paral 4+ years legal experience
Senior £40 Qualified UK solic5+ years post-qualification for solicitors
Website & SEO Dev Associate £28 Strong portfolio, HT 1-2 years web development experience
Professional £35 Extensive portfoli 3-5 years professional development
Senior £40 Complex web ap5+ years in a senior developer role
Health & Safety (Associate £30 IOSH Managing S1-2 years in a role with H&S duties
Professional £35 NEBOSH General 3-5 years in a dedicated H&S role
Senior £40 NEBOSH Diplom5+ years H&S management experience
HR Consultant Associate £28 CIPD Level 3 or equ 1-2 years in HR admin/assistant role
Professional £38 CIPD Level 5 or hi 3-5 years as HR generalist/advisor
Senior £40 CIPD Level 7 (Charter 5+ years in HR management/business partner rol
tner role
Tier Max Hourly Rate ( Minimum QualificMinimum Experience
Associate £25/hr Part-qualified (e.g1-2 years of experience in a bookkeeping or junior accounting rol
Professional £35/hr Fully qualified Boo3+ years of experience. Proven ability to handle VAT returns, pay
Senior £40/hr Qualified Accounta 5+ years post-qualification experience. Experience acting as a fina
 as a financial controller or senior accountant. Able to provide strategic financial advice and forecasting.
Tier Max Hourly Rate ( Minimum QualificMinimum Experience
Associate £30/hr Relevant busines2-4 years in a business environment. Demonstrable experience i
Professional £35/hr Business degree (MB 5+ years of experience in a management or strategic role. Proven
Senior £40/hr Extensive industr8+ years of experience with demonstrable results in business grow
Proven track record of successful projects, such as launching a product, entering a new market, or process improvem
ss growth, turnaround, or strategy implementation for multiple clients. Experience advising at a senior/board level.
provement.
Tier Max Hourly Rate ( Minimum QualificMinimum Experience
Associate £30/hr Legal or business1-2 years in a compliance or regulatory support role. Experience w
Professional £35/hr Relevant degree an 3-5 years of direct experience in a compliance role. Proven ability
Senior £40/hr Advanced degrees 7+ years of dedicated compliance experience, including managing
anaging responses to regulatory bodies and advising senior management on complex compliance strategy.
Tier Max Hourly Rate ( Minimum QualificMinimum Experience
Associate £22/hr Demonstrable prof 1-2 years managing social media accounts (personal brand or sm
Professional £30/hr Strong portfolio o3-5 years of dedicated social media management experience. Pr
Senior £38/hr Extensive portfoli 5+ years in a senior social media or digital marketing role. Experi
ce. Proven ability to develop content strategy, manage ad budgets, and produce performance reports.
Experience managing large-scale campaigns, leading a team, or setting a brand's overall social strategy.
Tier Max Hourly Rate ( Minimum QualificMinimum Experience
Associate £30/hr Law degree (LLB1-2 years of experience in a law firm or in-house legal departmen
Professional £35/hr Experienced paral 4+ years of legal experience. Proven ability to handle complex pa
Senior £40/hr Qualified UK solic5+ years post-qualification experience for solicitors. Able to provi
rtment. Experience with legal research, document drafting, and case management support.
plex paralegal tasks independently or provide expert advice on specific areas of law.
o provide end-to-end management of legal tasks for startups (e.g., investment rounds, IP strategy).
Tier Max Hourly Rate ( Minimum QualificMinimum Experience
Associate £28/hr Strong portfolio o1-2 years of web development experience. Ability to build or custom
Professional £35/hr Extensive portfoli 3-5 years of professional development experience. Proven ability
Senior £40/hr Portfolio of compl 5+ years of experience in a senior developer role. Capable of lea
e of leading development projects, architecting complex solutions, and driving significant organic traffic growth through S
through SEO.
GRAPHQL QUERIES
A. Public-Facing Pages (Unauthenticated)
These queries are generally public and do not require authentication.
1. Homepage
● Query.getHomepageContent: Fetches dynamic content for the homepage (e.g.,
featured testimonials, blog posts). Returns HomepageContent.
● Query.getFeaturedAddons: Fetches a list of featured addons for display. Returns
[Addon].
● Query.getFeaturedExperts: Fetches a list of featured experts. Returns [Expert].
2. Features Page
● Query.getFeaturesPageContent: Fetches detailed descriptions and media for each
platform feature. Returns FeaturesContent.
3. Pricing / Plans Page
● Query.getSubscriptionPlans: Fetches details for all subscription tiers (name, price,
features, token allocation). Returns [SubscriptionPlan].
4. For Experts / Become a Partner Page
● Query.getExpertsPageContent: Fetches marketing content aimed at attracting new
experts. Returns ExpertsPageContent.
5. Blog / Resources
● Query.getBlogPosts(page: Int, limit: Int): Fetches a paginated list of blog posts. Returns
[BlogPost].
● Query.getBlogPost(slug: String): Fetches a single blog post by its URL slug. Returns
BlogPost.
● Query.getBlogCategories: Fetches a list of all blog categories. Returns [Category].
6. Contact Us Page
● Mutation.submitContactForm(input: ContactFormInput!): Submits the contact form
data. Returns SubmissionStatus.
7. Add-on Marketplace Showcase
● Query.getPublicAddons(filter: AddonFilter, page: Int, limit: Int): Fetches a searchable
and paginated list of public addons. Returns [Addon].
8. Expert Directory Showcase
● Query.getPublicExperts(filter: ExpertFilter, page: Int, limit: Int): Fetches a searchable
and paginated list of public experts. Returns [Expert].
B. Platform User (Client) Dashboard & Tools
These operations require user authentication.
11. Welcome / Onboarding Wizard
● Mutation.completeOnboarding(input: OnboardingInput!): Submits the user's initial
setup information. Returns User.
12. Client Dashboard
● Query.getClientDashboard: Fetches aggregated data for the main dashboard
(analytics snapshot, notifications, token balance). Returns ClientDashboard.
13. My Sites
● Query.getUserWebsites: Fetches a list of all websites created by the user. Returns
[Website].
● Mutation.createWebsite(name: String!): Creates a new website record. Returns
Website.
● Mutation.deleteWebsite(id: ID!): Deletes a user's website. Returns Status.
14. Website Builder Interface
● Query.getWebsiteData(id: ID!): Fetches all pages, components, and configurations for
a specific website. Returns Website.
● Query.getAvailableAddons: Fetches all addons the user is entitled to use. Returns
[Addon].
● Query.getCodeSnippets: Fetches the library of predefined code snippets. Returns
[CodeSnippet].
● Mutation.saveWebsiteData(id: ID!, input: WebsiteInput!): Saves the entire state of the
website being edited. Returns Website.
15. Site Settings
● Mutation.updateSiteSettings(id: ID!, input: SiteSettingsInput!): Updates general, SEO,
or domain settings. Returns Website.
● Mutation.regenerateApiKey(id: ID!): Generates a new API key for a published site.
Returns String.
16. Publishing Status & History
● Mutation.publishWebsite(id: ID!): Triggers the compilation and publishing process.
Returns PublishStatus.
● Query.getPublishHistory(id: ID!): Fetches a log of all previous publications for a site.
Returns [PublishRecord].
21. Browse Experts
● Query.searchExperts(filter: ExpertFilter, page: Int, limit: Int): Fetches a searchable and
paginated list of all experts. Returns [Expert].
23. My Engagements / My Jobs
● Query.getClientEngagements(status: EngagementStatus): Fetches a list of the client's
projects with experts. Returns [Engagement].
● Mutation.createEngagement(expertId: ID!, details: String!): Initiates a new job request
with an expert. Returns Engagement.
24. Engagement Detail Page
● Query.getEngagementDetails(id: ID!): Fetches the full details, messages, and files for a
specific engagement. Returns Engagement.
● Mutation.sendEngagementMessage(id: ID!, message: String!, file: Upload): Sends a
message or file in the project chat. Returns Message.
● Mutation.approveEngagementCompletion(id: ID!): Marks a job as complete and
authorizes token release. Returns Engagement.
● Subscription.onNewEngagementMessage(id: ID!): Listens for new messages in real-time
for a specific engagement. Returns Message.
25. Browse Add-ons
● Query.searchAddons(filter: AddonFilter, page: Int, limit: Int): Fetches a searchable and
paginated list of all addons. Returns [Addon].
● Mutation.purchaseAddon(addonId: ID!): Purchases or subscribes to a paid addon
using tokens or saved payment method. Returns User.
30. Subscription & Billing
● Query.getBillingInfo: Fetches the user's current plan, billing history, and saved
payment methods. Returns BillingInfo.
● Mutation.changeSubscription(planId: ID!): Upgrades or downgrades the user's
subscription plan. Returns User.
31. Token Management
● Query.getTokenBalance: Fetches the user's current token balance and transaction
history. Returns TokenInfo.
● Mutation.purchaseTokens(amount: Int!): Buys a token top-up. Returns TokenInfo.
C. Expert Advisor (Freelancer) Dashboard
These operations require expert authentication.
33. Expert Application Form
● Mutation.submitExpertApplication(input: ExpertApplicationInput!): Submits the
application form data. Returns ApplicationStatus.
35. Expert Dashboard
● Query.getExpertDashboard: Fetches aggregated data for the expert's dashboard
(earnings, new requests, active projects). Returns ExpertDashboard.
36. My Public Profile
● Query.getExpertProfile: Fetches the expert's own profile for editing. Returns Expert.
● Mutation.updateExpertProfile(input: ExpertProfileInput!): Updates the expert's public
profile. Returns Expert.
37. Engagement Requests
● Query.getEngagementRequests: Fetches a queue of new job requests from clients.
Returns [Engagement].
● Mutation.acceptEngagement(id: ID!, estimatedHours: Float!): Accepts a job and
provides a time estimate. Returns Engagement.
● Mutation.declineEngagement(id: ID!, reason: String): Declines a job request. Returns
Engagement.
39. My Earnings & Payouts
● Query.getEarnings: Fetches the expert's earnings history and payout settings. Returns
EarningsInfo.
● Mutation.updatePayoutSettings(input: PayoutSettingsInput!): Updates the expert's bank
or payment details. Returns Expert.
D. Platform Admin Dashboard (Internal)
These operations require admin authentication and have elevated privileges.
41. Admin Master Dashboard
● Query.getPlatformKPIs: Fetches high-level platform metrics. Returns PlatformStats.
42. User Management (Clients)
● Query.adminSearchUsers(filter: UserFilter): Searches for clients. Returns [User].
● Mutation.adminUpdateUser(userId: ID!, input: UserAdminInput!): Updates a user's
profile or subscription. Returns User.
● Mutation.adminImpersonateUser(userId: ID!): Generates a temporary token to log in as
a user for support. Returns String.
43. Expert Management
● Query.adminGetExpertApplications: Fetches pending expert applications. Returns
[ExpertApplication].
● Mutation.adminApproveExpertApplication(id: ID!, tier: ExpertTier!): Approves an expert
and assigns them a tier. Returns Expert.
● Mutation.adminUpdateExpert(expertId: ID!, input: ExpertAdminInput!): Updates an
expert's status or tier. Returns Expert.
44. Marketplace Management
● Query.adminGetAddonReviewQueue: Fetches addons pending review. Returns
[Addon].
● Mutation.adminApproveAddon(id: ID!): Approves a third-party addon for the
marketplace. Returns Addon.
● Mutation.adminManageCodeSnippet(input: SnippetInput!): Creates, updates, or deletes
a predefined code snippet. Returns CodeSnippet.
45. Platform Configuration
● Mutation.adminUpdateSubscriptionPlan(planId: ID!, input: PlanInput!): Edits the details
of a subscription plan. Returns SubscriptionPlan.
E. Shared / Utility Pages
48. Login Page
● Mutation.login(email: String!, password: String!): Authenticates a user and returns a
session token (JWT). Returns AuthPayload.
49. Sign Up Page
● Mutation.register(input: RegistrationInput!): Creates a new client account. Returns
AuthPayload.
50. Forgot Password / Reset Password Flow
● Mutation.requestPasswordReset(email: String!): Initiates the password reset process.
Returns Status.
● Mutation.resetPassword(token: String!, newPassword: String!): Sets a new password
using a reset token. Returns Status.
Tier Max Hourly Rate ( Minimum QualificMinimum Experience
Associate £30/hr IOSH Managing S1-2 years in a role with H&S responsibilities. Experience assisting
Professional £35/hr NEBOSH National 3-5 years in a dedicated H&S role. Proven ability to develop and i
Senior £40/hr NEBOSH Diplom5+ years of H&S management experience. Capable of creating an
ting and managing a full H&S management system (e.g., ISO 45001) and advising on complex compliance issues.
ues.
Tier Max Hourly Rate ( Minimum QualificMinimum Experience
Associate £28/hr CIPD Level 3 Fou1-2 years of experience in an HR administrator or assistant role.
Professional £38/hr CIPD Level 5 Ass3-5 years of experience as an HR generalist or advisor. Proven ab
Senior £40/hr CIPD Level 7 Adv5+ years in an HR management or business partner role. Experien
t role. Experience with HR documentation (e.g., offer letters), onboarding processes, and HRIS data entry.
oven ability to handle employee relations issues, manage recruitment cycles, and develop HR policies.
xperience leading complex HR projects (e.g., restructuring, TUPE transfers) and providing strategic HR advice to lea
e to leadership.
Product Requirements Document: Velocity Platform
1. Introduction
Velocity Platform is a dynamic, API-first platform designed for building and deploying multipage websites and feature-rich user dashboards. Its core philosophy emphasizes simplicity
in the platform itself, with complexity and customization driven by a powerful addon system.
Crucially, the Velocity Platform's own public-facing user interface (including the website
builder and dashboards) will also be constructed using this component-based system,
allowing platform administrators to customize its appearance and functionality using the
same tools available to end-users.
Users will leverage a component-based website builder, drawing components from an
Addon Builder or a library of Predefined Code Snippets, and an intuitive drag-and-drop
addon builder (with client-side UI configuration via Rete.js) capable of integrating with,
manipulating, and transforming data from common external API technologies or data
sources.
All computations, business logic, and complex data transformations will be performed solely
by the platform's API/server-side components; the frontend will primarily serve as a visual UI
for display and triggering server-side operations. All client-side addon logic will operate
within a secure, sandboxed environment, transitioning to a production state only after
thorough review and approval by platform administrators.
Published websites will include a unique, securely shipped API key to enable dynamic
interactions and data retrieval via HTMX calls back to the platform's API. The platform will
utilize a specific, modern tech stack (Bootstrap, HTMX, Handlebars.js, Rete.js, PostgreSQL
with JSONB for addon data and storage for code snippets, and Go with GraphQL hosted
directly on the physical machine) to ensure efficient development and deployment.2. Goals
● Empower Users: Enable users with varying technical skills to build and manage
websites and dashboards using a flexible component system.
● Foster Extensibility: Provide a robust addon builder for users and third parties to
create dynamic "Web Addons," alongside a curated library of "Predefined Code
Snippets."
● API-First Architecture: Ensure all platform operations and data transfers are
handled via a secure and well-documented GraphQL API built with Go, including
auto-generated endpoints (exposed as GraphQL fields/types) for addons and
retrieval of code snippets. All significant computations, business logic, and complex
data transformations will reside solely within the API.
● Simplified Deployment: Allow websites to be compiled into static HTML/HTMX, JS,
and CSS for easy publishing to external hosting.
● Maintain Platform Simplicity: Focus complexity within addons rather than the core
platform, providing a straightforward user experience for core platform functionalities.
● Secure and Compliant Data Management: Implement multi-tenancy with strict data
isolation, ensuring GDPR and DPA adherence, and encrypting all data.
● Enable Flexible Monetization: Provide robust mechanisms for tiered platform
access (Free, Basic, Pro, Enterprise) and for third-party addons (free/paid) from day
one. Resource limits will be configurable per subscription level. Platform
subscriptions and addon purchases will be managed through a dedicated, platformmanaged addon.
● Self-Configurable Platform UI: Allow platform administrators to customize the
platform's own frontend using its internal component-based building tools.
● External Data Integration: Enable Addon Creators to securely integrate with,
manipulate, and transform data from common external API technologies and data
sources within their addons, with server-side mediation.
● Secure Published Site API Access: Ensure secure and controlled API access for
published websites back to the platform for dynamic content and interactions using a
unique API key.
● Controlled Addon Execution Environment: Implement a secure, isolated sandbox
environment for addon development and testing, requiring explicit approval for
promotion to production, and allowing admins to control/revert addon status for
safety.
● Automated Error Notification: Provide automated notifications of crashes or errors
to platform administrators and relevant addon developers.
3. Target Users
● Platform Owner: Responsible for overall platform management, maintenance, core
feature development, curating Predefined Code Snippets, ensuring compliance, and
overseeing monetization strategies.
● Platform Admins: Manage user accounts, platform settings, review and approve
addons, manage the library of Predefined Code Snippets, monitor platform health,
configure platform tiers and addon pricing, and customize the platform's own publicfacing UI. They are also responsible for the review and approval of addons
transitioning from sandbox to production, and for managing addon statuses (e.g.,
disabling, reverting to sandbox) for safety. They will receive automated error
notifications.
● Platform Users (Website/Dashboard Creators): Individuals or businesses using
the Velocity Platform to build, deploy, and manage their websites and internal
dashboards, choosing platform tiers and acquiring addons. They will also manage
the publishing of their websites, which involves API key generation.
● Third-Party Developers (Addon Creators): Developers creating, publishing, and
potentially distributing dynamic, free, or paid addons for the Velocity Platform, now
with the ability to integrate external data sources and understanding the sandbox
development environment. They will receive automated error notifications related to
their specific addons.
4. System Architecture
The Velocity Platform will be built exclusively using the following components:
● Platform Frontend (Public-Facing UI): This refers to the Velocity Platform's own
administrative and user-facing interfaces (e.g., dashboards, the site builder interface
itself). It will be built and managed using the platform's own Component-Based
Website Builder, leveraging Web Addons and Predefined Code Snippets, and
compiled into static HTML, HTMX, JS, and CSS (Bootstrap). This ensures the
platform's UI is as flexible and customizable as any user-created website.
● Webpage Creator (Frontend - Site Builder): This is the tool within the Platform
Frontend that allows users (and platform admins for the platform's own UI) to visually
assemble multi-page websites. It utilizes a custom frontend built with Bootstrap
(HTML, CSS, JS) for the visual, component-based website building interface. Runs
client-side. This builder will rely on pre-built Bootstrap components and a theming
system to ensure consistency and customizability.
● Platform Frontend Framework: HTMX - For dynamic and interactive user interfaces
within the compiled platform frontend itself. Runs client-side.
● Platform Frontend Templating: Handlebars.js - For rendering dynamic content
within the HTMX-driven platform frontend. Runs client-side.
● Addon Builder Interface: Rete.js - To provide the drag-and-drop interface for
creating dynamic "Web Addons" and "Dashboard Addons." Runs entirely client-side.
Rete.js will serve as a visual configuration tool to define server-side data processing
workflows and API call sequences for addons. All computations, complex data
manipulations, and business logic stemming from these definitions will be performed
by the Go GraphQL API. Addon configurations will be serialized and loaded via API
calls. During development and testing, addons and their configured logic will operate
within a strictly sandboxed client-side environment.
● Database: PostgreSQL - For all persistent data storage, including:
○ User accounts, site configurations, addon definitions (Rete.js configurations).
○ Predefined Code Snippets (HTML, CSS, JS content and metadata) for the
website builder.
○ Tenant data.
○ Data specific to each addon instance will be stored as JSONB.
○ Monetization Data: User tier information, addon pricing details, addon
purchase/subscription records.
○ Platform UI Configuration Data: The structure and content of the platform's
own frontend (as built by admins using the Website Builder).
○ External Data Source Configurations: Securely stored connection details and
credentials for external APIs and data sources.
○ Published Website API Keys: Securely generated, site-specific API keys for
published websites.
○ Addon Deployment Status: Addon status (e.g., 'development', 'pending
review', 'approved', 'rejected', 'disabled', 'sandbox') and associated
environment configurations (e.g., sandbox vs. production API endpoints).
● API Backbone: Go with GraphQL - This will serve as the robust backend, handling:
○ Data transfer for all platform operations through a single GraphQL endpoint.
○ All computations, complex data manipulations, and business logic.
○ Saving and loading addon configurations from Rete.js via GraphQL
mutations.
○ Retrieval of Predefined Code Snippets for the website builder via GraphQL
queries.
○ Automatic generation and management of GraphQL fields/types for each
created addon, interacting with their respective JSONB data stores.
○ Authentication and Authorization for platform access and external published
sites via GraphQL. Published websites will use a dedicated, securely
generated API key for authentication when making calls back to the platform.
○ Interaction with the PostgreSQL database.
○ Ensuring data encryption for all data handled.
○ Monetization Logic: Enforcement of platform tier-based feature access and
addon purchase/subscription entitlements based on configurable resource
limits per subscription level. This logic will primarily interact with a dedicated,
platform-managed payment addon.
○ Secure External Data Integration: The GraphQL API will serve as the secure
intermediary for all external data source integrations initiated by addons. It will
manage authentication credentials for external APIs, perform server-side data
fetching, aggregation, and complex transformations before exposing the data
via GraphQL.
○ Addon Environment Enforcement: The API will enforce strict access controls
based on an addon's deployment status (development sandbox vs.
production) and its overall active/disabled status, limiting the scope of API
calls accordingly
Feature Free Plan Business From Hom Sle Tradies Plan
Monthly Fee £0 £10 £20
Included Monthly Tokens (£1 = 1 Token) 0 0 0
Website Builder 3 Pages ✓ ✓
Custom Domain & Email - ✓ ✓
Basic Add-ons - ✓ ✓
Trade-Specific Add-ons - - ✓
Automated Payments (Stripe/GoCardless)- - ✓
Exclusive Resource Library - - -
Grant & Funding Alerts - - -
Custom Document Creation - - -
Dedicated Support Line - - -
Dedicated Account Manager - - -
Business Phone & Broadband - - -
Utilities Expert & Cost Optimisation - - -
Add-on Builder & Custom Integrations - - -
Growth Plan Pro Plan Enterprise Plan
£55 £139 £350
40 100 (+£25 per employee add-on) 200 (+£25 per employee add-on)
✓ ✓ ✓
✓ ✓ ✓
✓ ✓ ✓
✓ ✓ ✓
✓ ✓ ✓
✓ ✓ ✓
✓ ✓ ✓
- Up to 3/quarter ✓
- ✓ ✓
- - ✓
- - ✓
- - ✓
- - ✓
VELOCITYWAVE PLATFORM COOKIE POLICY
Last Updated: 5 July 2025
1. Introduction
This Cookie Policy explains what cookies are, how VelocityWave Ltd. (“VelocityWave”,
“we”, “us”, or “our”) uses them on our website (www.velocitywave.com) and our associated
platform (collectively, the “Platform”), and your choices regarding their use.
This policy should be read alongside our main Privacy Policy, which provides more detail on
how we process your personal information.
2. What Are Cookies?
Cookies are small text files that are placed on your computer, smartphone, or other device
when you visit a website. They are widely used to make websites work, or work more
efficiently, as well as to provide information to the owners of the site.
Cookies can be:
● Session cookies: These are temporary and expire once you close your browser.
● Persistent cookies: These remain on your device for a set period or until you delete
them.
● First-party cookies: These are set by the website you are visiting (in this case,
VelocityWave).
● Third-party cookies: These are set by a domain other than the one you are visiting,
for example, by our analytics or advertising partners.
We may also use similar technologies like web beacons, pixels, and local storage, which
function in a similar way. For simplicity, we refer to all of these as “cookies” in this policy.
3. How We Use Cookies
We use cookies for several purposes. We have categorised them below, and you can
manage your preferences for each category through our Cookie Consent Manager.
a) Strictly Necessary Cookies These cookies are essential for you to browse the Platform
and use its features, such as accessing secure areas, processing payments, or managing
your user session. The law permits us to set these cookies without your consent. If you
disable these via your browser, parts of our Platform will not function.
b) Performance and Analytics Cookies These cookies collect anonymous information
about how you and other visitors use our Platform. They help us understand which pages
are most popular, how users navigate the site, and if they receive error messages. The
information gathered is aggregated and anonymous and is used purely to improve how our
Platform works. We will only set these cookies if you give us your consent.
c) Functionality Cookies These cookies allow the Platform to remember choices you make
(such as your username, language, or the region you are in) and provide enhanced, more
personal features. For example, they can be used to remember your login details so you
don't have to sign in every time. We will only set these cookies if you give us your consent.
d) Marketing and Targeting Cookies These cookies are used to deliver advertisements
that are more relevant to you and your interests. They are also used to limit the number of
times you see an advertisement and help measure the effectiveness of advertising
campaigns. They are usually placed by advertising networks with our permission. They
remember that you have visited a website, and this information is shared with other
organisations such as advertisers. We will only set these cookies if you give us your
consent.
4. Cookies We Use
Below is a list of some of the key cookies we use. Please note that this list is illustrative and
the specific cookies in use may change. The most up-to-date and complete list can always
be found in our Cookie Consent Manager.
Cookie Name(s) Provider Purpose Duration
sessionid, csrftoken VelocityWave Strictly Necessary: Manages your
secure login session and protects
against cross-site request forgery.
Session
stripe_mid,
stripe_sid
Stripe Strictly Necessary: Used by our
payment processor for fraud prevention
and to process payments securely.
1 Year
_ga, _gid Google
Analytics
Performance: Helps us distinguish
users and analyse website traffic and
user behaviour anonymously.
2 Years
_gat Google
Analytics
Performance: Used to throttle request
rate to Google's servers.
1 Minute
remember_me_toke
n
VelocityWave Functionality: Remembers your login
details if you select "Remember Me".
30 Days
_fbp, fr Facebook/Met
a
Marketing: Used to deliver, measure,
and improve the relevancy of ads on
the Facebook platform.
3 Months
_gcl_au Google Ads Marketing: Used by Google AdSense
for experimenting with advertising
efficiency.
3 Months
lidc, li_sugr LinkedIn Marketing: Used for tracking the use of
embedded services and for ad
targeting on LinkedIn.
1 Day / 3
Months
5. Your Choices and How to Manage Cookies
You are in control of how cookies are used on your device.
● Our Cookie Consent Manager: When you first visit our Platform, you will see a
banner asking for your consent to set non-essential cookies. You can accept all,
reject all, or manage your preferences for each category of cookie. You can change
your mind and update your preferences at any time by clicking on the "Manage
Cookies" link in the footer of our website.
● Browser Settings: Most web browsers allow some control of most cookies through
the browser settings. You can set your browser to block cookies or to alert you when
cookies are being sent to your device. Please be aware that if you block all cookies
(including strictly necessary ones), you may not be able to access all or parts of our
Platform.
Find out how to manage cookies on popular browsers:
○ Google Chrome
○ Microsoft Edge
○ Mozilla Firefox
○ Apple Safari
● Opting out of Analytics and Advertising: To opt out of being tracked by Google
Analytics across all websites, visit http://tools.google.com/dlpage/gaoptout. You can
also opt out of interest-based advertising from many third-party ad networks by
visiting the Digital Advertising Alliance’s opt-out portal.
6. Cookies on Client Websites
Please note that this Cookie Policy applies only to the VelocityWave Platform itself.
Our Clients who build and operate websites using our Platform are responsible for their own
use of cookies and tracking technologies on their sites. They are required by law and by our
terms of service to have their own cookie policy and to obtain valid consent from their End
Users. VelocityWave is not responsible for the cookie practices of our Clients' websites.
7. Changes to This Cookie Policy
We may update this policy from time to time to reflect changes in technology, law, or our
business operations. When we do, we will revise the "Last Updated" date at the top of this
policy. We encourage you to review this policy periodically to stay informed about our use of
cookies.
8. Contact Us
If you have any questions or comments about this Cookie Policy or our data practices,
please contact us at:
VelocityWave Ltd. Attn: Data Protection Officer [Company Address] [City, Postcode] United
Kingdom
Email: <EMAIL>
VELOCITYWAVE PLATFORM PRIVACY POLICY
Last Updated: 5 July 2025
1. Introduction
Welcome to VelocityWave. This Privacy Policy explains how VelocityWave Ltd.
(“VelocityWave”, “we”, “us”, or “our”) collects, uses, shares, and protects information in
relation to our website (www.velocitywave.com), our platform, and all related tools and
services (collectively, the “Platform”).
Our commitment is to be transparent about the data we collect about you, how it is used,
and with whom it is shared. This policy applies to all users of the Platform, including
business users who create accounts (“Clients”), their website visitors (“End Users”), and
professional advisors who partner with us (“Expert Advisors”).
VelocityWave Ltd. is a company registered in England and Wales with registered number
[Company Number] at [Registered Address]. For the purpose of the Data Protection Laws, our
ICO registration number is [ICO Registration Number].
2. Our Role: Data Controller vs. Data Processor
It is crucial to understand our role in relation to your data:
● VelocityWave as Data Controller: We act as the Data Controller when we process
personal data for our own purposes. This includes information you provide when you
create an account, data from our Expert Advisors, and data from visitors to our main
website (www.velocitywave.com). We determine the purposes and means of
processing this data.
● VelocityWave as Data Processor: When you, as a Client, use our Platform to build
your website or use our business tools, you upload content which may contain
personal data of your own customers, employees, or users (End Users). For this
"Client Content," you are the Data Controller, and VelocityWave acts as the Data
Processor. We only process this data on your behalf, in accordance with your
instructions and our Data Processing Agreement. This policy does not cover how our
Clients handle their End Users' data; Clients must provide their own privacy policies
for their websites.
3. What Information We Collect
We collect different types of information depending on your interaction with our Platform.
a) Information You Provide Directly to Us:
● For Clients (Business Users):
○ Account Information: When you register for an account, you provide us with
your name, email address, password, phone number, and business name.
○ Billing Information: To process payments for our services, we and our thirdparty payment processors (e.g., Stripe) may collect your payment card
details, billing address, and transaction history.
○ Communications: If you contact us directly for support or feedback, we will
keep a record of that correspondence.
● For Expert Advisors:
○ Application and Verification Information: To join our platform, you provide
us with your name, contact details, professional qualifications, proof of identity
(like a passport or driving licence), proof of address, business registration
details, and professional indemnity insurance, as detailed in our Expert
Advisor Agreement.
○ Payment Information: We collect your bank account or other payment
details to process your fees.
● For End Users (Visitors to our Clients' websites):
○ As a Data Processor, we do not directly collect personal information from End
Users for our own purposes. We may process information that our Clients
collect, such as IP addresses, order details, or form submissions, as part of
providing the service to the Client.
b) Information We Collect Automatically:
● Usage and Log Data: When you use the Platform, we automatically collect
information about your device and your usage, including your IP address, browser
type, operating system, pages viewed, time spent on pages, links clicked, and the
page you visited before navigating to our Platform.
● Cookies and Tracking Technologies: We use cookies, web beacons, and similar
technologies to operate and improve the Platform, analyse performance, and for
marketing purposes. For more information, please see our Cookie Policy (a link will
be provided on the platform).
4. How We Use Your Information and Our Lawful Basis
We only use your personal data when the law allows us to. Most commonly, we will use your
personal data in the following circumstances and on the following lawful bases:
Purpose of Processing Type of Data Lawful Basis under UK
GDPR
To provide the core Platform
services and manage your
account.
Account Information,
Usage Data
Performance of a Contract
with you.
To process payments for
subscriptions and services.
Billing Information,
Account Information
Performance of a Contract
with you.
To verify the identity and
credentials of Expert Advisors.
Application &
Verification
Information
Performance of a Contract
and Legal Obligation (for
KYC/AML).
To facilitate payments to Expert
Advisors.
Payment Information Performance of a Contract.
To communicate with you about
your account, support requests,
and service updates.
Account Information,
Communications
Performance of a Contract
and our Legitimate Interest
(to keep you informed).
To improve and secure our
Platform, and to analyse usage.
Usage and Log
Data, Cookie Data
Our Legitimate Interest (to
maintain and develop our
service and ensure its
security).
To send you marketing
communications about new
features or services.
Account Information
(Email)
Your Consent, which you
can withdraw at any time via
the unsubscribe link in the
email.
To comply with our legal and
regulatory obligations (e.g.,
financial record keeping, fraud
prevention).
Account Information,
Billing Information
Legal Obligation.
5. Who We Share Your Information With
We do not sell your personal data. We may share your information with the following
categories of third parties:
● Expert Advisors: If a Client requests a service from an expert, we will share
necessary Client information (like name, business name, and the nature of the
request) with the matched Expert Advisor so they can provide the service.
● Third-Party Service Providers: We engage trusted third-party companies to
perform services on our behalf. These providers are our processors or subprocessors and only have access to your information to perform these tasks under a
strict data processing agreement with us. This includes:
○ Cloud Hosting Providers (e.g., Amazon Web Services, Google Cloud)
○ Payment Processors (e.g., Stripe)
○ Analytics Providers (e.g., Google Analytics)
○ Email and Communication Providers (e.g., Mailchimp, Twilio)
○ Customer Support Software Providers (e.g., Zendesk)
● Legal and Law Enforcement: We may disclose your information if required to do so
by law or in the good faith belief that such action is necessary to comply with a legal
obligation, protect and defend our rights or property, or prevent fraud.
● Business Transfers: If VelocityWave is involved in a merger, acquisition, or sale of
assets, your information may be transferred as part of that transaction. We will notify
you of any such change in ownership or control of your personal data.
6. International Data Transfers
We primarily store and process your data within the United Kingdom (UK). However, some
of our third-party service providers may be based outside the UK.
When we transfer your personal data out of the UK, we ensure a similar degree of protection
is afforded to it by ensuring at least one of the following safeguards is implemented:
● The transfer is to a country that has been deemed to provide an adequate level of
protection for personal data by the UK authorities.
● We use specific contracts approved by the UK authorities which give personal data
the same protection it has in the UK, such as the International Data Transfer
Agreement (IDTA).
7. Data Security
We have implemented appropriate technical and organisational security measures designed
to protect the security of any personal information we process. This includes encryption of
data in transit and at rest, access controls, and regular security reviews. However, please
remember that no method of transmission over the Internet or method of electronic storage
is 100% secure.
8. Data Retention
We will only retain your personal data for as long as is necessary to fulfil the purposes we
collected it for, including for the purposes of satisfying any legal, accounting, or reporting
requirements.
● Account Data: We retain your account data for as long as your account is active and
for a period of up to 6 years after your account is closed to comply with our legal
obligations (e.g., for tax and accounting purposes).
● Client Content: We process Client Content for as long as the Client’s account is
active. Upon account termination, we will delete Client Content in accordance with
the terms of our agreement.
9. Your Data Protection Rights
Under UK data protection law, you have rights including:
● Right of access: You have the right to request a copy of the personal information we
hold about you.
● Right to rectification: You have the right to request correction of any inaccurate or
incomplete personal information.
● Right to erasure (‘right to be forgotten’): You have the right to request that we
delete your personal data, under certain conditions.
● Right to restrict processing: You have the right to request that we restrict the
processing of your personal data, under certain conditions.
● Right to data portability: You have the right to request that we transfer the data that
we have collected to another organisation, or directly to you, under certain
conditions.
● Right to object: You have the right to object to our processing of your personal data
where we are relying on a legitimate interest.
● Rights in relation to automated decision making and profiling: You have the
right not to be subject to a decision based solely on automated processing.
● Right to withdraw consent: Where we rely on your consent to process your data,
you have the right to withdraw that consent at any time.
To exercise any of these rights, please contact <NAME_EMAIL>.
10. Right to Lodge a Complaint
You have the right to lodge a complaint at any time with the Information Commissioner's
Office (ICO), the UK's supervisory authority for data protection issues. We would, however,
appreciate the chance to deal with your concerns before you approach the ICO, so please
contact us in the first instance.
Information Commissioner’s Office (ICO): Website: www.ico.org.uk Telephone: 0303 123
1113
11. Children's Privacy
Our Platform is not intended for or directed at individuals under the age of 18. We do not
knowingly collect personal information from children under 18.
12. Changes to This Privacy Policy
We may update this Privacy Policy from time to time. We will notify you of any changes by
posting the new policy on this page and updating the “Last Updated” date. We may also
notify you via email or through the Platform.
13. How to Contact Us
If you have any questions, comments, or requests regarding this Privacy Policy or our data
practices, please do not hesitate to contact us at:
VelocityWave Ltd. Attn: Data Protection Officer [Company Address] [City, Postcode] United
Kingdom
Email: <EMAIL>
VELOCITYWAVE PLATFORM TERMS OF SERVICE
Last Updated: 5 July 2025
Welcome to VelocityWave. These Terms of Service (“Terms”) govern your access to and
use of the website, platform, tools, and services (collectively, the “Platform”) provided by
VelocityWave Ltd. (“VelocityWave”, “we”, “us”, or “our”).
By creating an account, or by accessing or using our Platform, you agree to be bound
by these Terms, our Privacy Policy, our Cookie Policy, and our Data Processing
Agreement (if applicable), which are all incorporated by reference. Please read them
carefully.
If you are using the Platform on behalf of a company or other legal entity, you represent that
you have the authority to bind that entity to these Terms, in which case “you” or “your” will
refer to that entity. If you do not agree to these Terms, you must not access or use our
Platform.
1. Definitions
● “Account” means the account you create to access the Platform.
● “Client” means a User who signs up for the Platform to build a website, use our
business tools, or engage Expert Advisors.
● “Content” means any and all information, data, text, software, music, sound,
photographs, graphics, video, messages, tags, or other materials that you post,
upload, or display on or through the Platform.
● “Expert Advisor” means an independent, third-party professional who offers their
services to Clients via the Platform.
● “Services” means the specific features and functionalities offered by VelocityWave,
including but not limited to the website builder, business planning tools, hosting, and
the marketplace for connecting with Expert Advisors.
● “User”, “you”, “your” means any person or entity who accesses or uses the
Platform, including Clients and Expert Advisors.
2. User Accounts
2.1. Registration: To use most features of the Platform, you must register for an Account.
You agree to provide accurate, current, and complete information during the registration
process and to update such information to keep it accurate, current, and complete. 2.2.
Eligibility: You must be at least 18 years old and capable of entering into a legally binding
contract to use our Platform. 2.3. Account Security: You are responsible for safeguarding
your password and for all activities that occur under your Account. You must notify
VelocityWave immediately of any unauthorized use of your Account. VelocityWave is not
liable for any loss or damage arising from your failure to comply with this security obligation.
3. The Services
3.1. Service Provision: Subject to your compliance with these Terms, we grant you a
limited, non-exclusive, non-transferable, revocable licence to access and use the Platform
and Services for your internal business purposes. 3.2. Modification of Services: We are
constantly changing and improving our Services. We reserve the right to modify, suspend, or
discontinue any part of the Services at any time, with or without notice. We will not be liable
to you or to any third party for any modification, suspension, or discontinuance of the
Services.
4. Subscriptions, Fees, and Payment
4.1. Subscriptions: We offer various subscription plans, including a free tier and paid
subscription plans (“Subscriptions”). The features and fees for each plan are described on
our website. 4.2. Billing: By purchasing a Subscription, you authorise us to charge your
nominated payment method on a recurring basis (e.g., monthly or annually) without further
authorisation from you, until you cancel your Subscription. 4.3. Automatic Renewal: Your
paid Subscription will automatically renew at the end of each billing cycle for a further period
of the same duration, unless you cancel it through your Account settings before the end of
the current billing cycle. 4.4. Price Changes: We reserve the right to change our
subscription fees. We will provide you with at least 30 days’ notice of any fee changes. Your
continued use of the Service after the price change comes into effect constitutes your
agreement to pay the modified fee. 4.5. Cancellation: You may cancel your Subscription at
any time through your Account settings. The cancellation will take effect at the end of your
current billing period. 4.6. No Refunds: Subscription fees are non-refundable. We do not
provide refunds or credits for any partial subscription periods or unused Services, unless
required by law.
5. User Content and Conduct
5.1. Your Content: You retain all ownership rights to your Content. We do not claim any
ownership over your Content. 5.2. Licence to Us: By uploading Content to the Platform, you
grant VelocityWave a worldwide, non-exclusive, royalty-free, sublicensable, and transferable
licence to use, reproduce, distribute, prepare derivative works of, display, and perform your
Content solely for the purposes of operating, providing, promoting, and improving the
Platform and Services. 5.3. Acceptable Use: You agree not to use the Platform to post or
transmit any Content or engage in any conduct that: a) Is unlawful, fraudulent, harmful,
threatening, abusive, harassing, defamatory, vulgar, obscene, or otherwise objectionable. b)
Infringes on any third party’s intellectual property rights, privacy rights, or other proprietary
rights. c) Constitutes unsolicited or unauthorised advertising, promotional materials, “junk
mail,” “spam,” or any other form of solicitation. d) Contains software viruses, worms, or any
other harmful computer code, files, or programs. e) Attempts to interfere with, compromise
the system integrity or security of, or decipher any transmissions to or from the servers
running the Platform. f) Involves impersonating any person or entity or misrepresenting your
affiliation with a person or entity. g) Violates any applicable local, national, or international
law or regulation. 5.4. Responsibility: You are solely responsible for your Content and the
consequences of posting or publishing it. We have the right, but not the obligation, to monitor
and remove any Content that we believe, in our sole discretion, violates these Terms.
6. Expert Advisor Services
6.1. Platform Only: VelocityWave provides a platform to connect Clients with independent
Expert Advisors. The Expert Advisors are not employees, agents, or representatives of
VelocityWave. 6.2. No Endorsement: We do not endorse and are not responsible for the
advice, opinions, or services provided by Expert Advisors. Any engagement for services is a
direct contract between you (the Client) and the Expert Advisor. 6.3. Disclaimer: We make
no warranties regarding the quality, qualifications, or reliability of any Expert Advisor. Any
dispute between a Client and an Expert Advisor must be resolved directly between them.
VelocityWave disclaims all liability in this regard.
7. Intellectual Property
7.1. Our IP: All rights, title, and interest in and to the Platform and its components, including
but not limited to all software, text, graphics, logos, trademarks, and the “look and feel” of the
Platform, are and will remain the exclusive property of VelocityWave and its licensors. 7.2.
Your Feedback: If you provide us with any feedback, comments, or suggestions regarding
the Platform (“Feedback”), you hereby grant VelocityWave a perpetual, irrevocable,
worldwide, royalty-free licence to use and incorporate such Feedback into any of our
products and services.
8. Term and Termination
8.1. Termination by You: You can terminate this Agreement at any time by cancelling your
Subscription and ceasing to use the Platform. 8.2. Termination by Us: We may suspend or
terminate your Account and access to the Platform with immediate effect if we reasonably
believe you have materially breached these Terms. We may also terminate for convenience
by providing you with 30 days’ notice. 8.3. Effect of Termination: Upon termination, your
right to use the Platform will cease immediately. We may delete your Content from our live
systems, subject to our data retention policies as outlined in our Privacy Policy. Clauses that
by their nature should survive termination will survive termination, including, without
limitation, ownership provisions, warranty disclaimers, indemnity, and limitations of liability.
9. Disclaimers of Warranties
THE PLATFORM AND SERVICES ARE PROVIDED ON AN “AS IS” AND “AS AVAILABLE”
BASIS. TO THE FULLEST EXTENT PERMITTED BY LAW, VELOCITYWAVE EXPRESSLY
DISCLAIMS ALL WARRANTIES, WHETHER EXPRESS, IMPLIED, STATUTORY OR
OTHERWISE, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NONINFRINGEMENT. WE DO NOT WARRANT THAT THE SERVICE WILL BE
UNINTERRUPTED, SECURE, OR ERROR-FREE.
10. Limitation of Liability
TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, IN NO EVENT SHALL
VELOCITYWAVE, ITS AFFILIATES, DIRECTORS, EMPLOYEES, OR AGENTS BE LIABLE
FOR ANY INDIRECT, PUNITIVE, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR
EXEMPLARY DAMAGES, INCLUDING WITHOUT LIMITATION DAMAGES FOR LOSS OF
PROFITS, GOODWILL, DATA, OR OTHER INTANGIBLE LOSSES, THAT RESULT FROM
THE USE OF, OR INABILITY TO USE, THIS SERVICE.
OUR TOTAL AGGREGATE LIABILITY TO YOU FOR ALL CLAIMS ARISING OUT OF OR
RELATING TO THESE TERMS OR YOUR USE OF THE PLATFORM IS LIMITED TO THE
GREATER OF (A) THE TOTAL AMOUNT OF FEES YOU HAVE PAID TO VELOCITYWAVE
IN THE TWELVE (12) MONTHS PRIOR TO THE EVENT GIVING RISE TO THE LIABILITY,
OR (B) ONE HUNDRED POUNDS STERLING (£100).
11. Indemnification
You agree to defend, indemnify, and hold harmless VelocityWave and its affiliates, officers,
directors, employees, and agents from and against any and all claims, damages, obligations,
losses, liabilities, costs, or debt, and expenses (including but not limited to legal fees) arising
from: (i) your use of and access to the Platform; (ii) your violation of any term of these
Terms; (iii) your violation of any third-party right, including without limitation any copyright,
property, or privacy right; or (iv) any claim that your Content caused damage to a third party.
12. General Provisions
12.1. Governing Law: These Terms shall be governed by and construed in accordance with
the laws of England and Wales, without regard to its conflict of law principles. The parties
irrevocably agree that the courts of England and Wales shall have exclusive jurisdiction to
settle any dispute. 12.2. Changes to Terms: We reserve the right, at our sole discretion, to
modify or replace these Terms at any time. If a revision is material, we will provide at least
30 days’ notice prior to any new terms taking effect. By continuing to access or use our
Platform after those revisions become effective, you agree to be bound by the revised terms.
12.3. Entire Agreement: These Terms, together with our Privacy Policy, Cookie Policy, and
any other legal notices published by us on the Platform, shall constitute the entire agreement
between you and VelocityWave concerning the Platform. 12.4. Severability: If any provision
of these Terms is deemed invalid by a court of competent jurisdiction, the invalidity of such
provision shall not affect the validity of the remaining provisions of these Terms, which shall
remain in full force and effect. 12.5. Contact Information: If you have any questions about
these Terms, please contact <NAME_EMAIL>.
VelocityWave Ltd. [Company Address] [City, Postcode] United Kingdom 

PRD
1. Introduction
Velocity Platform is a dynamic, API-first platform designed for building and deploying multi-page websites and feature-rich user dashboards. Its core philosophy is simplicity in the platform itself, with complexity and customization driven by a powerful addon system. Crucially, the Velocity Platform's own public-facing user interface (including the website builder and dashboards) will also be constructed using this component-based system, allowing platform administrators to customize its appearance and functionality using the same tools available to end-users. Users will leverage a component-based website builder, drawing components from an Addon Builder or a library of Predefined Code Snippets, and an intuitive drag-and-drop addon builder (with client-side UI configuration via Rete.js) capable of integrating with, manipulating, and transforming data from common external API technologies or data sources. All computations, business logic, and complex data transformations will be performed solely by the platform's API/server-side components; the frontend will primarily serve as a visual UI for display and triggering server-side operations. All client-side addon logic will operate within a secure, sandboxed environment, transitioning to a production state only after thorough review and approval by platform administrators. Published websites will include a unique, securely shipped API key to enable dynamic interactions and data retrieval via HTMX calls back to the platform's API. The platform will utilize a specific, modern tech stack (Bootstrap, HTMX, Handlebars.js, Rete.js, PostgreSQL with JSONB for addon data and storage for code snippets, and Go with GraphQL hosted directly on the physical machine) to ensure efficient development and deployment.

2. Goals
Empower Users: Enable users with varying technical skills to build and manage websites and dashboards using a flexible component system.

Foster Extensibility: Provide a robust addon builder for users and third parties to create dynamic "Web Addons," alongside a curated library of "Predefined Code Snippets."

API-First Architecture: Ensure all platform operations and data transfers are handled via a secure and well-documented GraphQL API built with Go, including auto-generated endpoints (exposed as GraphQL fields/types) for addons and retrieval of code snippets. All significant computations, business logic, and complex data transformations will reside solely within the API.

Simplified Deployment: Allow websites to be compiled into static HTML/HTMX, JS and CSS for easy publishing to external hosting.

Maintain Platform Simplicity: Focus complexity within addons rather than the core platform, providing a straightforward user experience for core platform functionalities.

Secure and Compliant Data Management: Implement multi-tenancy with strict data isolation, ensuring GDPR and DPA adherence, and encrypting all data.

Enable Flexible Monetization: Provide robust mechanisms for tiered platform access (Free, Basic, Pro, Enterprise) and for third-party addons (free/paid) from day one. Resource limits will be configurable per subscription level. Platform subscriptions and addon purchases will be managed through a dedicated, platform-managed addon.

Self-Configurable Platform UI: Allow platform administrators to customize the platform's own frontend using its internal component-based building tools.

External Data Integration: Enable Addon Creators to securely integrate with, manipulate, and transform data from common external API technologies and data sources within their addons, with server-side mediation.

Secure Published Site API Access: Ensure secure and controlled API access for published websites back to the platform for dynamic content and interactions using a unique API key.

Controlled Addon Execution Environment: Implement a secure, isolated sandbox environment for addon development and testing, requiring explicit approval for promotion to production, and allowing admins to control/revert addon status for safety.

Automated Error Notification: Provide automated notifications of crashes or errors to platform administrators and relevant addon developers.

3. Target Users
Platform Owner: Responsible for overall platform management, maintenance, core feature development, curating Predefined Code Snippets, ensuring compliance, and overseeing monetization strategies.

Platform Admins: Manage user accounts, platform settings, review and approve addons, manage the library of Predefined Code Snippets, monitor platform health, configure platform tiers and addon pricing, and customize the platform's own public-facing UI. They are also responsible for the review and approval of addons transitioning from sandbox to production, and for managing addon statuses (e.g., disabling, reverting to sandbox) for safety. They will receive automated error notifications.

Platform Users (Website/Dashboard Creators): Individuals or businesses using the Velocity Platform to build, deploy, and manage their websites and internal dashboards, choosing platform tiers and acquiring addons. They will also manage the publishing of their websites, which involves API key generation.

Third-Party Developers (Addon Creators): Developers creating, publishing, and potentially distributing dynamic, free, or paid addons for the Velocity Platform, now with the ability to integrate external data sources and understanding the sandbox development environment. They will receive automated error notifications related to their specific addons.

4. System Architecture
The Velocity Platform will be built exclusively using the following components:

Platform Frontend (Public-Facing UI): This refers to the Velocity Platform's own administrative and user-facing interfaces (e.g., dashboards, the site builder interface itself). It will be built and managed using the platform's own Component-Based Website Builder, leveraging Web Addons and Predefined Code Snippets, and compiled into static HTML, HTMX, JS, and CSS (Bootstrap). This ensures the platform's UI is as flexible and customizable as any user-created website.

Webpage Creator (Frontend - Site Builder): This is the tool within the Platform Frontend that allows users (and platform admins for the platform's own UI) to visually assemble multi-page websites. It utilizes a custom frontend built with Bootstrap (HTML, CSS, JS) for the visual, component-based website building interface. Runs client-side. This builder will rely on pre-built Bootstrap components and a theming system to ensure consistency and customizability.

Platform Frontend Framework: HTMX - For dynamic and interactive user interfaces within the compiled platform frontend itself. Runs client-side.

Platform Frontend Templating: Handlebars.js - For rendering dynamic content within the HTMX-driven platform frontend. Runs client-side.

Addon Builder Interface: Rete.js - To provide the drag-and-drop interface for creating dynamic "Web Addons" and "Dashboard Addons." Runs entirely client-side. Rete.js will serve as a visual configuration tool to define server-side data processing workflows and API call sequences for addons. All computations, complex data manipulations, and business logic stemming from these definitions will be performed by the Go GraphQL API. Addon configurations will be serialized and loaded via API calls. During development and testing, addons and their configured logic will operate within a strictly sandboxed client-side environment.

Database: PostgreSQL - For all persistent data storage, including:

User accounts, site configurations, addon definitions (Rete.js configurations).

Predefined Code Snippets (HTML, CSS, JS content and metadata) for the website builder.

Tenant data.

Data specific to each addon instance will be stored as JSONB.

Monetization Data: User tier information, addon pricing details, addon purchase/subscription records.

Platform UI Configuration Data: The structure and content of the platform's own frontend (as built by admins using the Website Builder).

External Data Source Configurations: Securely stored connection details and credentials for external APIs and data sources.

Published Website API Keys: Securely generated, site-specific API keys for published websites.

Addon Deployment Status: Addon status (e.g., 'development', 'pending review', 'approved', 'rejected', 'disabled', 'sandbox') and associated environment configurations (e.g., sandbox vs. production API endpoints).

API Backbone: Go with GraphQL - This will serve as the robust backend, handling:

Data transfer for all platform operations through a single GraphQL endpoint.

All computations, complex data manipulations, and business logic.

Saving and loading addon configurations from Rete.js via GraphQL mutations.

Retrieval of Predefined Code Snippets for the website builder via GraphQL queries.

Automatic generation and management of GraphQL fields/types for each created addon, interacting with their respective JSONB data stores.

Authentication and Authorization for platform access and external published sites via GraphQL. Published websites will use a dedicated, securely generated API key for authentication when making calls back to the platform.

Interaction with the PostgreSQL database.

Ensuring data encryption for all data handled.

Monetization Logic: Enforcement of platform tier-based feature access and addon purchase/subscription entitlements based on configurable resource limits per subscription level. This logic will primarily interact with a dedicated, platform-managed payment addon.

Secure External Data Integration: The GraphQL API will serve as the secure intermediary for all external data source integrations initiated by addons. It will manage authentication credentials for external APIs, perform server-side data fetching, aggregation, and complex transformations before exposing the data via GraphQL.

Addon Environment Enforcement: The API will enforce strict access controls based on an addon's deployment status (development sandbox vs. production) and its overall active/disabled status, limiting the scope of API calls accordingly.

Deployment Environment: Directly on the physical machine (VPS), running the Go backend and serving platform frontend static assets.

Diagrammatic Flow (Conceptual - Updated):
(Diagram remains largely the same, but the flow for "Webpage Creator" now implicitly includes fetching "Predefined Code Snippets" from PostgreSQL via the Go GraphQL API, similar to how addon configurations are handled. The "Platform Frontend" itself is now shown as being built and served by the platform's own mechanisms. New arrows/nodes for "External Data Source" flow into the API Backbone, and then to Addons. A new connection from "Published Website" back to "API Backbone" is shown, labeled with "API Key".)

5. Key Features
5.1. Component-Based Website Builder
Functionality:

Users (and platform admins for the platform's own UI) can create multi-page websites.

Pages can be visually assembled by dragging and dropping components. These components will be styled using Bootstrap's classes and JavaScript components and can be:

Dynamic "Web Addons": Created via the Addon Builder, potentially with configurable settings, their own data logic, and exposed data via auto-generated GraphQL types/fields.

"Predefined Code Snippets": A library of ready-to-use HTML, CSS (using Bootstrap classes), and JavaScript blocks (e.g., stylized buttons, content sections, simple interactive elements) stored in the platform's database and managed by Platform Admins/Owners. These are generally more static in nature or offer simpler client-side interactions. Bootstrap Icons will be available for use within these snippets.

The Website Builder interface will provide a way to browse and select from both Web Addons and Predefined Code Snippets, leveraging Bootstrap's visual components for display.

Intuitive interface for managing pages, page hierarchy, and navigation, also styled with Bootstrap.

Bootstrap Theming Integration: The Website Builder will include tools (e.g., a color picker) to update Bootstrap's default theme colors. Changes will be saved to a dedicated CSS theme file which will be shipped with all compiled websites, allowing users to customize the look and feel of Bootstrap components and predefined snippets.

Ability to link between pages within the same website.

Web Addons can display static content or connect to dynamic data sources by querying the platform's GraphQL API, which can in turn fetch data from external sources. Predefined Code Snippets will primarily render their stored content, with their JavaScript strictly for UI interactions and initiating HTMX requests (no business logic or data transformation), and will inherit the website's defined Bootstrap theme/colors.

Site structure and component configurations saved via the Go GraphQL API.

Output:

Websites must be compilable into pure HTML, HTMX, JavaScript, and CSS (primarily Bootstrap).

The compiled output should be deployable to any standard external web hosting.

5.2. Dashboards
Functionality: Owner, Admin, and Platform User Dashboards will provide their respective functionalities.

Interface: These dashboards will be constructed using the platform's own Component-Based Website Builder, leveraging Web Addons and Predefined Code Snippets, styled with Bootstrap and using Bootstrap Icons. All underlying data computations and business logic will be handled by the Go GraphQL API, with the frontend primarily displaying results and triggering API calls.

5.3. Addon Builder
Functionality:

Drag-and-drop interface (client-side Rete.js) for creating dynamic "Web Addons" and "Dashboard Addons". These are distinct from the centrally managed "Predefined Code Snippets."

Addon configurations serialized and saved/loaded via Go GraphQL API mutations.

Automatic GraphQL Type and Field Generation for these addons, allowing querying and mutation of their data.

Addons include HTML, CSS (Bootstrap classes), and client-side JavaScript (strictly for UI interactions and initiating HTMX requests), and can define data requirements. Bootstrap Icons can be included in addon designs.

Metadata managed via Go GraphQL API, including options to mark addons as Free or Paid and set pricing.

Addon Review and Lifecycle:

Development Sandbox: During creation and initial testing, addons will run within a highly restrictive client-side sandbox environment. In this phase, direct external API calls from the client-side may be proxied through controlled platform endpoints, and access to the platform's GraphQL API will be limited to a development scope. The client-side JavaScript of addons within the sandbox will be strictly limited to UI interactions and triggering API calls; no complex computations or data manipulations will be performed client-side.

Addon Review by Platform Admin: Once an addon developer believes their addon is ready, they can submit it for review by Platform Admins.

Production Deployment: Upon successful review and approval by Platform Owners/Admins, the addon will be promoted to production. In production, the addon will have access to its full intended capabilities, including mediated external API calls via the Go backend, and will be made available to platform users based on their subscription level and if they have purchased it.

Admin Control for Safety: Platform Admins will have the ability to immediately disable ("turn off") an addon in production, rendering it inactive on all websites. They can also revert an addon back to its sandbox state for further investigation or remediation if safety concerns arise.

External Data Integration within Addons:

Ability to define and configure connections to common external API technologies (e.g., generic REST/HTTP endpoints, GraphQL endpoints, webhook definitions, or basic authenticated services). This involves configuring API endpoints, authentication methods (e.g., API keys, OAuth tokens – managed securely by the backend).

Visual tools within Rete.js for defining server-side data manipulation and transformation workflows (e.g., mapping fields, applying filters, reformatting data). These definitions will be executed exclusively by the Go GraphQL API backend.

Ability to define GraphQL mutations to push transformed data to the platform's database (JSONB) or to other external APIs (mediated via backend proxy). Addons are intended to create "micro-apps" capable of varied tasks such as collecting customer data via forms, interfacing with onsite machinery (via API mediation), or initiating product delivery after payment processing, with no artificial restrictions on their conceptual application.

Interface: Rete.js (client-side) for visual building; HTMX/Handlebars.js for overall UI and API interaction, all styled with Bootstrap.

5.4. API First Backbone (Go with GraphQL)
Functionality: Central Go API handles all platform interactions exclusively via a single GraphQL endpoint. This includes:

Serving and managing dynamic "Web Addons" and "Dashboard Addons" (configurations, auto-generated GraphQL types/fields, JSONB data).

Listing and retrieving "Predefined Code Snippets" for use in the Website Builder through GraphQL queries.

All data for published sites accessed via GraphQL.

All data transfer is encrypted.

All computations, complex data manipulations, and business logic, including executing data transformation logic defined by Rete.js.

Authentication & Authorization: Centralized (e.g., JWT-based authentication) integrated into the GraphQL context, with API key/token system for tenant data access. Published websites will authenticate using a unique, site-specific API key sent in request headers. This system will also enforce platform tier-based feature access and addon purchase/subscription entitlements based on configurable resource limits per subscription level. The API will enforce strict access controls based on an addon's deployment status (development sandbox vs. production) and its overall active/disabled status, limiting the scope of API calls accordingly.

Secure External Data Integration: The GraphQL API will serve as the secure intermediary for all external data source integrations initiated by addons. It will store and manage sensitive credentials, execute external API calls on the server-side, and perform server-side data aggregation and complex transformations before delivering to the client.

Technology: Go with a robust GraphQL server implementation.

5.5. Data Management & Tenancy
Database: PostgreSQL. Will store:

Core platform data (users, sites, addon definitions).

A dedicated table or structure for "Predefined Code Snippets" (storing their HTML, CSS, JS, name, description, category, etc.).

Tenant data.

Data specific to each addon instance will be stored as JSONB.

Monetization Data: User tier information, addon pricing details, addon purchase/subscription records, payment gateway transaction IDs (if applicable).

Platform UI Configuration Data: The structured data representing the platform's own frontend layout, components, and content, managed through the Website Builder.

External Data Source Configurations: Securely stored connection details, credentials (e.g., API keys, OAuth tokens), and schema/endpoint configurations for external APIs and data sources used by addons.

Published Website API Keys: Securely generated, site-specific API keys for published websites, linked to tenant and site IDs.

Addon Deployment Status: Addon status (e.g., 'development', 'pending review', 'approved', 'rejected', 'disabled', 'sandbox') and associated environment configurations (e.g., sandbox vs. production API endpoints).

Data Access: Exclusively via Go GraphQL API.

Data Security: All data encrypted (at rest and in transit). Adherence to GDPR and DPA.

Data Structure: Supports platform data, user data, addon definitions (Rete.js configs), Predefined Code Snippets, and tenant-specific website data (including JSONB for addons).

5.6. Website Publishing
Compilation: Websites (composed of Web Addons and Predefined Code Snippets) compile to pure HTML, HTMX, JavaScript, and CSS (Bootstrap).

The compiled output should be deployable to any standard external web hosting.

API Key Generation and Inclusion: During the publishing process, a unique API key will be securely generated for the published website. This key will be embedded within the compiled website files (e.g., as a client-side configuration variable or within HTMX script tags) to enable authenticated HTMX calls back to the platform's GraphQL API for dynamic content and interactions.

Dynamic Data: Powered by HTMX calling platform Go GraphQL API for data retrieval and submission, including data sourced from external APIs. Predefined snippets will have their JavaScript strictly limited to UI interactions and initiating HTMX requests.

5.7. Monetization & Tier Management
Functionality:

Platform Tiers: Define and manage different platform tiers (Free, Basic, Pro, Enterprise) with associated feature sets and configurable resource limits (e.g., number of sites, storage, bandwidth limits if applicable). This management will be available via the Admin Dashboard.

Addon Pricing: Enable third-party developers to designate their addons as "Free" or "Paid" and set pricing (one-time or recurring, if applicable). Addon pricing details will be managed through the Addon Builder and Admin Dashboard.

Subscription Management (Platform) via Addon: Platform subscriptions and addon purchases will be handled by a dedicated, platform-managed addon (e.g., a "Stripe Addon"). This addon, built using the platform's own Addon Builder, will contain the necessary server-side logic (executed by the Go API) and client-side UI components to manage payment gateway interactions (e.g., Stripe, PayPal) for recurring subscriptions and one-time addon purchases. The platform's core system will manage user entitlements (which tiers/addons they have access to) based on the successful transactions reported by this payment addon.

Addon Purchase/Activation (Client): Ability for platform users to browse, subscribe to, or purchase paid addons from an addon listing, interacting with the payment addon's UI and logic.

Access Control by Tier: Implement logic in the Go backend (via GraphQL resolvers and business logic) to strictly enforce feature and resource limits based on a user's active platform tier.

Addon Access by Purchase: Implement logic in the Go backend to verify a user's entitlement to use paid addons based on their purchase/subscription records.

Interface: Admin dashboard for tier/pricing configuration; User dashboard for subscription/addon management, primarily driven by the payment addon's UI.

6. Guiding Principles
Simplicity: Core platform UX is straightforward; complexity is in addons.

API-First: Central Go GraphQL API with auto-generated addon types/fields and snippet retrieval. All computations and business logic reside in the API, with the frontend as a pure UI layer.

Strict Technology Adherence: Bootstrap (including Bootstrap Icons), HTMX, Handlebars.js, Rete.js (client-side), PostgreSQL (with JSONB & snippet storage), Go with GraphQL running directly on a physical machine (VPS). No exceptions.

Clear Data Separation & Security: Multi-tenancy, API-gated access, full encryption, GDPR/DPA compliance.

Flexible Monetization: Provide robust mechanisms for tiered platform access and paid third-party addons from the outset, with payment processing managed by a dedicated platform-level addon.

Self-Configurable Platform UI: The platform's own frontend will be built and managed using its internal component-based system.

External Data Integration: Provide secure and flexible mechanisms for addons to connect to and transform data from external API sources, with server-side mediation for sensitive operations.

Published Site Security: Ensure published websites can securely interact with the platform's API via dedicated, managed API keys.

Controlled Addon Execution: Implement a robust sandbox-to-production lifecycle for addon execution, ensuring security and quality through strict client-side sandboxing and backend controls.

Comprehensive Logging & Analytics: Capture all significant platform actions and errors for detailed monitoring, auditing, and automated notifications.

7. Success Metrics / Key Performance Indicators (KPIs)
Number of active platform users.

Number of created and deployed websites/dashboards.

Number of unique addons created by third-party developers.

Platform uptime and performance metrics.

GraphQL query performance (response times, error rates) and usage patterns.

Number of paid platform subscriptions.

Number of paid addon purchases/subscriptions.

Revenue generated from platform subscriptions and addon sales.

Detailed platform usage metrics (e.g., most active sites, popular features, common user flows, frequently used addons/snippets, resource consumption per user/tier).

8. Release Criteria (for MVP/V1)
The full functionality must be available, comprising:

Core platform infrastructure.

Functional component-based Website Builder supporting both dynamic "Web Addons" from the Addon Builder and "Predefined Code Snippets" from the database, all styled with Bootstrap and including theming capabilities.

Ability to compile and publish a completed multi-page website to external hosting, including secure API key generation and embedding for HTMX calls.

Functional Addon Builder for dynamic Web Addons and Dashboard Addons, including the ability to configure server-side external API data ingestion and transformation. The addon development and review process, including the client-side sandbox environment, backend enforcement, and production promotion workflow, must be functional. Admin controls to disable and revert addons to sandbox must be in place.

Admin dashboard with addon review and Predefined Code Snippet management capabilities.

User, Owner, and Admin dashboards. The platform's own public-facing UI and dashboards must be configurable via the Website Builder.

Comprehensive GraphQL schema and resolvers for all platform operations, addon data, and snippet retrieval.

Implementation of security requirements, including external API credential management and client-side sandboxing.

Basic monetization capabilities (platform tiers, configurable resource limits, free/paid addons) must be functional, with a core payment addon (e.g., Stripe Addon) integrated and managing platform subscriptions and addon purchases.

Automated testing (unit, integration) implemented where possible for the Go backend and GraphQL API, ensuring core functionality and data integrity. Manual testing plans will be prepared and executed by testers for end-to-end functionality and user experience.

Basic automated notification system for crashes and errors, alerting platform admins and addon developers.

9. Security
Compliance: GDPR and DPA.

Data Encryption: All data at rest and in transit.

Addon Security & Sandboxing Environment: Human review for addons. Client-side sandboxing (e.g., iframes with sandbox attributes, strict CSP, and potentially Web Workers where appropriate for heavy client-side tasks) will be implemented for all user-contributed JavaScript within addons and snippets to mitigate malicious code execution and ensure isolation. Crucially, the client-side JavaScript will be restricted to UI interactions and triggering API calls; no business logic, complex computations, or data transformations will occur client-side. During development and testing, addons will execute within a highly restrictive sandbox environment that limits access to host page resources, restricts network calls (proxying them through controlled backend endpoints), and prevents potentially harmful operations. Upon approval by platform owners/admins, addons will be promoted to a production environment where they gain the necessary permissions for their intended functionality, while still operating within strict client-side sandboxing to protect the host environment.

Input Validation & Output Encoding: Standard practices to prevent common web vulnerabilities (e.g., XSS, SQL injection). This includes robust input validation within GraphQL resolvers and for data ingested from external APIs.

Authentication & Authorization: Secure platform and API access (e.g., JWT for GraphQL authentication, robust user authentication). Authorization logic applied at the GraphQL resolver level, enforcing tier-based feature access and addon entitlement.

Payment Processing Security: Sensitive payment data will be handled directly by the chosen payment gateway (e.g., Stripe) via the dedicated payment addon's server-side logic, minimizing direct platform exposure. The platform will securely store only transaction IDs and subscription statuses. The payment addon itself will adhere to PCI DSS compliance standards for any data it processes, and its integration with the gateway will be secure.

External API Credentials Management: Secure storage and retrieval of API keys, tokens, and other sensitive credentials for external data sources within the Go backend. These credentials must never be exposed client-side. The Go backend will handle all server-to-server calls to external APIs.

Published Website API Keys: Implement robust generation, storage, and validation mechanisms for unique, site-specific API keys. For V1, keys will be generated upon publication and securely embedded within the compiled static files. This will primarily involve embedding the key as a configuration variable accessible by client-side HTMX. Initial revocation will be a manual process managed by platform admins via the backend. Mechanisms for API key rotation and automated revocation for compromised sites will be considered for future phases, along with rate-limiting and abuse prevention measures.

Data Flow Validation: Rigorous validation of data ingested from external APIs and data transformed client-side before it is stored or further processed by the platform's backend.

Dependency Management: Regular updates and vulnerability scanning of all third-party libraries and components.

10. Deployment and Operations
Platform Hosting: The Go backend will be deployed directly as an executable on the physical machine, which will be a Virtual Private Server (VPS). The frontend static assets will also be served from this machine. Scalability will be a focus after V1.

Database Management: PostgreSQL procedures for backups, replication, and performance tuning. The current setup involves daily snapshots of the server and 6-hourly database backups. For MVP, this provides a Recovery Point Objective (RPO) of 6 hours and a Recovery Time Objective (RTO) dependent on manual restoration from backups. Data resilience will include basic monitoring for backup job completion and success, and backup verification processes. As the database moves to a separate server (future phase), a more robust, automated disaster recovery plan will be developed, potentially including continuous archiving (WAL shipping) for near-zero RPO.

Monitoring: Comprehensive logging and monitoring (e.g., Prometheus, Grafana, or suitable system-level monitoring tools) for API performance, error rates, resource utilization, and detailed platform usage analytics. All actions and errors must be logged comprehensively (e.g., clicks, logins, page loads, API calls, form submissions, data transformations). Each log entry will include a meaningful description, precise date and time, the user's IP address, and User ID (if logged in) to enable thorough issue tracking and auditing. GraphQL-specific monitoring for query complexity, performance, and field usage will be implemented to inform future schema evolution.

Automated Testing Integration: CI/CD (even if manual deployment) will include automated tests for API endpoints and critical business logic.

Addon Lifecycle Management: Implementation of processes and tooling to facilitate the secure transition of addons from development/sandbox environments to production, tied to the approval workflow. This includes controls for disabling and reverting addons.

Automated Error Notifications: An integrated system will detect crashes and errors from logs/monitoring and automatically send notifications (e.g., via email, Slack) to platform administrators and the specific developers responsible for the affected addon/micro-app.

11. Future Considerations / Roadmap (Beyond V1)
AI-driven analytics and content generation for website and dashboard components.

Advanced addon marketplace features, including more sophisticated monetization models, reviews, and versioning.

Versioning for websites, addons, and snippets (automated migration).

Platform-level analytics for site owners to track their website performance.

Integrated CDN for optimized asset delivery.

GraphQL Subscriptions for real-time updates.

Full internationalization/localization of the platform UI (for future UK business expansion).

Automated CI/CD pipelines for user-created sites.

Enhanced API key management features for published sites (e.g., user-initiated key rotation, detailed usage dashboards).

Dedicated database server for improved scalability and resilience.

12. Non-Goals (Explicitly Out of Scope for V1)
Direct server-side code execution in published addons/snippets.

Integrated hosting solution for user-created websites (beyond serving the platform's own compiled frontend).

Complex e-commerce backends or payment gateways within the core platform (focus on robust third-party integration via addons).

Granular roles and permissions management within individual published sites.