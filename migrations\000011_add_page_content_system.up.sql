-- Add page content and media management
CREATE TABLE IF NOT EXISTS page_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    page_id UUID NOT NULL REFERENCES pages(id) ON DELETE CASCADE,
    content JSONB NOT NULL DEFAULT '{}',
    version INTEGER NOT NULL DEFAULT 1,
    is_published BOOLEAN NOT NULL DEFAULT false,
    created_by INT NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS media_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    filename <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    original_filename VA<PERSON>HA<PERSON>(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    alt_text TEXT,
    is_public BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS site_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    site_name VARCHAR(255) NOT NULL,
    site_description TEXT,
    custom_domain VARCHAR(255),
    ssl_enabled BOOLEAN NOT NULL DEFAULT false,
    is_published BOOLEAN NOT NULL DEFAULT false,
    theme_config JSONB NOT NULL DEFAULT '{}',
    seo_config JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_page_content_page_id ON page_content(page_id);
CREATE INDEX IF NOT EXISTS idx_page_content_version ON page_content(page_id, version DESC);
CREATE INDEX IF NOT EXISTS idx_page_content_published ON page_content(is_published);
CREATE INDEX IF NOT EXISTS idx_media_assets_user_id ON media_assets(user_id);
CREATE INDEX IF NOT EXISTS idx_media_assets_tenant_id ON media_assets(tenant_id);
CREATE INDEX IF NOT EXISTS idx_site_settings_user_id ON site_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_site_settings_custom_domain ON site_settings(custom_domain);

-- Add missing tenant_id to pages table if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'pages' AND column_name = 'tenant_id') THEN
        ALTER TABLE pages ADD COLUMN tenant_id UUID REFERENCES tenants(id);
        UPDATE pages SET tenant_id = (SELECT id FROM tenants WHERE name = 'Default Tenant');
        ALTER TABLE pages ALTER COLUMN tenant_id SET NOT NULL;
    END IF;
END $$;

-- Add user_id to pages table if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'pages' AND column_name = 'user_id') THEN
        ALTER TABLE pages ADD COLUMN user_id INT REFERENCES users(id);
        -- Set to first user for existing pages
        UPDATE pages SET user_id = (SELECT id FROM users LIMIT 1);
        ALTER TABLE pages ALTER COLUMN user_id SET NOT NULL;
    END IF;
END $$;

-- Create default site settings for existing users
INSERT INTO site_settings (user_id, tenant_id, site_name, site_description)
SELECT 
    u.id,
    u.tenant_id,
    'My Website',
    'A website built with Velocity Platform'
FROM users u
WHERE NOT EXISTS (
    SELECT 1 FROM site_settings ss WHERE ss.user_id = u.id
);