package auth

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"goVwPlatformAPI/graph/model"
	"goVwPlatformAPI/internal/database"
	"golang.org/x/crypto/bcrypt"
)

// Claims represents JWT claims
type Claims struct {
	UserID   string `json:"user_id"`
	Email    string `json:"email"`
	Role     string `json:"role"`
	TenantID string `json:"tenant_id"`
	jwt.RegisteredClaims
}

// UserContextKey is the key used to store user claims in context
const UserContextKey = "user"

type AuthService struct {
	DB        *database.DB
	JWTSecret string
}

type RegisterRequest struct {
	FirstName       string `json:"firstName"`
	LastName        string `json:"lastName"`
	Email           string `json:"email"`
	Company         string `json:"company"`
	AccountType     string `json:"accountType"`
	Password        string `json:"password"`
	AgreeTerms      bool   `json:"agreeTerms"`
	MarketingEmails bool   `json:"marketingEmails"`
}

type LoginRequest struct {
	Email      string `json:"email"`
	Password   string `json:"password"`
	RememberMe bool   `json:"rememberMe"`
}

type ForgotPasswordRequest struct {
	Email string `json:"email"`
}

type ResetPasswordRequest struct {
	Token       string `json:"token"`
	NewPassword string `json:"newPassword"`
}

type AuthResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Token   string      `json:"token,omitempty"`
	User    *model.User `json:"user,omitempty"`
}

// NewAuthService creates a new authentication service
func NewAuthService(db *database.DB) *AuthService {
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		jwtSecret = "your_strong_secret_here" // Fallback for development
	}

	return &AuthService{
		DB:        db,
		JWTSecret: jwtSecret,
	}
}

// ValidateEmail checks if email format is valid
func (s *AuthService) ValidateEmail(email string) error {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return fmt.Errorf("invalid email format")
	}
	return nil
}

// ValidatePassword checks if password meets security requirements
func (s *AuthService) ValidatePassword(password string) error {
	if len(password) < 8 {
		return fmt.Errorf("password must be at least 8 characters long")
	}

	hasUpper := false
	hasLower := false
	hasNumber := false
	hasSpecial := false

	for _, char := range password {
		switch {
		case 'A' <= char && char <= 'Z':
			hasUpper = true
		case 'a' <= char && char <= 'z':
			hasLower = true
		case '0' <= char && char <= '9':
			hasNumber = true
		case strings.ContainsRune("!@#$%^&*(),.?\":{}|<>", char):
			hasSpecial = true
		}
	}

	if !hasUpper {
		return fmt.Errorf("password must contain at least one uppercase letter")
	}
	if !hasLower {
		return fmt.Errorf("password must contain at least one lowercase letter")
	}
	if !hasNumber {
		return fmt.Errorf("password must contain at least one number")
	}
	if !hasSpecial {
		return fmt.Errorf("password must contain at least one special character")
	}

	return nil
}

// CheckEmailExists checks if email is already registered
func (s *AuthService) CheckEmailExists(email string) (bool, error) {
	var count int
	err := s.DB.Pool.QueryRow(context.Background(), "SELECT COUNT(*) FROM users WHERE email = $1", email).Scan(&count)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// HashPassword creates a bcrypt hash of the password
func (s *AuthService) HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// CheckPasswordHash compares a password with its hash
func (s *AuthService) CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// GenerateEmailVerificationToken creates a secure token for email verification
func (s *AuthService) GenerateEmailVerificationToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// Register creates a new user account
func (s *AuthService) Register(req *RegisterRequest) (*AuthResponse, error) {
	// Validate input
	if req.FirstName == "" || req.LastName == "" {
		return &AuthResponse{
			Success: false,
			Message: "First name and last name are required",
		}, nil
	}

	if err := s.ValidateEmail(req.Email); err != nil {
		return &AuthResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	if err := s.ValidatePassword(req.Password); err != nil {
		return &AuthResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	if req.AccountType != "client" && req.AccountType != "expert" {
		return &AuthResponse{
			Success: false,
			Message: "Invalid account type",
		}, nil
	}

	if !req.AgreeTerms {
		return &AuthResponse{
			Success: false,
			Message: "You must agree to the terms and conditions",
		}, nil
	}

	// Check if email already exists
	exists, err := s.CheckEmailExists(req.Email)
	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Database error occurred",
		}, nil
	}

	if exists {
		return &AuthResponse{
			Success: false,
			Message: "An account with this email already exists",
		}, nil
	}

	// Hash password
	passwordHash, err := s.HashPassword(req.Password)
	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Error processing password",
		}, nil
	}

	// Generate verification token
	verificationToken, err := s.GenerateEmailVerificationToken()
	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Error generating verification token",
		}, nil
	}

	// Create user
	userID := uuid.New().String()
	defaultTenantID := "********-1111-1111-1111-********1111" // Default tenant from migration

	query := `
		INSERT INTO users (id, email, first_name, last_name, company, role, password_hash, 
		                   email_verification_token, tenant_id, marketing_emails, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
	`

	now := time.Now()
	_, err = s.DB.Pool.Exec(context.Background(), query, userID, req.Email, req.FirstName, req.LastName,
		req.Company, req.AccountType, passwordHash, verificationToken,
		defaultTenantID, req.MarketingEmails, now, now)

	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Error creating account",
		}, nil
	}

	// TODO: Send verification email
	// For now, we'll auto-verify for development
	_, err = s.DB.Pool.Exec(context.Background(), "UPDATE users SET email_verified = true WHERE id = $1", userID)
	if err != nil {
		// Log error but don't fail registration
		fmt.Printf("Error auto-verifying email: %v\n", err)
	}

	return &AuthResponse{
		Success: true,
		Message: "Account created successfully! Please check your email to verify your account.",
	}, nil
}

// Login authenticates a user and returns a JWT token
func (s *AuthService) Login(req *LoginRequest) (*AuthResponse, error) {
	// Validate input
	if req.Email == "" || req.Password == "" {
		return &AuthResponse{
			Success: false,
			Message: "Email and password are required",
		}, nil
	}

	// Get user from database
	var user model.User
	query := `
		SELECT id, email, first_name, last_name, company, role, password_hash, 
		       email_verified, tenant_id, created_at, updated_at
		FROM users 
		WHERE email = $1
	`

	var passwordHash string
	err := s.DB.Pool.QueryRow(context.Background(), query, req.Email).Scan(
		&user.ID, &user.Email, &user.FirstName, &user.LastName, &user.Company,
		&user.Role, &passwordHash, &user.EmailVerified, &user.ID,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil && err.Error() == "no rows in result set" {
		return &AuthResponse{
			Success: false,
			Message: "Invalid email or password",
		}, nil
	}

	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Database error occurred",
		}, nil
	}

	// Check password
	if !s.CheckPasswordHash(req.Password, passwordHash) {
		return &AuthResponse{
			Success: false,
			Message: "Invalid email or password",
		}, nil
	}

	// Check if email is verified
	if !user.EmailVerified {
		return &AuthResponse{
			Success: false,
			Message: "Please verify your email address before logging in",
		}, nil
	}

	// Generate JWT token

	token, err := s.GenerateJWT(user.ID, user.Email, user.Role, user.ID)
	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Error generating authentication token",
		}, nil
	}

	// Update last login time
	_, err = s.DB.Pool.Exec(context.Background(), "UPDATE users SET last_login = $1 WHERE id = $2", time.Now(), user.ID)
	if err != nil {
		// Log error but don't fail login
		fmt.Printf("Error updating last login: %v\n", err)
	}

	// Password hash is not included in model.User, so no need to clear it

	return &AuthResponse{
		Success: true,
		Message: "Login successful",
		Token:   token,
		User:    &user,
	}, nil
}

// GetUserByID retrieves a user by their ID
func (s *AuthService) GetUserByID(userID string) (*model.User, error) {
	var user model.User
	query := `
		SELECT id, email, first_name, last_name, company, role, 
		       email_verified, tenant_id, created_at, updated_at
		FROM users 
		WHERE id = $1
	`

	err := s.DB.Pool.QueryRow(context.Background(), query, userID).Scan(
		&user.ID, &user.Email, &user.FirstName, &user.LastName, &user.Company,
		&user.Role, &user.EmailVerified, &user.ID,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return &user, nil
}

// GetUserFromContext retrieves user claims from context
func GetUserFromContext(ctx context.Context) (*Claims, error) {
	claims, ok := ctx.Value(UserContextKey).(*Claims)
	if !ok {
		return nil, fmt.Errorf("user not found in context")
	}
	return claims, nil
}

// GenerateJWT generates a JWT token for the user
func (s *AuthService) GenerateJWT(userID, email, role, tenantID string) (string, error) {
	claims := Claims{
		UserID:   userID,
		Email:    email,
		Role:     role,
		TenantID: tenantID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.JWTSecret))
}

// ForgotPassword initiates password reset process
func (s *AuthService) ForgotPassword(req *ForgotPasswordRequest) (*AuthResponse, error) {
	// Validate email
	if err := s.ValidateEmail(req.Email); err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Invalid email format",
		}, nil
	}

	// Check if user exists
	var userID string
	err := s.DB.Pool.QueryRow(context.Background(), "SELECT id FROM users WHERE email = $1", req.Email).Scan(&userID)
	if err != nil {
		// Don't reveal if email exists or not for security
		return &AuthResponse{
			Success: true,
			Message: "If an account with that email exists, we've sent a password reset link.",
		}, nil
	}

	// Generate reset token
	resetToken, err := s.GenerateEmailVerificationToken()
	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Error generating reset token",
		}, nil
	}

	// Store reset token with expiry (1 hour)
	expiresAt := time.Now().Add(time.Hour)
	_, err = s.DB.Pool.Exec(context.Background(),
		"UPDATE users SET password_reset_token = $1, password_reset_expires = $2 WHERE id = $3",
		resetToken, expiresAt, userID)

	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Error processing reset request",
		}, nil
	}

	// TODO: Send reset email
	// For development, we'll just return success
	return &AuthResponse{
		Success: true,
		Message: "If an account with that email exists, we've sent a password reset link.",
	}, nil
}

// ResetPassword completes the password reset process
func (s *AuthService) ResetPassword(req *ResetPasswordRequest) (*AuthResponse, error) {
	// Validate new password
	if err := s.ValidatePassword(req.NewPassword); err != nil {
		return &AuthResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	// Find user by reset token
	var userID string
	var expiresAt time.Time
	err := s.DB.Pool.QueryRow(context.Background(),
		"SELECT id, password_reset_expires FROM users WHERE password_reset_token = $1",
		req.Token).Scan(&userID, &expiresAt)

	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Invalid or expired reset token",
		}, nil
	}

	// Check if token is expired
	if time.Now().After(expiresAt) {
		return &AuthResponse{
			Success: false,
			Message: "Reset token has expired",
		}, nil
	}

	// Hash new password
	passwordHash, err := s.HashPassword(req.NewPassword)
	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Error processing new password",
		}, nil
	}

	// Update password and clear reset token
	_, err = s.DB.Pool.Exec(context.Background(),
		"UPDATE users SET password_hash = $1, password_reset_token = NULL, password_reset_expires = NULL, updated_at = $2 WHERE id = $3",
		passwordHash, time.Now(), userID)

	if err != nil {
		return &AuthResponse{
			Success: false,
			Message: "Error updating password",
		}, nil
	}

	return &AuthResponse{
		Success: true,
		Message: "Password reset successfully. You can now log in with your new password.",
	}, nil
}
