{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-6">
            <h3>Page Hierarchy</h3>
            <div id="page-tree" class="dd">
                <ol class="dd-list">
                    {% for page in navigation %}
                        {% include "builder/navigation_item.html" %}
                    {% endfor %}
                </ol>
            </div>
        </div>
        <div class="col-md-6">
            <h3>Navigation Preview</h3>
            <div id="nav-preview" class="list-group">
                {% for page in navigation %}
                    {% include "builder/navigation_preview_item.html" %}
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<script src="/static/js/dragdrop.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const pageTree = document.getElementById('page-tree');
    
    new Sortable(pageTree, {
        group: 'pages',
        animation: 150,
        onEnd: function(evt) {
            const pageId = evt.item.dataset.pageId;
            const newParentId = evt.to.parentElement.closest('[data-page-id]')?.dataset.pageId || null;
            const newPosition = Array.from(evt.to.children).indexOf(evt.item);
            
            fetch('/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    query: `mutation MovePage($id: ID!, $parentId: ID, $position: Int!) {
                        movePage(id: $id, parentId: $parentId, position: $position) {
                            id
                        }
                    }`,
                    variables: {
                        id: pageId,
                        parentId: newParentId,
                        position: newPosition
                    }
                })
            }).then(updateNavigationPreview);
        }
    });

    function updateNavigationPreview() {
        fetch('/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: JSON.stringify({
                query: `{ getNavigation { id title slug parentId position } }`
            })
        }).then(response => response.json())
          .then(data => {
              // Update preview logic here
          });
    }
});
</script>
{% endblock %}