{{define "content"}}
<div class="container py-4">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-5 fw-bold">Business Plan Builder</h1>
        <p class="lead text-muted">Create a comprehensive business plan with our guided templates</p>
    </div>
    
    <!-- Template Selection -->
    <div class="row mb-5" id="templateSelection">
        <div class="col-12">
            <h4 class="mb-4">Choose Your Business Plan Template</h4>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 template-card" onclick="selectTemplate('startup')">
                        <div class="card-body text-center">
                            <i class="bi bi-rocket-takeoff display-4 text-primary mb-3"></i>
                            <h5>Startup Business Plan</h5>
                            <p class="text-muted">Perfect for new businesses seeking investment or loans</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="bi bi-check text-success me-2"></i>Executive Summary</li>
                                <li><i class="bi bi-check text-success me-2"></i>Market Analysis</li>
                                <li><i class="bi bi-check text-success me-2"></i>Financial Projections</li>
                                <li><i class="bi bi-check text-success me-2"></i>Funding Requirements</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card h-100 template-card" onclick="selectTemplate('growth')">
                        <div class="card-body text-center">
                            <i class="bi bi-graph-up-arrow display-4 text-success mb-3"></i>
                            <h5>Growth & Expansion Plan</h5>
                            <p class="text-muted">For established businesses planning to scale</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="bi bi-check text-success me-2"></i>Current Performance</li>
                                <li><i class="bi bi-check text-success me-2"></i>Growth Strategy</li>
                                <li><i class="bi bi-check text-success me-2"></i>Market Expansion</li>
                                <li><i class="bi bi-check text-success me-2"></i>Resource Planning</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card h-100 template-card" onclick="selectTemplate('lean')">
                        <div class="card-body text-center">
                            <i class="bi bi-lightning display-4 text-warning mb-3"></i>
                            <h5>Lean Business Plan</h5>
                            <p class="text-muted">Quick and focused plan for internal use</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="bi bi-check text-success me-2"></i>Value Proposition</li>
                                <li><i class="bi bi-check text-success me-2"></i>Key Activities</li>
                                <li><i class="bi bi-check text-success me-2"></i>Revenue Streams</li>
                                <li><i class="bi bi-check text-success me-2"></i>Cost Structure</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Business Plan Builder -->
    <div id="businessPlanBuilder" style="display: none;">
        <div class="row">
            <!-- Sidebar Navigation -->
            <div class="col-md-3">
                <div class="card sticky-top">
                    <div class="card-header">
                        <h6 class="mb-0">Plan Sections</h6>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar" id="overallProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush" id="sectionNavigation">
                            <!-- Navigation items will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0" id="currentSectionTitle">Executive Summary</h5>
                            <div>
                                <button class="btn btn-outline-secondary btn-sm me-2" onclick="saveProgress()">
                                    <i class="bi bi-save me-1"></i>Save
                                </button>
                                <button class="btn btn-primary btn-sm" onclick="exportPlan()">
                                    <i class="bi bi-download me-1"></i>Export
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="sectionContent">
                            <!-- Section content will be loaded here -->
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button class="btn btn-outline-secondary" id="prevButton" onclick="previousSection()" disabled>
                                <i class="bi bi-arrow-left me-1"></i>Previous
                            </button>
                            <button class="btn btn-primary" id="nextButton" onclick="nextSection()">
                                Next<i class="bi bi-arrow-right ms-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentTemplate = null;
let currentSectionIndex = 0;
let businessPlanData = {};
let templateSections = {};

// Define template structures
const templates = {
    startup: {
        name: 'Startup Business Plan',
        sections: [
            { id: 'executive_summary', title: 'Executive Summary', required: true },
            { id: 'company_description', title: 'Company Description', required: true },
            { id: 'market_analysis', title: 'Market Analysis', required: true },
            { id: 'organization', title: 'Organization & Management', required: true },
            { id: 'products_services', title: 'Products & Services', required: true },
            { id: 'marketing_sales', title: 'Marketing & Sales', required: true },
            { id: 'funding_request', title: 'Funding Request', required: true },
            { id: 'financial_projections', title: 'Financial Projections', required: true },
            { id: 'appendix', title: 'Appendix', required: false }
        ]
    },
    growth: {
        name: 'Growth & Expansion Plan',
        sections: [
            { id: 'executive_summary', title: 'Executive Summary', required: true },
            { id: 'current_situation', title: 'Current Business Situation', required: true },
            { id: 'growth_strategy', title: 'Growth Strategy', required: true },
            { id: 'market_expansion', title: 'Market Expansion', required: true },
            { id: 'operational_plan', title: 'Operational Plan', required: true },
            { id: 'financial_plan', title: 'Financial Plan', required: true },
            { id: 'risk_analysis', title: 'Risk Analysis', required: true },
            { id: 'implementation', title: 'Implementation Timeline', required: true }
        ]
    },
    lean: {
        name: 'Lean Business Plan',
        sections: [
            { id: 'value_proposition', title: 'Value Proposition', required: true },
            { id: 'key_activities', title: 'Key Activities', required: true },
            { id: 'key_resources', title: 'Key Resources', required: true },
            { id: 'customer_segments', title: 'Customer Segments', required: true },
            { id: 'revenue_streams', title: 'Revenue Streams', required: true },
            { id: 'cost_structure', title: 'Cost Structure', required: true }
        ]
    }
};

function selectTemplate(templateId) {
    currentTemplate = templateId;
    templateSections = templates[templateId];
    
    // Hide template selection and show builder
    document.getElementById('templateSelection').style.display = 'none';
    document.getElementById('businessPlanBuilder').style.display = 'block';
    
    // Initialize business plan data
    businessPlanData = {
        template: templateId,
        templateName: templateSections.name,
        sections: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    
    // Setup navigation and load first section
    setupNavigation();
    loadSection(0);
}

function setupNavigation() {
    const navigation = document.getElementById('sectionNavigation');
    navigation.innerHTML = templateSections.sections.map((section, index) => `
        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" 
           onclick="loadSection(${index})" data-section-index="${index}">
            <div>
                <i class="bi bi-circle me-2 section-status" id="status-${index}"></i>
                ${section.title}
                ${section.required ? '<span class="text-danger">*</span>' : ''}
            </div>
            <small class="text-muted" id="progress-${index}">0%</small>
        </a>
    `).join('');
}

function loadSection(sectionIndex) {
    currentSectionIndex = sectionIndex;
    const section = templateSections.sections[sectionIndex];
    
    // Update navigation
    document.querySelectorAll('.list-group-item').forEach((item, index) => {
        item.classList.toggle('active', index === sectionIndex);
    });
    
    // Update section title
    document.getElementById('currentSectionTitle').textContent = section.title;
    
    // Load section content
    const content = getSectionContent(section.id);
    document.getElementById('sectionContent').innerHTML = content;
    
    // Update navigation buttons
    document.getElementById('prevButton').disabled = sectionIndex === 0;
    document.getElementById('nextButton').textContent = 
        sectionIndex === templateSections.sections.length - 1 ? 'Finish' : 'Next';
    
    // Load existing data if available
    if (businessPlanData.sections[section.id]) {
        populateSectionData(section.id, businessPlanData.sections[section.id]);
    }
}

function getSectionContent(sectionId) {
    const contentTemplates = {
        executive_summary: `
            <div class="mb-3">
                <label class="form-label">Business Overview</label>
                <textarea class="form-control" id="business_overview" rows="4" 
                          placeholder="Provide a brief overview of your business..."></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Mission Statement</label>
                <textarea class="form-control" id="mission_statement" rows="2" 
                          placeholder="What is your company's mission?"></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Key Success Factors</label>
                <textarea class="form-control" id="success_factors" rows="3" 
                          placeholder="What factors will drive your success?"></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Financial Summary</label>
                <textarea class="form-control" id="financial_summary" rows="3" 
                          placeholder="Brief overview of financial projections..."></textarea>
            </div>
        `,
        company_description: `
            <div class="mb-3">
                <label class="form-label">Company Name</label>
                <input type="text" class="form-control" id="company_name" placeholder="Your company name">
            </div>
            <div class="mb-3">
                <label class="form-label">Legal Structure</label>
                <select class="form-select" id="legal_structure">
                    <option value="">Select legal structure</option>
                    <option value="sole_trader">Sole Trader</option>
                    <option value="partnership">Partnership</option>
                    <option value="limited_company">Limited Company</option>
                    <option value="llp">Limited Liability Partnership</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Industry</label>
                <input type="text" class="form-control" id="industry" placeholder="e.g., Technology, Retail, Healthcare">
            </div>
            <div class="mb-3">
                <label class="form-label">Company History</label>
                <textarea class="form-control" id="company_history" rows="4" 
                          placeholder="How and when was the company founded?"></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Location</label>
                <input type="text" class="form-control" id="location" placeholder="Primary business location">
            </div>
        `,
        market_analysis: `
            <div class="mb-3">
                <label class="form-label">Target Market</label>
                <textarea class="form-control" id="target_market" rows="3" 
                          placeholder="Describe your target customers..."></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Market Size</label>
                <textarea class="form-control" id="market_size" rows="2" 
                          placeholder="What is the size of your target market?"></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Competition Analysis</label>
                <textarea class="form-control" id="competition" rows="4" 
                          placeholder="Who are your main competitors and how do you differentiate?"></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Market Trends</label>
                <textarea class="form-control" id="market_trends" rows="3" 
                          placeholder="What trends are affecting your market?"></textarea>
            </div>
        `,
        value_proposition: `
            <div class="mb-3">
                <label class="form-label">Value Proposition</label>
                <textarea class="form-control" id="value_prop" rows="3" 
                          placeholder="What unique value do you provide to customers?"></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Customer Problems</label>
                <textarea class="form-control" id="customer_problems" rows="3" 
                          placeholder="What problems are you solving for customers?"></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Solution</label>
                <textarea class="form-control" id="solution" rows="3" 
                          placeholder="How does your product/service solve these problems?"></textarea>
            </div>
        `
    };
    
    return contentTemplates[sectionId] || `
        <div class="mb-3">
            <label class="form-label">${sectionId.replace('_', ' ').toUpperCase()}</label>
            <textarea class="form-control" id="${sectionId}_content" rows="6" 
                      placeholder="Enter content for this section..."></textarea>
        </div>
    `;
}

function populateSectionData(sectionId, data) {
    Object.keys(data).forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.value = data[fieldId];
        }
    });
}

function saveSectionData() {
    const section = templateSections.sections[currentSectionIndex];
    const sectionData = {};
    
    // Get all form inputs in the current section
    const inputs = document.querySelectorAll('#sectionContent input, #sectionContent textarea, #sectionContent select');
    inputs.forEach(input => {
        if (input.value.trim()) {
            sectionData[input.id] = input.value.trim();
        }
    });
    
    businessPlanData.sections[section.id] = sectionData;
    businessPlanData.updatedAt = new Date().toISOString();
    
    // Update progress indicators
    updateSectionProgress(currentSectionIndex, sectionData);
    updateOverallProgress();
}

function updateSectionProgress(sectionIndex, sectionData) {
    const section = templateSections.sections[sectionIndex];
    const totalFields = document.querySelectorAll('#sectionContent input, #sectionContent textarea, #sectionContent select').length;
    const completedFields = Object.keys(sectionData).length;
    const progress = totalFields > 0 ? Math.round((completedFields / totalFields) * 100) : 0;
    
    // Update progress indicator
    document.getElementById(`progress-${sectionIndex}`).textContent = `${progress}%`;
    
    // Update status icon
    const statusIcon = document.getElementById(`status-${sectionIndex}`);
    if (progress === 100) {
        statusIcon.className = 'bi bi-check-circle-fill text-success me-2 section-status';
    } else if (progress > 0) {
        statusIcon.className = 'bi bi-circle-half text-warning me-2 section-status';
    } else {
        statusIcon.className = 'bi bi-circle me-2 section-status';
    }
}

function updateOverallProgress() {
    const totalSections = templateSections.sections.length;
    const completedSections = Object.keys(businessPlanData.sections).length;
    const progress = Math.round((completedSections / totalSections) * 100);
    
    document.getElementById('overallProgress').style.width = `${progress}%`;
}

function nextSection() {
    saveSectionData();
    
    if (currentSectionIndex < templateSections.sections.length - 1) {
        loadSection(currentSectionIndex + 1);
    } else {
        // Finish - show completion message
        showCompletionMessage();
    }
}

function previousSection() {
    saveSectionData();
    
    if (currentSectionIndex > 0) {
        loadSection(currentSectionIndex - 1);
    }
}

function saveProgress() {
    saveSectionData();
    showAlert('Progress saved successfully!', 'success');
}

function exportPlan() {
    saveSectionData();
    
    // Create a simple text export (in a real implementation, this would generate a PDF)
    let exportContent = `${businessPlanData.templateName}\n`;
    exportContent += `Generated on: ${new Date().toLocaleDateString()}\n\n`;
    
    templateSections.sections.forEach(section => {
        const sectionData = businessPlanData.sections[section.id];
        if (sectionData && Object.keys(sectionData).length > 0) {
            exportContent += `${section.title.toUpperCase()}\n`;
            exportContent += '='.repeat(section.title.length) + '\n\n';
            
            Object.entries(sectionData).forEach(([key, value]) => {
                exportContent += `${key.replace('_', ' ').toUpperCase()}:\n${value}\n\n`;
            });
        }
    });
    
    // Download as text file
    const blob = new Blob([exportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'business-plan.txt';
    a.click();
    URL.revokeObjectURL(url);
    
    showAlert('Business plan exported successfully!', 'success');
}

function showCompletionMessage() {
    document.getElementById('sectionContent').innerHTML = `
        <div class="text-center py-5">
            <i class="bi bi-check-circle display-1 text-success mb-3"></i>
            <h3>Congratulations!</h3>
            <p class="lead">You've completed your business plan.</p>
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <button class="btn btn-primary" onclick="exportPlan()">
                    <i class="bi bi-download me-1"></i>Download Plan
                </button>
                <button class="btn btn-outline-secondary" onclick="reviewPlan()">
                    <i class="bi bi-eye me-1"></i>Review Plan
                </button>
            </div>
        </div>
    `;
    
    document.getElementById('nextButton').style.display = 'none';
}

function reviewPlan() {
    // TODO: Implement plan review functionality
    showAlert('Plan review feature coming soon!', 'info');
}

function showAlert(message, type) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    document.body.insertAdjacentHTML('afterbegin', alertHTML);
    
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 3000);
}
</script>

<style>
.template-card {
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.section-status {
    font-size: 0.875rem;
}

.sticky-top {
    top: 20px;
}
</style>
{{end}}

{{define "title"}}Business Plan Builder - VelocityWave{{end}}